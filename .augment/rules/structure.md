---
type: "agent_requested"
description: "Example description"
---
# Cool Admin Project Structure

## Root Directory Layout
```
cool-admin/
├── cool-admin-java/     # Backend Spring Boot application
├── cool-admin-vue/      # Frontend Vue.js application
├── assets/              # Static assets and uploads
├── .kiro/              # Kiro AI assistant configuration
├── .cursor/            # Cursor IDE rules and configuration
└── .trae/              # Additional project rules
```

## Backend Structure (cool-admin-java/)

### Main Application Structure
```
src/main/java/com/cool/
├── CoolApplication.java          # Main application entry point
├── core/                         # Core framework components
│   ├── ai/                      # AI integration services
│   ├── annotation/              # Custom annotations
│   ├── base/                    # Base classes (BaseEntity, BaseService, etc.)
│   ├── cache/                   # Caching components
│   ├── config/                  # Configuration classes
│   ├── exception/               # Exception handling
│   ├── mybatis/                 # MyBatis extensions
│   ├── security/                # Security configuration
│   └── util/                    # Utility classes
└── modules/                      # Business modules
    ├── base/                    # Base system module (users, roles, menus)
    ├── task/                    # Task management
    ├── sop/                     # SOP (Standard Operating Procedures)
    ├── organization/            # Organization management
    ├── project/                 # Project management
    └── [other-modules]/         # Additional business modules
```

### Module Structure Pattern
Each business module follows this structure:
```
modules/[module-name]/
├── controller/                  # REST controllers
├── service/                     # Business logic services
│   └── impl/                   # Service implementations
├── entity/                      # JPA entities
├── mapper/                      # MyBatis mappers
├── dto/                         # Data transfer objects
└── enums/                       # Enumerations
```

## Frontend Structure (cool-admin-vue/)

### Main Application Structure
```
src/
├── App.vue                      # Root component
├── main.ts                      # Application entry point
├── config/                      # Configuration files
│   ├── dev.ts                  # Development config
│   ├── prod.ts                 # Production config
│   └── proxy.ts                # Proxy configuration
├── cool/                        # Cool framework core
│   ├── bootstrap/              # Application bootstrap
│   ├── hooks/                  # Reusable composition functions
│   ├── module/                 # Module management
│   ├── router/                 # Router configuration
│   ├── service/                # Service layer
│   └── utils/                  # Utility functions
├── modules/                     # Business modules
│   ├── base/                   # Base system module
│   ├── demo/                   # Demo examples
│   ├── task/                   # Task management
│   ├── sop/                    # SOP management
│   ├── organization/           # Organization management
│   └── [other-modules]/        # Additional modules
└── plugins/                     # Plugin system
    ├── crud/                   # CRUD plugin
    ├── element-ui/             # Element Plus integration
    ├── upload/                 # File upload plugin
    └── [other-plugins]/        # Additional plugins
```

### Module Structure Pattern
Each frontend module follows this structure:
```
modules/[module-name]/
├── views/                       # Page components
│   ├── index.vue               # Main list/table view
│   ├── detail.vue              # Detail view
│   └── [other-pages].vue       # Additional pages
├── components/                  # Reusable components
├── dict/                        # Data dictionaries
├── services/                    # Module-specific services (optional)
├── locales/                     # Internationalization
├── config.ts                    # Module configuration
└── index.ts                     # Module entry point
```

## Key Architectural Patterns

### Backend Patterns
- **Modular Architecture**: Business logic organized in separate modules
- **Base Classes**: Common functionality in BaseEntity, BaseService, BaseController
- **Annotation-Driven**: Heavy use of Spring annotations for configuration
- **Auto-Generation**: Automatic table creation from entity classes
- **Service Layer Pattern**: Clear separation between controllers and business logic

### Frontend Patterns
- **Composition API**: Vue 3 Composition API with TypeScript
- **Plugin Architecture**: Extensible plugin system
- **CRUD Components**: Reusable CRUD components via @cool-vue/crud
- **Module Federation**: Independent, self-contained modules
- **Service Auto-Generation**: EPS system generates services from backend APIs

## Configuration Conventions

### Backend Configuration
- Environment-specific configs: `application-{env}.yml`
- Custom properties under `cool.*` namespace
- Database auto-configuration via AutoTable
- AI services configured under `cool.ai.*`

### Frontend Configuration
- Environment configs in `src/config/`
- Module configs in each module's `config.ts`
- Plugin configurations in plugin directories
- Build configuration in `vite.config.ts`

## File Naming Conventions

### Backend
- **Entities**: `[Name]Entity.java`
- **Services**: `[Name]Service.java` and `[Name]ServiceImpl.java`
- **Controllers**: `[Name]Controller.java`
- **Mappers**: `[Name]Mapper.java`

### Frontend
- **Components**: PascalCase (e.g., `UserSelect.vue`)
- **Pages**: kebab-case (e.g., `user-list.vue`)
- **Services**: Auto-generated via EPS system
- **Modules**: kebab-case directory names