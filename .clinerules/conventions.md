# Cool Admin 开发规范

## 后端开发规范 (Java)

### JAVA项目遵循阿里巴巴java开发手册规范

### 项目架构规范

#### 模块化架构
- 业务逻辑按模块组织，每个模块独立且职责单一
- 核心框架组件位于 `core/` 目录
- 业务模块位于 `modules/` 目录下

#### 标准模块结构
```
modules/[module]/
├── controller/           # 控制器层
├── service/             # 服务层
│   └── impl/           # 服务实现
├── entity/             # 实体类
├── mapper/             # Mapper接口
├── dto/                # 数据传输对象
├── enums/              # 枚举类
```

### CRUD 最佳实践

#### 1. Entity（实体类）规范
- 实体类放在 `modules/[module]/entity/`，类名以 `Entity` 结尾
- 继承 `BaseEntity`，使用 `@Table` 注解指定表名和注释
- 字段使用 `@ColumnDefine` 注解，注明注释、类型、长度、默认值
- 不需要手写建表SQL，Cool Admin会自动建表

```java
import cn.hutool.json.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
@Table(value = "demo_example", comment = "示例表")
public class DemoExampleEntity extends BaseEntity<DemoExampleEntity> {
    @ColumnDefine(comment = "名称", length = 100)
    private String name;
    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;
}
```

#### 2. Mapper规范
- Mapper接口放在 `modules/[module]/mapper/`
- 继承 `BaseMapper<Entity>`，无需多余代码

```java
public interface DemoExampleMapper extends BaseMapper<DemoExampleEntity> {}
```

#### 3. Service规范
- Service接口放在 `modules/[module]/service/`，实现放在 `service/impl/`
- Service接口继承 `BaseService<Entity>`，实现继承 `BaseServiceImpl<Mapper, Entity>`
- 使用 `@Service` 注解

```java
public interface DemoExampleService extends BaseService<DemoExampleEntity> {}

@Service
public class DemoExampleServiceImpl extends BaseServiceImpl<DemoExampleMapper, DemoExampleEntity> implements DemoExampleService {}
```

#### 4. Controller规范
- Controller放在 `modules/[module]/controller/admin/`
- 继承 `BaseController<Service, Entity>`，使用 `@CoolRestController` 注解
- 使用 `@Tag` 注解标注接口分组和描述
- **必须重写 `init` 方法**，使用 TableDef 静态字段进行类型安全的查询
- **重要**: `fieldEq` 和 `keyWordLikeFields` 方法必须传入 `QueryColumn` 对象，不能传入字符串

```java
@Tag(name = "示例管理", description = "示例模块统一管理")
@CoolRestController(api = { "add", "delete", "update", "info", "page" })
public class AdminDemoExampleController extends BaseController<DemoExampleService, DemoExampleEntity> {
    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 引入TableDef以实现类型安全的查询
        DemoExampleEntityTableDef example = DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY;
        
        setListOption(
            createOp()
                .fieldEq(example.STATUS, example.TYPE)  // 使用QueryColumn，不是字符串
                .keyWordLikeFields(example.NAME, example.DESCRIPTION)  // 使用QueryColumn，不是字符串
        );
    }
}
```

#### 5. MyBatis-Flex查询规范
- 使用 `QueryWrapper.create()` 创建查询条件
- 复杂的 `or` 条件必须使用 lambda 表达式包装
- **错误写法**: `.and(wrapper -> wrapper.eq("field1", value1).or().eq("field2", value2))`
- **正确写法**: `.and(wrapper -> wrapper.eq("field1", value1)).or(wrapper -> wrapper.eq("field2", value2))`

```java
// 正确的复杂查询写法
QueryWrapper.create()
    .and(wrapper -> {
        wrapper.eq("target_entity_type", "PROJECT").eq("target_entity_id", projectId);
    })
    .or(wrapper -> {
        wrapper.eq("project_id", projectId);
    })
    .ge("create_time", startDate)
    .orderBy("create_time", false)
    .limit(limit);
```

#### 6. Service方法参数顺序规范
- 保持方法参数顺序的一致性，特别是在接口和实现类之间
- 常见的参数顺序: `userId, reason, durationMinutes` 而不是 `userId, durationMinutes, reason`
- 在Controller调用Service方法时，确保参数顺序与接口定义一致

### 开发最佳实践

#### 异常处理
- 使用 `CoolPreconditions.check(condition, message)` 进行参数校验
- 业务异常抛出 `CoolException`
- 统一异常处理通过 `GlobalExceptionHandler`

#### 事务管理
- 服务层方法添加 `@Transactional` 注解
- 只读操作使用 `@Transactional(readOnly = true)`

#### 缓存使用
```java
@Cacheable(value = "example", key = "#id")
public ExampleEntity getById(Long id) {
    return super.getById(id);
}
```

#### 响应格式
```java
// 成功响应
return R.ok(data);

// 失败响应  
return R.error("错误信息");
```

### 注释规范
- 所有 Service 层方法必须添加方法注释
- 复杂业务流、关键分支必须有详细的行内注释
- 核心业务流、AI能力调用、状态流转等核心代码，注释要覆盖设计意图

## 前端开发规范 (Vue.js)

### 组件开发规范

#### 组件定义规范
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script lang="ts" setup>
// 组件名定义 - 必须
defineOptions({
  name: "module-component-name" // 格式：模块-组件名
});

// 导入
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';

// Props定义
interface Props {
  modelValue?: any;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'change': [value: any];
}>();
</script>
```

### CRUD组件使用规范

#### 基本用法
- 使用 `useCrud`、`useTable`、`useUpsert` hooks
- 通过 ref 自动注入配置，**不要使用 v-bind 绑定**
- 正确写法：`<cl-table ref="Table" />`
- 错误写法：`<cl-table ref="Table" v-bind="Table" />`

#### 标准页面结构
```vue
<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <cl-multi-delete-btn />
      <cl-flex1 />
      <cl-search-key />
    </cl-row>
    
    <cl-row>
      <cl-table ref="Table" />
    </cl-row>
    
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>
```

### 服务调用规范

#### EPS系统规范

##### 基本原则
- EPS (Endpoint Service) 是Cool Admin的自动化API服务生成系统
- 严格使用 EPS 生成的服务路径，**不要手动创建服务文件**
- 服务路径由后端Controller的包结构和类名自动生成

##### 路径生成规则
EPS根据后端Controller生成前端服务路径，遵循以下规则：

1. **包名转换**：将Java包名转换为JavaScript对象路径
   ```java
   // 后端Controller路径
   com.cool.modules.organization.controller.admin.AdminProjectInfoController
   
   // 生成的EPS路径
   service.organization.project.info
   ```

2. **Controller名称处理**：
   - 移除 `Admin` 前缀和 `Controller` 后缀
   - 将驼峰命名转换为点分隔的小写路径
   - `AdminProjectInfoController` → `project.info`
   - `AdminProjectMemberController` → `project.member`

3. **API方法映射**：
   ```java
   // 后端AdminProjectInfoController方法
   @PostMapping("/options")
   public R options(@RequestBody OptionsRequest request)
   
   // 生成的EPS方法
   service.organization.project.info.options(data)
   ```
4. **特殊API方法映射**：
   ```java
   // 后端AdminProjectMemberController方法
   @PostMapping("/batch-add-members")
   public R batchAddMembers(@RequestBody BatchAddMembersRequest request)
   
   // 生成的EPS方法
   service.organization.project.member['batch-add-members'](data)
   ```

##### 正确的调用方式
```typescript
// ✅ 正确 - 使用EPS生成的服务路径
await service.organization.project.info.options();
await service.organization.project.member['batch-add-members'](data);
await service.sop.s.o.p.industry.active();
await service.base.sys.user.page(params);

// ❌ 错误 - 手动service.request调用
await service.request({
  url: '/admin/organization/project-info/options',
  method: 'GET'
});
```

##### 常见路径映射示例
| 后端Controller | EPS服务路径 | 说明 |
|---|---|---|
| `AdminBaseSysUserController` | `service.base.sys.user` | 基础用户管理 |
| `AdminSOPIndustryController` | `service.sop.s.o.p.industry` | SOP行业管理 |
| `AdminProjectInfoController` | `service.organization.project.info` | 项目信息管理 |
| `AdminProjectMemberController` | `service.organization.project.member` | 项目成员管理 |
| `AdminTaskInfoController` | `service.task.info` | 任务信息管理 |

##### 类型安全特性
- EPS自动生成TypeScript类型定义文件 (`eps.d.ts`)
- 提供完整的接口参数和返回值类型
- 支持IDE智能提示和类型检查

```typescript
// 自动类型提示
const result: ProjectInfoEntity = await service.organization.project.info.info({ id: 1 });
const list: ProjectInfoEntity[] = await service.organization.project.info.list();
```

##### 开发调试
- 开发环境可通过 `/__cool_eps` 接口刷新EPS配置
- 使用开发工具面板查看所有可用的EPS服务路径
- EPS配置文件位于 `build/cool/eps.json` 和 `build/cool/eps.d.ts`

##### 注意事项
- EPS路径中的特殊字符会被转换（如 `SOP` → `s.o.p`）
- 方法名会转换为小写，连字符会被移除
- 只有标注了 `@CoolRestController` 的Controller才会生成EPS服务

#### RestController接口调用
- 所有自定义 RestController 接口必须统一用 `service.request({ url, method, data })` 调用
- url 必须为全量 path，method 必须大写，参数放 data 字段

```javascript
await service.request({
  url: '/admin/sop/ai-task-generator/preview',
  method: 'POST',
  data: { ... }
})
```

### 组件命名规范
- 必须使用 `defineOptions({ name: "模块-页面" })` 定义组件名
- 组件名格式：`sop-industry`、`sop-template` 等，用于路由缓存
- Table 和 Upsert 首字母大写：`const Table = useTable()`

### 样式规范

#### SCSS使用规范
```scss
// 变量定义
$primary-color: #409eff;
$success-color: #67c23a;

// 混入定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 组件样式
.component-name {
  padding: 16px;
  
  &__header {
    @include flex-center;
    margin-bottom: 16px;
  }
}
```

### 性能优化

#### 组件懒加载
```typescript
// 路由懒加载
const routes = [
  {
    path: '/task/info',
    component: () => import('./views/info.vue')
  }
];

// 组件懒加载
const TaskStepsFlow = defineAsyncComponent(() => import('./components/task-steps-flow.vue'));
```

#### 防抖节流
```typescript
import { debounce, throttle } from 'lodash-es';

// 搜索防抖
const handleSearch = debounce(async (keyword: string) => {
  await loadData({ keyword });
}, 300);
```

## 开发流程规范

### 开发步骤
1. 先查看 `src/modules/demo` 的对应示例
2. 严格按照 demo 的代码结构编写
3. 避免使用复杂的 Element Plus 图标导入，优先使用 emoji 或简单图标

### 组件复用规范
- 组件应尽量复用，避免重复造轮子
- 单文件（.vue/.ts）代码行数建议不超过 500 行
- 拆分时优先按功能、UI块、业务逻辑分层
- 复用组件应放在 modules/xxx/components 或 plugins/xxx/components 目录

### 重要提醒
1. **组件命名**: 必须使用`defineOptions({ name: "模块-页面" })`定义组件名
2. **CRUD组件**: 使用`<cl-table ref="Table" />`而不是`<cl-table ref="Table" v-bind="Table" />`
3. **服务调用**: 严格使用EPS生成的服务路径，不要手动创建服务文件
4. **页面结构**: 复杂页面使用`<el-scrollbar>`包装，参考demo模块的标准写法
5. **开发流程**: 先查看demo模块示例，严格按照demo的代码结构编写

## AI增强功能开发

### AI能力与业务流分离
- AI 能力（如场景识别、对话、质量检查等）全部由 AILLMService 负责，**不包含任何业务流**
- 业务流（如任务生成、进度、SSE推送、工单/任务包/任务落地、历史记录等）全部由具体业务Service负责
- Controller 层只负责接收请求、参数校验、调用 Service 层、组装/返回响应

### Controller/Service 职责边界
- 超级管理员具有所有的数据权限,判断是否是超级管理员baseSysUserService.isSuperAdmin(userId)
- Controller 层：只负责接收请求、参数校验、权限校验、调用 Service 层、组装/返回响应
- 禁止API接口定义使用特殊字符，只能使用驼峰命名
- Service 层：只负责业务逻辑实现、数据处理、AI能力调用、事务控制
- 禁止 Service 之间循环依赖，业务流与 AI 能力分层清晰