{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "everything-search": {"command": "python -m mcp_server_everything_search", "env": {"EVERYTHING_SDK_PATH": "C:\\Dev\\sdk\\Everything-SDK\\dll\\Everything64.dll"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DEBUG": "false", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765"}, "autoApprove": ["interactive_feedback"]}, "SequentialThinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mcp_server_mysql": {"command": "npx", "args": ["-y", "@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "**************", "MYSQL_PORT": "13306", "MYSQL_USER": "root", "MYSQL_PASS": "A@1234cn", "MYSQL_DB": "cool", "MYSQL_POOL_SIZE": "10", "MYSQL_QUERY_TIMEOUT": "30000", "MYSQL_CACHE_TTL": "60000", "MYSQL_RATE_LIMIT": "100", "MYSQL_MAX_QUERY_COMPLEXITY": "1000", "MYSQL_SSL": "true", "ENABLE_LOGGING": "true", "MYSQL_LOG_LEVEL": "info", "MYSQL_METRICS_ENABLED": "true", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "true", "SCHEMA_DDL_PERMISSIONS": "true", "ALLOW_DDL_OPERATION": "true", "MULTI_DB_WRITE_MODE": "true"}}, "time": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/3a42faaa817849/sse"}, "12306-mcp": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/4679493b196242/sse"}, "mcp-trends-hub": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/321fb6b1b54445/sse"}, "amap-maps": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/0c606765bf5c48/sse"}, "Context7": {"url": "https://mcp.context7.com/mcp"}, "Playwright": {"command": "npx @playwright/mcp@latest", "env": {}}}}