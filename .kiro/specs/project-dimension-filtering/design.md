# 项目维度筛选功能技术设计文档

## 1. 技术架构概述

### 1.1 设计原则
- **最小化改动**：在现有架构基础上进行扩展，避免大规模重构
- **权限一致性**：保持与现有双维度权限体系的一致性
- **性能优化**：合理使用缓存和索引，确保筛选性能
- **用户体验**：提供直观、高效的筛选界面

### 1.2 架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    项目维度筛选系统                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue3 + TypeScript)                                 │
│  ├── 项目筛选组件 (ProjectFilter)                           │
│  ├── 工单管理页面 (集成筛选)                                 │
│  ├── 任务包管理页面 (集成筛选)                               │
│  ├── 任务管理页面 (集成筛选)                                 │
│  └── 顶部导航栏 (移除切换器)                                 │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Spring Boot)                                       │
│  ├── 项目访问权限服务                                        │
│  ├── 工单筛选服务扩展                                        │
│  ├── 任务包筛选服务扩展                                      │
│  └── 任务筛选服务扩展                                        │
├─────────────────────────────────────────────────────────────┤
│  数据层 (MySQL + Redis)                                     │
│  ├── 现有业务表 (添加索引优化)                               │
│  ├── 用户项目权限缓存                                        │
│  └── 筛选结果缓存                                            │
└─────────────────────────────────────────────────────────────┘
```

## 2. 数据库设计

### 2.1 现有表结构分析

#### 2.1.1 相关业务表
```sql
-- 工单表 (work_order)
-- 已有字段包含 project_id，可直接用于筛选

-- 任务包表 (task_package) 
-- 已有字段包含 project_id，可直接用于筛选

-- 任务信息表 (task_info)
-- 需要通过 package_id 关联到 task_package 获取 project_id

-- 项目信息表 (project_info)
-- 用于获取项目基本信息

-- 用户组织关系表 (user_organization)
-- 用于获取用户的项目权限
```

### 2.2 索引优化

#### 2.2.1 新增索引
```sql
-- 工单表项目ID索引
ALTER TABLE work_order ADD INDEX idx_project_id (project_id);

-- 任务包表项目ID索引  
ALTER TABLE task_package ADD INDEX idx_project_id (project_id);

-- 任务信息表包ID索引（如果不存在）
ALTER TABLE task_info ADD INDEX idx_package_id (package_id);

-- 用户组织关系表复合索引
ALTER TABLE user_organization ADD INDEX idx_user_org_type (user_id, organization_type, status);
```

### 2.3 缓存设计
```
用户可访问项目列表: user:accessible:projects:{userId} (10分钟)
项目基本信息: project:info:{projectId} (30分钟)
筛选结果缓存: filter:result:{userId}:{entityType}:{projectId} (5分钟)
```

## 3. 后端技术实现

### 3.1 项目访问权限服务

#### 3.1.1 服务接口定义
```java
@Service
@RequiredArgsConstructor
public class ProjectAccessService {
    
    private final UserOrganizationService userOrganizationService;
    private final ProjectInfoService projectInfoService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 获取用户可访问的项目列表
     */
    @Cacheable(value = "user:accessible:projects", key = "#userId")
    public List<ProjectAccessDTO> getUserAccessibleProjects(Long userId) {
        // 获取用户在项目维度的组织关系
        List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
            userId, OrganizationModeEnum.PROJECT.getCode());
        
        if (projectRoles.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取项目基本信息
        List<Long> projectIds = projectRoles.stream()
            .filter(role -> role.getStatus() == 1)
            .filter(role -> role.getExpireTime() == null || role.getExpireTime().after(new Date()))
            .map(UserOrganizationEntity::getOrganizationId)
            .distinct()
            .collect(Collectors.toList());
        
        List<ProjectInfoEntity> projects = projectInfoService.listByIds(projectIds);
        
        // 构建返回数据
        return projects.stream()
            .map(project -> {
                ProjectAccessDTO dto = new ProjectAccessDTO();
                dto.setProjectId(project.getId());
                dto.setProjectName(project.getProjectName());
                dto.setProjectCode(project.getProjectCode());
                
                // 获取用户在该项目中的角色
                String userRole = projectRoles.stream()
                    .filter(role -> role.getOrganizationId().equals(project.getId()))
                    .map(UserOrganizationEntity::getRoleCode)
                    .findFirst()
                    .orElse(null);
                dto.setUserRole(userRole);
                
                return dto;
            })
            .collect(Collectors.toList());
    }
    
    /**
     * 验证用户是否有项目访问权限
     */
    public boolean hasProjectAccess(Long userId, Long projectId) {
        if (projectId == null) {
            return true; // 不筛选项目时默认有权限
        }
        
        List<ProjectAccessDTO> accessibleProjects = getUserAccessibleProjects(userId);
        return accessibleProjects.stream()
            .anyMatch(project -> project.getProjectId().equals(projectId));
    }
}
```

#### 3.1.2 数据传输对象
```java
@Data
public class ProjectAccessDTO {
    private Long projectId;
    private String projectName;
    private String projectCode;
    private String userRole;
    private Date joinTime;
}
```

### 3.2 Controller层扩展

#### 3.2.1 工单Controller扩展
```java
@Tag(name = "工作工单管理", description = "工作工单的增删改查及状态管理")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
public class AdminWorkOrderController extends BaseController<WorkOrderService, WorkOrderEntity> {

    @Autowired
    private ProjectAccessService projectAccessService;

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        WorkOrderEntityTableDef workOrder = WorkOrderEntityTableDef.WORK_ORDER_ENTITY;
        
        setPageOption(
            createOp()
                .fieldEq(
                    workOrder.STATUS,
                    workOrder.PRIORITY,
                    workOrder.ASSIGNEE_ID,
                    workOrder.PROJECT_ID  // 添加项目ID筛选
                )
                .keyWordLikeFields(
                    workOrder.TITLE,
                    workOrder.DESCRIPTION
                )
        );
        
        // 应用项目权限过滤
        applyProjectPermissionFilter(requestParams);
    }
    
    /**
     * 应用项目权限过滤
     */
    private void applyProjectPermissionFilter(JSONObject requestParams) {
        Long userId = CoolSecurityUtil.getAdminUserId();
        Long projectId = requestParams.getLong("projectId");
        
        // 验证项目访问权限
        if (projectId != null && !projectAccessService.hasProjectAccess(userId, projectId)) {
            throw new CoolException("无权限访问该项目的工单");
        }
        
        // 如果没有指定项目，则只显示用户有权限的项目工单
        if (projectId == null) {
            List<ProjectAccessDTO> accessibleProjects = projectAccessService.getUserAccessibleProjects(userId);
            List<Long> accessibleProjectIds = accessibleProjects.stream()
                .map(ProjectAccessDTO::getProjectId)
                .collect(Collectors.toList());
            
            if (!accessibleProjectIds.isEmpty()) {
                // 添加项目权限过滤条件
                setPageOption(getPageOption().fieldIn(WorkOrderEntityTableDef.WORK_ORDER_ENTITY.PROJECT_ID, accessibleProjectIds));
            }
        }
    }
    
    /**
     * 获取用户可访问的项目列表
     */
    @Operation(summary = "获取用户可访问的项目列表")
    @GetMapping("/accessible-projects")
    public R<List<ProjectAccessDTO>> getAccessibleProjects() {
        Long userId = CoolSecurityUtil.getAdminUserId();
        List<ProjectAccessDTO> projects = projectAccessService.getUserAccessibleProjects(userId);
        return R.ok(projects);
    }
}
```

## 4. 前端技术实现

### 4.1 项目筛选组件

#### 4.1.1 组件定义
```vue
<template>
  <div class="project-filter">
    <el-select
      v-model="selectedProjectId"
      placeholder="选择项目"
      clearable
      filterable
      :loading="loading"
      @change="handleProjectChange"
      style="width: 200px"
    >
      <el-option
        v-for="project in projectList"
        :key="project.projectId"
        :label="project.projectName"
        :value="project.projectId"
      >
        <div class="project-option">
          <span class="project-name">{{ project.projectName }}</span>
          <el-tag
            v-if="project.userRole"
            size="small"
            type="info"
            effect="plain"
          >
            {{ getRoleName(project.userRole) }}
          </el-tag>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';

// Props
interface Props {
  modelValue?: number | null;
  apiPath: string; // API路径，如 '/admin/sop/work-order/accessible-projects'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number | null];
  'change': [value: number | null];
}>();

// 组件名定义
defineOptions({
  name: "project-filter"
});

// 响应式数据
const { service } = useCool();
const selectedProjectId = ref<number | null>(props.modelValue);
const projectList = ref<ProjectAccessDTO[]>([]);
const loading = ref(false);

// 接口数据类型
interface ProjectAccessDTO {
  projectId: number;
  projectName: string;
  projectCode: string;
  userRole: string;
  joinTime?: string;
}

// 方法
const loadProjectList = async () => {
  loading.value = true;
  try {
    const response = await service.request({
      url: props.apiPath,
      method: 'GET'
    });
    
    if (response.code === 200) {
      projectList.value = response.data || [];
    } else {
      ElMessage.error('获取项目列表失败');
    }
  } catch (error: any) {
    console.error('加载项目列表失败:', error);
    ElMessage.error('获取项目列表失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

const handleProjectChange = (value: number | null) => {
  selectedProjectId.value = value;
  emit('update:modelValue', value);
  emit('change', value);
};

const getRoleName = (roleCode: string): string => {
  const roleMap: Record<string, string> = {
    'PROJECT_OWNER': '负责人',
    'PROJECT_ADMIN': '管理员',
    'PROJECT_MEMBER': '成员',
    'PROJECT_VIEWER': '观察者'
  };
  return roleMap[roleCode] || roleCode;
};

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  selectedProjectId.value = newValue;
});

// 生命周期
onMounted(() => {
  loadProjectList();
});
</script>

<style lang="scss" scoped>
.project-filter {
  .project-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    
    .project-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
```

### 4.2 顶部导航栏优化

#### 4.2.1 移除组织模式切换器
```vue
<template>
	<div class="app-topbar">
		<div class="cl-comm__icon mr-[10px]" @click="app.fold()">
			<cl-svg name="fold" v-if="app.isFold" />
			<cl-svg name="expand" v-else />
		</div>

		<!-- 路由导航 -->
		<a-menu v-if="app.info.menu.isGroup" />
		<route-nav v-else />

		<div class="flex1"></div>

		<!-- 工具栏 -->
		<ul class="app-topbar__tools">
			<!-- 移除组织模式切换器 -->
			<li v-for="(item, index) in toolbarComponents" :key="index">
				<component :is="item.component" />
			</li>
		</ul>

		<!-- 用户信息 -->
		<template v-if="user.info">
			<el-dropdown
				hide-on-click
				popper-class="app-topbar__user-popper"
				:popper-options="{}"
				@command="onCommand"
			>
				<div class="app-topbar__user">
					<el-text class="mr-[10px]">{{ user.info.nickName }}</el-text>
					<cl-avatar :size="26" :src="user.info.headImg" />
				</div>

				<template #dropdown>
					<div class="user">
						<cl-avatar :size="34" :src="user.info.headImg" />

						<div class="det">
							<el-text size="small" tag="p">{{ user.info.nickName }}</el-text>
							<el-text size="small" type="info">{{ user.info.email }}</el-text>
						</div>
					</div>

					<el-dropdown-menu>
						<el-dropdown-item command="my">
							<cl-svg name="my" />
							<span>{{ t('个人中心') }}</span>
						</el-dropdown-item>
						<el-dropdown-item command="exit">
							<cl-svg name="exit" />
							<span>{{ t('退出登录') }}</span>
						</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</template>
	</div>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'app-topbar'
});

import { computed, markRaw, onMounted, reactive } from 'vue';
import { isFunction, orderBy } from 'lodash-es';
import { useBase } from '/$/base';
import { module, useCool } from '/@/cool';
import { ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
import RouteNav from './route-nav.vue';
import AMenu from './amenu.vue';
// 移除组织模式切换器的导入

const { router, service, browser } = useCool();
const { user, app } = useBase();
const { t } = useI18n();

// 其余代码保持不变...
</script>
```

## 5. 性能优化

### 5.1 数据库查询优化

#### 5.1.1 索引策略
```sql
-- 工单表优化索引
CREATE INDEX idx_work_order_project_status ON work_order(project_id, status);
CREATE INDEX idx_work_order_create_time ON work_order(create_time DESC);

-- 任务包表优化索引
CREATE INDEX idx_task_package_project_status ON task_package(project_id, status);
CREATE INDEX idx_task_package_create_time ON task_package(create_time DESC);

-- 任务信息表优化索引
CREATE INDEX idx_task_info_package_status ON task_info(package_id, task_status);
CREATE INDEX idx_task_info_create_time ON task_info(create_time DESC);

-- 用户组织关系表优化索引
CREATE INDEX idx_user_org_user_type_status ON user_organization(user_id, organization_type, status);
```

### 5.2 缓存策略
```java
@Configuration
public class ProjectFilterCacheConfig {
    
    @Bean
    public CacheManager projectFilterCacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory)
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

## 6. 安全设计

### 6.1 权限验证
```java
@Aspect
@Component
@RequiredArgsConstructor
public class ProjectFilterSecurityAspect {
    
    private final ProjectAccessService projectAccessService;
    
    @Around("@annotation(projectFilter)")
    public Object checkProjectAccess(ProceedingJoinPoint joinPoint, 
                                   ProjectFilter projectFilter) throws Throwable {
        Long currentUserId = CoolSecurityUtil.getAdminUserId();
        
        // 获取项目ID参数
        Object[] args = joinPoint.getArgs();
        Long projectId = extractProjectId(args, projectFilter.projectIdParam());
        
        // 验证项目访问权限
        if (projectId != null && !projectAccessService.hasProjectAccess(currentUserId, projectId)) {
            throw new ProjectAccessException("无权限访问项目ID: " + projectId);
        }
        
        return joinPoint.proceed();
    }
    
    private Long extractProjectId(Object[] args, String paramName) {
        // 从参数中提取项目ID的逻辑
        return null; // 具体实现
    }
}
```

## 7. 部署方案

### 7.1 数据库升级脚本
```sql
-- 项目筛选功能数据库升级脚本
-- 版本: 1.0.0

-- 1. 添加索引优化查询性能
ALTER TABLE work_order ADD INDEX idx_work_order_project_id (project_id);
ALTER TABLE task_package ADD INDEX idx_task_package_project_id (project_id);
ALTER TABLE task_info ADD INDEX idx_task_info_package_id (package_id);
ALTER TABLE user_organization ADD INDEX idx_user_org_user_type_status (user_id, organization_type, status);

-- 2. 创建复合索引提升查询效率
CREATE INDEX idx_work_order_project_status_time ON work_order(project_id, status, create_time DESC);
CREATE INDEX idx_task_package_project_status_time ON task_package(project_id, status, create_time DESC);
```

### 7.2 配置文件更新
```yaml
# application.yml 配置更新
spring:
  cache:
    type: redis
    redis:
      time-to-live: 600000 # 10分钟
      
# 项目筛选功能配置
cool:
  project-filter:
    cache:
      enabled: true
      ttl: 600 # 10分钟
    performance:
      max-projects-per-user: 100
      query-timeout: 5000 # 5秒
```

## 8. 总结

本技术设计文档详细描述了项目维度筛选功能的实现方案，主要包括：

### 8.1 核心特性
- **项目筛选功能**：为工单、任务包、任务管理页面添加项目维度筛选
- **权限集成**：与现有双维度权限体系无缝集成
- **界面优化**：移除顶部导航栏的组织模式切换器
- **性能优化**：通过缓存和索引优化提升查询性能

### 8.2 技术亮点
- **最小化改动**：在现有架构基础上进行扩展
- **权限一致性**：保持与现有权限体系的一致性
- **缓存策略**：多层缓存提升系统性能
- **错误处理**：完善的错误处理和用户提示

### 8.3 安全保障
- **权限验证**：严格的项目访问权限验证
- **参数校验**：完整的输入参数验证
- **操作审计**：详细的操作日志记录

该设计方案既满足了业务需求，又保持了系统的稳定性和可扩展性，为用户提供了更好的使用体验。