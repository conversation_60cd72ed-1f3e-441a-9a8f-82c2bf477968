# 项目维度筛选功能需求文档

## 1. 项目背景

### 1.1 现状分析
当前Cool Admin系统已实现双维度组织架构（部门维度和项目维度），但在工单管理、任务包管理、任务管理等核心业务模块中，缺乏对项目维度的有效筛选功能。同时，顶部菜单的维度切换功能对用户造成了困扰，需要进行优化。

### 1.2 业务需求
- 在工单管理、任务包管理、任务管理界面中增加项目维度筛选功能
- 移除顶部菜单中的组织维度切换器，简化用户界面
- 保持现有的双维度权限体系不变

## 2. 需求概述

### 2.1 核心目标
- **增强筛选功能**：为工单、任务包、任务管理页面添加项目维度筛选
- **简化用户界面**：移除顶部导航栏的组织模式切换器
- **保持兼容性**：确保现有功能和权限体系不受影响

### 2.2 目标用户
- **项目经理**：需要快速筛选特定项目的工单和任务
- **普通员工**：需要查看自己参与项目的相关任务
- **系统管理员**：需要管理跨项目的工单和任务

## 3. 功能需求

### 3.1 项目维度筛选功能

#### 3.1.1 工单管理筛选
- **筛选条件**：在工单列表页面添加项目筛选下拉框
- **筛选逻辑**：根据选择的项目ID过滤工单列表
- **权限控制**：只显示用户有权限访问的项目
- **默认行为**：默认显示所有有权限的工单

#### 3.1.2 任务包管理筛选
- **筛选条件**：在任务包列表页面添加项目筛选下拉框
- **筛选逻辑**：根据选择的项目ID过滤任务包列表
- **权限控制**：只显示用户有权限访问的项目
- **默认行为**：默认显示所有有权限的任务包

#### 3.1.3 任务管理筛选
- **筛选条件**：在任务列表页面添加项目筛选下拉框
- **筛选逻辑**：根据选择的项目ID过滤任务列表
- **权限控制**：只显示用户有权限访问的项目
- **默认行为**：默认显示所有有权限的任务

### 3.2 顶部菜单优化

#### 3.2.1 移除组织模式切换器
- **移除位置**：从顶部导航栏工具栏中移除组织模式切换器
- **保留功能**：保留组织模式切换的后端API和Store功能
- **替代方案**：可在个人中心或系统设置中提供组织模式切换功能

#### 3.2.2 界面简化
- **布局调整**：调整顶部导航栏布局，移除切换器后的空间优化
- **用户体验**：简化界面，减少用户操作复杂度

## 4. 技术需求

### 4.1 后端接口扩展

#### 4.1.1 项目列表接口
- **接口路径**：`GET /admin/organization/project/user-accessible`
- **功能描述**：获取当前用户可访问的项目列表
- **返回数据**：项目ID、项目名称、用户在项目中的角色

#### 4.1.2 筛选参数支持
- **工单接口**：`AdminWorkOrderController` 支持 `projectId` 筛选参数
- **任务包接口**：`AdminTaskPackageController` 支持 `projectId` 筛选参数
- **任务接口**：`AdminTaskInfoController` 支持 `projectId` 筛选参数

### 4.2 前端组件开发

#### 4.2.1 项目筛选组件
- **组件名称**：`ProjectFilter`
- **功能**：提供项目下拉选择和筛选功能
- **位置**：集成到各管理页面的筛选区域

#### 4.2.2 页面改造
- **工单管理页面**：集成项目筛选组件
- **任务包管理页面**：集成项目筛选组件
- **任务管理页面**：集成项目筛选组件
- **顶部导航栏**：移除组织模式切换器

## 5. 用户界面需求

### 5.1 筛选界面设计

#### 5.1.1 筛选条件布局
- **位置**：在页面顶部搜索区域添加项目筛选下拉框
- **样式**：与现有筛选条件保持一致的设计风格
- **交互**：支持搜索、清空、多选等功能

#### 5.1.2 筛选状态显示
- **当前筛选**：显示当前选择的项目名称
- **筛选结果**：显示筛选后的数据数量
- **清空筛选**：提供清空筛选条件的快捷操作

### 5.2 顶部导航栏优化

#### 5.2.1 布局调整
- **移除元素**：移除组织模式切换器
- **空间利用**：优化剩余空间的布局
- **保持美观**：确保移除后界面仍然美观协调

## 6. 权限和安全需求

### 6.1 项目权限验证
- **访问控制**：用户只能看到有权限访问的项目
- **数据过滤**：根据项目权限过滤工单、任务包、任务数据
- **操作权限**：确保用户只能操作有权限的项目数据

### 6.2 安全性要求
- **参数验证**：对项目ID参数进行严格验证
- **权限检查**：在后端接口中进行权限检查
- **日志记录**：记录项目筛选和访问操作日志

## 7. 性能需求

### 7.1 查询性能
- **索引优化**：确保项目ID相关查询有适当的数据库索引
- **缓存策略**：对用户可访问项目列表进行缓存
- **分页支持**：大数据量时支持分页加载

### 7.2 用户体验
- **响应时间**：筛选操作响应时间 < 1秒
- **加载状态**：提供加载状态指示
- **错误处理**：友好的错误提示和处理

## 8. 兼容性需求

### 8.1 现有功能兼容
- **API兼容**：保持现有API接口的向后兼容
- **数据兼容**：不影响现有数据结构和业务逻辑
- **权限兼容**：保持现有权限体系不变

### 8.2 浏览器兼容
- **主流浏览器**：支持Chrome、Firefox、Safari、Edge
- **响应式设计**：支持不同屏幕尺寸的设备
- **移动端适配**：确保移动端界面正常显示

## 9. 验收标准

### 9.1 功能验收
- [ ] 工单管理页面成功添加项目筛选功能
- [ ] 任务包管理页面成功添加项目筛选功能
- [ ] 任务管理页面成功添加项目筛选功能
- [ ] 项目筛选功能正确过滤数据
- [ ] 顶部导航栏成功移除组织模式切换器
- [ ] 用户只能看到有权限的项目选项

### 9.2 性能验收
- [ ] 筛选操作响应时间 < 1秒
- [ ] 项目列表加载时间 < 500ms
- [ ] 大数据量筛选不影响页面性能
- [ ] 缓存机制正常工作

### 9.3 兼容性验收
- [ ] 现有功能正常运行
- [ ] 权限体系保持不变
- [ ] 主流浏览器正常显示
- [ ] 移动端界面适配良好

## 10. 风险评估

### 10.1 技术风险
- **数据查询性能**：大量项目数据可能影响查询性能
- **权限复杂性**：项目权限验证可能增加系统复杂性
- **界面兼容性**：移除切换器可能影响现有界面布局

### 10.2 业务风险
- **用户适应性**：用户需要适应新的筛选方式
- **功能缺失**：移除切换器可能影响部分用户的使用习惯
- **数据准确性**：筛选逻辑错误可能导致数据显示不准确

### 10.3 风险缓解措施
- 充分的性能测试和优化
- 详细的权限验证逻辑测试
- 提供用户使用指南和培训
- 建立完善的错误处理机制

## 11. 实施计划

### 11.1 开发阶段
- **第一阶段（1周）**：后端接口开发和权限验证
- **第二阶段（1周）**：前端组件开发和页面集成
- **第三阶段（0.5周）**：顶部导航栏优化和界面调整
- **第四阶段（0.5周）**：测试和优化

### 11.2 测试计划
- **单元测试**：后端接口和前端组件测试
- **集成测试**：整体功能流程测试
- **性能测试**：筛选功能性能测试
- **用户测试**：用户体验和界面测试

## 12. 成功指标

### 12.1 技术指标
- 筛选功能响应时间 < 1秒
- 项目权限验证准确率 100%
- 系统稳定性 > 99.9%
- 零安全漏洞

### 12.2 业务指标
- 用户筛选功能使用率 > 70%
- 用户满意度 > 85%
- 界面操作效率提升 > 20%
- 零功能回退问题