# 项目维度筛选功能实施任务列表

## 1. 后端基础架构开发

- [ ] 1.1 创建项目访问权限服务
  - 创建 `ProjectAccessService` 服务类
  - 实现 `getUserAccessibleProjects` 方法获取用户可访问项目列表
  - 实现 `hasProjectAccess` 方法验证项目访问权限
  - 添加 Redis 缓存支持，缓存用户项目列表
  - 编写单元测试验证服务功能
  - _Requirements: 3.1.1, 3.1.2, 3.1.3_

- [ ] 1.2 创建项目访问数据传输对象
  - 创建 `ProjectAccessDTO` 类定义项目访问信息结构
  - 包含项目ID、项目名称、项目编码、用户角色等字段
  - 添加必要的验证注解和序列化配置
  - _Requirements: 3.1.1, 3.1.2_

- [ ] 1.3 数据库索引优化
  - 为 `work_order` 表添加 `project_id` 索引
  - 为 `task_package` 表添加 `project_id` 索引
  - 为 `task_info` 表添加 `package_id` 索引（如不存在）
  - 为 `user_organization` 表添加复合索引 `(user_id, organization_type, status)`
  - 创建复合索引优化查询性能
  - 编写数据库升级脚本
  - _Requirements: 7.1_

## 2. 工单管理筛选功能开发

- [ ] 2.1 扩展工单Controller支持项目筛选
  - 修改 `AdminWorkOrderController` 的 `init` 方法
  - 添加 `project_id` 字段到筛选条件中
  - 实现 `applyProjectPermissionFilter` 方法应用项目权限过滤
  - 添加项目访问权限验证逻辑
  - 当未指定项目时，只显示用户有权限的项目工单
  - _Requirements: 3.1.1, 3.1.2_

- [ ] 2.2 添加工单项目列表接口
  - 在 `AdminWorkOrderController` 中添加 `getAccessibleProjects` 接口
  - 返回用户可访问的项目列表用于前端筛选组件
  - 添加接口文档注解和错误处理
  - 编写接口测试验证功能
  - _Requirements: 3.1.1, 4.1.1_

- [ ] 2.3 工单Service层权限过滤支持
  - 确保工单查询时正确应用项目权限过滤
  - 添加必要的权限验证逻辑
  - 优化查询性能，避免N+1查询问题
  - _Requirements: 3.1.1, 6.1_

## 3. 任务包管理筛选功能开发

- [ ] 3.1 扩展任务包Controller支持项目筛选
  - 修改 `AdminTaskPackageController` 的 `init` 方法
  - 添加 `project_id` 字段到筛选条件中
  - 实现 `applyProjectPermissionFilter` 方法应用项目权限过滤
  - 添加项目访问权限验证逻辑
  - 当未指定项目时，只显示用户有权限的项目任务包
  - _Requirements: 3.1.1, 3.1.2_

- [ ] 3.2 添加任务包项目列表接口
  - 在 `AdminTaskPackageController` 中添加 `getAccessibleProjects` 接口
  - 返回用户可访问的项目列表用于前端筛选组件
  - 添加接口文档注解和错误处理
  - 编写接口测试验证功能
  - _Requirements: 3.1.1, 4.1.1_

- [ ] 3.3 扩展任务包Service支持项目查询
  - 在 `TaskPackageServiceImpl` 中添加 `getPackageIdsByProjectId` 方法
  - 添加 `getPackageIdsByProjectIds` 方法支持批量项目查询
  - 优化查询性能，使用合适的索引
  - 添加缓存支持提升查询效率
  - _Requirements: 3.3.1_

## 4. 任务管理筛选功能开发

- [ ] 4.1 扩展任务Controller支持项目筛选
  - 修改 `AdminTaskInfoController` 的 `init` 方法
  - 实现 `applyProjectPermissionFilter` 方法
  - 通过任务包关联实现项目筛选（任务->任务包->项目）
  - 添加项目访问权限验证逻辑
  - 处理项目无任务包的边界情况
  - _Requirements: 3.1.1, 3.1.3_

- [ ] 4.2 添加任务项目列表接口
  - 在 `AdminTaskInfoController` 中添加 `getAccessibleProjects` 接口
  - 返回用户可访问的项目列表用于前端筛选组件
  - 添加接口文档注解和错误处理
  - 编写接口测试验证功能
  - _Requirements: 3.1.1, 4.1.1_

- [ ] 4.3 任务Service层项目关联查询优化
  - 优化任务通过任务包关联项目的查询逻辑
  - 添加批量查询支持，避免N+1查询问题
  - 实现查询结果缓存提升性能
  - _Requirements: 6.1_

## 5. 前端项目筛选组件开发

- [ ] 5.1 创建项目筛选组件
  - 创建 `ProjectFilter.vue` 组件
  - 实现项目下拉选择功能，支持搜索和清空
  - 显示项目名称和用户在项目中的角色
  - 支持 v-model 双向绑定和 change 事件
  - 添加加载状态和错误处理
  - _Requirements: 4.1.1_

- [ ] 5.2 项目筛选组件样式设计
  - 设计符合系统风格的组件样式
  - 实现响应式布局，适配不同屏幕尺寸
  - 添加项目选项的角色标签显示
  - 优化用户交互体验
  - _Requirements: 5.1.1, 5.1.2_

- [ ] 5.3 项目筛选组件功能测试
  - 编写组件单元测试
  - 测试组件的各种交互场景
  - 验证数据绑定和事件触发
  - 测试错误处理和边界情况
  - _Requirements: 11.3_

## 6. 工单管理页面集成筛选功能

- [ ] 6.1 工单管理页面集成项目筛选
  - 在工单管理页面中集成 `ProjectFilter` 组件
  - 配置正确的API路径 `/admin/sop/work-order/accessible-projects`
  - 实现筛选变化时的数据刷新逻辑
  - 调整页面布局，合理放置筛选组件
  - _Requirements: 4.2.1_

- [ ] 6.2 工单列表表格列调整
  - 在工单列表中添加"所属项目"列显示
  - 优化列宽和显示顺序
  - 确保项目信息正确显示
  - _Requirements: 4.2.1_

- [ ] 6.3 工单创建/编辑表单项目选择
  - 在工单创建和编辑表单中添加项目选择功能
  - 使用项目筛选组件或类似的选择器
  - 添加表单验证确保项目选择的正确性
  - _Requirements: 4.2.1_

## 7. 任务包管理页面集成筛选功能

- [ ] 7.1 任务包管理页面集成项目筛选
  - 在任务包管理页面中集成 `ProjectFilter` 组件
  - 配置正确的API路径 `/admin/task/package/accessible-projects`
  - 实现筛选变化时的数据刷新逻辑
  - 调整页面布局，合理放置筛选组件
  - _Requirements: 4.2.2_

- [ ] 7.2 任务包列表表格列调整
  - 在任务包列表中添加"所属项目"列显示
  - 优化列宽和显示顺序
  - 确保项目信息正确显示
  - _Requirements: 4.2.2_

- [ ] 7.3 任务包创建/编辑表单项目选择
  - 在任务包创建和编辑表单中添加项目选择功能
  - 使用项目筛选组件或类似的选择器
  - 添加表单验证确保项目选择的正确性
  - _Requirements: 4.2.2_

## 8. 任务管理页面集成筛选功能

- [ ] 8.1 任务管理页面集成项目筛选
  - 在任务管理页面中集成 `ProjectFilter` 组件
  - 配置正确的API路径 `/admin/task/info/accessible-projects`
  - 实现筛选变化时的数据刷新逻辑
  - 调整页面布局，合理放置筛选组件
  - _Requirements: 4.2.3_

- [ ] 8.2 任务列表表格列调整
  - 在任务列表中添加"所属项目"列显示
  - 通过任务包关联显示项目信息
  - 优化列宽和显示顺序
  - 确保项目信息正确显示
  - _Requirements: 4.2.3_

- [ ] 8.3 任务创建/编辑功能优化
  - 确保任务创建时正确关联到任务包和项目
  - 在任务编辑界面显示相关项目信息
  - 添加必要的验证逻辑
  - _Requirements: 4.2.3_

## 9. 顶部导航栏优化

- [ ] 9.1 移除组织模式切换器
  - 从 `topbar.vue` 组件中移除 `OrganizationModeSwitcher` 组件
  - 删除相关的导入语句和模板引用
  - 调整工具栏布局，优化剩余空间
  - _Requirements: 3.2.1, 4.3.1_

- [ ] 9.2 顶部导航栏布局优化
  - 优化移除切换器后的空间布局
  - 确保界面仍然美观协调
  - 测试不同屏幕尺寸下的显示效果
  - _Requirements: 3.2.2, 5.2.1_

- [ ] 9.3 保留组织模式切换功能
  - 保留后端组织模式切换的API和Store功能
  - 确保系统的双维度权限体系正常工作
  - 为将来可能的功能恢复保留代码结构
  - _Requirements: 3.2.1_

## 10. 缓存和性能优化

- [ ] 10.1 实现项目筛选缓存策略
  - 配置 Redis 缓存管理器
  - 实现用户项目列表缓存（10分钟TTL）
  - 实现项目基本信息缓存（30分钟TTL）
  - 实现筛选结果缓存（5分钟TTL）
  - _Requirements: 5.1, 5.2_

- [ ] 10.2 缓存更新和清理机制
  - 实现 `ProjectFilterCacheService` 缓存服务
  - 添加用户项目缓存清理方法
  - 添加项目相关缓存清理方法
  - 添加筛选结果缓存批量清理方法
  - 在相关数据变更时触发缓存清理
  - _Requirements: 5.2_

- [ ] 10.3 前端性能优化
  - 实现项目列表数据缓存（5分钟本地缓存）
  - 添加组件懒加载支持
  - 优化筛选组件的渲染性能
  - 添加防抖处理避免频繁请求
  - _Requirements: 6.2.1, 6.2.2_

## 11. 权限验证和安全加固

- [ ] 11.1 实现项目访问权限验证切面
  - 创建 `ProjectFilterSecurityAspect` 切面类
  - 实现 `@ProjectFilter` 注解和权限验证逻辑
  - 添加项目ID参数提取功能
  - 在相关Controller方法上应用权限验证
  - _Requirements: 6.1, 8.1_

- [ ] 11.2 参数验证和错误处理
  - 创建 `ProjectFilterValidator` 参数验证器
  - 实现项目ID格式和范围验证
  - 实现筛选参数合法性验证
  - 添加统一的异常处理机制
  - _Requirements: 6.2, 8.2_

- [ ] 11.3 安全异常处理
  - 创建 `ProjectFilterExceptionHandler` 异常处理器
  - 处理项目访问权限异常
  - 处理项目不存在异常
  - 处理筛选参数异常
  - 提供友好的错误提示信息
  - _Requirements: 7.1, 8.2_

## 12. 监控和日志记录

- [ ] 12.1 操作审计日志
  - 创建 `ProjectFilterAuditService` 审计服务
  - 记录项目筛选操作日志
  - 包含用户ID、实体类型、项目ID、操作时间等信息
  - 集成到现有的审计日志系统
  - _Requirements: 9.1_

- [ ] 12.2 性能监控指标
  - 创建 `ProjectFilterMetrics` 监控组件
  - 添加筛选请求计数器
  - 添加筛选响应时间计时器
  - 按实体类型分类统计指标
  - 集成到系统监控面板
  - _Requirements: 9.2_

- [ ] 12.3 错误日志和告警
  - 添加详细的错误日志记录
  - 实现关键错误的告警机制
  - 记录性能异常和超时情况
  - 提供日志查询和分析功能
  - _Requirements: 9.2_

## 13. 测试和质量保证

- [ ] 13.1 后端单元测试
  - 编写 `ProjectAccessService` 单元测试
  - 编写各Controller筛选功能测试
  - 编写权限验证逻辑测试
  - 编写缓存功能测试
  - 确保测试覆盖率 > 80%
  - _Requirements: 11.1_

- [ ] 13.2 后端集成测试
  - 编写工单筛选功能集成测试
  - 编写任务包筛选功能集成测试
  - 编写任务筛选功能集成测试
  - 测试权限验证和错误处理
  - 测试性能和并发场景
  - _Requirements: 11.2_

- [ ] 13.3 前端组件测试
  - 编写 `ProjectFilter` 组件单元测试
  - 测试组件的各种交互场景
  - 测试数据绑定和事件触发
  - 测试错误处理和边界情况
  - 编写页面集成测试
  - _Requirements: 11.3_

## 14. 配置和部署准备

- [ ] 14.1 配置文件更新
  - 更新 `application.yml` 添加项目筛选相关配置
  - 配置缓存TTL和性能参数
  - 配置安全和监控参数
  - 添加环境特定的配置项
  - _Requirements: 7.2_

- [ ] 14.2 数据库升级脚本
  - 编写完整的数据库升级脚本
  - 包含索引创建和优化
  - 添加脚本执行验证
  - 准备回滚脚本
  - _Requirements: 10.1_

- [ ] 14.3 部署文档和说明
  - 编写功能部署指南
  - 说明配置变更和注意事项
  - 提供功能验证步骤
  - 准备用户使用说明
  - _Requirements: 10.1, 10.2_

## 15. 功能验证和优化

- [ ] 15.1 功能完整性验证
  - 验证工单管理项目筛选功能
  - 验证任务包管理项目筛选功能
  - 验证任务管理项目筛选功能
  - 验证顶部导航栏优化效果
  - 验证权限控制正确性
  - _Requirements: 9.1_

- [ ] 15.2 性能测试和优化
  - 测试筛选功能响应时间
  - 测试大数据量场景性能
  - 测试并发用户访问性能
  - 根据测试结果进行性能优化
  - _Requirements: 9.2_

- [ ] 15.3 用户体验测试
  - 测试界面交互的流畅性
  - 测试不同浏览器的兼容性
  - 测试移动端界面适配
  - 收集用户反馈并进行优化
  - _Requirements: 9.3_

## 16. 文档和培训

- [ ] 16.1 技术文档更新
  - 更新API接口文档
  - 更新系统架构文档
  - 更新开发规范文档
  - 更新部署运维文档
  - _Requirements: 14.3_

- [ ] 16.2 用户使用指南
  - 编写项目筛选功能使用指南
  - 制作功能演示视频
  - 准备常见问题解答
  - 组织用户培训会议
  - _Requirements: 8.1_

- [ ] 16.3 开发团队知识分享
  - 组织技术方案分享会
  - 分享开发过程中的经验和教训
  - 更新团队开发最佳实践
  - 建立功能维护和支持流程
  - _Requirements: 8.1_