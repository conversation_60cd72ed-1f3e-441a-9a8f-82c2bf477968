# Design Document

## Overview

项目选择器数据显示问题的根本原因是前端代码在处理后端返回数据时使用了错误的数据路径。后端接口返回的数据格式为 `{"code":1000,"message":"success","data":[{"value":1,"label":"测试项目"}]}`，但前端代码错误地使用了 `res.data` 而不是直接使用 `res`。

## Architecture

### 数据流架构
```
后端接口 -> EPS服务层 -> 前端组件
AdminProjectInfoController.options() -> service.organization.project.info.options() -> AIProjectSelector
```

### 问题分析
1. **后端返回格式**: `R.ok(options)` 返回 `{"code":1000,"message":"success","data":[...]}`
2. **EPS处理**: EPS系统自动解包装，直接返回data字段的内容
3. **前端错误**: 代码中使用 `res.data` 导致获取到 `undefined`

## Components and Interfaces

### AIProjectSelector组件
- **输入**: `options` 属性，期望格式为 `Array<{value: number, label: string}>`
- **输出**: `v-model` 绑定的项目ID数组
- **功能**: 多选下拉框，支持搜索过滤

### 数据获取服务
- **接口**: `service.organization.project.info.options()`
- **返回**: 直接返回项目选项数组，无需额外解包装
- **格式**: `[{value: 1, label: "项目名"}]`

## Data Models

### 项目选项数据模型
```typescript
interface ProjectOption {
  value: number;    // 项目ID
  label: string;    // 项目名称
}
```

### 组件Props模型
```typescript
interface AIProjectSelectorProps {
  modelValue: number[];           // 选中的项目ID数组
  options: ProjectOption[];       // 项目选项数组
  filterable: boolean;           // 是否支持搜索
  placeholder: string;           // 占位符文本
  clearable: boolean;            // 是否可清空
}
```

## Error Handling

### 数据获取错误处理
1. **网络错误**: 捕获异常，设置空数组，显示错误日志
2. **权限错误**: 后端返回空数组，前端正常显示"暂无项目"
3. **数据格式错误**: 使用兼容性处理，支持多种数据格式

### 用户体验优化
1. **加载状态**: 显示加载指示器
2. **空状态**: 友好的"暂无项目"提示
3. **错误状态**: 提供重试机制

## Testing Strategy

### 单元测试
1. **组件渲染测试**: 验证不同数据格式下的正确渲染
2. **事件处理测试**: 验证选择变更事件的正确触发
3. **边界条件测试**: 空数据、错误数据的处理

### 集成测试
1. **数据获取测试**: 验证从后端获取数据的完整流程
2. **用户交互测试**: 验证搜索、选择、清空等操作
3. **权限测试**: 验证不同权限用户看到的项目列表

### 修复方案

#### 主要修复点
1. **数据获取修复**: 将 `res.data` 改为 `res`
2. **组件兼容性**: 确保组件支持 `{value, label}` 格式
3. **错误处理**: 添加完善的错误处理和用户反馈

#### 实现步骤
1. 修复 `fetchProjects` 函数中的数据路径错误
2. 验证 AIProjectSelector 组件的数据格式兼容性
3. 添加加载状态和错误处理
4. 测试各种场景下的数据显示