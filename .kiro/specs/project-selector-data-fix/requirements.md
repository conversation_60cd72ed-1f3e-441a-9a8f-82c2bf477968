# Requirements Document

## Introduction

修复AI任务生成器中的项目选择器组件数据显示问题。当前项目选择器显示"暂无数据"，但后端接口已正确返回项目数据。需要确保前端组件能够正确处理和显示后端返回的项目数据。

## Requirements

### Requirement 1

**User Story:** 作为用户，我希望在AI任务生成器中能够看到可选的项目列表，以便选择任务所属的项目

#### Acceptance Criteria

1. WHEN 用户打开AI任务生成器页面 THEN 系统应该自动加载并显示可用的项目选项
2. WHEN 后端返回项目数据格式为 `{"value": 1, "label": "项目名"}` THEN 前端组件应该正确解析并显示项目选项
3. WHEN 用户选择生成模式为"按项目生成" THEN 项目选择器应该显示当前用户有权限访问的所有项目

### Requirement 2

**User Story:** 作为开发者，我希望项目选择器组件能够兼容不同的数据格式，以便提高组件的复用性和健壮性

#### Acceptance Criteria

1. WHEN 组件接收到 `{value, label}` 格式的数据 THEN 应该正确显示项目选项
2. WHEN 组件接收到 `{id, projectName}` 格式的数据 THEN 应该正确显示项目选项
3. WHEN 组件没有接收到options属性时 THEN 应该自动调用后端接口获取项目数据

### Requirement 3

**User Story:** 作为用户，我希望项目选择器具有搜索和筛选功能，以便快速找到目标项目

#### Acceptance Criteria

1. WHEN 用户在项目选择器中输入关键词 THEN 系统应该实时过滤显示匹配的项目
2. WHEN 项目列表为空时 THEN 应该显示友好的"暂无项目"提示信息
3. WHEN 用户清空选择时 THEN 应该能够正常清除已选择的项目