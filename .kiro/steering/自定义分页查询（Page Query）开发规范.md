# 自定义分页查询（Page Query）开发规范

## 1. 概述

在 Cool Admin 项目中，我们经常需要对数据进行分页查询，并且查询条件可能涉及复杂的业务逻辑、多维度过滤（如部门、项目权限）以及关联查询。本规范旨在指导开发者如何正确、高效地实现自定义分页查询，避免将业务逻辑泄露到 Controller 层，并确保代码的可维护性和复用性。

## 2. 反模式：Controller 层处理复杂查询逻辑

**问题描述**：
在重构 `AdminProjectTaskController` 之前，其 `init` 方法直接处理了 `projectId` 的校验、用户项目访问权限的判断，并根据这些逻辑动态构建 `QueryWrapper`，甚至在无权限时使用 `where("1 = 0")` 来返回空结果。

**反模式示例 (AdminProjectTaskController - 重构前)**：
```java
@Override
protected void init(HttpServletRequest request, JSONObject requestParams) {
    Long projectId = requestParams.getLong("projectId");
    if (projectId == null) {
        // 如果没有提供projectId，返回空结果
        QueryWrapper queryWrapper = QueryWrapper.create().where("1 = 0");
        setPageOption(createOp().queryWrapper(queryWrapper));
        setListOption(createOp().queryWrapper(queryWrapper));
        return;
    }

    Long userId = CoolSecurityUtil.getCurrentUserId();
    if (!projectAccessService.hasProjectAccess(userId, projectId)) {
        // 如果用户没有项目访问权限，返回空结果
        QueryWrapper queryWrapper = QueryWrapper.create().where("1 = 0");
        setPageOption(createOp().queryWrapper(queryWrapper));
        setListOption(createOp().queryWrapper(queryWrapper));
        return;
    }
    // ... 更多复杂逻辑
}
```

**反模式的危害**：
- **职责不清**：Controller 层承担了过多的业务逻辑和数据过滤职责，违反了单一职责原则。
- **可维护性差**：业务逻辑与 UI 交互逻辑耦合，当业务规则变化时，需要修改 Controller。
- **复用性低**：这部分复杂的查询和权限逻辑无法在其他 Service 或 Controller 中复用。
- **测试困难**：难以对 Controller 中的复杂逻辑进行独立的单元测试。

## 3. 最佳实践：Service 层实现自定义分页查询

**核心原则**：
将所有复杂的查询逻辑、权限过滤、数据组装等业务逻辑封装在 Service 层。Controller 层只负责接收请求、参数传递和结果返回。

**实现步骤**：

### 3.1. Service 层：重写 `page` 方法

在需要自定义查询的 Service 实现类中，重写 `BaseServiceImpl` 的 `page` 方法。这个方法将成为处理所有复杂查询逻辑的入口。

**示例 (TaskInfoServiceImpl.java)**：
```java
@Override
public Object page(JSONObject requestParams, Page<TaskInfoEntity> page, QueryWrapper queryWrapper) {
    try {
        JSONObject adminUserInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
        Long currentUserId = adminUserInfo.getLong("userId");
        boolean isAdmin = "admin".equals(adminUserInfo.getStr("username"));

        // 1. 参数转换与提取
        Map<String, Object> params = new HashMap<>();
        if (requestParams != null) {
            params.putAll(requestParams);
        }

        // 2. 处理项目维度过滤和权限检查
        Long projectId = requestParams.getLong("projectId");
        if (projectId != null) {
            // 检查用户是否有项目访问权限
            if (!isAdmin && !projectAccessService.hasProjectAccess(currentUserId, projectId)) {
                log.warn("用户 {} 无权访问项目 {}", currentUserId, projectId);
                return new Page<TaskInfoEntity>(page.getPageNumber(), page.getPageSize(), 0); // 无权限直接返回空页
            }
            // 根据项目ID获取任务包ID列表，用于过滤任务
            List<Long> projectPackageIds = taskPackageService.getPackagesByProjectId(projectId).stream()
                    .map(TaskPackageEntity::getId)
                    .collect(Collectors.toList());

            if (projectPackageIds.isEmpty()) {
                return new Page<TaskInfoEntity>(page.getPageNumber(), page.getPageSize(), 0); // 项目无任务包直接返回空页
            }
            params.put("packageIds", projectPackageIds); // 将过滤条件传递给 Mapper
        }

        // 3. 处理部门权限过滤（通用逻辑）
        if (!isAdmin && departmentPermissionService != null && currentUserId != null) {
            try {
                Long[] userDepartmentIds = departmentPermissionService.getUserDepartmentIds(currentUserId);
                if (userDepartmentIds != null && userDepartmentIds.length > 0) {
                    params.put("departmentIds", Arrays.asList(userDepartmentIds)); // 将过滤条件传递给 Mapper
                }
            } catch (Exception e) {
                log.warn("应用部门权限过滤失败，跳过权限过滤", e);
            }
        }

        // 4. 计算分页参数
        int pageNum = (int) page.getPageNumber();
        int pageSize = (int) page.getPageSize();
        int offset = (pageNum - 1) * pageSize;
        params.put("offset", offset);
        params.put("limit", pageSize);

        // 5. 调用 Mapper 自定义查询方法
        List<TaskInfoEntity> records = mapper.selectTaskInfoWithDetails(params);
        int total = mapper.countTaskInfoWithDetails(params);

        // 6. 数据后处理（例如关联执行人信息）
        List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(records);

        // 7. 构造并返回分页结果
        Page<TaskInfoEntity> resultPage = new Page<>();
        resultPage.setRecords(enrichedTasks);
        resultPage.setTotalRow(total);
        resultPage.setPageNumber(pageNum);
        resultPage.setPageSize(pageSize);

        return resultPage;

    } catch (Exception e) {
        log.error("查询任务信息列表失败", e);
        // 异常回退到基类方法，并进行数据后处理
        Page<TaskInfoEntity> result = (Page<TaskInfoEntity>) super.page(requestParams, page, queryWrapper);
        List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(result.getRecords());
        result.setRecords(enrichedTasks);
        return result;
    }
}
```

### 3.2. Mapper 层：自定义查询方法

当 Service 层需要执行复杂的关联查询或多条件过滤时，通常需要自定义 Mapper 方法，而不是仅仅依赖 MyBatis-Flex 的 `QueryWrapper`。

**示例 (TaskInfoMapper.java)**：
```java
public interface TaskInfoMapper extends BaseMapper<TaskInfoEntity> {
    /**
     * 查询任务信息及关联的部门、项目信息
     * @param params 包含查询参数的Map，如 departmentIds, packageIds, offset, limit 等
     * @return 任务信息列表
     */
    List<TaskInfoEntity> selectTaskInfoWithDetails(@Param("params") Map<String, Object> params);

    /**
     * 统计任务信息及关联的部门、项目信息的总数
     * @param params 包含查询参数的Map
     * @return 总数
     */
    int countTaskInfoWithDetails(@Param("params") Map<String, Object> params);
}
```

**对应的 XML 配置 (例如 TaskInfoMapper.xml)**：
```xml
<mapper namespace="com.cool.modules.task.mapper.TaskInfoMapper">
    <select id="selectTaskInfoWithDetails" resultType="com.cool.modules.task.entity.TaskInfoEntity">
        SELECT
            ti.*,
            td.name AS departmentName,
            tp.package_name AS packageName,
            pi.project_name AS projectName
        FROM
            task_info ti
        LEFT JOIN
            base_sys_department td ON ti.department_id = td.id
        LEFT JOIN
            task_package tp ON ti.package_id = tp.id
        LEFT JOIN
            project_info pi ON ti.project_id = pi.id
        <where>
            ti.is_deleted = 0
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND ti.department_id IN
                <foreach item="item" index="index" collection="params.departmentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.packageIds != null and params.packageIds.size() > 0">
                AND ti.package_id IN
                <foreach item="item" index="index" collection="params.packageIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 其他查询条件，例如关键词搜索 -->
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    ti.name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.description LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
            <!-- ... 其他动态条件 -->
        </where>
        ORDER BY ti.create_time DESC
        LIMIT #{params.offset}, #{params.limit}
    </select>

    <select id="countTaskInfoWithDetails" resultType="int">
        SELECT
            COUNT(ti.id)
        FROM
            task_info ti
        LEFT JOIN
            base_sys_department td ON ti.department_id = td.id
        LEFT JOIN
            task_package tp ON ti.package_id = tp.id
        LEFT JOIN
            project_info pi ON ti.project_id = pi.id
        <where>
            ti.is_deleted = 0
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND ti.department_id IN
                <foreach item="item" index="index" collection="params.departmentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.packageIds != null and params.packageIds.size() > 0">
                AND ti.package_id IN
                <foreach item="item" index="index" collection="params.packageIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 其他查询条件，例如关键词搜索 -->
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    ti.name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.description LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
            <!-- ... 其他动态条件 -->
        </where>
    </select>
</mapper>
```

### 3.3. Controller 层：简化 `init` 方法

Controller 的 `init` 方法应只负责将请求参数传递给 Service 层，并设置基本的查询选项。

**示例 (AdminProjectTaskController.java - 重构后)**：
```java
@Override
protected void init(HttpServletRequest request, JSONObject requestParams) {
    TaskInfoEntityTableDef task = TaskInfoEntityTableDef.TASK_INFO_ENTITY;
    Long projectId = requestParams.getLong("projectId");

    QueryWrapper queryWrapper = QueryWrapper.create();

    if (projectId != null) {
        // 将 projectId 添加到 QueryWrapper 中，Service 层会处理权限和任务包过滤
        queryWrapper.eq(task.PROJECT_ID, projectId);
    }

    setPageOption(
            createOp()
                    .queryWrapper(queryWrapper)
                    .keyWordLikeFields(task.NAME, task.DESCRIPTION));
    setListOption(
            createOp()
                    .queryWrapper(queryWrapper)
                    .keyWordLikeFields(task.NAME, task.DESCRIPTION));
}
```

## 4. 总结

通过将复杂的查询逻辑和权限过滤下沉到 Service 层，并利用 Mapper 层的自定义 SQL，我们可以实现：
- **清晰的职责划分**：Controller 专注于请求处理，Service 专注于业务逻辑。
- **更高的复用性**：Service 层的查询逻辑可以在多个 Controller 或其他 Service 中复用。
- **更好的可维护性**：业务规则的变化只需修改 Service 层，不影响 Controller。
- **更易于测试**：可以对 Service 层的复杂查询逻辑进行独立的单元测试。

**参考示例**：
- `com.cool.modules.task.service.impl.TaskInfoServiceImpl#page`
- `com.cool.modules.task.service.impl.TaskPackageServiceImpl#page`

请务必遵循此规范，以确保代码质量和项目健康。
