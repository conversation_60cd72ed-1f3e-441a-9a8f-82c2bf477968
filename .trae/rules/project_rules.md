# Cool Admin Product Overview

Cool Admin is a modern, modular administrative management system built for rapid development and AI-enhanced functionality.

## Core Features

- **AI-Enhanced Development**: AI-powered code generation from API to frontend pages
- **Workflow Orchestration**: Drag-and-drop workflow design for complex business processes
- **Modular Architecture**: Clean, maintainable modular codebase structure
- **Plugin System**: Extensible plugin architecture for payments, SMS, email, and other integrations
- **Multi-tenant Support**: Built-in multi-tenancy capabilities
- **Real-time Features**: SSE (Server-Sent Events) for real-time updates and progress tracking

## Business Domains

The system includes several key business modules:
- **User Management**: Authentication, authorization, roles, and permissions
- **Organization Management**: Department and organizational structure
- **Task Management**: Task creation, assignment, and tracking
- **SOP (Standard Operating Procedures)**: AI-powered SOP generation and management
- **Project Management**: Project organization and member management
- **Workflow Management**: Business process automation and orchestration

## Target Use Cases

- Enterprise administrative systems
- Task and project management platforms
- Business process automation
- AI-assisted workflow management
- Multi-tenant SaaS applications


# Cool Admin Project Structure

## Root Directory Layout
```
cool-admin/
├── cool-admin-java/     # Backend Spring Boot application
├── cool-admin-vue/      # Frontend Vue.js application
├── assets/              # Static assets and uploads
├── .kiro/              # Kiro AI assistant configuration
├── .cursor/            # Cursor IDE rules and configuration
└── .trae/              # Additional project rules
```

## Backend Structure (cool-admin-java/)

### Main Application Structure
```
src/main/java/com/cool/
├── CoolApplication.java          # Main application entry point
├── core/                         # Core framework components
│   ├── ai/                      # AI integration services
│   ├── annotation/              # Custom annotations
│   ├── base/                    # Base classes (BaseEntity, BaseService, etc.)
│   ├── cache/                   # Caching components
│   ├── config/                  # Configuration classes
│   ├── exception/               # Exception handling
│   ├── mybatis/                 # MyBatis extensions
│   ├── security/                # Security configuration
│   └── util/                    # Utility classes
└── modules/                      # Business modules
    ├── base/                    # Base system module (users, roles, menus)
    ├── task/                    # Task management
    ├── sop/                     # SOP (Standard Operating Procedures)
    ├── organization/            # Organization management
    ├── project/                 # Project management
    └── [other-modules]/         # Additional business modules
```

### Module Structure Pattern
Each business module follows this structure:
```
modules/[module-name]/
├── controller/                  # REST controllers
├── service/                     # Business logic services
│   └── impl/                   # Service implementations
├── entity/                      # JPA entities
├── mapper/                      # MyBatis mappers
├── dto/                         # Data transfer objects
└── enums/                       # Enumerations
```

## Frontend Structure (cool-admin-vue/)

### Main Application Structure
```
src/
├── App.vue                      # Root component
├── main.ts                      # Application entry point
├── config/                      # Configuration files
│   ├── dev.ts                  # Development config
│   ├── prod.ts                 # Production config
│   └── proxy.ts                # Proxy configuration
├── cool/                        # Cool framework core
│   ├── bootstrap/              # Application bootstrap
│   ├── hooks/                  # Reusable composition functions
│   ├── module/                 # Module management
│   ├── router/                 # Router configuration
│   ├── service/                # Service layer
│   └── utils/                  # Utility functions
├── modules/                     # Business modules
│   ├── base/                   # Base system module
│   ├── demo/                   # Demo examples
│   ├── task/                   # Task management
│   ├── sop/                    # SOP management
│   ├── organization/           # Organization management
│   └── [other-modules]/        # Additional modules
└── plugins/                     # Plugin system
    ├── crud/                   # CRUD plugin
    ├── element-ui/             # Element Plus integration
    ├── upload/                 # File upload plugin
    └── [other-plugins]/        # Additional plugins
```

### Module Structure Pattern
Each frontend module follows this structure:
```
modules/[module-name]/
├── views/                       # Page components
│   ├── index.vue               # Main list/table view
│   ├── detail.vue              # Detail view
│   └── [other-pages].vue       # Additional pages
├── components/                  # Reusable components
├── dict/                        # Data dictionaries
├── services/                    # Module-specific services (optional)
├── locales/                     # Internationalization
├── config.ts                    # Module configuration
└── index.ts                     # Module entry point
```

## Key Architectural Patterns

### Backend Patterns
- **Modular Architecture**: Business logic organized in separate modules
- **Base Classes**: Common functionality in BaseEntity, BaseService, BaseController
- **Annotation-Driven**: Heavy use of Spring annotations for configuration
- **Auto-Generation**: Automatic table creation from entity classes
- **Service Layer Pattern**: Clear separation between controllers and business logic

### Frontend Patterns
- **Composition API**: Vue 3 Composition API with TypeScript
- **Plugin Architecture**: Extensible plugin system
- **CRUD Components**: Reusable CRUD components via @cool-vue/crud
- **Module Federation**: Independent, self-contained modules
- **Service Auto-Generation**: EPS system generates services from backend APIs

## Configuration Conventions

### Backend Configuration
- Environment-specific configs: `application-{env}.yml`
- Custom properties under `cool.*` namespace
- Database auto-configuration via AutoTable
- AI services configured under `cool.ai.*`

### Frontend Configuration
- Environment configs in `src/config/`
- Module configs in each module's `config.ts`
- Plugin configurations in plugin directories
- Build configuration in `vite.config.ts`

## File Naming Conventions

### Backend
- **Entities**: `[Name]Entity.java`
- **Services**: `[Name]Service.java` and `[Name]ServiceImpl.java`
- **Controllers**: `[Name]Controller.java`
- **Mappers**: `[Name]Mapper.java`

### Frontend
- **Components**: PascalCase (e.g., `UserSelect.vue`)
- **Pages**: kebab-case (e.g., `user-list.vue`)
- **Services**: Auto-generated via EPS system
- **Modules**: kebab-case directory names

# Cool Admin Technology Stack

## Backend (Java)

### Core Framework
- **Spring Boot**: 3.2.5 (Java 17+)
- **Spring Security**: Authentication and authorization
- **Spring Data**: Data access layer
- **Spring Quartz**: Task scheduling
- **Spring Cache**: Caching abstraction

### Database & ORM
- **MyBatis-Flex**: 1.10.9 - Modern MyBatis enhancement
- **AutoTable**: Automatic table generation from entities
- **MySQL/PostgreSQL**: Primary databases
- **Redis**: Caching and session storage
- **HikariCP**: Connection pooling

### Build & Dependencies
- **Maven**: Dependency management and build tool
- **Lombok**: Code generation for boilerplate reduction
- **Hutool**: Java utility library
- **FastJSON2**: JSON processing
- **SpringDoc OpenAPI**: API documentation

### AI Integration
- **OpenAI GPT**: AI model integration
- **Dify**: Workflow orchestration platform
- **Custom AI Services**: Task generation, SOP parsing, quality checks

## Frontend (Vue.js)

### Core Framework
- **Vue 3**: Composition API with TypeScript
- **Vite**: Build tool and dev server
- **Element Plus**: UI component library
- **Vue Router 4**: Client-side routing
- **Pinia**: State management

### Development Tools
- **TypeScript**: Type safety
- **SCSS**: CSS preprocessing
- **ESLint + Prettier**: Code formatting and linting
- **Vue DevTools**: Development debugging

### Cool Framework Features
- **@cool-vue/crud**: CRUD component system
- **EPS System**: Automatic service generation
- **Plugin Architecture**: Modular plugin system
- **Real-time Updates**: SSE integration


## Development Ports
- **Backend**: 18001 (configurable via SERVER_PORT)
- **Frontend**: 19000
- **API Documentation**: http://localhost:18001/swagger

## Key Configuration Files
- **Backend**: `application.yml`, `application-local.yml`, `application-prod.yml`
- **Frontend**: `vite.config.ts`, `src/config/dev.ts`, `src/config/prod.ts`
- **Build**: `pom.xml` (Maven), `package.json` (npm/pnpm)

# Cool Admin 开发规范

## 后端开发规范 (Java)

### JAVA项目遵循阿里巴巴java开发手册规范

### 项目架构规范

#### 模块化架构
- 业务逻辑按模块组织，每个模块独立且职责单一
- 核心框架组件位于 `core/` 目录
- 业务模块位于 `modules/` 目录下

#### 标准模块结构
```
modules/[module]/
├── controller/           # 控制器层
├── service/             # 服务层
│   └── impl/           # 服务实现
├── entity/             # 实体类
├── mapper/             # Mapper接口
├── dto/                # 数据传输对象
├── enums/              # 枚举类
```

### CRUD 最佳实践

#### 1. Entity（实体类）规范
- 实体类放在 `modules/[module]/entity/`，类名以 `Entity` 结尾
- 继承 `BaseEntity`，使用 `@Table` 注解指定表名和注释
- 字段使用 `@ColumnDefine` 注解，注明注释、类型、长度、默认值
- 不需要手写建表SQL，Cool Admin会自动建表

```java
@Table(value = "demo_example", comment = "示例表")
public class DemoExampleEntity extends BaseEntity<DemoExampleEntity> {
    @ColumnDefine(comment = "名称", length = 100)
    private String name;
    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;
}
```

#### 2. Mapper规范
- Mapper接口放在 `modules/[module]/mapper/`
- 继承 `BaseMapper<Entity>`，无需多余代码

```java
public interface DemoExampleMapper extends BaseMapper<DemoExampleEntity> {}
```

#### 3. Service规范
- Service接口放在 `modules/[module]/service/`，实现放在 `service/impl/`
- Service接口继承 `BaseService<Entity>`，实现继承 `BaseServiceImpl<Mapper, Entity>`
- 使用 `@Service` 注解

```java
public interface DemoExampleService extends BaseService<DemoExampleEntity> {}

@Service
public class DemoExampleServiceImpl extends BaseServiceImpl<DemoExampleMapper, DemoExampleEntity> implements DemoExampleService {}
```

#### 4. Controller规范
- Controller放在 `modules/[module]/controller/admin/`
- 继承 `BaseController<Service, Entity>`，使用 `@CoolRestController` 注解
- 使用 `@Tag` 注解标注接口分组和描述
- **必须重写 `init` 方法**，使用 TableDef 静态字段进行类型安全的查询
- **重要**: `fieldEq` 和 `keyWordLikeFields` 方法必须传入 `QueryColumn` 对象，不能传入字符串

```java
@Tag(name = "示例管理", description = "示例模块统一管理")
@CoolRestController(api = { "add", "delete", "update", "info", "page" })
public class AdminDemoExampleController extends BaseController<DemoExampleService, DemoExampleEntity> {
    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 引入TableDef以实现类型安全的查询
        DemoExampleEntityTableDef example = DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY;
        
        setListOption(
            createOp()
                .fieldEq(example.STATUS, example.TYPE)  // 使用QueryColumn，不是字符串
                .keyWordLikeFields(example.NAME, example.DESCRIPTION)  // 使用QueryColumn，不是字符串
        );
    }
}
```

#### 5. MyBatis-Flex查询规范
- 使用 `QueryWrapper.create()` 创建查询条件
- 复杂的 `or` 条件必须使用 lambda 表达式包装
- **错误写法**: `.and(wrapper -> wrapper.eq("field1", value1).or().eq("field2", value2))`
- **正确写法**: `.and(wrapper -> wrapper.eq("field1", value1)).or(wrapper -> wrapper.eq("field2", value2))`

```java
// 正确的复杂查询写法
QueryWrapper.create()
    .and(wrapper -> {
        wrapper.eq("target_entity_type", "PROJECT").eq("target_entity_id", projectId);
    })
    .or(wrapper -> {
        wrapper.eq("project_id", projectId);
    })
    .ge("create_time", startDate)
    .orderBy("create_time", false)
    .limit(limit);
```

#### 6. Service方法参数顺序规范
- 保持方法参数顺序的一致性，特别是在接口和实现类之间
- 常见的参数顺序: `userId, reason, durationMinutes` 而不是 `userId, durationMinutes, reason`
- 在Controller调用Service方法时，确保参数顺序与接口定义一致

### 开发最佳实践

#### 异常处理
- 使用 `CoolPreconditions.check(condition, message)` 进行参数校验
- 业务异常抛出 `CoolException`
- 统一异常处理通过 `GlobalExceptionHandler`

#### 事务管理
- 服务层方法添加 `@Transactional` 注解
- 只读操作使用 `@Transactional(readOnly = true)`

#### 缓存使用
```java
@Cacheable(value = "example", key = "#id")
public ExampleEntity getById(Long id) {
    return super.getById(id);
}
```

#### 响应格式
```java
// 成功响应
return R.ok(data);

// 失败响应  
return R.error("错误信息");
```

### 注释规范
- 所有 Service 层方法必须添加方法注释
- 复杂业务流、关键分支必须有详细的行内注释
- 核心业务流、AI能力调用、状态流转等核心代码，注释要覆盖设计意图

## 前端开发规范 (Vue.js)

### 组件开发规范

#### 组件定义规范
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script lang="ts" setup>
// 组件名定义 - 必须
defineOptions({
  name: "module-component-name" // 格式：模块-组件名
});

// 导入
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';

// Props定义
interface Props {
  modelValue?: any;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'change': [value: any];
}>();
</script>
```

### CRUD组件使用规范

#### 基本用法
- 使用 `useCrud`、`useTable`、`useUpsert` hooks
- 通过 ref 自动注入配置，**不要使用 v-bind 绑定**
- 正确写法：`<cl-table ref="Table" />`
- 错误写法：`<cl-table ref="Table" v-bind="Table" />`

#### 标准页面结构
```vue
<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <cl-multi-delete-btn />
      <cl-flex1 />
      <cl-search-key />
    </cl-row>
    
    <cl-row>
      <cl-table ref="Table" />
    </cl-row>
    
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>
```

### 服务调用规范

#### EPS系统规范

##### 基本原则
- EPS (Endpoint Service) 是Cool Admin的自动化API服务生成系统
- 严格使用 EPS 生成的服务路径，**不要手动创建服务文件**
- 服务路径由后端Controller的包结构和类名自动生成

##### 路径生成规则
EPS根据后端Controller生成前端服务路径，遵循以下规则：

1. **包名转换**：将Java包名转换为JavaScript对象路径
   ```java
   // 后端Controller路径
   com.cool.modules.organization.controller.admin.AdminProjectInfoController
   
   // 生成的EPS路径
   service.organization.project.info
   ```

2. **Controller名称处理**：
   - 移除 `Admin` 前缀和 `Controller` 后缀
   - 将驼峰命名转换为点分隔的小写路径
   - `AdminProjectInfoController` → `project.info`
   - `AdminProjectMemberController` → `project.member`

3. **API方法映射**：
   ```java
   // 后端AdminProjectInfoController方法
   @PostMapping("/options")
   public R options(@RequestBody OptionsRequest request)
   
   // 生成的EPS方法
   service.organization.project.info.options(data)
   ```
4. **特殊API方法映射**：
   ```java
   // 后端AdminProjectMemberController方法
   @PostMapping("/batch-add-members")
   public R batchAddMembers(@RequestBody BatchAddMembersRequest request)
   
   // 生成的EPS方法
   service.organization.project.member['batch-add-members'](data)
   ```

##### 正确的调用方式
```typescript
// ✅ 正确 - 使用EPS生成的服务路径
await service.organization.project.info.options();
await service.organization.project.member['batch-add-members'](data);
await service.sop.s.o.p.industry.active();
await service.base.sys.user.page(params);

// ❌ 错误 - 手动service.request调用
await service.request({
  url: '/admin/organization/project-info/options',
  method: 'GET'
});
```

##### 常见路径映射示例
| 后端Controller | EPS服务路径 | 说明 |
|---|---|---|
| `AdminBaseSysUserController` | `service.base.sys.user` | 基础用户管理 |
| `AdminSOPIndustryController` | `service.sop.s.o.p.industry` | SOP行业管理 |
| `AdminProjectInfoController` | `service.organization.project.info` | 项目信息管理 |
| `AdminProjectMemberController` | `service.organization.project.member` | 项目成员管理 |
| `AdminTaskInfoController` | `service.task.info` | 任务信息管理 |

##### 类型安全特性
- EPS自动生成TypeScript类型定义文件 (`eps.d.ts`)
- 提供完整的接口参数和返回值类型
- 支持IDE智能提示和类型检查

```typescript
// 自动类型提示
const result: ProjectInfoEntity = await service.organization.project.info.info({ id: 1 });
const list: ProjectInfoEntity[] = await service.organization.project.info.list();
```

##### 开发调试
- 开发环境可通过 `/__cool_eps` 接口刷新EPS配置
- 使用开发工具面板查看所有可用的EPS服务路径
- EPS配置文件位于 `build/cool/eps.json` 和 `build/cool/eps.d.ts`

##### 注意事项
- EPS路径中的特殊字符会被转换（如 `SOP` → `s.o.p`）
- 方法名会转换为小写，连字符会被移除
- 只有标注了 `@CoolRestController` 的Controller才会生成EPS服务

#### RestController接口调用
- 所有自定义 RestController 接口必须统一用 `service.request({ url, method, data })` 调用
- url 必须为全量 path，method 必须大写，参数放 data 字段

```javascript
await service.request({
  url: '/admin/sop/ai-task-generator/preview',
  method: 'POST',
  data: { ... }
})
```

### 组件命名规范
- 必须使用 `defineOptions({ name: "模块-页面" })` 定义组件名
- 组件名格式：`sop-industry`、`sop-template` 等，用于路由缓存
- Table 和 Upsert 首字母大写：`const Table = useTable()`

### 样式规范

#### SCSS使用规范
```scss
// 变量定义
$primary-color: #409eff;
$success-color: #67c23a;

// 混入定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 组件样式
.component-name {
  padding: 16px;
  
  &__header {
    @include flex-center;
    margin-bottom: 16px;
  }
}
```

### 性能优化

#### 组件懒加载
```typescript
// 路由懒加载
const routes = [
  {
    path: '/task/info',
    component: () => import('./views/info.vue')
  }
];

// 组件懒加载
const TaskStepsFlow = defineAsyncComponent(() => import('./components/task-steps-flow.vue'));
```

#### 防抖节流
```typescript
import { debounce, throttle } from 'lodash-es';

// 搜索防抖
const handleSearch = debounce(async (keyword: string) => {
  await loadData({ keyword });
}, 300);
```

## 开发流程规范

### 开发步骤
1. 先查看 `src/modules/demo` 的对应示例
2. 严格按照 demo 的代码结构编写
3. 避免使用复杂的 Element Plus 图标导入，优先使用 emoji 或简单图标

### 组件复用规范
- 组件应尽量复用，避免重复造轮子
- 单文件（.vue/.ts）代码行数建议不超过 500 行
- 拆分时优先按功能、UI块、业务逻辑分层
- 复用组件应放在 modules/xxx/components 或 plugins/xxx/components 目录

### 重要提醒
1. **组件命名**: 必须使用`defineOptions({ name: "模块-页面" })`定义组件名
2. **CRUD组件**: 使用`<cl-table ref="Table" />`而不是`<cl-table ref="Table" v-bind="Table" />`
3. **服务调用**: 严格使用EPS生成的服务路径，不要手动创建服务文件
4. **页面结构**: 复杂页面使用`<el-scrollbar>`包装，参考demo模块的标准写法
5. **开发流程**: 先查看demo模块示例，严格按照demo的代码结构编写

## AI增强功能开发

### AI能力与业务流分离
- AI 能力（如场景识别、对话、质量检查等）全部由 AILLMService 负责，**不包含任何业务流**
- 业务流（如任务生成、进度、SSE推送、工单/任务包/任务落地、历史记录等）全部由具体业务Service负责
- Controller 层只负责接收请求、参数校验、调用 Service 层、组装/返回响应

### Controller/Service 职责边界
- 超级管理员具有所有的数据权限,判断是否是超级管理员baseSysUserService.isSuperAdmin(userId)
- Controller 层：只负责接收请求、参数校验、权限校验、调用 Service 层、组装/返回响应
- 禁止API接口定义使用特殊字符，只能使用驼峰命名
- Service 层：只负责业务逻辑实现、数据处理、AI能力调用、事务控制
- 禁止 Service 之间循环依赖，业务流与 AI 能力分层清晰

# 自定义分页查询（Page Query）开发规范

## 1. 概述

在 Cool Admin 项目中，我们经常需要对数据进行分页查询，并且查询条件可能涉及复杂的业务逻辑、多维度过滤（如部门、项目权限）以及关联查询。本规范旨在指导开发者如何正确、高效地实现自定义分页查询，避免将业务逻辑泄露到 Controller 层，并确保代码的可维护性和复用性。

## 2. 反模式：Controller 层处理复杂查询逻辑

**问题描述**：
在重构 `AdminProjectTaskController` 之前，其 `init` 方法直接处理了 `projectId` 的校验、用户项目访问权限的判断，并根据这些逻辑动态构建 `QueryWrapper`，甚至在无权限时使用 `where("1 = 0")` 来返回空结果。

**反模式示例 (AdminProjectTaskController - 重构前)**：
```java
@Override
protected void init(HttpServletRequest request, JSONObject requestParams) {
    Long projectId = requestParams.getLong("projectId");
    if (projectId == null) {
        // 如果没有提供projectId，返回空结果
        QueryWrapper queryWrapper = QueryWrapper.create().where("1 = 0");
        setPageOption(createOp().queryWrapper(queryWrapper));
        setListOption(createOp().queryWrapper(queryWrapper));
        return;
    }

    Long userId = CoolSecurityUtil.getCurrentUserId();
    if (!projectAccessService.hasProjectAccess(userId, projectId)) {
        // 如果用户没有项目访问权限，返回空结果
        QueryWrapper queryWrapper = QueryWrapper.create().where("1 = 0");
        setPageOption(createOp().queryWrapper(queryWrapper));
        setListOption(createOp().queryWrapper(queryWrapper));
        return;
    }
    // ... 更多复杂逻辑
}
```

**反模式的危害**：
- **职责不清**：Controller 层承担了过多的业务逻辑和数据过滤职责，违反了单一职责原则。
- **可维护性差**：业务逻辑与 UI 交互逻辑耦合，当业务规则变化时，需要修改 Controller。
- **复用性低**：这部分复杂的查询和权限逻辑无法在其他 Service 或 Controller 中复用。
- **测试困难**：难以对 Controller 中的复杂逻辑进行独立的单元测试。

## 3. 最佳实践：Service 层实现自定义分页查询

**核心原则**：
将所有复杂的查询逻辑、权限过滤、数据组装等业务逻辑封装在 Service 层。Controller 层只负责接收请求、参数传递和结果返回。

**实现步骤**：

### 3.1. Service 层：重写 `page` 方法

在需要自定义查询的 Service 实现类中，重写 `BaseServiceImpl` 的 `page` 方法。这个方法将成为处理所有复杂查询逻辑的入口。

**示例 (TaskInfoServiceImpl.java)**：
```java
@Override
public Object page(JSONObject requestParams, Page<TaskInfoEntity> page, QueryWrapper queryWrapper) {
    try {
        JSONObject adminUserInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
        Long currentUserId = adminUserInfo.getLong("userId");
        boolean isAdmin = "admin".equals(adminUserInfo.getStr("username"));

        // 1. 参数转换与提取
        Map<String, Object> params = new HashMap<>();
        if (requestParams != null) {
            params.putAll(requestParams);
        }

        // 2. 处理项目维度过滤和权限检查
        Long projectId = requestParams.getLong("projectId");
        if (projectId != null) {
            // 检查用户是否有项目访问权限
            if (!isAdmin && !projectAccessService.hasProjectAccess(currentUserId, projectId)) {
                log.warn("用户 {} 无权访问项目 {}", currentUserId, projectId);
                return new Page<TaskInfoEntity>(page.getPageNumber(), page.getPageSize(), 0); // 无权限直接返回空页
            }
            // 根据项目ID获取任务包ID列表，用于过滤任务
            List<Long> projectPackageIds = taskPackageService.getPackagesByProjectId(projectId).stream()
                    .map(TaskPackageEntity::getId)
                    .collect(Collectors.toList());

            if (projectPackageIds.isEmpty()) {
                return new Page<TaskInfoEntity>(page.getPageNumber(), page.getPageSize(), 0); // 项目无任务包直接返回空页
            }
            params.put("packageIds", projectPackageIds); // 将过滤条件传递给 Mapper
        }

        // 3. 处理部门权限过滤（通用逻辑）
        if (!isAdmin && departmentPermissionService != null && currentUserId != null) {
            try {
                Long[] userDepartmentIds = departmentPermissionService.getUserDepartmentIds(currentUserId);
                if (userDepartmentIds != null && userDepartmentIds.length > 0) {
                    params.put("departmentIds", Arrays.asList(userDepartmentIds)); // 将过滤条件传递给 Mapper
                }
            } catch (Exception e) {
                log.warn("应用部门权限过滤失败，跳过权限过滤", e);
            }
        }

        // 4. 计算分页参数
        int pageNum = (int) page.getPageNumber();
        int pageSize = (int) page.getPageSize();
        int offset = (pageNum - 1) * pageSize;
        params.put("offset", offset);
        params.put("limit", pageSize);

        // 5. 调用 Mapper 自定义查询方法
        List<TaskInfoEntity> records = mapper.selectTaskInfoWithDetails(params);
        int total = mapper.countTaskInfoWithDetails(params);

        // 6. 数据后处理（例如关联执行人信息）
        List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(records);

        // 7. 构造并返回分页结果
        Page<TaskInfoEntity> resultPage = new Page<>();
        resultPage.setRecords(enrichedTasks);
        resultPage.setTotalRow(total);
        resultPage.setPageNumber(pageNum);
        resultPage.setPageSize(pageSize);

        return resultPage;

    } catch (Exception e) {
        log.error("查询任务信息列表失败", e);
        // 异常回退到基类方法，并进行数据后处理
        Page<TaskInfoEntity> result = (Page<TaskInfoEntity>) super.page(requestParams, page, queryWrapper);
        List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(result.getRecords());
        result.setRecords(enrichedTasks);
        return result;
    }
}
```

### 3.2. Mapper 层：自定义查询方法

当 Service 层需要执行复杂的关联查询或多条件过滤时，通常需要自定义 Mapper 方法，而不是仅仅依赖 MyBatis-Flex 的 `QueryWrapper`。

**示例 (TaskInfoMapper.java)**：
```java
public interface TaskInfoMapper extends BaseMapper<TaskInfoEntity> {
    /**
     * 查询任务信息及关联的部门、项目信息
     * @param params 包含查询参数的Map，如 departmentIds, packageIds, offset, limit 等
     * @return 任务信息列表
     */
    List<TaskInfoEntity> selectTaskInfoWithDetails(@Param("params") Map<String, Object> params);

    /**
     * 统计任务信息及关联的部门、项目信息的总数
     * @param params 包含查询参数的Map
     * @return 总数
     */
    int countTaskInfoWithDetails(@Param("params") Map<String, Object> params);
}
```

**对应的 XML 配置 (例如 TaskInfoMapper.xml)**：
```xml
<mapper namespace="com.cool.modules.task.mapper.TaskInfoMapper">
    <select id="selectTaskInfoWithDetails" resultType="com.cool.modules.task.entity.TaskInfoEntity">
        SELECT
            ti.*,
            td.name AS departmentName,
            tp.package_name AS packageName,
            pi.project_name AS projectName
        FROM
            task_info ti
        LEFT JOIN
            base_sys_department td ON ti.department_id = td.id
        LEFT JOIN
            task_package tp ON ti.package_id = tp.id
        LEFT JOIN
            project_info pi ON ti.project_id = pi.id
        <where>
            ti.is_deleted = 0
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND ti.department_id IN
                <foreach item="item" index="index" collection="params.departmentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.packageIds != null and params.packageIds.size() > 0">
                AND ti.package_id IN
                <foreach item="item" index="index" collection="params.packageIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 其他查询条件，例如关键词搜索 -->
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    ti.name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.description LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
            <!-- ... 其他动态条件 -->
        </where>
        ORDER BY ti.create_time DESC
        LIMIT #{params.offset}, #{params.limit}
    </select>

    <select id="countTaskInfoWithDetails" resultType="int">
        SELECT
            COUNT(ti.id)
        FROM
            task_info ti
        LEFT JOIN
            base_sys_department td ON ti.department_id = td.id
        LEFT JOIN
            task_package tp ON ti.package_id = tp.id
        LEFT JOIN
            project_info pi ON ti.project_id = pi.id
        <where>
            ti.is_deleted = 0
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND ti.department_id IN
                <foreach item="item" index="index" collection="params.departmentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.packageIds != null and params.packageIds.size() > 0">
                AND ti.package_id IN
                <foreach item="item" index="index" collection="params.packageIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 其他查询条件，例如关键词搜索 -->
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    ti.name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.description LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
            <!-- ... 其他动态条件 -->
        </where>
    </select>
</mapper>
```

### 3.3. Controller 层：简化 `init` 方法

Controller 的 `init` 方法应只负责将请求参数传递给 Service 层，并设置基本的查询选项。

**示例 (AdminProjectTaskController.java - 重构后)**：
```java
@Override
protected void init(HttpServletRequest request, JSONObject requestParams) {
    TaskInfoEntityTableDef task = TaskInfoEntityTableDef.TASK_INFO_ENTITY;
    Long projectId = requestParams.getLong("projectId");

    QueryWrapper queryWrapper = QueryWrapper.create();

    if (projectId != null) {
        // 将 projectId 添加到 QueryWrapper 中，Service 层会处理权限和任务包过滤
        queryWrapper.eq(task.PROJECT_ID, projectId);
    }

    setPageOption(
            createOp()
                    .queryWrapper(queryWrapper)
                    .keyWordLikeFields(task.NAME, task.DESCRIPTION));
    setListOption(
            createOp()
                    .queryWrapper(queryWrapper)
                    .keyWordLikeFields(task.NAME, task.DESCRIPTION));
}
```

## 4. 总结

通过将复杂的查询逻辑和权限过滤下沉到 Service 层，并利用 Mapper 层的自定义 SQL，我们可以实现：
- **清晰的职责划分**：Controller 专注于请求处理，Service 专注于业务逻辑。
- **更高的复用性**：Service 层的查询逻辑可以在多个 Controller 或其他 Service 中复用。
- **更好的可维护性**：业务规则的变化只需修改 Service 层，不影响 Controller。
- **更易于测试**：可以对 Service 层的复杂查询逻辑进行独立的单元测试。

**参考示例**：
- `com.cool.modules.task.service.impl.TaskInfoServiceImpl#page`
- `com.cool.modules.task.service.impl.TaskPackageServiceImpl#page`

请务必遵循此规范，以确保代码质量和项目健康。
