---
trigger: always_on
---

# Cool Admin 后端开发规范

## 项目概述
Cool Admin是一个基于Spring Boot 3的现代化管理系统后端，采用模块化架构设计，支持AI编码、多租户、流程编排等特性。

## 技术栈
- **框架**: Spring Boot 3.2.5, Spring Security, Spring Data
- **数据库**: MyBatis-Flex, MySQL/PostgreSQL, Redis
- **构建工具**: Maven 3.8+
- **JDK版本**: Java 17+
- **任务调度**: Spring Quartz
- **缓存**: Caffeine/Redis
- **文档**: SpringDoc OpenAPI 3
- **代码生成**: AutoTable

## 目录结构规范

### 核心模块结构
```
src/main/java/com/cool/
├── core/                    # 核心框架
│   ├── annotation/         # 注解定义
│   ├── base/              # 基础类(BaseEntity, BaseService等)
│   ├── cache/             # 缓存组件
│   ├── config/            # 配置类
│   ├── exception/         # 异常处理
│   ├── interceptor/       # 拦截器
│   ├── mybatis/           # MyBatis扩展
│   └── util/              # 工具类
├── modules/               # 业务模块
│   ├── base/             # 基础模块(用户、角色、菜单等)
│   ├── sop/              # SOP工单模块
│   ├── user/             # 用户模块
│   ├── task/             # 任务模块
│   └── [module]/         # 其他业务模块
└── CoolApplication.java   # 启动类
```

### 业务模块标准结构
```
modules/[module]/
├── controller/           # 控制器层
├── service/             # 服务层
│   └── impl/           # 服务实现
├── entity/             # 实体类
├── mapper/             # Mapper接口
├── dto/                # 数据传输对象
├── enums/              # 枚举类
└── config.ts           # 模块配置(前端)
```

## 编码规范

### 1. 实体类规范
```java
@Table(value = "模块_表名", comment = "表注释")
public class ExampleEntity extends BaseEntity {
    
    @Id
    @KeyType(KeyType.Auto)
    @ColumnDefine(comment = "主键ID")
    private Long id;
    
    @ColumnDefine(comment = "字段注释", length = 100)
    private String fieldName;
    
    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;
    
    @ColumnDefine(comment = "创建时间")
    private Date createTime;
}
```

### 2. 服务层规范
```java
@Service
public class ExampleServiceImpl extends BaseServiceImpl<ExampleMapper, ExampleEntity> implements ExampleService {
    
    @Override
    @Transactional
    public void customMethod(Long id) {
        // 业务逻辑实现
        ExampleEntity entity = getById(id);
        if (entity == null) {
            throw new CoolPreconditions.check("记录不存在");
        }
        // 更新操作
        updateById(entity);
    }
}
```

### 3. 控制器规范
```java
@RestController
@RequestMapping("/admin/example")
@Tag(name = "示例管理", description = "示例模块相关接口")
public class AdminExampleController extends BaseController<ExampleService, ExampleEntity> {
    
    @PostMapping("/custom")
    @Operation(summary = "自定义操作")
    public R customAction(@RequestBody @Valid CustomRequest request) {
        // 调用服务层方法
        exampleService.customMethod(request.getId());
        return R.ok();
    }
}
```

### 4. DTO规范
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExampleRequest {
    
    @NotNull(message = "ID不能为空")
    private Long id;
    
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称长度不能超过100字符")
    private String name;
}
```

## 任务系统设计模式

### 1. 多层次任务架构
```
任务系统架构
├── 任务包层 (TaskPackage) - 场景级任务容器
├── 任务层 (Task) - 具体执行单元  
├── 执行层 (TaskExecution) - 执行人分配与状态管理
└── 历史层 (TaskHistory) - 操作历史与审计跟踪
```

### 2. 状态流转管理
```java
// 任务业务状态枚举
public enum TaskBusinessStatusEnum {
    PENDING_ASSIGN(0, "待分配"),
    PENDING_EXECUTE(1, "待执行"),
    EXECUTING(2, "执行中"),
    COMPLETED(3, "已完成"),
    CLOSED(4, "已关闭");
}

// 执行状态枚举
public enum ExecutionStatusEnum {
    ASSIGNED("ASSIGNED", "已分配"),
    IN_PROGRESS("IN_PROGRESS", "执行中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消"),
    REJECTED("REJECTED", "已拒绝");
}
```

### 3. 状态管理服务
```java
@Service
@RequiredArgsConstructor
public class TaskStatusServiceImpl implements TaskStatusService {
    
    private final TaskExecutionService taskExecutionService;
    private final TaskInfoService taskInfoService;
    private final TaskHistoryService taskHistoryService;
    private final TaskPermissionService taskPermissionService;
    
    @Override
    @Transactional
    public Boolean completeTaskExecution(TaskCompletionRequest request) {
        // 1. 权限检查
        if (!taskPermissionService.canCompleteTaskExecution(request.getTaskId(), request.getAssigneeId())) {
            return false;
        }
        
        // 2. 更新执行记录
        TaskExecutionEntity execution = taskExecutionService.getByTaskIdAndAssigneeId(
            request.getTaskId(), request.getAssigneeId());
        execution.setExecutionStatus("COMPLETED");
        execution.setCompletionTime(new Date());
        taskExecutionService.updateById(execution);
        
        // 3. 检查是否所有执行人都完成
        if (areAllExecutionsCompleted(request.getTaskId())) {
            updateTaskStatus(request.getTaskId(), TaskBusinessStatusEnum.COMPLETED.getCode());
        }
        
        // 4. 记录操作历史
        taskHistoryService.recordTaskHistory(
            request.getTaskId(), "COMPLETE", oldStatus, newStatus, 
            request.getAssigneeId(), execution.getAssigneeName(), "完成任务执行");
            
        return true;
    }
}
```

### 4. 历史记录与审计
```java
@Service
public class TaskHistoryServiceImpl implements TaskHistoryService {
    
    @Override
    public void recordTaskHistory(Long taskId, String actionType, Integer oldStatus, Integer newStatus,
                                 Long actionBy, String actionByName, String note) {
        TaskHistoryEntity history = new TaskHistoryEntity();
        history.setTaskId(taskId);
        history.setActionType(actionType);
        history.setOldStatus(oldStatus);
        history.setNewStatus(newStatus);
        history.setActionBy(actionBy);
        history.setActionByName(actionByName);
        history.setActionTime(new Date());
        history.setNote(note);
        save(history);
    }
}
```

## AI增强功能开发

### 1. AI驱动的智能SOP工单系统架构

#### 核心AI能力模块
```java
/**
 * SOP智能解析服务
 */
@Service
public class AISOPParserService {
    
    /**
     * 自然语言SOP解析
     */
    public SOPParseResult parseNaturalLanguageSOP(String description, String industry);
    
    /**
     * SOP模板智能生成
     */
    public SOPTemplate generateSOPTemplate(SOPGenerateRequest request);
    
    /**
     * SOP知识库检索
     */
    public List<SOPKnowledge> searchSOPKnowledge(String keyword, String industry);
}

/**
 * 智能调度引擎
 */
@Service
public class IntelligentScheduleEngineService {
    
    /**
     * 智能任务调度
     */
    public ScheduleDecision intelligentSchedule(ScheduleContext context);
    
    /**
     * 动态调度优化
     */
    public ScheduleOptimization optimizeSchedule(Long workOrderId);
}
```

### 2. AI配置规范
```yaml
cool:
  ai:
    # 大语言模型配置
    llm:
      openai:
        api-key: ${OPENAI_API_KEY:your-api-key}
        base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
        model: ${OPENAI_MODEL:gpt-4}
        timeout: 30000
    
    # AI功能开关
    features:
      sop-generation: true
      intelligent-scheduling: true
      execution-guidance: true
      quality-check: true
      process-optimization: true
```

## 开发最佳实践

### 1. 异常处理
- 使用`CoolPreconditions.check(condition, message)`进行参数校验
- 业务异常抛出`CoolException`
- 统一异常处理通过`GlobalExceptionHandler`

### 2. 事务管理
- 服务层方法添加`@Transactional`注解
- 只读操作使用`@Transactional(readOnly = true)`
- 复杂业务逻辑考虑事务传播机制

### 3. 缓存使用
```java
@Cacheable(value = "example", key = "#id")
public ExampleEntity getById(Long id) {
    return super.getById(id);
}

@CacheEvict(value = "example", key = "#entity.id")
public void updateById(ExampleEntity entity) {
    super.updateById(entity);
}
```

### 4. 数据库查询
```java
// 使用QueryWrapper构建查询条件
QueryWrapper wrapper = QueryWrapper.create()
    .eq("status", 1)
    .like("name", keyword)
    .between("create_time", startTime, endTime)
    .orderBy("create_time", false);
```

### 5. 日志规范
```java
@Slf4j
public class ExampleService {
    
    public void method() {
        log.info("执行业务操作，参数: {}", param);
        log.debug("调试信息: {}", debugInfo);
        log.error("操作失败", exception);
    }
}
```

## 数据库设计规范

### 1. 表命名
- 使用模块前缀：`模块名_表名`
- 小写字母，下划线分隔
- 例如：`task_info`, `task_execution`, `task_history`

### 2. 字段规范
- 主键：`id` (bigint, auto_increment)
- 创建时间：`create_time` (datetime)
- 更新时间：`update_time` (datetime)
- 创建人：`create_by` (bigint)
- 更新人：`update_by` (bigint)
- 状态字段：`status` (tinyint, 1-启用 0-禁用)

### 3. 索引规范
- 主键索引：`PRIMARY KEY`
- 唯一索引：`uk_字段名`
- 普通索引：`idx_字段名`
- 组合索引：`idx_字段1_字段2`

## 安全规范

### 1. 接口权限
- 管理端接口：`/admin/**` 需要登录验证
- 应用端接口：`/app/**` 需要token验证
- 开放接口：`/open/**` 无需验证

### 2. 数据权限
- 多租户数据隔离
- 角色权限控制
- 数据范围权限

### 3. 参数校验
- 使用Bean Validation注解
- 自定义校验器
- SQL注入防护

## 性能优化

### 1. 查询优化
- 避免N+1查询
- 合理使用分页
- 索引优化

### 2. 缓存策略
- 热点数据缓存
- 查询结果缓存
- 分布式缓存

### 3. 异步处理
- 耗时操作异步执行
- 消息队列处理
- 线程池配置

## 测试规范

### 1. 单元测试
```java
@SpringBootTest
class ExampleServiceTest {
    
    @Autowired
    private ExampleService exampleService;
    
    @Test
    void testCustomMethod() {
        // 测试逻辑
    }
}
```

### 2. 集成测试
- 数据库集成测试
- API接口测试
- 业务流程测试

## 部署配置

### 1. 环境配置
- `application-local.yml` - 本地开发
- `application-dev.yml` - 开发环境
- `application-prod.yml` - 生产环境

### 2. Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/cool-admin.jar app.jar
EXPOSE 8001
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 代码审查清单

### 1. 代码质量
- [ ] 遵循命名规范
- [ ] 异常处理完整
- [ ] 日志记录合理
- [ ] 注释清晰完整

### 2. 安全检查
- [ ] 参数校验完整
- [ ] SQL注入防护
- [ ] 权限控制正确
- [ ] 敏感数据保护

### 3. 性能考虑
- [ ] 查询效率优化
- [ ] 缓存使用合理
- [ ] 事务范围适当
- [ ] 资源释放及时

## 常用工具类

### 1. 工具类使用
- `CoolUtil` - 通用工具
- `DateUtil` - 日期处理
- `JsonUtil` - JSON操作
- `StringUtil` - 字符串处理
- `ValidateUtil` - 数据校验

### 2. 响应格式
```java
// 成功响应
return R.ok(data);

// 失败响应  
return R.error("错误信息");

// 分页响应
return R.ok(pageResult);
```

## 重要提醒

1. **状态更新规则**: 所有任务状态更新必须通过`/admin/task/status`接口进行，确保数据同步和统计正确
2. **实体类路径**: 实体类必须放在正确的包路径下，影响EPS生成的服务路径
3. **Controller规范**: 继承BaseController，确保所有CRUD接口正确实现
4. **EPS系统**: 不要手动修改EPS生成的文件，实体类变更后需要重新生成EPS

遵循以上规范可以确保代码质量、可维护性和团队协作效率。 