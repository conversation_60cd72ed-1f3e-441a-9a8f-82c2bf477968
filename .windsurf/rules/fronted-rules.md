---
trigger: always_on
---

---
description: cl-crud 组件示例
globs: *.tsx, *.ts, *.vue
---

# Cool Admin 前端开发规范

## 项目概述
Cool Admin前端是基于Vue 3 + TypeScript + Element Plus的现代化管理系统，采用模块化架构设计，支持组件化开发、状态管理和实时交互。

## 技术栈
- **框架**: Vue 3 + TypeScript + Composition API
- **构建工具**: Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Cool框架集成的service
- **包管理**: pnpm
- **CSS预处理器**: SCSS

## 目录结构规范

### 前端项目结构
```
src/
├── App.vue               # 根组件
├── main.ts              # 入口文件
├── config/              # 配置文件
│   ├── dev.ts           # 开发环境配置
│   ├── prod.ts          # 生产环境配置
│   └── index.ts         # 配置入口
├── cool/                # Cool框架核心
│   ├── bootstrap/       # 启动引导
│   ├── hooks/           # 通用hooks
│   ├── module/          # 模块管理
│   ├── router/          # 路由配置
│   ├── service/         # 服务层
│   ├── types/           # 类型定义
│   └── utils/           # 工具函数
├── modules/             # 业务模块
│   ├── base/            # 基础模块
│   ├── task/            # 任务模块
│   ├── sop/             # SOP工单模块
│   └── [module]/        # 其他业务模块
└── plugins/             # 插件系统
    ├── crud/            # CRUD插件
    ├── element-ui/      # Element UI插件
    └── ...              # 其他插件
```

### 业务模块标准结构
```
modules/[module]/
├── views/               # 页面视图
│   ├── index.vue       # 主页面
│   ├── detail.vue      # 详情页面
│   └── [page].vue      # 其他页面
├── components/          # 组件库
│   ├── [Component].vue # 业务组件
│   └── index.ts        # 组件导出
├── dict/                # 数据字典
│   └── index.ts        # 字典定义
├── services/            # 服务层(可选)
├── locales/             # 国际化
├── config.ts            # 模块配置
└── index.ts             # 模块入口
```

## 编码规范

### 1. 组件定义规范
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script lang="ts" setup>
// 组件名定义 - 必须
defineOptions({
  name: "module-component-name" // 格式：模块-组件名
});

// 导入
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';

// Props定义
interface Props {
  modelValue?: any;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'change': [value: any];
}>();

// 响应式数据
const loading = ref(false);
const formData = ref({});

// 计算属性
const isDisabled = computed(() => props.disabled || loading.value);

// 方法定义
const handleSubmit = async () => {
  loading.value = true;
  try {
    // 业务逻辑
    emit('change', formData.value);
  } catch (error) {
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style lang="scss" scoped>
.component-name {
  // 样式定义
}
</style>
```

## 用户体验优化

### 1. 加载状态管理
```typescript
const loading = ref(false);
const buttonLoading = ref<Record<string, boolean>>({});

const handleAction = async (id: string) => {
  buttonLoading.value[id] = true;
  try {
    await performAction(id);
  } finally {
    buttonLoading.value[id] = false;
  }
};
```

### 2. 错误处理
```typescript
const handleError = (error: any, operation: string) => {
  console.error(`${operation}失败:`, error);
  
  if (error.response?.status === 401) {
    ElMessage.error('登录已过期，请重新登录');
    // 跳转到登录页
  } else if (error.response?.status === 403) {
    ElMessage.error('权限不足，无法执行此操作');
  } else {
    ElMessage.error(`${operation}失败: ${error.message || '未知错误'}`);
  }
};
```

### 3. 懒加载优化
```typescript
// Tab切换懒加载
const handleTabChange = async (tabName: string) => {
  const currentData = getDataByTab(tabName);
  
  // 只有当前Tab没有数据时才加载
  if (currentData.length === 0) {
    await loadTabData(tabName);
  }
};

// 虚拟滚动
const virtualListRef = ref();
const itemSize = 80; // 每个项目的高度
```

## 样式规范

### 1. SCSS使用规范
```scss
// 变量定义
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;

// 混入定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 组件样式
.component-name {
  padding: 16px;
  
  &__header {
    @include flex-center;
    margin-bottom: 16px;
  }
  
  &__content {
    .item {
      margin-bottom: 8px;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
```

### 2. 响应式设计
```scss
// 断点定义
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}

// 使用示例
.component {
  padding: 8px;
  
  @include respond-to(md) {
    padding: 16px;
  }
  
  @include respond-to(lg) {
    padding: 24px;
  }
}
```

## 性能优化

### 1. 组件懒加载
```typescript
// 路由懒加载
const routes = [
  {
    path: '/task/info',
    component: () => import('./views/info.vue')
  }
];

// 组件懒加载
const TaskStepsFlow = defineAsyncComponent(() => import('./components/task-steps-flow.vue'));
```

### 2. 数据缓存
```typescript
// 使用Map缓存数据
const dataCache = new Map<string, any>();

const loadDataWithCache = async (key: string) => {
  if (dataCache.has(key)) {
    return dataCache.get(key);
  }
  
  const data = await service.loadData(key);
  dataCache.set(key, data);
  return data;
};
```

### 3. 防抖节流
```typescript
import { debounce, throttle } from 'lodash-es';

// 搜索防抖
const handleSearch = debounce(async (keyword: string) => {
  await loadData({ keyword });
}, 300);

// 滚动节流
const handleScroll = throttle((event: Event) => {
  // 滚动处理逻辑
}, 100);
```


## 重要提醒

1. **组件命名**: 必须使用`defineOptions({ name: "模块-页面" })`定义组件名
2. **CRUD组件**: 使用`<cl-table ref="Table" />`而不是`<cl-table ref="Table" v-bind="Table" />`
3. **服务调用**: 严格使用EPS生成的服务路径，不要手动创建服务文件
4. **页面结构**: 复杂页面使用`<el-scrollbar>`包装，参考demo模块的标准写法
5. **开发流程**: 先查看demo模块示例，严格按照demo的代码结构编写

遵循以上规范可以确保代码质量、组件复用性和开发效率。

### 最佳实践规则
1. **始终使用 `<el-scrollbar>` 包装页面内容**
2. **保持Cool Admin框架的全局CSS不变**
3. **参考 `manager-dashboard.vue` 等标准页面的写法**
4. **不要尝试修改 `#app` 容器的滚动设置**

### 经验教训
- Cool Admin框架的滚动机制是经过精心设计的，不要轻易修改
- `<el-scrollbar>` 组件专门用于在固定容器内提供滚动功能
- 这种方案保持了左侧菜单和顶部导航的固定，只让内容区域滚动
- 简单、稳定、符合框架设计理念


## 开发规则和最佳实践

### 1. CRUD组件使用规范
- 使用 `useCrud`、`useTable`、`useUpsert` hooks
- 通过 ref 自动注入配置，**不要使用 v-bind 绑定**
- 正确写法：`<cl-table ref="Table" />`
- 错误写法：`<cl-table ref="Table" v-bind="Table" />`

### 2. 组件命名规范
- 必须使用 `defineOptions({ name: "模块-页面" })` 定义组件名
- 组件名格式：`sop-industry`、`sop-template` 等，用于路由缓存
- Table 和 Upsert 首字母大写：`const Table = useTable()`

### 3. 服务调用规范
- 严格使用 EPS 生成的服务路径，如 `service.sop.s.o.p.industry`
- **不要手动创建服务文件**，会与 EPS 系统冲突
- 服务路径由后端实体类包结构自动生成

【重要】RestController接口调用规范：
- 所有自定义 RestController 接口（如 AI 任务生成器、任务状态等）必须统一用 `service.request({ url, method, data })` 调用，不允许直接 import request。
- url 必须为全量 path，method 必须大写，参数放 data 字段。
- 参考任务状态接口写法，保证全局拦截、token、mock、权限、异常处理一致。
- 示例：
```js
await service.request({
  url: '/admin/sop/ai-task-generator/preview',
  method: 'POST',
  data: { ... }
})
```

### 4. 页面结构规范
```vue
<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <cl-multi-delete-btn />
      <cl-flex1 />
      <cl-search-key />
    </cl-row>
    
    <cl-row>
      <cl-table ref="Table" />
    </cl-row>
    
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>
```

### 5. 开发流程
- 先查看 `src/modules/demo` 的对应示例
- 严格按照 demo 的代码结构编写
- 避免使用复杂的 Element Plus 图标导入，优先使用 emoji 或简单图标

【重要】组件复用与单文件长度规范：
- 组件应尽量复用，避免重复造轮子。
- 单文件（.vue/.ts）代码行数建议不超过 500 行，超出需合理拆分为多个子组件或模块。
- 拆分时优先按功能、UI块、业务逻辑分层，保持每个文件职责单一、易维护。
- 拆分后的子组件/模块应有清晰命名和注释，便于团队协作和复用。
- 复用组件应放在 modules/xxx/components 或 plugins/xxx/components 目录，避免散落。
- 参考 demo、base、task 等模块的组件拆分和复用方式。
