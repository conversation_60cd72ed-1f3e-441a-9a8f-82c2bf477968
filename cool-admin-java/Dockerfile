# 使用 OpenJDK 17 作为基础镜像（兼容性更好）
FROM ghcr.io/graalvm/graalvm-ce:latest

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置容器内的工作目录
WORKDIR /app

# 创建应用用户（安全考虑）
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 将可执行的jar文件复制到容器内
COPY target/cool-admin-8.0.0.jar /app/cool-admin.jar

# 创建日志和临时目录
RUN mkdir -p /app/logs /app/temp && \
    chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 暴露Spring Boot应用程序运行的端口
EXPOSE 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:18001/actuator/health || exit 1

# 运行Spring Boot应用程序的命令
ENTRYPOINT ["java", \
    "-Djava.security.egd=file:/dev/./urandom", \
    "-Dspring.profiles.active=prod", \
    "-Duser.timezone=Asia/Shanghai", \
    "-jar", \
    "/app/cool-admin.jar"]
