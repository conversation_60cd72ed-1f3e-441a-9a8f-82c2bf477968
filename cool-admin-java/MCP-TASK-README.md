# Cool Admin 任务MCP工具实现

## 🎯 概述

基于参考文章 https://www.cnblogs.com/lori/p/18874293 实现的任务管理MCP工具，使用Spring AI的`spring-ai-mcp-server-webflux-spring-boot-starter`和`@Tool`注解方式。

## 🛠️ 技术架构

```
AI助手客户端
     ↓ MCP协议 (SSE异步模式)
┌─────────────────────────────────────┐
│        Spring Boot应用              │
├─────────────────────────────────────┤
│  spring-ai-mcp-server-webflux      │
│  - McpServer (SSE模式)             │
│  - ToolCallbackProvider            │
│  - @Tool注解                       │
├─────────────────────────────────────┤
│         MCP工具服务层               │
│  - TaskMcpService                  │
│  - 10个任务相关工具                 │
├─────────────────────────────────────┤
│         业务服务层                   │
│  - TaskInfoService                 │
│  - TaskPackageService              │
│  - TaskExecutionService            │
├─────────────────────────────────────┤
│         数据访问层                   │
│  - TaskInfoEntity                  │
│  - TaskPackageEntity               │
│  - TaskExecutionEntity             │
└─────────────────────────────────────┘
```

## 🔧 核心配置

### application.yml配置
```yaml
spring:
  ai:
    mcp:
      server:
        enabled: true
        type: ASYNC              # 异步模式
        sse-message-endpoint: mcp/messages
        stdio:
          enabled: false
        sse:
          enabled: true          # 启用SSE
```

### MCP服务器配置
```java
@Configuration
public class McpServerConfig {
    @Bean
    public McpServer mcpServer() {
        return McpServer.builder()
            .serverInfo(McpSchema.ServerInfo.builder()
                .name("sop-ai-mcp")
                .version("1.0.0")
                .description("Cool Admin系统的MCP工具服务器")
                .build())
            .toolCallbackProviders(toolCallbackProviders)
            .build();
    }
}
```

## 🔨 任务MCP工具 (10个)

### 基础任务操作
1. **task_list** - 获取任务列表
   - 支持分页和多条件筛选
   - 参数：page, size, keyword, taskStatus, taskCategory, priority, scenarioId

2. **task_get** - 获取任务详情
   - 根据ID获取任务详细信息
   - 参数：id

3. **task_create** - 创建新任务
   - 支持设置优先级、类别和场景关联
   - 参数：name, description, priority, taskCategory, scenarioId

4. **task_update** - 更新任务信息
   - 更新状态、优先级、描述等
   - 参数：id, name, description, priority, taskStatus, taskCategory

### 任务流程操作
5. **task_assign** - 分配任务
   - 分配任务给指定用户
   - 参数：taskId, assigneeId, assignReason

6. **task_start** - 开始执行任务
   - 开始执行任务，记录开始时间
   - 参数：taskId, startNote

7. **task_complete** - 完成任务
   - 完成任务，记录完成时间
   - 参数：taskId, completeNote

### 任务包操作
8. **task_package_list** - 获取任务包列表
   - 任务包是一组相关任务的集合
   - 参数：page, size, keyword

9. **task_package_get** - 获取任务包详情
   - 获取任务包详细信息
   - 参数：id

### 统计分析
10. **task_stats** - 任务统计
    - 获取任务统计信息
    - 返回：总数、各状态数量、完成率等

## 🚀 快速开始

### 1. 构建项目
```bash
mvn clean package -DskipTests
```

### 2. 启动服务器
```cmd
start-mcp-server.bat
```

### 3. 验证服务器
```bash
# 健康检查
curl http://localhost:18001/admin/mcp/health

# 获取工具列表
curl http://localhost:18001/admin/mcp/tools

# 获取服务器信息
curl http://localhost:18001/admin/mcp/info
```

## 📝 使用示例

### 1. 获取任务列表
```bash
curl -X POST http://localhost:18001/admin/mcp/test/task \
  -H "Content-Type: application/json" \
  -d '{
    "action": "list",
    "page": 1,
    "size": 10,
    "taskStatus": 0
  }'
```

### 2. 创建任务
```bash
curl -X POST http://localhost:18001/admin/mcp/test/task \
  -H "Content-Type: application/json" \
  -d '{
    "action": "create",
    "name": "测试任务",
    "description": "这是一个测试任务",
    "priority": 2,
    "taskCategory": "AD_HOC"
  }'
```

### 3. 分配任务
```bash
curl -X POST http://localhost:18001/admin/mcp/test/task \
  -H "Content-Type: application/json" \
  -d '{
    "action": "assign",
    "taskId": 1,
    "assigneeId": 100,
    "assignReason": "根据技能匹配分配"
  }'
```

### 4. 开始任务
```bash
curl -X POST http://localhost:18001/admin/mcp/test/task \
  -H "Content-Type: application/json" \
  -d '{
    "action": "start",
    "taskId": 1,
    "startNote": "开始执行任务"
  }'
```

### 5. 完成任务
```bash
curl -X POST http://localhost:18001/admin/mcp/test/task \
  -H "Content-Type: application/json" \
  -d '{
    "action": "complete",
    "taskId": 1,
    "completeNote": "任务已完成"
  }'
```

## 🔍 任务状态流转

```
待分配(0) → 待执行(1) → 执行中(2) → 已完成(3)
    ↓           ↓           ↓
  已关闭(4)   已关闭(4)   已关闭(4)
```

- **0**: 待分配 - 任务已创建，等待分配给执行人
- **1**: 待执行 - 任务已分配，等待开始执行
- **2**: 执行中 - 任务正在执行
- **3**: 已完成 - 任务执行完成
- **4**: 已关闭 - 任务被取消或终止

## 🎯 @Tool注解特性

### 优势
1. **简洁明了**: 直接在方法上标注，无需复杂配置
2. **自动发现**: Spring自动扫描并注册工具
3. **类型安全**: 编译时检查参数类型
4. **易于维护**: 工具定义和实现在同一位置
5. **统一返回**: 使用Cool Admin的R类型统一返回格式

### 示例
```java
@Tool(name = "task_create", description = "创建新任务，支持设置优先级、类别和场景关联")
public R taskCreate(
    String name,
    String description,
    Integer priority,
    String taskCategory,
    Long scenarioId
) {
    // 实现逻辑
    return R.ok(task);
}
```

## 🔄 R类型返回格式

### 使用Cool Admin的R类型
所有MCP工具方法都使用`com.cool.core.request.R`作为统一返回类型：

```java
// 成功返回
return R.ok(data);

// 错误返回
return R.error("错误信息");
```

### R类型优势
- **统一格式**: 与Cool Admin框架保持一致
- **类型安全**: 泛型支持，编译时检查
- **简洁API**: 简单的ok/error方法
- **数据封装**: 自动包装返回数据

## 🌐 SSE异步模式

### 配置特点
- **type: ASYNC**: 启用异步模式
- **sse.enabled: true**: 启用Server-Sent Events
- **stdio.enabled: false**: 禁用标准输入输出模式

### 优势
- 支持实时双向通信
- 更好的性能和响应性
- 适合Web环境部署

## 🚨 故障排除

### 常见问题

1. **MCP服务器启动失败**
   - 检查Spring AI版本兼容性
   - 确认配置文件正确
   - 查看启动日志

2. **工具未注册**
   - 确认@Service注解
   - 检查@Tool注解
   - 验证组件扫描

3. **任务操作失败**
   - 检查任务状态是否正确
   - 确认参数类型匹配
   - 查看业务逻辑异常

### 调试技巧

1. **查看工具列表**
   ```bash
   curl http://localhost:18001/admin/mcp/tools
   ```

2. **检查服务器状态**
   ```bash
   curl http://localhost:18001/admin/mcp/health
   ```

3. **启用调试日志**
   ```yaml
   logging:
     level:
       org.springframework.ai.mcp: DEBUG
       com.cool.modules.mcp: DEBUG
   ```

## 📚 扩展开发

### 添加新的任务工具

1. **在TaskMcpService中添加方法**
   ```java
   @Tool(name = "task_custom", description = "自定义任务操作")
   public BaseResult taskCustom(String param) {
       // 实现逻辑
       return BaseResult.ok("成功");
   }
   ```

2. **在测试控制器中添加测试接口**
   ```java
   case "custom":
       return taskMcpService.taskCustom((String) params.get("param"));
   ```

### 集成其他业务模块

1. **创建新的MCP服务类**
   ```java
   @Service
   public class SopMcpService {
       @Tool(name = "sop_list", description = "获取SOP列表")
       public BaseResult sopList() {
           // 实现逻辑
       }
   }
   ```

2. **Spring会自动发现并注册**
   - 无需修改配置类
   - 工具会自动添加到MCP服务器

## 🎊 总结

这个实现提供了完整的任务管理MCP工具集，支持：

- ✅ 10个任务相关工具
- ✅ SSE异步模式
- ✅ @Tool注解方式
- ✅ 完整的任务生命周期管理
- ✅ 任务包管理
- ✅ 统计分析功能
- ✅ 完善的测试接口
- ✅ 详细的文档说明

现在您的Cool Admin系统已经具备了强大的任务管理MCP工具，可以为AI助手提供完整的任务操作能力！
