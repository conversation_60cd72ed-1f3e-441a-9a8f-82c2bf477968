# Cool Admin MCP SSE 访问指南

## 🌐 概述

Cool Admin MCP服务器支持通过Server-Sent Events (SSE) 方式访问taskTools，提供实时双向通信能力。

## 🔧 配置说明

### application.yml配置
```yaml
server:
  ai:
    mcp:
      server:
        enabled: true
        type: ASYNC                    # 异步模式
        sse-message-endpoint: mcp/messages  # SSE端点
        stdio:
          enabled: false
        sse:
          enabled: true                # 启用SSE
```

## 🚀 访问方式

### 1. SSE连接端点
```
GET /mcp/messages
```

**参数:**
- `clientId` (可选): 客户端标识符

**响应:** Server-Sent Events流

### 2. 工具调用端点
```
POST /mcp/call
```

**请求体:**
```json
{
  "tool": "task_list",
  "params": {
    "page": 1,
    "size": 10,
    "keyword": "测试"
  },
  "clientId": "client_123"
}
```

### 3. 状态查询端点
```
GET /mcp/status
```

## 🛠️ 可用工具

### 任务管理工具 (10个)

| 工具名称 | 描述 | 参数 |
|---------|------|------|
| `task_list` | 获取任务列表 | page, size, keyword, taskStatus, taskCategory, priority, scenarioId |
| `task_get` | 获取任务详情 | id |
| `task_create` | 创建任务 | name, description, priority, taskCategory, scenarioId |
| `task_update` | 更新任务 | id, name, description, priority, taskStatus, taskCategory |
| `task_assign` | 分配任务 | taskId, assigneeId, assignReason |
| `task_start` | 开始任务 | taskId, startNote |
| `task_complete` | 完成任务 | taskId, completeNote |
| `task_stats` | 任务统计 | 无参数 |
| `task_package_list` | 任务包列表 | page, size, keyword |
| `task_package_get` | 任务包详情 | id |

## 📝 使用示例

### JavaScript客户端示例

```javascript
// 1. 建立SSE连接
const eventSource = new EventSource('/mcp/messages?clientId=my-client');

// 2. 监听连接事件
eventSource.addEventListener('connected', function(event) {
    const data = JSON.parse(event.data);
    console.log('连接成功:', data);
});

// 3. 监听工具调用结果
eventSource.addEventListener('tool_call_result', function(event) {
    const data = JSON.parse(event.data);
    console.log('工具调用结果:', data);
});

// 4. 监听心跳
eventSource.addEventListener('heartbeat', function(event) {
    const data = JSON.parse(event.data);
    console.log('心跳:', data);
});

// 5. 调用工具
function callTool(toolName, params) {
    fetch('/mcp/call', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            tool: toolName,
            params: params,
            clientId: 'my-client'
        })
    })
    .then(response => response.json())
    .then(data => console.log('调用结果:', data))
    .catch(error => console.error('调用失败:', error));
}

// 示例：获取任务列表
callTool('task_list', {
    page: 1,
    size: 10,
    taskStatus: 0
});

// 示例：创建任务
callTool('task_create', {
    name: '测试任务',
    description: '这是一个测试任务',
    priority: 2,
    taskCategory: 'TEST'
});
```

### cURL示例

```bash
# 1. 建立SSE连接（在终端中保持连接）
curl -N -H "Accept: text/event-stream" \
  "http://localhost:18001/mcp/messages?clientId=curl-client"

# 2. 调用工具（在另一个终端中）
curl -X POST http://localhost:18001/mcp/call \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "task_list",
    "params": {
      "page": 1,
      "size": 5
    }
  }'

# 3. 创建任务
curl -X POST http://localhost:18001/mcp/call \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "task_create",
    "params": {
      "name": "SSE测试任务",
      "description": "通过SSE创建的任务",
      "priority": 2,
      "taskCategory": "SSE_TEST"
    }
  }'

# 4. 获取状态
curl http://localhost:18001/mcp/status
```

## 🎯 SSE事件类型

### 1. connected
连接建立成功时发送
```json
{
  "clientId": "client_123",
  "message": "SSE连接已建立",
  "timestamp": 1703123456789,
  "availableTools": ["task_list", "task_get", ...]
}
```

### 2. tool_call_start
工具调用开始时发送
```json
{
  "tool": "task_create",
  "params": {"name": "测试任务"},
  "timestamp": 1703123456789
}
```

### 3. tool_call_result
工具调用完成时发送
```json
{
  "tool": "task_create",
  "result": {
    "code": 1000,
    "message": "success",
    "data": {...}
  },
  "timestamp": 1703123456789
}
```

### 4. tool_call_error
工具调用错误时发送
```json
{
  "error": "任务名称不能为空",
  "timestamp": 1703123456789
}
```

### 5. heartbeat
定期心跳（每30秒）
```json
{
  "timestamp": 1703123456789,
  "activeConnections": 3
}
```

## 🌐 浏览器测试页面

我们提供了一个完整的HTML测试页面：`sse-client-example.html`

### 功能特性：
- ✅ 实时SSE连接状态显示
- ✅ 所有10个任务工具的图形界面
- ✅ 实时消息日志显示
- ✅ 参数输入和验证
- ✅ 错误处理和状态反馈

### 使用方法：
1. 启动Cool Admin服务器
2. 在浏览器中打开 `sse-client-example.html`
3. 点击"连接"按钮建立SSE连接
4. 使用各种工具测试功能

## 🔍 调试和监控

### 1. 查看连接状态
```bash
curl http://localhost:18001/mcp/status
```

### 2. 服务器日志
服务器会输出SSE连接的建立、断开和错误信息：
```
SSE连接完成: client_1703123456789
SSE连接超时: client_1703123456790
SSE连接错误: client_1703123456791, Connection reset
```

### 3. 浏览器开发者工具
- Network标签：查看SSE连接状态
- Console标签：查看JavaScript日志
- Application标签：查看EventSource状态

## 🚨 故障排除

### 常见问题

1. **SSE连接失败**
   - 检查服务器是否启动
   - 确认端口18001是否可访问
   - 检查防火墙设置

2. **工具调用失败**
   - 检查参数格式是否正确
   - 确认必需参数是否提供
   - 查看服务器错误日志

3. **连接频繁断开**
   - 检查网络稳定性
   - 调整心跳间隔
   - 检查服务器资源使用情况

### 调试技巧

1. **启用详细日志**
   ```yaml
   logging:
     level:
       com.cool.core.mcp: DEBUG
   ```

2. **监控连接数量**
   ```bash
   watch -n 1 'curl -s http://localhost:18001/mcp/status | jq .data.activeConnections'
   ```

3. **测试工具可用性**
   ```bash
   for tool in task_list task_get task_create; do
     echo "Testing $tool..."
     curl -X POST http://localhost:18001/mcp/call \
       -H "Content-Type: application/json" \
       -d "{\"tool\":\"$tool\",\"params\":{}}"
   done
   ```

## 🎊 最佳实践

1. **连接管理**
   - 实现自动重连机制
   - 处理连接超时和错误
   - 合理设置心跳间隔

2. **错误处理**
   - 捕获所有SSE事件错误
   - 提供用户友好的错误信息
   - 实现重试机制

3. **性能优化**
   - 避免频繁的工具调用
   - 合理使用分页参数
   - 及时清理无用连接

4. **安全考虑**
   - 验证客户端身份
   - 限制连接数量
   - 过滤敏感信息

现在您可以通过SSE方式实时访问所有的taskTools了！🚀
