-- 项目信息测试数据
INSERT INTO org_project_info (id, project_name, project_code, description, status, priority, owner_id, creator_id, planned_start_time, planned_end_time, enabled, order_num, create_time, update_time) VALUES
(1, '阳光社区项目', 'PROJ_001', '阳光社区智慧化改造项目，包含基础设施升级、智能化系统部署等', 1, 3, 1, 1, '2025-01-01 09:00:00', '2025-12-31 18:00:00', true, 1, NOW(), NOW()),
(2, '数字化转型项目', 'PROJ_002', '企业数字化转型项目，涵盖业务流程优化、系统集成等', 1, 4, 1, 1, '2025-02-01 09:00:00', '2025-11-30 18:00:00', true, 2, NOW(), NOW()),
(3, '客户服务优化项目', 'PROJ_003', '客户服务体系优化项目，提升客户满意度和服务效率', 0, 2, 1, 1, '2025-03-01 09:00:00', '2025-10-31 18:00:00', true, 3, NOW(), NOW()),
(4, '产品创新研发项目', 'PROJ_004', '新产品研发项目，包含市场调研、产品设计、技术开发等', 1, 4, 1, 1, '2025-01-15 09:00:00', '2025-08-31 18:00:00', true, 4, NOW(), NOW()),
(5, '运营效率提升项目', 'PROJ_005', '运营流程优化项目，通过自动化和标准化提升运营效率', 1, 3, 1, 1, '2025-02-15 09:00:00', '2025-09-30 18:00:00', true, 5, NOW(), NOW());