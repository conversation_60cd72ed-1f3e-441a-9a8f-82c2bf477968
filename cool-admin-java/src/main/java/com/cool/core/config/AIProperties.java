package com.cool.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties(prefix = "cool.ai")
@Component
public class AIProperties {
    private boolean enabled = true;
    private String defaultProvider = "openai";
    private Openai openai;
    private Dify dify;
    private Claude claude;
    private LocalModel localModel;
    private Features features;
    private Performance performance;

    @Data
    public static class Openai {
        private String apiKey;
        private String baseUrl;
        private String model;
        private Integer maxTokens;
        private Double temperature;
        private Integer timeout;
        private Integer retryCount;
    }

    @Data
    public static class Dify {
        private String baseUrl;
        private String apiKey;
        private Workflows workflows;

        @Data
        public static class Workflows {
            private String sopGeneration;
            private String taskGeneration;
            private String scenarioSelection;
            private String qualityCheck;
        }
    }

    @Data
    public static class Claude {
        private String apiKey;
        private String baseUrl;
        private String model;
        private Integer maxTokens;
        private Double temperature;
    }

    @Data
    public static class LocalModel {
        private String endpoint;
        private String model;
        private Integer maxTokens;
        private Double temperature;
    }

    @Data
    public static class Features {
        private boolean sopGeneration = true;
        private boolean intelligentScheduling = true;
        private boolean executionGuidance = true;
        private boolean qualityAssessment = true;
        private boolean processOptimization = true;
        private boolean predictiveAnalysis = true;
    }

    @Data
    public static class Performance {
        private Integer threadPoolSize = 10;
        private Integer queueCapacity = 100;
        private boolean cacheEnabled = true;
        private Integer cacheExpireMinutes = 30;
    }

    // 为了向后兼容，保留getLlm()方法
    public Llm getLlm() {
        Llm llm = new Llm();
        llm.setOpenai(this.openai);
        llm.setClaude(this.claude);
        return llm;
    }

    @Data
    public static class Llm {
        private Openai openai;
        private Claude claude;
    }
} 