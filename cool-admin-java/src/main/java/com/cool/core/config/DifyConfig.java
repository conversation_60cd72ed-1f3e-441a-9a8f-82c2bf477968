package com.cool.core.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Dify相关配置
 */
@Configuration
public class DifyConfig {
    
    /**
     * 配置用于Dify的RestTemplate
     */
    @Bean("difyRestTemplate")
    public RestTemplate difyRestTemplate(RestTemplateBuilder builder, DifyProperties difyProperties) {
        return builder
                .setConnectTimeout(Duration.ofMillis(difyProperties.getTimeout()))
                .setReadTimeout(Duration.ofMillis(difyProperties.getTimeout()))
                .build();
    }
    
    /**
     * 配置ObjectMapper（如果不存在的话）
     */
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }
}
