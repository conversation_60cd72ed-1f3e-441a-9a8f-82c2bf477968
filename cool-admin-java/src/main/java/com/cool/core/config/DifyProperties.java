package com.cool.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * Dify工作流配置属性
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cool.ai.dify")
public class DifyProperties {
    
    /**
     * Dify API基础地址
     */
    private String baseUrl = "http://localhost:5001";
    
    /**
     * Dify API密钥
     */
    private String apiKey;
    
    /**
     * 请求超时时间(毫秒)
     */
    private Integer timeout = 120000;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 3;
    
    /**
     * 是否启用流式响应
     */
    private Boolean streamEnabled = true;
    
    /**
     * 工作流配置
     */
    @NestedConfigurationProperty
    private Map<String, WorkflowConfig> workflows;
    
    /**
     * 工作流配置
     */
    @Data
    public static class WorkflowConfig {
        /**
         * 应用ID
         */
        private String appId;
        
        /**
         * 工作流ID
         */
        private String workflowId;
        
        /**
         * 工作流名称
         */
        private String name;
        
        /**
         * 工作流描述
         */
        private String description;
        
        /**
         * 是否启用
         */
        private Boolean enabled = true;
        
        /**
         * 超时时间(毫秒)，如果不设置则使用全局配置
         */
        private Integer timeout;
        
        /**
         * 重试次数，如果不设置则使用全局配置
         */
        private Integer retryCount;
    }
}
