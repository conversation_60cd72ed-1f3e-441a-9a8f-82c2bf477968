package com.cool.core.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;

@Configuration
@RequiredArgsConstructor
public class ThreadPoolConfig {

    private final LogProperties logProperties;

    @Bean(name = "logTaskExecutor")
    public Executor loggingTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        int corePoolSize = Runtime.getRuntime().availableProcessors() * logProperties.getCorePoolSizeMultiplier();
        int maxPoolSize = corePoolSize * logProperties.getMaxPoolSizeMultiplier();
        int queueCapacity = maxPoolSize * logProperties.getQueueCapacityMultiplier();

        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("logTask-");

        // 自定义拒绝策略
        executor.setRejectedExecutionHandler(new LogDiscardPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = "cachedThreadPool")
    public ExecutorService cachedThreadPool() {
        // 创建一个虚拟线程池，每个任务使用一个虚拟线程执行
        return Executors.newCachedThreadPool();
    }

    @Bean(name = "taskExecutor")
    public Executor aiTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // AI任务通常是IO密集型，配置较多的线程
        int corePoolSize = Math.max(4, Runtime.getRuntime().availableProcessors());
        int maxPoolSize = corePoolSize * 2;
        int queueCapacity = 100;
        
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("aiTask-");
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：由调用者线程执行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        // 用安全上下文代理包装
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }
}
