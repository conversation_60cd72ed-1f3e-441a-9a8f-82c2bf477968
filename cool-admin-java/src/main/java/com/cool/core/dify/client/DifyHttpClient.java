package com.cool.core.dify.client;

import com.cool.core.config.DifyProperties;
import com.cool.core.dify.dto.DifyWorkflowRequest;
import com.cool.core.dify.dto.DifyWorkflowResponse;
import com.cool.core.dify.exception.DifyException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * Dify HTTP客户端
 * 负责与Dify API的HTTP通信
 */
@Component
public class DifyHttpClient {

    private static final Logger log = LoggerFactory.getLogger(DifyHttpClient.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private DifyProperties difyProperties;
    
    /**
     * 执行工作流(阻塞模式)
     */
    public DifyWorkflowResponse executeWorkflow(String workflowKey, Map<String, Object> inputs, String userId) {
        // 验证工作流配置
        getWorkflowConfig(workflowKey);

        DifyWorkflowRequest request = DifyWorkflowRequest.blocking(inputs, userId);

        try {
            log.info("执行Dify工作流: {} (User: {})", workflowKey, userId);

            HttpHeaders headers = createHeaders();
            HttpEntity<DifyWorkflowRequest> entity = new HttpEntity<>(request, headers);

            String url = buildWorkflowUrl();

            ResponseEntity<DifyWorkflowResponse> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, DifyWorkflowResponse.class
            );

            DifyWorkflowResponse result = response.getBody();
            if (result == null) {
                throw new DifyException("工作流响应为空");
            }

            if (result.isFailed()) {
                throw new DifyException.WorkflowExecutionException(
                    result.getError(), workflowKey, result.getTaskId()
                );
            }

            log.info("工作流执行成功: {}", workflowKey);

            return result;

        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                throw new DifyException.AuthenticationException("Dify API认证失败，请检查API密钥");
            }
            throw new DifyException("工作流执行失败: " + e.getMessage(), e);
        } catch (HttpServerErrorException e) {
            throw new DifyException("Dify服务器错误: " + e.getMessage(), e);
        } catch (ResourceAccessException e) {
            throw new DifyException.NetworkException("网络连接失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("工作流执行异常: {}", workflowKey, e);
            throw new DifyException("工作流执行异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行工作流(流式模式)
     */
    public CompletableFuture<DifyWorkflowResponse> executeWorkflowStream(
            String workflowKey,
            Map<String, Object> inputs,
            String userId,
            Consumer<String> onMessage) {

        return CompletableFuture.supplyAsync(() -> {
            // TODO: 实现SSE流式处理
            log.warn("流式模式暂未实现，使用阻塞模式");
            return executeWorkflow(workflowKey, inputs, userId);
        });
    }
    
    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + difyProperties.getApiKey());
        headers.set("User-Agent", "Cool-Admin-Java/1.0");
        return headers;
    }

    /**
     * 构建工作流URL
     */
    private String buildWorkflowUrl() {
        return difyProperties.getBaseUrl() + "/v1/workflows/run";
    }

    /**
     * 验证工作流配置
     */
    private void getWorkflowConfig(String workflowKey) {
        // 简化实现，只做基本验证
        if (workflowKey == null || workflowKey.trim().isEmpty()) {
            throw new DifyException.ValidationException("工作流Key不能为空");
        }
    }
}
