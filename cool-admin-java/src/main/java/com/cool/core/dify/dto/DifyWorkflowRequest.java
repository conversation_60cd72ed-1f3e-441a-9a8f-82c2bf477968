package com.cool.core.dify.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Dify工作流请求DTO
 */
public class DifyWorkflowRequest {
    
    /**
     * 输入参数
     */
    private Map<String, Object> inputs;
    
    /**
     * 响应模式: blocking(阻塞) 或 streaming(流式)
     */
    @JsonProperty("response_mode")
    private String responseMode = "blocking";
    
    /**
     * 用户标识
     */
    private String user;
    
    /**
     * 对话ID(可选)
     */
    @JsonProperty("conversation_id")
    private String conversationId;
    
    /**
     * 文件列表(可选)
     */
    private Object[] files;
    
    /**
     * 自动生成标题(可选)
     */
    @JsonProperty("auto_generate_name")
    private Boolean autoGenerateName;
    
    // 构造函数
    public DifyWorkflowRequest() {}

    public DifyWorkflowRequest(Map<String, Object> inputs, String responseMode, String user) {
        this.inputs = inputs;
        this.responseMode = responseMode;
        this.user = user;
    }

    /**
     * 创建阻塞请求
     */
    public static DifyWorkflowRequest blocking(Map<String, Object> inputs, String user) {
        return new DifyWorkflowRequest(inputs, "blocking", user);
    }

    /**
     * 创建流式请求
     */
    public static DifyWorkflowRequest streaming(Map<String, Object> inputs, String user) {
        return new DifyWorkflowRequest(inputs, "streaming", user);
    }

    // Getter和Setter方法
    public Map<String, Object> getInputs() { return inputs; }
    public void setInputs(Map<String, Object> inputs) { this.inputs = inputs; }

    public String getResponseMode() { return responseMode; }
    public void setResponseMode(String responseMode) { this.responseMode = responseMode; }

    public String getUser() { return user; }
    public void setUser(String user) { this.user = user; }

    public String getConversationId() { return conversationId; }
    public void setConversationId(String conversationId) { this.conversationId = conversationId; }

    public Object[] getFiles() { return files; }
    public void setFiles(Object[] files) { this.files = files; }

    public Boolean getAutoGenerateName() { return autoGenerateName; }
    public void setAutoGenerateName(Boolean autoGenerateName) { this.autoGenerateName = autoGenerateName; }
}
