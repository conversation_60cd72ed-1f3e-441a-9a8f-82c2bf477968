package com.cool.core.dify.exception;

/**
 * Dify工作流异常
 */
public class DifyException extends RuntimeException {
    
    private String errorCode;
    private String workflowId;
    private String taskId;
    
    public DifyException(String message) {
        super(message);
    }
    
    public DifyException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public DifyException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public DifyException(String errorCode, String message, String workflowId) {
        super(message);
        this.errorCode = errorCode;
        this.workflowId = workflowId;
    }
    
    public DifyException(String errorCode, String message, String workflowId, String taskId) {
        super(message);
        this.errorCode = errorCode;
        this.workflowId = workflowId;
        this.taskId = taskId;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getWorkflowId() {
        return workflowId;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    /**
     * 网络连接异常
     */
    public static class NetworkException extends DifyException {
        public NetworkException(String message, Throwable cause) {
            super("NETWORK_ERROR", message,cause.getMessage());
        }
    }
    
    /**
     * 认证异常
     */
    public static class AuthenticationException extends DifyException {
        public AuthenticationException(String message) {
            super("AUTH_ERROR", message);
        }
    }
    
    /**
     * 工作流执行异常
     */
    public static class WorkflowExecutionException extends DifyException {
        public WorkflowExecutionException(String message, String workflowId, String taskId) {
            super("WORKFLOW_EXECUTION_ERROR", message, workflowId, taskId);
        }
    }
    
    /**
     * 超时异常
     */
    public static class TimeoutException extends DifyException {
        public TimeoutException(String message, String workflowId) {
            super("TIMEOUT_ERROR", message, workflowId);
        }
    }
    
    /**
     * 参数验证异常
     */
    public static class ValidationException extends DifyException {
        public ValidationException(String message) {
            super("VALIDATION_ERROR", message);
        }
    }
}
