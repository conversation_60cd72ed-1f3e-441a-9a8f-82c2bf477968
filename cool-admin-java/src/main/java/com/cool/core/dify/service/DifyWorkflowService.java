package com.cool.core.dify.service;

import com.cool.core.dify.client.DifyHttpClient;
import com.cool.core.dify.dto.DifyWorkflowResponse;
import com.cool.core.dify.exception.DifyException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * Dify工作流服务
 * 提供高级的工作流调用接口
 */
@Service
public class DifyWorkflowService {

    private static final Logger log = LoggerFactory.getLogger(DifyWorkflowService.class);

    @Autowired
    private DifyHttpClient difyHttpClient;
    
    /**
     * SOP解析工作流
     */
    public Map<String, Object> parseSOPByWorkflow(String description, String industry, String userId) {
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("description", description);
        inputs.put("industry", industry);
        inputs.put("language", "zh-CN");
        
        try {
            DifyWorkflowResponse response = difyHttpClient.executeWorkflow("sop-parse", inputs, userId);
            return response.getData();
        } catch (DifyException e) {
            log.error("SOP解析工作流执行失败", e);
            throw new RuntimeException("SOP解析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 任务生成工作流
     */
    public Map<String, Object> generateTasksByWorkflow(String taskDescription, String scenarioContext, String userId) {
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("task_description", taskDescription);
        inputs.put("scenario_context", scenarioContext);
        inputs.put("language", "zh-CN");
        
        try {
            DifyWorkflowResponse response = difyHttpClient.executeWorkflow("task-generate", inputs, userId);
            return response.getData();
        } catch (DifyException e) {
            log.error("任务生成工作流执行失败", e);
            throw new RuntimeException("任务生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 质量检查工作流
     */
    public Map<String, Object> qualityCheckByWorkflow(Long taskId, Map<String, Object> taskDetail, String userId) {
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("task_id", taskId);
        inputs.put("task_detail", taskDetail);
        inputs.put("language", "zh-CN");
        
        try {
            DifyWorkflowResponse response = difyHttpClient.executeWorkflow("quality-check", inputs, userId);
            return response.getData();
        } catch (DifyException e) {
            log.error("质量检查工作流执行失败", e);
            throw new RuntimeException("质量检查失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 流程优化工作流
     */
    public Map<String, Object> optimizeProcessByWorkflow(Long sopTemplateId, 
                                                        Map<String, Object> executionHistory, 
                                                        String userId) {
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("sop_template_id", sopTemplateId);
        inputs.put("execution_history", executionHistory);
        inputs.put("language", "zh-CN");
        
        try {
            DifyWorkflowResponse response = difyHttpClient.executeWorkflow("process-optimize", inputs, userId);
            return response.getData();
        } catch (DifyException e) {
            log.error("流程优化工作流执行失败", e);
            throw new RuntimeException("流程优化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行自定义工作流
     */
    public Map<String, Object> executeCustomWorkflow(String workflowKey, 
                                                    Map<String, Object> inputs, 
                                                    String userId) {
        try {
            DifyWorkflowResponse response = difyHttpClient.executeWorkflow(workflowKey, inputs, userId);
            return response.getData();
        } catch (DifyException e) {
            log.error("自定义工作流执行失败: {}", workflowKey, e);
            throw new RuntimeException("工作流执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步执行工作流
     */
    public CompletableFuture<Map<String, Object>> executeWorkflowAsync(String workflowKey, 
                                                                      Map<String, Object> inputs, 
                                                                      String userId) {
        return CompletableFuture.supplyAsync(() -> {
            return executeCustomWorkflow(workflowKey, inputs, userId);
        });
    }
    
    /**
     * 流式执行工作流
     */
    public CompletableFuture<Map<String, Object>> executeWorkflowStream(String workflowKey, 
                                                                       Map<String, Object> inputs, 
                                                                       String userId,
                                                                       Consumer<String> onMessage) {
        return difyHttpClient.executeWorkflowStream(workflowKey, inputs, userId, onMessage)
                .thenApply(DifyWorkflowResponse::getData);
    }
    
    /**
     * 批量执行工作流
     */
    public CompletableFuture<Map<String, Map<String, Object>>> executeBatchWorkflows(
            Map<String, Map<String, Object>> workflowInputs, 
            String userId) {
        
        Map<String, CompletableFuture<Map<String, Object>>> futures = new HashMap<>();
        
        workflowInputs.forEach((workflowKey, inputs) -> {
            futures.put(workflowKey, executeWorkflowAsync(workflowKey, inputs, userId));
        });
        
        return CompletableFuture.allOf(futures.values().toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    Map<String, Map<String, Object>> results = new HashMap<>();
                    futures.forEach((key, future) -> {
                        try {
                            results.put(key, future.get());
                        } catch (Exception e) {
                            log.error("批量工作流执行失败: {}", key, e);
                            results.put(key, Map.of("error", e.getMessage()));
                        }
                    });
                    return results;
                });
    }
    
    /**
     * 获取工作流执行状态
     */
    @Cacheable(value = "dify-workflow-status", key = "#workflowKey + ':' + #taskId")
    public Map<String, Object> getWorkflowStatus(String workflowKey, String taskId) {
        try {
            // 简化实现，返回模拟状态
            Map<String, Object> status = new HashMap<>();
            status.put("status", "completed");
            status.put("elapsed_time", 5.0);
            status.put("total_tokens", 100);
            status.put("data", Map.of("result", "success"));
            return status;
        } catch (Exception e) {
            log.error("获取工作流状态失败: {} (TaskId: {})", workflowKey, taskId, e);
            throw new RuntimeException("获取工作流状态失败: " + e.getMessage(), e);
        }
    }
}
