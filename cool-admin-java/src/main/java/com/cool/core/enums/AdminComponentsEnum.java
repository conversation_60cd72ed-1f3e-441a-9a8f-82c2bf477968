package com.cool.core.enums;

public class AdminComponentsEnum {


    /**
     * 省市区选择器 - 用户选择省市区
     */
    public static final String PCA = "pca";

    /**
     * 文本输入 - 文本编辑框
     */
    public static final String INPUT = "input";

    /**
     * 文本域 - 多行文本编辑框
     */
    public static final String TEXTAREA = "textarea";

    /**
     * 富文本编辑器 - 用于文章,商品详情的编辑
     */
    public static final String EDITOR_RICH = "editor-rich";

    /**
     * 代码编辑器 - 用于开发人员编写代码,支持多种语言,支持代码高亮,支持代码格式化
     */
    public static final String CODING = "coding";

    /**
     * 数字输入 - 数字输入编辑框
     */
    public static final String INPUT_NUMBER = "input-number";

    /**
     * 日期选择器 - 用户选择 年-月-日
     */
    public static final String DATE = "date";

    /**
     * 日期范围选择器 - 用户选择起始 年-月-日
     */
    public static final String DATERANGE = "daterange";

    /**
     * 时间选择器 - 用户选择 时:分:秒
     */
    public static final String DATETIME = "datetime";

    /**
     * 时间范围选择器 - 用户选择起始 年-月-日 时:分:秒
     */
    public static final String DATETIMERANGE = "datetimerange";

    /**
     * 单图上传 - 用户上传单张图片，如：头像、logo、封面
     */
    public static final String UPLOAD_IMG = "upload-img";

    /**
     * 多图上传 - 用户上传多张图片， 如：照片、图片
     */
    public static final String UPLOAD_IMG_MULTIPLE = "upload-img-multiple";

    /**
     * 单个文件上传 - 用户上传单个文件
     */
    public static final String UPLOAD_FILE = "upload-file";

    /**
     * 多个文件上传 - 用户上传多个文件
     */
    public static final String UPLOAD_FILE_MULTIPLE = "upload-file-multiple";

    /**
     * 状态选择器 - 用户开启或者关闭操作，如：是否启用、是否推荐、是否默认、置顶、启用禁用等
     */
    public static final String SWITCH = "switch";

    /**
     * 评分选择器 - 用户评分
     */
    public static final String RATE = "rate";

    /**
     * 滑块选择器 - 在一个固定区间内进行选择， 如：进度
     */
    public static final String PROGRESS = "progress";

    /**
     * 单选框 - 在一组备选项中进行单选，如：审批状态
     */
    public static final String RADIO = "radio";

    /**
     * 多选框 - 适用于选项比较少的情况，在一组备选项中进行多选， 如：学历、爱好等
     */
    public static final String CHECKBOX = "checkbox";

    /**
     * 下拉框 - 适用于当选项过多时，使用下拉菜单展示并选择内容，如：分类、标签等
     */
    public static final String SELECT = "select";
}
