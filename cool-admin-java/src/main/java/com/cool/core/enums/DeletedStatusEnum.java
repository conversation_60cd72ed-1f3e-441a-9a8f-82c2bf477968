package com.cool.core.enums;

import lombok.AllArgsConstructor;

import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeletedStatusEnum {
    ENABLED(1,"标记已删除"),
    DISABLED(0,"标记未删除");

    private Integer code;
    private String name;

    public static String getNameByCode(Integer code) {
        for (DeletedStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    } 
    
    public static Integer getCodeByName(String name) {
        for (DeletedStatusEnum value : values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }
}
