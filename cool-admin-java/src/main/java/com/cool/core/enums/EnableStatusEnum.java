package com.cool.core.enums;

import lombok.AllArgsConstructor;

import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EnableStatusEnum {
    ENABLED(1,"启用"),
    DISABLED(0,"禁用");

    private Integer code;
    private String name;

    public static String getNameByCode(Integer code) {
        for (EnableStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    } 
    
    public static Integer getCodeByName(String name) {
        for (EnableStatusEnum value : values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }
}
