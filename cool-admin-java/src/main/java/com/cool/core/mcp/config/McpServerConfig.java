package com.cool.core.mcp.config;

import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import com.cool.core.mcp.service.TaskMcpService;

/**
 * MCP服务器配置类
 * 支持SSE异步模式和ToolCallbackProvider
 */
@Component
public class McpServerConfig {

    @Bean
    public ToolCallbackProvider taskTools(TaskMcpService taskMcpService) {
        return MethodToolCallbackProvider.builder().toolObjects(taskMcpService).build();
    }
}
