package com.cool.core.mcp.service;

import java.util.List;
import java.util.Map;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cool.core.request.R;
import com.cool.modules.sop.entity.SOPIndustryEntity;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.entity.SOPStepEntity;
import com.cool.modules.sop.service.SOPIndustryService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.sop.service.SOPStepService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.json.JSONObject;

/**
 * SOP相关的MCP工具服务
 * 提供SOP场景、步骤、行业管理的MCP工具
 */
@Service
public class SOPMcpService {

    @Autowired
    private SOPScenarioService sopScenarioService;

    @Autowired
    private SOPStepService sopStepService;

    @Autowired
    private SOPIndustryService sopIndustryService;

    /**
     * 获取SOP场景列表
     */
    @Tool(name = "sop_scenario_list", description = "获取SOP场景列表，支持分页和多条件筛选")
    public R sopScenarioList(
        Integer page,
        Integer size,
        String keyword,
        String industryCode,
        String phaseCode,
        Integer status
    ) {
        try {
            JSONObject params = new JSONObject();
            if (keyword != null) params.put("keyword", keyword);
            if (industryCode != null) params.put("industryCode", industryCode);
            if (phaseCode != null) params.put("phaseCode", phaseCode);
            if (status != null) params.put("status", status);

            Page<SOPScenarioEntity> pageInfo = new Page<>(
                page != null ? page : 1, 
                size != null ? size : 20
            );
            
            QueryWrapper queryWrapper = QueryWrapper.create();
            Object result = sopScenarioService.page(params, pageInfo, queryWrapper);
            
            return R.ok(result);
        } catch (Exception e) {
            return R.error("获取SOP场景列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取SOP场景详情
     */
    @Tool(name = "sop_scenario_get", description = "根据ID获取SOP场景详细信息")
    public R sopScenarioGet(Long id) {
        try {
            if (id == null) {
                return R.error("场景ID不能为空");
            }

            SOPScenarioEntity scenario = sopScenarioService.getById(id);
            if (scenario == null) {
                return R.error("SOP场景不存在");
            }

            return R.ok(scenario);
        } catch (Exception e) {
            return R.error("获取SOP场景详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建SOP场景
     */
    @Tool(name = "sop_scenario_create", description = "创建新的SOP场景")
    public R sopScenarioCreate(
        String name,
        String description,
        String industryCode,
        String phaseCode,
        String executionCycle,
        Integer totalSteps
    ) {
        try {
            if (name == null || name.trim().isEmpty()) {
                return R.error("场景名称不能为空");
            }

            SOPScenarioEntity scenario = new SOPScenarioEntity();
            scenario.setName(name);
            scenario.setDescription(description);
            scenario.setIndustryCode(industryCode);
            scenario.setPhaseCode(phaseCode);
            scenario.setExecutionCycle(executionCycle);
            scenario.setTotalSteps(totalSteps != null ? totalSteps : 0);

            sopScenarioService.save(scenario);
            return R.ok(scenario);
        } catch (Exception e) {
            return R.error("创建SOP场景失败: " + e.getMessage());
        }
    }

    /**
     * 更新SOP场景
     */
    @Tool(name = "sop_scenario_update", description = "更新SOP场景信息")
    public R sopScenarioUpdate(
        Long id,
        String name,
        String description,
        String industryCode,
        String phaseCode,
        String executionCycle,
        Integer totalSteps
    ) {
        try {
            if (id == null) {
                return R.error("场景ID不能为空");
            }

            SOPScenarioEntity scenario = sopScenarioService.getById(id);
            if (scenario == null) {
                return R.error("SOP场景不存在");
            }

            if (name != null) scenario.setName(name);
            if (description != null) scenario.setDescription(description);
            if (industryCode != null) scenario.setIndustryCode(industryCode);
            if (phaseCode != null) scenario.setPhaseCode(phaseCode);
            if (executionCycle != null) scenario.setExecutionCycle(executionCycle);
            if (totalSteps != null) scenario.setTotalSteps(totalSteps);

            sopScenarioService.updateById(scenario);
            return R.ok(scenario);
        } catch (Exception e) {
            return R.error("更新SOP场景失败: " + e.getMessage());
        }
    }

    /**
     * 获取SOP场景统计
     */
    @Tool(name = "sop_scenario_stats", description = "获取SOP场景统计信息")
    public R sopScenarioStats(String industryCode) {
        try {
            Map<String, Object> stats = sopScenarioService.getScenarioStats(industryCode);
            return R.ok(stats);
        } catch (Exception e) {
            return R.error("获取SOP场景统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取SOP步骤列表
     */
    @Tool(name = "sop_step_list", description = "获取SOP步骤列表")
    public R sopStepList(
        Integer page,
        Integer size,
        Long sopId,
        String keyword
    ) {
        try {
            JSONObject params = new JSONObject();
            if (sopId != null) params.put("sopId", sopId);
            if (keyword != null) params.put("keyword", keyword);

            Page<SOPStepEntity> pageInfo = new Page<>(
                page != null ? page : 1, 
                size != null ? size : 20
            );
            
            QueryWrapper queryWrapper = QueryWrapper.create();
            if (sopId != null) {
                queryWrapper.eq("sop_id", sopId);
            }
            queryWrapper.orderBy("step_order", true);
            
            Object result = sopStepService.page(params, pageInfo, queryWrapper);
            
            return R.ok(result);
        } catch (Exception e) {
            return R.error("获取SOP步骤列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取SOP步骤详情
     */
    @Tool(name = "sop_step_get", description = "根据ID获取SOP步骤详细信息")
    public R sopStepGet(Long id) {
        try {
            if (id == null) {
                return R.error("步骤ID不能为空");
            }

            SOPStepEntity step = sopStepService.getById(id);
            if (step == null) {
                return R.error("SOP步骤不存在");
            }

            return R.ok(step);
        } catch (Exception e) {
            return R.error("获取SOP步骤详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取SOP行业列表
     */
    @Tool(name = "sop_industry_list", description = "获取SOP行业列表")
    public R sopIndustryList(Boolean activeOnly) {
        try {
            List<SOPIndustryEntity> industries;
            if (activeOnly != null && activeOnly) {
                industries = sopIndustryService.getActiveIndustries();
            } else {
                industries = sopIndustryService.list();
            }
            
            return R.ok(industries);
        } catch (Exception e) {
            return R.error("获取SOP行业列表失败: " + e.getMessage());
        }
    }
}
