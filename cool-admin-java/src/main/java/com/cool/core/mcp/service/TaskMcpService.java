package com.cool.core.mcp.service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cool.core.request.R;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.json.JSONObject;

/**
 * 任务相关的MCP工具服务
 * 基于参考文章实现的任务管理工具
 */
@Service
public class TaskMcpService {

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private TaskPackageService taskPackageService;

    /**
     * 获取任务列表
     */
    @Tool(name = "task_list", description = "获取任务列表，支持分页和多条件筛选")
    public R taskList(
        Integer page,
        Integer size,
        String keyword,
        Integer taskStatus,
        String taskCategory,
        Integer priority,
        Long scenarioId
    ) {
        try {
            JSONObject params = new JSONObject();
            if (keyword != null) params.put("keyword", keyword);
            if (taskStatus != null) params.put("taskStatus", taskStatus);
            if (taskCategory != null) params.put("taskCategory", taskCategory);
            if (priority != null) params.put("priority", priority);
            if (scenarioId != null) params.put("scenarioId", scenarioId);

            Page<TaskInfoEntity> pageInfo = new Page<>(
                page != null ? page : 1, 
                size != null ? size : 20
            );
            
            QueryWrapper queryWrapper = QueryWrapper.create();
            Object result = taskInfoService.page(params, pageInfo, queryWrapper);
            
            return R.ok(result);
        } catch (Exception e) {
            return R.error("获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    @Tool(name = "task_get", description = "根据ID获取任务详细信息")
    public R taskGet(Long id) {
        try {
            if (id == null) {
                return R.error("任务ID不能为空");
            }

            TaskInfoEntity task = taskInfoService.getById(id);
            if (task == null) {
                return R.error("任务不存在");
            }

            return R.ok(task);
        } catch (Exception e) {
            return R.error("获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建任务
     */
    @Tool(name = "task_create", description = "创建新任务，支持设置优先级、类别和场景关联")
    public R taskCreate(
        String name,
        String description,
        Integer priority,
        String taskCategory,
        Long scenarioId
    ) {
        try {
            if (name == null || name.trim().isEmpty()) {
                return R.error("任务名称不能为空");
            }

            TaskInfoEntity task = new TaskInfoEntity();
            task.setName(name);
            task.setDescription(description);
            task.setPriority(priority != null ? priority : 1);
            task.setTaskCategory(taskCategory != null ? taskCategory : "AD_HOC");
            task.setScenarioId(scenarioId);
            task.setTaskStatus(0); // 待分配

            taskInfoService.save(task);
            return R.ok(task);
        } catch (Exception e) {
            return R.error("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务
     */
    @Tool(name = "task_update", description = "更新任务信息，包括状态、优先级、描述等")
    public R taskUpdate(
        Long id,
        String name,
        String description,
        Integer priority,
        Integer taskStatus,
        String taskCategory
    ) {
        try {
            if (id == null) {
                return R.error("任务ID不能为空");
            }

            TaskInfoEntity task = taskInfoService.getById(id);
            if (task == null) {
                return R.error("任务不存在");
            }

            if (name != null) task.setName(name);
            if (description != null) task.setDescription(description);
            if (priority != null) task.setPriority(priority);
            if (taskStatus != null) task.setTaskStatus(taskStatus);
            if (taskCategory != null) task.setTaskCategory(taskCategory);

            taskInfoService.updateById(task);
            return R.ok(task);
        } catch (Exception e) {
            return R.error("更新任务失败: " + e.getMessage());
        }
    }

    /**
     * 分配任务
     */
    @Tool(name = "task_assign", description = "分配任务给指定用户")
    public R taskAssign(Long taskId, Long assigneeId, String assignReason) {
        try {
            if (taskId == null) {
                return R.error("任务ID不能为空");
            }

            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return R.error("任务不存在");
            }

            if (task.getTaskStatus() != 0) {
                return R.error("只有待分配状态的任务才能分配");
            }

            // 更新任务状态和分配信息
            task.setAssigneeId(assigneeId);
            task.setTaskStatus(1); // 待执行

            taskInfoService.updateById(task);
            return R.ok(task);
        } catch (Exception e) {
            return R.error("分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 开始执行任务
     */
    @Tool(name = "task_start", description = "开始执行任务")
    public R taskStart(Long taskId, String startNote) {
        try {
            if (taskId == null) {
                return R.error("任务ID不能为空");
            }

            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return R.error("任务不存在");
            }

            if (task.getTaskStatus() != 1) {
                return R.error("只有待执行状态的任务才能开始");
            }

            // 更新任务状态
            task.setTaskStatus(2); // 执行中
            task.setStartTime(new Date());

            taskInfoService.updateById(task);
            return R.ok(task);
        } catch (Exception e) {
            return R.error("开始任务失败: " + e.getMessage());
        }
    }

    /**
     * 完成任务
     */
    @Tool(name = "task_complete", description = "完成任务")
    public R taskComplete(Long taskId, String completeNote) {
        try {
            if (taskId == null) {
                return R.error("任务ID不能为空");
            }

            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return R.error("任务不存在");
            }

            if (task.getTaskStatus() != 2) {
                return R.error("只有执行中的任务才能完成");
            }

            // 更新任务状态
            task.setTaskStatus(3); // 已完成
            task.setCompletionTime(new Date());

            taskInfoService.updateById(task);
            return R.ok(task);
        } catch (Exception e) {
            return R.error("完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务包列表
     */
    @Tool(name = "task_package_list", description = "获取任务包列表")
    public R taskPackageList(Integer page, Integer size, String keyword) {
        try {
            JSONObject params = new JSONObject();
            if (keyword != null) params.put("keyword", keyword);

            Page<TaskPackageEntity> pageInfo = new Page<>(
                page != null ? page : 1, 
                size != null ? size : 20
            );
            
            QueryWrapper queryWrapper = QueryWrapper.create();
            Object result = taskPackageService.page(params, pageInfo, queryWrapper);
            
            return R.ok(result);
        } catch (Exception e) {
            return R.error("获取任务包列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务包详情
     */
    @Tool(name = "task_package_get", description = "获取任务包详情")

    public R taskPackageGet(@ToolParam(required=true,description="任务包ID")Long id) {
        try {
            if (id == null) {
                return R.error("任务包ID不能为空");
            }

            TaskPackageEntity taskPackage = taskPackageService.getById(id);
            if (taskPackage == null) {
                return R.error("任务包不存在");
            }

            return R.ok(taskPackage);
        } catch (Exception e) {
            return R.error("获取任务包详情失败: " + e.getMessage());
        }
    }

    /**
     * 任务统计
     */
    @Tool(name = "task_stats", description = "获取任务统计信息")
    public R taskStats() {
        try {
            // 模拟统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("total", 100);
            stats.put("pending", 20);
            stats.put("assigned", 30);
            stats.put("inProgress", 25);
            stats.put("completed", 25);
            stats.put("completionRate", 25.0);

            return R.ok(stats);
        } catch (Exception e) {
            return R.error("获取任务统计失败: " + e.getMessage());
        }
    }
}
