package com.cool.core.security;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import com.cool.core.cache.CoolCache;
import com.cool.core.enums.UserTypeEnum;
import com.cool.core.security.jwt.JwtTokenUtil;
import com.cool.core.security.jwt.JwtUser;
import com.cool.core.util.PathUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import com.cool.modules.base.entity.sys.BaseSysApiKeyEntity;
import com.cool.modules.base.service.sys.BaseSysApiKeyService;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;

/**
 * Token过滤器
 */
@Order(1)
@Component
@RequiredArgsConstructor
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    final private JwtTokenUtil jwtTokenUtil;
    final private CoolCache coolCache;
    final private IgnoredUrlsProperties ignoredUrlsProperties;
    final private BaseSysApiKeyService apiKeyService;
    final private BaseSysUserService userService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain chain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        if (PathUtils.isMatch(ignoredUrlsProperties.getAdminAuthUrls(), requestURI)) {
            chain.doFilter(request, response);
            return;
        }
        String authToken = request.getHeader("Authorization");
        if (StrUtil.isEmpty(authToken)) {
            authToken = request.getParameter("Authorization");
        }
        boolean authenticated = false;
        if (!StrUtil.isEmpty(authToken)) {
            String token = authToken.replace("Bearer ", "").trim();
            if (token.startsWith("CAK_")) {
                // APIKEY认证流程
                BaseSysApiKeyEntity apiKeyEntity = apiKeyService.findByApiKey(token);
                if (apiKeyEntity != null && apiKeyEntity.getStatus() != null && apiKeyEntity.getStatus() == 1) {
                    if (apiKeyEntity.getExpireTime() == null
                            || apiKeyEntity.getExpireTime().after(new java.util.Date())) {
                        // 加载用户信息
                        BaseSysUserEntity user = userService.getById(apiKeyEntity.getUserId());
                        if (user != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                            JwtUser jwtUser = new JwtUser(user.getId(), null, user.getStatus() == 1);
                            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                    jwtUser, token, null // 可根据需要加载权限
                            );
                            authentication.setDetails(
                                    new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            request.setAttribute("apiKeyUserId", user.getId());
                            request.setAttribute("apiKeyUsername", user.getUsername());
                            authenticated = true;
                        }
                    }
                }
            } else if (isJwtFormat(token)) {
                // JWT认证流程
                JWT jwt = jwtTokenUtil.getTokenInfo(token);
                Object userType = jwt.getPayload("userType");
                if (Objects.equals(userType, UserTypeEnum.APP.name())) {
                    handlerAppRequest(request, jwt, token);
                } else {
                    handlerAdminRequest(request, jwt, token);
                }
                authenticated = true;
            } else {
                // 非法token，直接拒绝
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("{\"code\":401,\"message\":\"无效的Token\"}");
                return;
            }
        }
        chain.doFilter(request, response);
    }

    private boolean isJwtFormat(String token) {
        return token != null && token.split("\\.").length == 3;
    }

    /**
     * 处理app请求
     */
    private void handlerAppRequest(HttpServletRequest request, JWT jwt, String authToken) {
        String userId = jwt.getPayload("userId").toString();
        if (ObjectUtil.isNotEmpty(userId)
                && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserDetails userDetails = coolCache.get("app:userDetails:" + userId,
                    JwtUser.class);
            if (jwtTokenUtil.validateToken(authToken) && userDetails != null) {
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        userDetails, authToken, userDetails.getAuthorities());
                authentication.setDetails(
                        new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
                request.setAttribute("userId", jwt.getPayload("userId"));
                request.setAttribute("tokenInfo", jwt);
            }
        }
    }

    /**
     * 处理后台请求
     */
    private void handlerAdminRequest(HttpServletRequest request, JWT jwt, String authToken) {
        String username = jwt.getPayload("username").toString();
        if (username != null
                && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserDetails userDetails = coolCache.get("admin:userDetails:" + username,
                    JwtUser.class);
            Integer passwordV = Convert.toInt(jwt.getPayload("passwordVersion"));
            Integer rv = coolCache.get("admin:passwordVersion:" + jwt.getPayload("userId"),
                    Integer.class);
            if (jwtTokenUtil.validateToken(authToken, username) && Objects.equals(passwordV, rv)
                    && userDetails != null) {
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        userDetails, authToken, userDetails.getAuthorities());
                authentication.setDetails(
                        new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
                request.setAttribute("adminUsername", jwt.getPayload("username"));
                request.setAttribute("adminUserId", jwt.getPayload("userId"));
                request.setAttribute("tokenInfo", jwt);
            }
        }
    }
}
