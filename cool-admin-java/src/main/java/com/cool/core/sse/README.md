# Cool Admin SSE 实时通信组件

## 概述

本组件提供了完整的 Server-Sent Events (SSE) 实时通信解决方案，支持与前端全局 SSE 侧边栏组件无缝对接。

## 功能特性

- 🚀 **高性能**: 基于 Spring Boot SseEmitter 实现，支持大量并发连接
- 🔧 **易配置**: 丰富的配置选项，支持自定义超时、心跳、连接数等参数
- 🛡️ **安全可靠**: 完善的异常处理和连接管理机制
- 📊 **实时监控**: 提供连接状态监控和统计信息
- 🎯 **类型安全**: 完整的消息类型定义和状态管理
- 🔄 **自动重连**: 支持客户端自动重连机制
- 🌐 **跨域支持**: 内置 CORS 配置，支持跨域访问
- 📝 **详细日志**: 完善的日志记录，便于调试和监控

## 组件结构

```
com.cool.core.sse/
├── SseManager.java              # SSE 管理器（核心组件）
├── SseController.java           # SSE 控制器
├── SseMessage.java              # SSE 消息实体
├── SseMessageType.java          # 消息类型枚举
├── SseMessageStatus.java        # 消息状态枚举
├── SseConfig.java               # SSE 配置类
├── SseUtils.java                # SSE 工具类
├── SseException.java            # SSE 异常类
├── SseEventListener.java        # SSE 事件监听器接口
├── SseExampleService.java       # 使用示例服务
├── SseAutoConfiguration.java    # 自动配置类
└── README.md                    # 说明文档
```

## 快速开始

### 1. 配置文件

在 `application.yml` 中添加 SSE 配置：

```yaml
cool:
  sse:
    enabled: true                    # 是否启用 SSE
    timeout: 1800000                 # 连接超时时间（30分钟）
    heartbeat-interval: 30000        # 心跳间隔（30秒）
    max-connections: 1000            # 最大连接数
    enable-heartbeat: true           # 是否启用心跳
    enable-connection-log: true      # 是否启用连接日志
    enable-message-log: false        # 是否启用消息日志
    max-queue-size: 100             # 消息队列最大长度
    enable-compression: false        # 是否启用消息压缩
    retry-count: 3                  # 重试次数
    retry-interval: 1000            # 重试间隔（毫秒）
    enable-cors: true               # 是否启用跨域
    cors-origins: "*"               # 允许的跨域来源
    event-prefix: "sse"             # 事件名称前缀
    enable-auto-cleanup: true       # 是否启用自动清理
    cleanup-interval: 300000        # 清理间隔（5分钟）
    core-pool-size: 5               # 核心线程数
    max-pool-size: 20               # 最大线程数
    queue-capacity: 100             # 队列容量
    keep-alive-seconds: 60          # 线程空闲时间
    enable-example: true            # 是否启用示例服务
```

### 2. 基本使用

#### 在 Service 中注入 SseManager

```java
@Service
@RequiredArgsConstructor
public class YourService {
    
    private final SseManager sseManager;
    
    public void sendNotification(String userId, String message) {
        String connectionId = "user_" + userId;
        
        // 发送信息消息
        sseManager.sendMessage(connectionId, SseMessage.info(message));
        
        // 发送成功消息
        sseManager.sendMessage(connectionId, SseMessage.success("操作成功"));
        
        // 发送进度消息
        sseManager.sendMessage(connectionId, SseMessage.progress("处理中...", 50));
        
        // 发送错误消息
        sseManager.sendMessage(connectionId, SseMessage.error("操作失败"));
    }
    
    public void broadcastSystemMessage(String message) {
        // 广播消息到所有连接
        sseManager.broadcast(SseMessage.info(message));
    }
}
```

#### 在 Controller 中使用

```java
@RestController
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
public class TaskController {
    
    private final SseManager sseManager;
    private final TaskService taskService;
    
    @PostMapping("/process")
    public R<String> processTask(@RequestBody TaskRequest request) {
        String connectionId = SseUtils.generateConnectionId("task");
        
        // 异步处理任务
        taskService.processTaskAsync(connectionId, request);
        
        return R.ok(connectionId); // 返回连接ID给前端
    }
}
```

### 3. 前端连接

前端可以通过以下方式连接 SSE：

```javascript
// 连接 SSE
const connectionId = 'user_123'; // 或从后端获取
const eventSource = new EventSource(`/admin/sse/connect/${connectionId}`);

// 监听消息
eventSource.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};

// 监听特定类型的消息
eventSource.addEventListener('progress', function(event) {
    const message = JSON.parse(event.data);
    console.log('进度更新:', message);
});

// 错误处理
eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
};
```

## API 接口

### SSE 连接接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/admin/sse/connect/{connectionId}` | GET | 创建 SSE 连接 |
| `/admin/sse/send/{connectionId}` | POST | 发送消息到指定连接 |
| `/admin/sse/broadcast` | POST | 广播消息到所有连接 |
| `/admin/sse/disconnect/{connectionId}` | DELETE | 关闭指定连接 |
| `/admin/sse/status` | GET | 获取连接状态 |
| `/admin/sse/check/{connectionId}` | GET | 检查连接是否存在 |

### 消息格式

```json
{
  "id": "msg_123",
  "content": "消息内容",
  "type": "info",
  "status": "success",
  "progress": 75,
  "timestamp": "2025-01-21 10:30:00",
  "data": {
    "key": "value"
  }
}
```

### 消息类型

- `message`: 普通消息
- `info`: 信息消息
- `success`: 成功消息
- `warning`: 警告消息
- `error`: 错误消息
- `progress`: 进度消息
- `status`: 状态消息
- `processing`: 处理中消息
- `connection`: 连接消息
- `system`: 系统消息
- `task`: 任务消息
- `ai`: AI消息
- `user`: 用户消息
- `notification`: 通知消息

### 消息状态

- `pending`: 等待中
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 已失败
- `cancelled`: 已取消
- `paused`: 已暂停
- `running`: 运行中
- `stopped`: 已停止
- `connected`: 已连接
- `disconnected`: 已断开
- `reconnecting`: 重连中
- `timeout`: 超时
- `error`: 错误
- `success`: 成功
- `warning`: 警告
- `info`: 信息

## 高级用法

### 1. 自定义事件监听器

```java
@Component
public class CustomSseEventListener implements SseEventListener {
    
    @Override
    public void onConnectionCreated(String connectionId, SseEmitter emitter) {
        // 连接创建时的自定义逻辑
        log.info("新连接创建: {}", connectionId);
    }
    
    @Override
    public void onConnectionClosed(String connectionId, String reason) {
        // 连接关闭时的自定义逻辑
        log.info("连接关闭: {}, 原因: {}", connectionId, reason);
    }
    
    @Override
    public boolean onBeforeMessageSent(String connectionId, SseMessage message) {
        // 消息发送前的拦截逻辑
        if (message.getType() == SseMessageType.ERROR) {
            // 可以在这里记录错误日志或发送告警
            log.error("发送错误消息: {}", message.getContent());
        }
        return true; // 返回 false 将阻止消息发送
    }
}
```

### 2. 长时间任务处理

```java
@Service
@RequiredArgsConstructor
public class LongRunningTaskService {
    
    private final SseManager sseManager;
    
    @Async
    public CompletableFuture<Void> processLongTask(String connectionId, TaskRequest request) {
        try {
            // 发送开始消息
            sseManager.sendMessage(connectionId, 
                SseMessage.status("任务开始", SseMessageStatus.RUNNING));
            
            int totalSteps = 10;
            for (int i = 1; i <= totalSteps; i++) {
                // 模拟处理
                Thread.sleep(1000);
                
                // 发送进度
                int progress = i * 100 / totalSteps;
                sseManager.sendMessage(connectionId, 
                    SseMessage.progress("步骤 " + i, progress));
            }
            
            // 发送完成消息
            sseManager.sendMessage(connectionId, 
                SseMessage.status("任务完成", SseMessageStatus.COMPLETED));
            
        } catch (Exception e) {
            sseManager.sendMessage(connectionId, 
                SseMessage.error("任务失败: " + e.getMessage()));
        }
        
        return CompletableFuture.completedFuture(null);
    }
}
```

### 3. 批量消息处理

```java
@Service
@RequiredArgsConstructor
public class BatchMessageService {
    
    private final SseManager sseManager;
    
    public void sendBatchNotifications(List<String> userIds, String message) {
        userIds.parallelStream().forEach(userId -> {
            String connectionId = "user_" + userId;
            if (sseManager.hasConnection(connectionId)) {
                sseManager.sendMessage(connectionId, SseMessage.info(message));
            }
        });
    }
    
    public void sendDepartmentNotification(String departmentId, String message) {
        // 假设连接ID包含部门信息
        sseManager.getConnectionIds().stream()
            .filter(id -> id.contains("dept_" + departmentId))
            .forEach(connectionId -> {
                sseManager.sendMessage(connectionId, SseMessage.info(message));
            });
    }
}
```

## 性能优化

### 1. 连接池配置

```yaml
cool:
  sse:
    core-pool-size: 10      # 根据并发需求调整
    max-pool-size: 50       # 根据系统资源调整
    queue-capacity: 200     # 根据消息量调整
```

### 2. 消息压缩

```yaml
cool:
  sse:
    enable-compression: true  # 启用消息压缩
    max-queue-size: 50       # 减少队列大小
```

### 3. 连接管理

```yaml
cool:
  sse:
    timeout: 900000          # 15分钟超时
    enable-auto-cleanup: true # 启用自动清理
    cleanup-interval: 180000  # 3分钟清理一次
```

## 监控和调试

### 1. 连接状态监控

```java
@RestController
@RequestMapping("/admin/monitor")
@RequiredArgsConstructor
public class SseMonitorController {
    
    private final SseManager sseManager;
    
    @GetMapping("/sse/stats")
    public R<Map<String, Object>> getSseStats() {
        return R.ok(Map.of(
            "connectionCount", sseManager.getConnectionCount(),
            "maxConnections", sseManager.getMaxConnections(),
            "connectionIds", sseManager.getConnectionIds()
        ));
    }
}
```

### 2. 日志配置

```yaml
logging:
  level:
    com.cool.core.sse: DEBUG  # 启用 SSE 调试日志
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查 `timeout` 配置
   - 确认客户端心跳机制
   - 检查网络连接稳定性

2. **消息发送失败**
   - 检查连接是否存在
   - 确认消息格式正确
   - 查看错误日志

3. **连接数过多**
   - 调整 `max-connections` 配置
   - 启用自动清理机制
   - 优化连接管理策略

4. **跨域问题**
   - 检查 `cors-origins` 配置
   - 确认前端请求头设置
   - 验证 CORS 过滤器配置

### 调试技巧

1. 启用详细日志
2. 使用浏览器开发者工具监控 SSE 连接
3. 通过 `/admin/sse/status` 接口检查连接状态
4. 使用 `/admin/sse/check/{connectionId}` 验证特定连接

## 最佳实践

1. **连接ID设计**: 使用有意义的连接ID，如 `user_{userId}` 或 `session_{sessionId}`
2. **消息类型**: 合理使用不同的消息类型，便于前端处理
3. **错误处理**: 完善的异常处理和错误消息推送
4. **资源管理**: 及时关闭不需要的连接，避免资源泄露
5. **安全考虑**: 验证连接权限，防止未授权访问
6. **性能监控**: 定期监控连接数和消息处理性能

## 与前端集成

本后端组件完全兼容前端的全局 SSE 侧边栏组件，支持：

- 自动连接管理
- 消息类型映射
- 状态同步
- 错误处理
- 重连机制

前端只需要配置正确的 SSE 端点即可实现完整的实时通信功能。