package com.cool.core.sse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * SSE自动配置类
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Slf4j
@Configuration
@EnableAsync
@EnableScheduling
@EnableConfigurationProperties(SseConfig.class)
@ConditionalOnProperty(prefix = "cool.sse", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SseAutoConfiguration {
    
    /**
     * SSE管理器Bean
     * 
     * @param sseConfig SSE配置
     * @return SSE管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public SseManager sseManager(SseConfig sseConfig) {
        log.info("初始化SSE管理器，配置: {}", sseConfig);
        return new SseManager();
    }
    
    /**
     * SSE控制器Bean
     * 
     * @param sseManager SSE管理器
     * @return SSE控制器
     */
    @Bean
    @ConditionalOnMissingBean
    public SseController sseController(SseManager sseManager) {
        log.info("初始化SSE控制器");
        // SseController使用@RequiredArgsConstructor，Spring会自动注入
        return new SseController(sseManager);
    }
    
    /**
     * SSE示例服务Bean
     * 
     * @param sseManager SSE管理器
     * @return SSE示例服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "cool.sse", name = "enable-example", havingValue = "true", matchIfMissing = false)
    public SseExampleService sseExampleService(SseManager sseManager) {
        log.info("初始化SSE示例服务");
        // SseExampleService使用@RequiredArgsConstructor，Spring会自动注入
        return new SseExampleService(sseManager);
    }
    
    /**
     * SSE异步任务执行器
     * 
     * @param sseConfig SSE配置
     * @return 任务执行器
     */
    @Bean("sseTaskExecutor")
    @ConditionalOnMissingBean(name = "sseTaskExecutor")
    public Executor sseTaskExecutor(SseConfig sseConfig) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(sseConfig.getCorePoolSize());
        executor.setMaxPoolSize(sseConfig.getMaxPoolSize());
        executor.setQueueCapacity(sseConfig.getQueueCapacity());
        executor.setKeepAliveSeconds(sseConfig.getKeepAliveSeconds());
        executor.setThreadNamePrefix("sse-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        
        log.info("初始化SSE任务执行器: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                sseConfig.getCorePoolSize(), sseConfig.getMaxPoolSize(), sseConfig.getQueueCapacity());
        
        return executor;
    }
    
    /**
     * SSE跨域过滤器
     * 
     * @param sseConfig SSE配置
     * @return 跨域过滤器
     */
    @Bean("sseCorsFilter")
    @ConditionalOnMissingBean(name = "sseCorsFilter")
    @ConditionalOnProperty(prefix = "cool.sse", name = "enable-cors", havingValue = "true", matchIfMissing = true)
    public CorsFilter sseCorsFilter(SseConfig sseConfig) {
        CorsConfiguration config = new CorsConfiguration();
        
        // 设置允许的来源
        if ("*".equals(sseConfig.getCorsOrigins())) {
            config.addAllowedOriginPattern("*");
        } else {
            String[] origins = sseConfig.getCorsOrigins().split(",");
            for (String origin : origins) {
                config.addAllowedOrigin(origin.trim());
            }
        }
        
        // 设置允许的方法
        config.addAllowedMethod("GET");
        config.addAllowedMethod("POST");
        config.addAllowedMethod("DELETE");
        config.addAllowedMethod("OPTIONS");
        
        // 设置允许的头部
        config.addAllowedHeader("*");
        
        // 允许携带凭证
        config.setAllowCredentials(true);
        
        // 设置预检请求的缓存时间
        config.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/admin/sse/**", config);
        
        log.info("初始化SSE跨域过滤器，允许来源: {}", sseConfig.getCorsOrigins());
        
        return new CorsFilter(source);
    }
    
    /**
     * 默认SSE事件监听器
     * 
     * @return 默认事件监听器
     */
    @Bean
    @ConditionalOnMissingBean
    public SseEventListener defaultSseEventListener() {
        return new SseEventListener() {
            @Override
            public void onConnectionCreated(String connectionId, org.springframework.web.servlet.mvc.method.annotation.SseEmitter emitter) {
                log.info("SSE连接已创建: {}", connectionId);
            }
            
            @Override
            public void onConnectionClosed(String connectionId, String reason) {
                log.info("SSE连接已关闭: {}, 原因: {}", connectionId, reason);
            }
            
            @Override
            public void onConnectionError(String connectionId, Throwable error) {
                log.error("SSE连接出错: {}", connectionId, error);
            }
            
            @Override
            public void onConnectionTimeout(String connectionId) {
                log.warn("SSE连接超时: {}", connectionId);
            }
            
            @Override
            public void onConnectionCountChanged(int currentCount, int maxCount) {
                if (currentCount > maxCount * 0.8) {
                    log.warn("SSE连接数量接近上限: {}/{}", currentCount, maxCount);
                }
            }
        };
    }
}