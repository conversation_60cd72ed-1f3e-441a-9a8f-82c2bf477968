package com.cool.core.sse;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SSE配置类
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Data
@Component
@ConfigurationProperties(prefix = "cool.sse")
public class SseConfig {
    
    /**
     * SSE连接超时时间（毫秒），默认30分钟
     */
    private Long timeout = 30 * 60 * 1000L;
    
    /**
     * 心跳间隔时间（毫秒），默认30秒
     */
    private Long heartbeatInterval = 30 * 1000L;
    
    /**
     * 最大连接数，默认1000
     */
    private Integer maxConnections = 1000;
    
    /**
     * 是否启用心跳检测，默认true
     */
    private Boolean enableHeartbeat = true;
    
    /**
     * 是否启用连接日志，默认true
     */
    private Boolean enableConnectionLog = true;
    
    /**
     * 是否启用消息日志，默认false
     */
    private Boolean enableMessageLog = false;
    
    /**
     * 消息队列最大长度，默认100
     */
    private Integer maxQueueSize = 100;
    
    /**
     * 是否启用消息压缩，默认false
     */
    private Boolean enableCompression = false;
    
    /**
     * 重试次数，默认3次
     */
    private Integer retryCount = 3;
    
    /**
     * 重试间隔时间（毫秒），默认1秒
     */
    private Long retryInterval = 1000L;
    
    /**
     * 是否启用跨域，默认true
     */
    private Boolean enableCors = true;
    
    /**
     * 允许的跨域来源，默认为*
     */
    private String corsOrigins = "*";
    
    /**
     * SSE事件名称前缀，默认为"sse"
     */
    private String eventPrefix = "sse";
    
    /**
     * 是否启用自动清理过期连接，默认true
     */
    private Boolean enableAutoCleanup = true;
    
    /**
     * 自动清理间隔时间（毫秒），默认5分钟
     */
    private Long cleanupInterval = 5 * 60 * 1000L;
    
    /**
     * 连接池核心线程数，默认5
     */
    private Integer corePoolSize = 5;
    
    /**
     * 连接池最大线程数，默认20
     */
    private Integer maxPoolSize = 20;
    
    /**
     * 连接池队列容量，默认100
     */
    private Integer queueCapacity = 100;
    
    /**
     * 线程空闲时间（秒），默认60秒
     */
    private Integer keepAliveSeconds = 60;
}