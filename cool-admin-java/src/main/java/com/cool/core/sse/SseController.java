package com.cool.core.sse;

import java.util.Map;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.cool.core.request.R;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * SSE控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Slf4j
@RestController
@RequestMapping("/admin/sse")
@RequiredArgsConstructor
@Tag(name = "SSE实时通信", description = "Server-Sent Events实时通信接口")
public class SseController{
    
    private final SseManager sseManager;
    
    /**
     * 创建SSE连接
     * 
     * @param connectionId 连接ID
     * @param request HTTP请求
     * @return SseEmitter
     */
    @GetMapping(value = "/connect/{connectionId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "创建SSE连接", description = "创建一个新的SSE连接")
    public SseEmitter connect(
            @Parameter(description = "连接ID", required = true)
            @PathVariable String connectionId,
            HttpServletRequest request) {
        
        try {
            String clientIp = getClientIp(request);
            String userAgent = request.getHeader("User-Agent");
            
            log.info("创建SSE连接: connectionId={}, clientIp={}, userAgent={}", 
                    connectionId, clientIp, userAgent);
            
            SseEmitter emitter = sseManager.createConnection(connectionId, null);
            
            // 发送连接成功消息
            sseManager.sendMessage(connectionId, SseMessage.connection("连接成功"));
            
            return emitter;
            
        } catch (Exception e) {
            log.error("创建SSE连接失败: connectionId={}", connectionId, e);
            throw new RuntimeException("创建SSE连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送消息到指定连接
     * 
     * @param connectionId 连接ID
     * @param message 消息内容
     * @return 响应结果
     */
    @PostMapping("/send/{connectionId}")
    @Operation(summary = "发送消息", description = "向指定连接发送消息")
    public R<Void> sendMessage(
            @Parameter(description = "连接ID", required = true)
            @PathVariable String connectionId,
            @Parameter(description = "消息内容", required = true)
            @RequestBody SseMessage message) {
        
        try {
            boolean success = sseManager.sendMessage(connectionId, message);
            if (success) {
                return R.ok();
            } else {
                return R.error("发送消息失败，连接不存在或已断开");
            }
        } catch (Exception e) {
            log.error("发送消息失败: connectionId={}, message={}", connectionId, message, e);
            return R.error("发送消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 广播消息到所有连接
     * 
     * @param message 消息内容
     * @return 响应结果
     */
    @PostMapping("/broadcast")
    @Operation(summary = "广播消息", description = "向所有连接广播消息")
    public R<Map<String, Object>> broadcast(
            @Parameter(description = "消息内容", required = true)
            @RequestBody SseMessage message) {
        
        try {
            int successCount = sseManager.broadcast(message);
            int totalConnections = sseManager.getConnectionCount();
            
            Map<String, Object> result = Map.of(
                    "successCount", successCount,
                    "totalConnections", totalConnections,
                    "failedCount", totalConnections - successCount
            );
            
            return R.ok(result);
        } catch (Exception e) {
            log.error("广播消息失败: message={}", message, e);
            return R.error("广播消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 关闭指定连接
     * 
     * @param connectionId 连接ID
     * @return 响应结果
     */
    @DeleteMapping("/disconnect/{connectionId}")
    @Operation(summary = "关闭连接", description = "关闭指定的SSE连接")
    public R<Void> disconnect(
            @Parameter(description = "连接ID", required = true)
            @PathVariable String connectionId) {
        
        try {
            boolean success = sseManager.closeConnection(connectionId);
            if (success) {
                return R.ok();
            } else {
                return R.error("关闭连接失败，连接不存在");
            }
        } catch (Exception e) {
            log.error("关闭连接失败: connectionId={}", connectionId, e);
            return R.error("关闭连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取连接状态
     * 
     * @return 连接状态信息
     */
    @GetMapping("/status")
    @Operation(summary = "获取连接状态", description = "获取当前所有SSE连接的状态信息")
    public R<Map<String, Object>> getStatus() {
        
        try {
            int connectionCount = sseManager.getConnectionCount();
            
            Map<String, Object> status = Map.of(
                    "connectionCount", connectionCount,
                    "maxConnections", sseManager.getMaxConnections(),
                    "timestamp", System.currentTimeMillis()
            );
            
            return R.ok(status);
        } catch (Exception e) {
            log.error("获取连接状态失败", e);
            return R.error("获取连接状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查连接是否存在
     * 
     * @param connectionId 连接ID
     * @return 检查结果
     */
    @GetMapping("/check/{connectionId}")
    @Operation(summary = "检查连接", description = "检查指定连接是否存在")
    public R<Map<String, Object>> checkConnection(
            @Parameter(description = "连接ID", required = true)
            @PathVariable String connectionId) {
        
        try {
            boolean exists = sseManager.hasConnection(connectionId);
            
            Map<String, Object> result = Map.of(
                    "connectionId", connectionId,
                    "exists", exists,
                    "timestamp", System.currentTimeMillis()
            );
            
            return R.ok(result);
        } catch (Exception e) {
            log.error("检查连接失败: connectionId={}", connectionId, e);
            return R.error("检查连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

}