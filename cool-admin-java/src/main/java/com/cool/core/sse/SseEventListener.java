package com.cool.core.sse;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SSE事件监听器接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
public interface SseEventListener {
    
    /**
     * 连接创建时触发
     * 
     * @param connectionId 连接ID
     * @param emitter SSE发射器
     */
    default void onConnectionCreated(String connectionId, SseEmitter emitter) {
        // 默认空实现
    }
    
    /**
     * 连接关闭时触发
     * 
     * @param connectionId 连接ID
     * @param reason 关闭原因
     */
    default void onConnectionClosed(String connectionId, String reason) {
        // 默认空实现
    }
    
    /**
     * 连接出错时触发
     * 
     * @param connectionId 连接ID
     * @param error 错误信息
     */
    default void onConnectionError(String connectionId, Throwable error) {
        // 默认空实现
    }
    
    /**
     * 消息发送前触发
     * 
     * @param connectionId 连接ID
     * @param message 消息内容
     * @return 是否继续发送消息，返回false将阻止消息发送
     */
    default boolean onBeforeMessageSent(String connectionId, SseMessage message) {
        return true; // 默认允许发送
    }
    
    /**
     * 消息发送后触发
     * 
     * @param connectionId 连接ID
     * @param message 消息内容
     * @param success 是否发送成功
     */
    default void onAfterMessageSent(String connectionId, SseMessage message, boolean success) {
        // 默认空实现
    }
    
    /**
     * 心跳检测时触发
     * 
     * @param connectionId 连接ID
     * @param isAlive 连接是否存活
     */
    default void onHeartbeat(String connectionId, boolean isAlive) {
        // 默认空实现
    }
    
    /**
     * 连接超时时触发
     * 
     * @param connectionId 连接ID
     */
    default void onConnectionTimeout(String connectionId) {
        // 默认空实现
    }
    
    /**
     * 连接数量变化时触发
     * 
     * @param currentCount 当前连接数
     * @param maxCount 最大连接数
     */
    default void onConnectionCountChanged(int currentCount, int maxCount) {
        // 默认空实现
    }
}