package com.cool.core.sse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * SSE使用示例服务
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SseExampleService {
    
    private final SseManager sseManager;
    
    /**
     * 模拟长时间任务处理
     * 
     * @param connectionId 连接ID
     * @param taskName 任务名称
     * @param totalSteps 总步骤数
     */
    @Async
    public CompletableFuture<Void> processLongRunningTask(String connectionId, String taskName, int totalSteps) {
        try {
            // 发送任务开始消息
            sseManager.sendMessage(connectionId, 
                SseMessage.status("任务开始: " + taskName, SseMessageStatus.RUNNING));
            
            for (int i = 1; i <= totalSteps; i++) {
                // 模拟处理时间
                Thread.sleep(1000);
                
                // 发送进度消息
                SseMessage progressMessage = SseUtils.createProgressMessage(taskName, i, totalSteps);
                sseManager.sendMessage(connectionId, progressMessage);
                
                // 每5步发送一个状态更新
                if (i % 5 == 0) {
                    sseManager.sendMessage(connectionId, 
                        SseMessage.info(String.format("已完成 %d/%d 步骤", i, totalSteps)));
                }
            }
            
            // 发送任务完成消息
            sseManager.sendMessage(connectionId, 
                SseMessage.status("任务完成: " + taskName, SseMessageStatus.COMPLETED));
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            sseManager.sendMessage(connectionId, 
                SseMessage.status("任务被中断: " + taskName, SseMessageStatus.CANCELLED));
        } catch (Exception e) {
            log.error("任务处理失败: {}", taskName, e);
            sseManager.sendMessage(connectionId, 
                SseMessage.error("任务失败: " + e.getMessage()));
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 模拟AI任务生成
     * 
     * @param connectionId 连接ID
     * @param prompt 提示词
     */
    @Async
    public CompletableFuture<Void> generateAiTask(String connectionId, String prompt) {
        try {
            // 发送开始消息
            sseManager.sendMessage(connectionId, 
                SseMessage.processing("开始生成AI任务..."));
            
            // 模拟AI处理阶段
            String[] stages = {
                "分析提示词",
                "生成任务结构",
                "优化任务内容",
                "验证任务可行性",
                "生成最终结果"
            };
            
            for (int i = 0; i < stages.length; i++) {
                Thread.sleep(2000); // 模拟处理时间
                
                int progress = (i + 1) * 100 / stages.length;
                sseManager.sendMessage(connectionId, 
                    SseMessage.progress(stages[i], progress));
            }
            
            // 发送成功结果
            Map<String, Object> result = Map.of(
                "taskId", "task_" + System.currentTimeMillis(),
                "title", "基于AI生成的任务",
                "description", "根据提示词 '" + prompt + "' 生成的任务",
                "priority", "高",
                "estimatedTime", "2小时"
            );
            
            sseManager.sendMessage(connectionId, 
                SseUtils.createMessageWithData("AI任务生成完成", SseMessageType.SUCCESS, result));
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            sseManager.sendMessage(connectionId, 
                SseMessage.error("AI任务生成被中断"));
        } catch (Exception e) {
            log.error("AI任务生成失败: {}", prompt, e);
            sseManager.sendMessage(connectionId, 
                SseMessage.error("AI任务生成失败: " + e.getMessage()));
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 模拟数据导入任务
     * 
     * @param connectionId 连接ID
     * @param fileName 文件名
     * @param totalRecords 总记录数
     */
    @Async
    public CompletableFuture<Void> importData(String connectionId, String fileName, int totalRecords) {
        try {
            sseManager.sendMessage(connectionId, 
                SseMessage.info("开始导入数据文件: " + fileName));
            
            int batchSize = 100;
            int processedRecords = 0;
            int successCount = 0;
            int errorCount = 0;
            
            while (processedRecords < totalRecords) {
                int currentBatch = Math.min(batchSize, totalRecords - processedRecords);
                
                // 模拟批量处理
                Thread.sleep(500);
                
                // 模拟一些记录处理失败
                int batchErrors = (int) (Math.random() * 5);
                int batchSuccess = currentBatch - batchErrors;
                
                processedRecords += currentBatch;
                successCount += batchSuccess;
                errorCount += batchErrors;
                
                // 发送进度更新
                int progress = processedRecords * 100 / totalRecords;
                String progressText = String.format(
                    "已处理 %d/%d 条记录 (成功: %d, 失败: %d)", 
                    processedRecords, totalRecords, successCount, errorCount
                );
                
                sseManager.sendMessage(connectionId, 
                    SseMessage.progress(progressText, progress));
                
                // 如果有错误，发送警告
                if (batchErrors > 0) {
                    sseManager.sendMessage(connectionId, 
                        SseMessage.warning(String.format("批次处理中有 %d 条记录失败", batchErrors)));
                }
            }
            
            // 发送最终结果
            Map<String, Object> summary = Map.of(
                "fileName", fileName,
                "totalRecords", totalRecords,
                "successCount", successCount,
                "errorCount", errorCount,
                "successRate", String.format("%.2f%%", (double) successCount / totalRecords * 100)
            );
            
            if (errorCount == 0) {
                sseManager.sendMessage(connectionId, 
                    SseUtils.createMessageWithData("数据导入完成，全部成功", SseMessageType.SUCCESS, summary));
            } else {
                sseManager.sendMessage(connectionId, 
                    SseUtils.createMessageWithData("数据导入完成，部分失败", SseMessageType.WARNING, summary));
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            sseManager.sendMessage(connectionId, 
                SseMessage.error("数据导入被中断"));
        } catch (Exception e) {
            log.error("数据导入失败: {}", fileName, e);
            sseManager.sendMessage(connectionId, 
                SseMessage.error("数据导入失败: " + e.getMessage()));
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 发送系统通知
     * 
     * @param message 通知消息
     */
    public void sendSystemNotification(String message) {
        SseMessage notification = SseMessage.builder()
                .content(message)
                .type(SseMessageType.NOTIFICATION)
                .status(SseMessageStatus.INFO)
                .build();
        
        int sentCount = sseManager.broadcast(notification);
        log.info("系统通知已发送给 {} 个连接: {}", sentCount, message);
    }
    
    /**
     * 发送用户特定消息
     * 
     * @param userId 用户ID
     * @param message 消息内容
     * @param type 消息类型
     */
    public void sendUserMessage(String userId, String message, SseMessageType type) {
        String connectionId = "user_" + userId;
        
        if (sseManager.hasConnection(connectionId)) {
            SseMessage userMessage = SseMessage.builder()
                    .content(message)
                    .type(type)
                    .build();
            
            boolean sent = sseManager.sendMessage(connectionId, userMessage);
            if (sent) {
                log.info("用户消息已发送: userId={}, message={}", userId, message);
            } else {
                log.warn("用户消息发送失败: userId={}, message={}", userId, message);
            }
        } else {
            log.warn("用户连接不存在: userId={}", userId);
        }
    }
    
    /**
     * 模拟定时任务状态推送
     */
    public void pushScheduledTaskStatus() {
        if (sseManager.getConnectionCount() > 0) {
            SseMessage statusMessage = SseMessage.builder()
                    .content("定时任务执行状态更新")
                    .type(SseMessageType.SYSTEM)
                    .data(Map.of(
                        "timestamp", System.currentTimeMillis(),
                        "activeConnections", sseManager.getConnectionCount(),
                        "systemStatus", "正常"
                    ))
                    .build();
            
            sseManager.broadcast(statusMessage);
        }
    }
}