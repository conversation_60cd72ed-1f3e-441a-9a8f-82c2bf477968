package com.cool.core.sse;

/**
 * SSE异常类
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
public class SseException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 连接ID
     */
    private String connectionId;
    
    public SseException(String message) {
        super(message);
    }
    
    public SseException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public SseException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public SseException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public SseException(String errorCode, String connectionId, String message) {
        super(message);
        this.errorCode = errorCode;
        this.connectionId = connectionId;
    }
    
    public SseException(String errorCode, String connectionId, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.connectionId = connectionId;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getConnectionId() {
        return connectionId;
    }
    
    /**
     * 连接不存在异常
     */
    public static class ConnectionNotFoundException extends SseException {
        public ConnectionNotFoundException(String connectionId) {
            super("SSE_CONNECTION_NOT_FOUND", connectionId, "SSE连接不存在: " + connectionId);
        }
    }
    
    /**
     * 连接已存在异常
     */
    public static class ConnectionAlreadyExistsException extends SseException {
        public ConnectionAlreadyExistsException(String connectionId) {
            super("SSE_CONNECTION_ALREADY_EXISTS", connectionId, "SSE连接已存在: " + connectionId);
        }
    }
    
    /**
     * 连接超时异常
     */
    public static class ConnectionTimeoutException extends SseException {
        public ConnectionTimeoutException(String connectionId) {
            super("SSE_CONNECTION_TIMEOUT", connectionId, "SSE连接超时: " + connectionId);
        }
        
        public ConnectionTimeoutException(String connectionId, long timeout) {
            super("SSE_CONNECTION_TIMEOUT", connectionId, 
                  String.format("SSE连接超时: %s (超时时间: %dms)", connectionId, timeout));
        }
    }
    
    /**
     * 连接数量超限异常
     */
    public static class TooManyConnectionsException extends SseException {
        public TooManyConnectionsException(int current, int max) {
            super("SSE_TOO_MANY_CONNECTIONS", 
                  String.format("SSE连接数量超限: 当前%d个，最大%d个", current, max));
        }
    }
    
    /**
     * 消息发送失败异常
     */
    public static class MessageSendFailedException extends SseException {
        public MessageSendFailedException(String connectionId, String message) {
            super("SSE_MESSAGE_SEND_FAILED", connectionId, "消息发送失败: " + message);
        }
        
        public MessageSendFailedException(String connectionId, String message, Throwable cause) {
            super("SSE_MESSAGE_SEND_FAILED", connectionId, "消息发送失败: " + message, cause);
        }
    }
    
    /**
     * 无效连接ID异常
     */
    public static class InvalidConnectionIdException extends SseException {
        public InvalidConnectionIdException(String connectionId) {
            super("SSE_INVALID_CONNECTION_ID", connectionId, "无效的连接ID: " + connectionId);
        }
    }
    
    /**
     * 连接已关闭异常
     */
    public static class ConnectionClosedException extends SseException {
        public ConnectionClosedException(String connectionId) {
            super("SSE_CONNECTION_CLOSED", connectionId, "SSE连接已关闭: " + connectionId);
        }
    }
    
    /**
     * 消息序列化异常
     */
    public static class MessageSerializationException extends SseException {
        public MessageSerializationException(String message, Throwable cause) {
            super("SSE_MESSAGE_SERIALIZATION_FAILED", "消息序列化失败: " + message, cause);
        }
    }
    
    /**
     * 配置错误异常
     */
    public static class ConfigurationException extends SseException {
        public ConfigurationException(String message) {
            super("SSE_CONFIGURATION_ERROR", "SSE配置错误: " + message);
        }
        
        public ConfigurationException(String message, Throwable cause) {
            super("SSE_CONFIGURATION_ERROR", "SSE配置错误: " + message, cause);
        }
    }
    
    /**
     * 服务不可用异常
     */
    public static class ServiceUnavailableException extends SseException {
        public ServiceUnavailableException(String message) {
            super("SSE_SERVICE_UNAVAILABLE", "SSE服务不可用: " + message);
        }
        
        public ServiceUnavailableException(String message, Throwable cause) {
            super("SSE_SERVICE_UNAVAILABLE", "SSE服务不可用: " + message, cause);
        }
    }
}