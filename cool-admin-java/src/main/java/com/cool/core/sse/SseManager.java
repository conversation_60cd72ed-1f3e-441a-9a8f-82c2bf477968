package com.cool.core.sse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * SSE连接管理器
 * 提供全局的SSE连接管理和消息推送功能
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Slf4j
@Component
public class SseManager {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 存储所有SSE连接
     * Key: 连接标识（可以是用户ID、任务ID等）
     * Value: 该标识下的所有SSE连接
     */
    private final Map<String, List<SseEmitter>> connectionMap = new ConcurrentHashMap<>();
    
    /**
     * 默认SSE连接超时时间（1小时）
     */
    private static final long DEFAULT_TIMEOUT = 3600_000L;
    
    /**
     * 创建SSE连接
     * 
     * @param connectionId 连接标识
     * @param timeout 超时时间（毫秒），为null时使用默认值
     * @return SseEmitter实例
     */
    public SseEmitter createConnection(String connectionId, Long timeout) {
        if (timeout == null) {
            timeout = DEFAULT_TIMEOUT;
        }
        
        SseEmitter emitter = new SseEmitter(timeout);
        
        // 添加到连接映射
        connectionMap.computeIfAbsent(connectionId, k -> new CopyOnWriteArrayList<>()).add(emitter);
        
        // 设置连接事件处理
        emitter.onCompletion(() -> removeConnection(connectionId, emitter));
        emitter.onTimeout(() -> removeConnection(connectionId, emitter));
        emitter.onError(e -> {
            log.warn("SSE连接发生错误，连接ID: {}, 错误: {}", connectionId, e.getMessage());
            removeConnection(connectionId, emitter);
        });
        
        log.info("创建SSE连接，连接ID: {}, 当前连接数: {}", connectionId, getConnectionCount(connectionId));
        
        // 发送连接成功消息
        sendWelcomeMessage(connectionId, emitter);
        
        return emitter;
    }
    
    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage(String connectionId, SseEmitter emitter) {
        try {
            SseMessage welcomeMessage = SseMessage.builder()
                    .type(SseMessageType.CONNECTION)
                    .content("连接已建立")
                    .timestamp(LocalDateTime.now())
                    .build();
            
            emitter.send(SseEmitter.event()
                    .name("message")
                    .data(objectMapper.writeValueAsString(welcomeMessage)));
        } catch (Exception e) {
            log.warn("发送欢迎消息失败，连接ID: {}, 错误: {}", connectionId, e.getMessage());
        }
    }
    
    /**
     * 向指定连接发送消息
     * 
     * @param connectionId 连接标识
     * @param message SSE消息
     * @return 是否发送成功
     */
    public boolean sendMessage(String connectionId, SseMessage message) {
        List<SseEmitter> emitters = connectionMap.get(connectionId);
        if (emitters == null || emitters.isEmpty()) {
            log.debug("没有找到连接ID为 {} 的SSE连接", connectionId);
            return false;
        }
        
        String json;
        try {
            json = objectMapper.writeValueAsString(message);
        } catch (JsonProcessingException e) {
            log.error("序列化SSE消息失败: {}", e.getMessage());
            return false;
        }
        
        boolean success = false;
        Iterator<SseEmitter> iterator = emitters.iterator();
        while (iterator.hasNext()) {
            SseEmitter emitter = iterator.next();
            try {
                emitter.send(SseEmitter.event().name("message").data(json));
                success = true;
            } catch (IOException e) {
                log.debug("向连接ID {} 发送消息失败，移除该连接: {}", connectionId, e.getMessage());
                iterator.remove();
                emitter.complete();
            }
        }
        return success;
    }
    
    /**
     * 向指定连接发送简单文本消息
     * 
     * @param connectionId 连接标识
     * @param content 消息内容
     * @param type 消息类型
     */
    public void sendMessage(String connectionId, String content, SseMessageType type) {
        SseMessage message = SseMessage.builder()
                .content(content)
                .type(type)
                .timestamp(LocalDateTime.now())
                .build();
        sendMessage(connectionId, message);
    }
    
    /**
     * 向指定连接发送进度消息
     * 
     * @param connectionId 连接标识
     * @param content 消息内容
     * @param progress 进度百分比（0-100）
     */
    public void sendProgressMessage(String connectionId, String content, Integer progress) {
        SseMessage message = SseMessage.builder()
                .content(content)
                .type(SseMessageType.PROGRESS)
                .progress(progress)
                .timestamp(LocalDateTime.now())
                .build();
        sendMessage(connectionId, message);
    }
    
    /**
     * 向指定连接发送状态消息
     * 
     * @param connectionId 连接标识
     * @param content 消息内容
     * @param status 状态
     */
    public void sendStatusMessage(String connectionId, String content, SseMessageStatus status) {
        SseMessage message = SseMessage.builder()
                .content(content)
                .type(SseMessageType.STATUS)
                .status(status)
                .timestamp(LocalDateTime.now())
                .build();
        sendMessage(connectionId, message);
    }
    
    /**
     * 向指定连接发送错误消息
     * 
     * @param connectionId 连接标识
     * @param errorMessage 错误信息
     */
    public void sendErrorMessage(String connectionId, String errorMessage) {
        sendMessage(connectionId, errorMessage, SseMessageType.ERROR);
    }
    
    /**
     * 向指定连接发送成功消息
     * 
     * @param connectionId 连接标识
     * @param successMessage 成功信息
     */
    public void sendSuccessMessage(String connectionId, String successMessage) {
        sendMessage(connectionId, successMessage, SseMessageType.SUCCESS);
    }
    
    /**
     * 向指定连接发送信息消息
     * 
     * @param connectionId 连接标识
     * @param infoMessage 信息内容
     */
    public void sendInfoMessage(String connectionId, String infoMessage) {
        sendMessage(connectionId, infoMessage, SseMessageType.INFO);
    }
    
    /**
     * 向指定连接发送警告消息
     * 
     * @param connectionId 连接标识
     * @param warningMessage 警告信息
     */
    public void sendWarningMessage(String connectionId, String warningMessage) {
        sendMessage(connectionId, warningMessage, SseMessageType.WARNING);
    }
    
    /**
     * 关闭指定连接的所有SSE
     * 
     * @param connectionId 连接标识
     * @param closeMessage 关闭消息
     * @return 是否关闭成功
     */
    public boolean closeConnection(String connectionId, String closeMessage) {
        List<SseEmitter> emitters = connectionMap.get(connectionId);
        if (emitters != null) {
            emitters.forEach(emitter -> {
                try {
                    if (closeMessage != null) {
                        emitter.send(SseEmitter.event().name("close").data(closeMessage));
                    }
                } catch (IOException e) {
                    log.warn("发送关闭消息失败，连接ID: {}, 错误: {}", connectionId, e.getMessage());
                } finally {
                    emitter.complete();
                }
            });
            connectionMap.remove(connectionId);
            log.info("关闭SSE连接，连接ID: {}", connectionId);
            return true;
        }
        return false;
    }
    
    /**
     * 关闭指定连接的所有SSE（无关闭消息）
     * 
     * @param connectionId 连接标识
     * @return 是否关闭成功
     */
    public boolean closeConnection(String connectionId) {
        return closeConnection(connectionId, "连接已关闭");
    }
    
    /**
     * 移除单个SSE连接
     * 
     * @param connectionId 连接标识
     * @param emitter SSE连接实例
     */
    private void removeConnection(String connectionId, SseEmitter emitter) {
        List<SseEmitter> emitters = connectionMap.get(connectionId);
        if (emitters != null) {
            emitters.remove(emitter);
            if (emitters.isEmpty()) {
                connectionMap.remove(connectionId);
            }
            log.debug("移除SSE连接，连接ID: {}, 剩余连接数: {}", connectionId, emitters.size());
        }
    }
    
    /**
     * 获取指定连接的连接数
     * 
     * @param connectionId 连接标识
     * @return 连接数
     */
    public int getConnectionCount(String connectionId) {
        List<SseEmitter> emitters = connectionMap.get(connectionId);
        return emitters != null ? emitters.size() : 0;
    }
    
    /**
     * 获取所有连接的总数
     * 
     * @return 总连接数
     */
    public int getTotalConnectionCount() {
        return connectionMap.values().stream()
                .mapToInt(List::size)
                .sum();
    }
    
    /**
     * 获取所有连接的总数（无参数版本）
     * 
     * @return 总连接数
     */
    public int getConnectionCount() {
        return getTotalConnectionCount();
    }
    
    /**
     * 广播消息给所有连接
     * 
     * @param message SSE消息
     * @return 成功发送的连接数
     */
    public int broadcast(SseMessage message) {
        int successCount = 0;
        for (String connectionId : connectionMap.keySet()) {
            if (sendMessage(connectionId, message)) {
                successCount++;
            }
        }
        return successCount;
    }
    
    /**
     * 获取最大连接数配置
     * 
     * @return 最大连接数
     */
    public int getMaxConnections() {
        return 1000; // 默认最大连接数
    }
    
    /**
     * 获取所有连接标识
     * 
     * @return 连接标识集合
     */
    public Set<String> getAllConnectionIds() {
        return new HashSet<>(connectionMap.keySet());
    }
    
    /**
     * 检查指定连接是否存在
     * 
     * @param connectionId 连接标识
     * @return 是否存在连接
     */
    public boolean hasConnection(String connectionId) {
        List<SseEmitter> emitters = connectionMap.get(connectionId);
        return emitters != null && !emitters.isEmpty();
    }
    
    /**
     * 广播消息给所有连接
     * 
     * @param message SSE消息
     */
    public void broadcastMessage(SseMessage message) {
        connectionMap.keySet().forEach(connectionId -> sendMessage(connectionId, message));
    }
    
    /**
     * 广播简单消息给所有连接
     * 
     * @param content 消息内容
     * @param type 消息类型
     */
    public void broadcastMessage(String content, SseMessageType type) {
        SseMessage message = SseMessage.builder()
                .content(content)
                .type(type)
                .timestamp(LocalDateTime.now())
                .build();
        broadcastMessage(message);
    }
    
    /**
     * 清理所有连接
     */
    public void clearAllConnections() {
        connectionMap.forEach((connectionId, emitters) -> {
            emitters.forEach(SseEmitter::complete);
        });
        connectionMap.clear();
        log.info("清理所有SSE连接");
    }
}