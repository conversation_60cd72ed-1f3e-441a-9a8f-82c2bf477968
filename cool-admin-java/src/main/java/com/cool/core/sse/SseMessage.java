package com.cool.core.sse;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * SSE消息实体
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SseMessage {
    
    /**
     * 消息ID（可选）
     */
    private String id;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息类型
     */
    private SseMessageType type;
    
    /**
     * 消息状态（可选）
     */
    private SseMessageStatus status;
    
    /**
     * 进度百分比（0-100，可选）
     */
    private Integer progress;
    
    /**
     * 消息时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * 扩展数据（可选）
     */
    private Map<String, Object> data;
    
    /**
     * 创建信息类型消息
     * 
     * @param content 消息内容
     * @return SSE消息
     */
    public static SseMessage info(String content) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.INFO)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建成功类型消息
     * 
     * @param content 消息内容
     * @return SSE消息
     */
    public static SseMessage success(String content) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.SUCCESS)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建警告类型消息
     * 
     * @param content 消息内容
     * @return SSE消息
     */
    public static SseMessage warning(String content) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.WARNING)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建错误类型消息
     * 
     * @param content 消息内容
     * @return SSE消息
     */
    public static SseMessage error(String content) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.ERROR)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建进度类型消息
     * 
     * @param content 消息内容
     * @param progress 进度百分比
     * @return SSE消息
     */
    public static SseMessage progress(String content, Integer progress) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.PROGRESS)
                .progress(progress)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建状态类型消息
     * 
     * @param content 消息内容
     * @param status 状态
     * @return SSE消息
     */
    public static SseMessage status(String content, SseMessageStatus status) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.STATUS)
                .status(status)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建处理中类型消息
     * 
     * @param content 消息内容
     * @return SSE消息
     */
    public static SseMessage processing(String content) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.PROCESSING)
                .status(SseMessageStatus.PROCESSING)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建连接类型消息
     * 
     * @param content 消息内容
     * @return SSE消息
     */
    public static SseMessage connection(String content) {
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.CONNECTION)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建带扩展数据的消息
     * 
     * @param content 消息内容
     * @param type 消息类型
     * @param data 扩展数据
     * @return SSE消息
     */
    public static SseMessage withData(String content, SseMessageType type, Map<String, Object> data) {
        return SseMessage.builder()
                .content(content)
                .type(type)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
}