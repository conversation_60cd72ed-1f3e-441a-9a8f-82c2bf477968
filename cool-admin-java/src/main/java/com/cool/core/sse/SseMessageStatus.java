package com.cool.core.sse;

/**
 * SSE消息状态枚举
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
public enum SseMessageStatus {
    
    /**
     * 等待中
     */
    PENDING("pending", "等待中"),
    
    /**
     * 处理中
     */
    PROCESSING("processing", "处理中"),
    
    /**
     * 已完成
     */
    COMPLETED("completed", "已完成"),
    
    /**
     * 已失败
     */
    FAILED("failed", "已失败"),
    
    /**
     * 已取消
     */
    CANCELLED("cancelled", "已取消"),
    
    /**
     * 已暂停
     */
    PAUSED("paused", "已暂停"),
    
    /**
     * 运行中
     */
    RUNNING("running", "运行中"),
    
    /**
     * 已停止
     */
    STOPPED("stopped", "已停止"),
    
    /**
     * 已连接
     */
    CONNECTED("connected", "已连接"),
    
    /**
     * 已断开
     */
    DISCONNECTED("disconnected", "已断开"),
    
    /**
     * 重连中
     */
    RECONNECTING("reconnecting", "重连中"),
    
    /**
     * 超时
     */
    TIMEOUT("timeout", "超时"),
    
    /**
     * 错误
     */
    ERROR("error", "错误"),
    
    /**
     * 成功
     */
    SUCCESS("success", "成功"),
    
    /**
     * 警告
     */
    WARNING("warning", "警告"),
    
    /**
     * 信息
     */
    INFO("info", "信息");
    
    private final String code;
    private final String description;
    
    SseMessageStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取消息状态
     * 
     * @param code 代码
     * @return 消息状态
     */
    public static SseMessageStatus fromCode(String code) {
        for (SseMessageStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return INFO; // 默认返回信息状态
    }
    
    /**
     * 判断是否为终态（已完成、已失败、已取消、已停止）
     * 
     * @return 是否为终态
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == CANCELLED || this == STOPPED;
    }
    
    /**
     * 判断是否为进行中状态（处理中、运行中、重连中）
     * 
     * @return 是否为进行中状态
     */
    public boolean isInProgress() {
        return this == PROCESSING || this == RUNNING || this == RECONNECTING;
    }
    
    /**
     * 判断是否为错误状态（已失败、错误、超时）
     * 
     * @return 是否为错误状态
     */
    public boolean isError() {
        return this == FAILED || this == ERROR || this == TIMEOUT;
    }
    
    @Override
    public String toString() {
        return code;
    }
}