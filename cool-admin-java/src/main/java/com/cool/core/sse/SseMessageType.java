package com.cool.core.sse;

/**
 * SSE消息类型枚举
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
public enum SseMessageType {
    
    /**
     * 普通消息
     */
    MESSAGE("message", "普通消息"),
    
    /**
     * 信息消息
     */
    INFO("info", "信息消息"),
    
    /**
     * 成功消息
     */
    SUCCESS("success", "成功消息"),
    
    /**
     * 警告消息
     */
    WARNING("warning", "警告消息"),
    
    /**
     * 错误消息
     */
    ERROR("error", "错误消息"),
    
    /**
     * 进度消息
     */
    PROGRESS("progress", "进度消息"),
    
    /**
     * 状态消息
     */
    STATUS("status", "状态消息"),
    
    /**
     * 处理中消息
     */
    PROCESSING("processing", "处理中消息"),
    
    /**
     * 连接消息
     */
    CONNECTION("connection", "连接消息"),
    
    /**
     * 系统消息
     */
    SYSTEM("system", "系统消息"),
    
    /**
     * 任务消息
     */
    TASK("task", "任务消息"),
    
    /**
     * AI消息
     */
    AI("ai", "AI消息"),
    
    /**
     * 用户消息
     */
    USER("user", "用户消息"),
    
    /**
     * 通知消息
     */
    NOTIFICATION("notification", "通知消息");
    
    private final String code;
    private final String description;
    
    SseMessageType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取消息类型
     * 
     * @param code 代码
     * @return 消息类型
     */
    public static SseMessageType fromCode(String code) {
        for (SseMessageType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return MESSAGE; // 默认返回普通消息类型
    }
    
    @Override
    public String toString() {
        return code;
    }
}