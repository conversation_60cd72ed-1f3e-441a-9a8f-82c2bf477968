package com.cool.core.sse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE工具类
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Slf4j
public class SseUtils {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 生成唯一的连接ID
     * 
     * @return 连接ID
     */
    public static String generateConnectionId() {
        return "sse_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成带前缀的连接ID
     * 
     * @param prefix 前缀
     * @return 连接ID
     */
    public static String generateConnectionId(String prefix) {
        return prefix + "_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成基于用户ID的连接ID
     * 
     * @param userId 用户ID
     * @return 连接ID
     */
    public static String generateUserConnectionId(String userId) {
        return "user_" + userId + "_" + System.currentTimeMillis();
    }
    
    /**
     * 生成基于会话ID的连接ID
     * 
     * @param sessionId 会话ID
     * @return 连接ID
     */
    public static String generateSessionConnectionId(String sessionId) {
        return "session_" + sessionId + "_" + System.currentTimeMillis();
    }
    
    /**
     * 将消息对象转换为JSON字符串
     * 
     * @param message 消息对象
     * @return JSON字符串
     */
    public static String toJson(SseMessage message) {
        try {
            return objectMapper.writeValueAsString(message);
        } catch (JsonProcessingException e) {
            log.error("消息序列化失败: {}", message, e);
            return "{\"error\":\"消息序列化失败\"}";
        }
    }
    
    /**
     * 将JSON字符串转换为消息对象
     * 
     * @param json JSON字符串
     * @return 消息对象
     */
    public static SseMessage fromJson(String json) {
        try {
            return objectMapper.readValue(json, SseMessage.class);
        } catch (JsonProcessingException e) {
            log.error("消息反序列化失败: {}", json, e);
            return SseMessage.error("消息反序列化失败");
        }
    }
    
    /**
     * 发送SSE消息
     * 
     * @param emitter SSE发射器
     * @param message 消息内容
     * @return 是否发送成功
     */
    public static boolean sendMessage(SseEmitter emitter, SseMessage message) {
        try {
            String jsonData = toJson(message);
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                    .data(jsonData)
                    .name(message.getType().getCode());
            
            if (message.getId() != null) {
                eventBuilder.id(message.getId());
            }
            
            emitter.send(eventBuilder);
            return true;
        } catch (IOException e) {
            log.error("发送SSE消息失败: {}", message, e);
            return false;
        }
    }
    
    /**
     * 发送心跳消息
     * 
     * @param emitter SSE发射器
     * @return 是否发送成功
     */
    public static boolean sendHeartbeat(SseEmitter emitter) {
        try {
            SseMessage heartbeat = SseMessage.builder()
                    .content("heartbeat")
                    .type(SseMessageType.SYSTEM)
                    .timestamp(LocalDateTime.now())
                    .build();
            
            return sendMessage(emitter, heartbeat);
        } catch (Exception e) {
            log.error("发送心跳消息失败", e);
            return false;
        }
    }
    
    /**
     * 发送连接关闭消息
     * 
     * @param emitter SSE发射器
     * @param reason 关闭原因
     * @return 是否发送成功
     */
    public static boolean sendCloseMessage(SseEmitter emitter, String reason) {
        try {
            SseMessage closeMessage = SseMessage.builder()
                    .content("连接关闭: " + reason)
                    .type(SseMessageType.CONNECTION)
                    .status(SseMessageStatus.DISCONNECTED)
                    .timestamp(LocalDateTime.now())
                    .build();
            
            return sendMessage(emitter, closeMessage);
        } catch (Exception e) {
            log.error("发送关闭消息失败", e);
            return false;
        }
    }
    
    /**
     * 安全地完成SSE连接
     * 
     * @param emitter SSE发射器
     * @param reason 完成原因
     */
    public static void safeComplete(SseEmitter emitter, String reason) {
        try {
            if (emitter != null) {
                sendCloseMessage(emitter, reason);
                emitter.complete();
            }
        } catch (Exception e) {
            log.error("完成SSE连接失败: {}", reason, e);
        }
    }
    
    /**
     * 安全地完成SSE连接并发送错误
     * 
     * @param emitter SSE发射器
     * @param error 错误信息
     */
    public static void safeCompleteWithError(SseEmitter emitter, Throwable error) {
        try {
            if (emitter != null) {
                SseMessage errorMessage = SseMessage.error("连接错误: " + error.getMessage());
                sendMessage(emitter, errorMessage);
                emitter.completeWithError(error);
            }
        } catch (Exception e) {
            log.error("完成SSE连接并发送错误失败", e);
        }
    }
    
    /**
     * 创建带扩展数据的消息
     * 
     * @param content 消息内容
     * @param type 消息类型
     * @param extraData 扩展数据
     * @return SSE消息
     */
    public static SseMessage createMessageWithData(String content, SseMessageType type, Object extraData) {
        Map<String, Object> data = new ConcurrentHashMap<>();
        if (extraData != null) {
            if (extraData instanceof Map) {
                data.putAll((Map<String, Object>) extraData);
            } else {
                data.put("data", extraData);
            }
        }
        
        return SseMessage.builder()
                .content(content)
                .type(type)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建进度消息
     * 
     * @param taskName 任务名称
     * @param current 当前进度
     * @param total 总进度
     * @return SSE消息
     */
    public static SseMessage createProgressMessage(String taskName, int current, int total) {
        int percentage = total > 0 ? (current * 100 / total) : 0;
        String content = String.format("%s: %d/%d (%d%%)", taskName, current, total, percentage);
        
        Map<String, Object> data = Map.of(
                "taskName", taskName,
                "current", current,
                "total", total,
                "percentage", percentage
        );
        
        return SseMessage.builder()
                .content(content)
                .type(SseMessageType.PROGRESS)
                .progress(percentage)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 验证连接ID格式
     * 
     * @param connectionId 连接ID
     * @return 是否有效
     */
    public static boolean isValidConnectionId(String connectionId) {
        return connectionId != null && 
               !connectionId.trim().isEmpty() && 
               connectionId.length() <= 100 && 
               connectionId.matches("^[a-zA-Z0-9_-]+$");
    }
    
    /**
     * 清理连接ID（移除无效字符）
     * 
     * @param connectionId 原始连接ID
     * @return 清理后的连接ID
     */
    public static String sanitizeConnectionId(String connectionId) {
        if (connectionId == null) {
            return generateConnectionId();
        }
        
        String sanitized = connectionId.replaceAll("[^a-zA-Z0-9_-]", "_");
        if (sanitized.isEmpty() || sanitized.length() > 100) {
            return generateConnectionId();
        }
        
        return sanitized;
    }
}