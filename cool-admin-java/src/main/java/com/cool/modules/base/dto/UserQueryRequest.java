package com.cool.modules.base.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 用户查询请求DTO
 */
@Data
@Schema(description = "用户查询请求")
public class UserQueryRequest {

    @Schema(description = "部门ID列表")
    private List<Long> departmentIds;

    @Schema(description = "项目ID列表")
    private List<Long> projectIds;

    @Schema(description = "角色ID列表")
    private List<Long> roleIds;

    @Schema(description = "角色名称列表")
    private List<String> roleNames;

    @Schema(description = "姓名模糊查询")
    private String name;

    @Schema(description = "手机号模糊查询")
    private String phone;

    @Schema(description = "用户名模糊查询")
    private String username;

    @Schema(description = "邮箱模糊查询")
    private String email;

    @Schema(description = "用户状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "是否排除admin用户", defaultValue = "true")
    private Boolean excludeAdmin = true;

    @Schema(description = "是否包含角色信息", defaultValue = "true")
    private Boolean includeRoles = true;

    @Schema(description = "是否包含部门信息", defaultValue = "true")
    private Boolean includeDepartment = true;

    @Schema(description = "是否包含项目信息", defaultValue = "true")
    private Boolean includeProject = true;

    @Schema(description = "排序字段")
    private String orderBy;

    @Schema(description = "排序方向：asc-升序，desc-降序", defaultValue = "asc")
    private String orderDirection = "asc";

    @Schema(description = "统一关键字模糊查询（姓名 / 用户名 / 手机号）")
    private String keyword;
} 