package com.cool.modules.base.entity.sys;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;

/**
 * 用户技能实体
 */
@Getter
@Setter
@Table(value = "base_sys_user_skill", comment = "用户技能表")
@Schema(description = "用户技能")
public class BaseSysUserSkillEntity extends BaseEntity<BaseSysUserSkillEntity> {

    @Index
    @ColumnDefine(comment = "用户ID", type = "bigint", notNull = true)
    @Schema(description = "用户ID")
    private Long userId;

    @Index
    @ColumnDefine(comment = "技能名称", length = 100, notNull = true)
    @Schema(description = "技能名称")
    private String skillName;

    @ColumnDefine(comment = "技能等级 1-5", defaultValue = "1")
    @Schema(description = "技能等级", example = "3")
    private Integer skillLevel;

    @ColumnDefine(comment = "技能描述", length = 500)
    @Schema(description = "技能描述")
    private String description;
}
