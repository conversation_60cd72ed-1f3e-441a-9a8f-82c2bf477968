package com.cool.modules.base.service.sys;

import cn.hutool.core.lang.Dict;
import com.cool.core.base.BaseService;
import com.cool.modules.base.dto.UserQueryRequest;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;

import java.util.List;

/**
 * 系统用户
 */
public interface BaseSysUserService extends BaseService<BaseSysUserEntity> {
    /**
     * 修改用户信息
     *
     * @param body 用户信息
     */
    void personUpdate(Long userId, Dict body);

    /**
     * 移动部门
     *
     * @param departmentId 部门ID
     * @param userIds      用户ID集合
     */
    void move(Long departmentId, Long[] userIds);

    /**
     * 根据条件查询用户列表（包含数据权限控制）
     * 
     * @param request 查询条件
     * @return 用户列表
     */
    List<BaseSysUserEntity> queryUsers(UserQueryRequest request);

    /**
     * 获取可用的执行人列表（包含部门和角色信息）
     * 已废弃，请使用 queryUsers 方法
     * 
     * @return 可用执行人列表
     * @deprecated 使用 queryUsers 方法替代
     */
    @Deprecated
    List<BaseSysUserEntity> getAvailableAssignees();

    /**
     * 检查用户是否为超级管理员
     * @param userId 用户ID
     * @return 是否为超级管理员
     */
    boolean isSuperAdmin(Long userId);
}
