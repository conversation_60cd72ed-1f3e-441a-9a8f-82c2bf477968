package com.cool.modules.organization.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.annotation.DataPermissionFilter;
import com.cool.modules.organization.exception.DualDimensionPermissionException;
import com.cool.modules.organization.service.DualDimensionDataPermissionService;
import com.cool.modules.organization.service.OrganizationAuditLogService;
import com.mybatisflex.core.query.QueryWrapper;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据权限AOP切面
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class DataPermissionAspect {

    private final DualDimensionDataPermissionService permissionService;
    private final OrganizationAuditLogService auditLogService;

    @Around("@annotation(dataPermissionFilter)")
    public Object applyDataPermissionFilter(ProceedingJoinPoint joinPoint,
            DataPermissionFilter dataPermissionFilter) throws Throwable {

        // 检查是否启用权限过滤
        if (!dataPermissionFilter.enable()) {
            return joinPoint.proceed();
        }

        try {
            // 获取当前用户ID和请求信息
            Long userId = CoolSecurityUtil.getCurrentUserId();
            String username = getCurrentUsername();
            HttpServletRequest request = getCurrentRequest();
            String operationType = joinPoint.getSignature().getName();

            // 检查是否忽略管理员权限
            if (!dataPermissionFilter.ignoreAdmin() && permissionService.isSystemAdmin(userId)) {
                log.debug("系统管理员跳过权限过滤: {}", dataPermissionFilter.entityType());
                // 记录管理员权限验证成功
                auditLogService.logPermissionCheck(userId, username, operationType,
                        dataPermissionFilter.entityType(), null, request, true, "系统管理员权限");
                return joinPoint.proceed();
            }

            // 查找QueryWrapper参数并应用权限过滤
            Object[] args = joinPoint.getArgs();
            boolean filterApplied = false;

            for (Object arg : args) {
                if (arg instanceof QueryWrapper) {
                    QueryWrapper queryWrapper = (QueryWrapper) arg;
                    permissionService.applyDataPermissionFilter(queryWrapper, userId,
                            dataPermissionFilter.entityType());
                    filterApplied = true;
                    log.debug("已应用数据权限过滤: entityType={}, userId={}", dataPermissionFilter.entityType(), userId);

                    // 记录权限验证成功
                    auditLogService.logPermissionCheck(userId, username, operationType,
                            dataPermissionFilter.entityType(), null, request, true, null);
                    break;
                }
            }

            if (!filterApplied) {
                String errorMsg = "未找到QueryWrapper参数，无法应用权限过滤";
                log.warn("{}: {}", errorMsg, dataPermissionFilter.entityType());

                // 记录权限验证失败
                auditLogService.logPermissionCheck(userId, username, operationType,
                        dataPermissionFilter.entityType(), null, request, false, errorMsg);

                throw new DualDimensionPermissionException.DataPermissionException(
                        errorMsg, dataPermissionFilter.entityType(), null, userId);
            }

            return joinPoint.proceed();

        } catch (DualDimensionPermissionException e) {
            // 重新抛出权限异常
            throw e;
        } catch (Exception e) {
            // 获取用户信息用于审计日志
            Long userId = CoolSecurityUtil.getCurrentUserId();
            String username = getCurrentUsername();
            HttpServletRequest request = getCurrentRequest();
            String operationType = joinPoint.getSignature().getName();

            log.error("应用数据权限过滤失败: entityType={}", dataPermissionFilter.entityType(), e);

            // 记录权限验证失败
            auditLogService.logPermissionCheck(userId, username, operationType,
                    dataPermissionFilter.entityType(), null, request, false, e.getMessage());

            // 权限过滤失败时，为了安全起见，抛出权限异常
            throw new DualDimensionPermissionException.DataPermissionException(
                    "数据权限验证失败: " + e.getMessage(), dataPermissionFilter.entityType(), null, userId);
        }
    }

    private HttpServletRequest getCurrentRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    private String getCurrentUsername() {
        return CoolSecurityUtil.getAdminUsername();
    }
}
