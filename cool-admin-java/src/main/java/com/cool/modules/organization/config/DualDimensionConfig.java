package com.cool.modules.organization.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 双维度组织架构配置
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cool.organization")
public class DualDimensionConfig {
    
    /**
     * 审计日志配置
     */
    private Audit audit = new Audit();
    
    /**
     * 安全监控配置
     */
    private Security security = new Security();
    
    /**
     * 缓存配置
     */
    private Cache cache = new Cache();
    
    @Data
    public static class Audit {
        /**
         * 是否启用审计日志
         */
        private boolean enabled = true;
        
        /**
         * 日志清理配置
         */
        private Cleanup cleanup = new Cleanup();
        
        /**
         * 日志保留天数
         */
        private int retentionDays = 90;
        
        @Data
        public static class Cleanup {
            /**
             * 是否启用自动清理
             */
            private boolean enabled = true;
            
            /**
             * 清理任务执行时间表达式
             */
            private String cron = "0 0 2 * * ?";
        }
    }
    
    @Data
    public static class Security {
        /**
         * 安全监控配置
         */
        private Monitor monitor = new Monitor();
        
        /**
         * 风险用户检测配置
         */
        private RiskUser riskUser = new RiskUser();
        
        /**
         * 用户锁定配置
         */
        private UserLock userLock = new UserLock();
        
        @Data
        public static class Monitor {
            /**
             * 是否启用安全监控
             */
            private boolean enabled = true;
            
            /**
             * 监控任务执行时间表达式
             */
            private String cron = "0 0 * * * ?";
            
            /**
             * 异常权限访问检测阈值
             */
            private int abnormalAccessThreshold = 5;
            
            /**
             * 频繁切换检测阈值
             */
            private int frequentSwitchThreshold = 10;
        }
        
        @Data
        public static class RiskUser {
            /**
             * 风险用户检测时间表达式
             */
            private String cron = "0 0 3 * * ?";
            
            /**
             * 风险用户失败次数阈值
             */
            private int failureThreshold = 10;
        }
        
        @Data
        public static class UserLock {
            /**
             * 默认锁定时长（分钟）
             */
            private int defaultDurationMinutes = 60;
            
            /**
             * 严重安全事件锁定时长（分钟）
             */
            private int criticalDurationMinutes = 60;
            
            /**
             * 高级安全事件锁定时长（分钟）
             */
            private int highDurationMinutes = 30;
        }
    }
    
    @Data
    public static class Cache {
        /**
         * 权限缓存配置
         */
        private Permission permission = new Permission();
        
        /**
         * 组织形态缓存配置
         */
        private Mode mode = new Mode();
        
        @Data
        public static class Permission {
            /**
             * 权限缓存过期时间（秒）
             */
            private int expireSeconds = 300;
            
            /**
             * 权限缓存键前缀
             */
            private String keyPrefix = "dual_dimension:permission:";
        }
        
        @Data
        public static class Mode {
            /**
             * 组织形态缓存过期时间（秒）
             */
            private int expireSeconds = 600;
            
            /**
             * 组织形态缓存键前缀
             */
            private String keyPrefix = "dual_dimension:mode:";
        }
    }
}