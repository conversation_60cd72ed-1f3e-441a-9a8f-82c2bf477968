package com.cool.modules.organization.config;

import com.cool.modules.base.entity.sys.BaseSysRoleEntity;
import com.cool.modules.base.service.sys.BaseSysRoleService;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目角色初始化器
 * 按照Cool Admin规范，只初始化角色，菜单通过JSON文件初始化
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Component
@Order(100) // 确保在其他初始化完成后执行
@RequiredArgsConstructor
public class ProjectRoleInitializer implements ApplicationRunner {
    
    private final BaseSysRoleService baseSysRoleService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(ApplicationArguments args) throws Exception {
        try {
            log.info("开始初始化项目角色...");
            
            // 创建全局项目角色
            createGlobalProjectRoles();
            
            log.info("项目角色初始化完成");
            
        } catch (Exception e) {
            log.error("项目角色初始化失败", e);
            // 不抛出异常，避免影响系统启动
        }
    }
    
    /**
     * 创建全局项目角色
     */
    private void createGlobalProjectRoles() {
        log.info("创建全局项目角色...");
        
        for (GlobalProjectRoleEnum roleEnum : GlobalProjectRoleEnum.values()) {
            createProjectRoleIfNotExists(roleEnum);
        }
    }
    
    /**
     * 创建项目角色（如果不存在）
     */
    private void createProjectRoleIfNotExists(GlobalProjectRoleEnum roleEnum) {
        BaseSysRoleEntity existingRole = baseSysRoleService.getOne(
            QueryWrapper.create().eq(BaseSysRoleEntity::getLabel, roleEnum.getCode()));
        
        if (existingRole == null) {
            BaseSysRoleEntity role = new BaseSysRoleEntity();
            role.setLabel(roleEnum.getCode());
            role.setName(roleEnum.getName());
            role.setRemark(roleEnum.getDescription());
            role.setUserId(1L); // 系统创建
            role.setRelevance(1); // 设置为系统角色
            
            baseSysRoleService.save(role);
            log.info("创建项目角色: {} - {}", roleEnum.getCode(), roleEnum.getName());
        } else {
            log.debug("项目角色已存在: {} - {}", roleEnum.getCode(), roleEnum.getName());
        }
    }
}