package com.cool.modules.organization.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.request.R;
import com.cool.modules.organization.dto.MigrationResultDTO;
import com.cool.modules.organization.service.DataMigrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 数据迁移管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "数据迁移管理", description = "双维度组织架构数据迁移")
@CoolRestController
@RequiredArgsConstructor
public class AdminDataMigrationController {

    private final DataMigrationService dataMigrationService;

    @PostMapping("/migrate-all")
    @Operation(summary = "执行完整数据迁移", description = "执行双维度组织架构的完整数据迁移")
    public R<MigrationResultDTO> migrateAllData() {
        MigrationResultDTO result = dataMigrationService.migrateAllData();

        if (result.getSuccess()) {
            return R.ok(result).put("message", "数据迁移成功完成");
        } else {
            return R.<MigrationResultDTO>error(result.getMessage()).put("data", result);
        }
    }

    @PostMapping("/migrate-users")
    @Operation(summary = "迁移用户数据", description = "迁移现有用户的部门数据到双维度架构")
    public R<Integer> migrateDepartmentUsers() {
        try {
            int migratedCount = dataMigrationService.migrateDepartmentUsers();
            return R.ok(migratedCount).put("message", "成功迁移 " + migratedCount + " 个用户的部门数据");
        } catch (Exception e) {
            return R.<Integer>error("用户数据迁移失败: " + e.getMessage());
        }
    }

    @PostMapping("/init-modes")
    @Operation(summary = "初始化用户组织形态", description = "为所有用户初始化默认的组织形态")
    public R<Integer> initializeUserModes() {
        try {
            int initializedCount = dataMigrationService.initializeUserModes();
            return R.ok(initializedCount).put("message", "成功初始化 " + initializedCount + " 个用户的组织形态");
        } catch (Exception e) {
            return R.<Integer>error("用户组织形态初始化失败: " + e.getMessage());
        }
    }

    @GetMapping("/validate")
    @Operation(summary = "验证数据迁移", description = "验证数据迁移的完整性和正确性")
    public R<MigrationResultDTO> validateMigration() {
        MigrationResultDTO result = dataMigrationService.validateMigration();

        if (result.getSuccess()) {
            return R.ok(result).put("message", "数据迁移验证完成");
        } else {
            return R.<MigrationResultDTO>error(result.getMessage()).put("data", result);
        }
    }

    @PostMapping("/rollback")
    @Operation(summary = "回滚数据迁移", description = "回滚双维度组织架构的数据迁移")
    public R<String> rollbackMigration() {
        boolean success = dataMigrationService.rollbackMigration();

        if (success) {
            return R.ok("数据迁移回滚成功");
        } else {
            return R.<String>error("数据迁移回滚失败");
        }
    }

    @GetMapping("/status")
    @Operation(summary = "获取迁移状态", description = "获取当前数据迁移的状态信息")
    public R<MigrationResultDTO> getMigrationStatus() {
        MigrationResultDTO result = dataMigrationService.getMigrationStatus();
        return R.ok(result);
    }
}