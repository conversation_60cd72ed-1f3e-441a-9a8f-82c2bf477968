package com.cool.modules.organization.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.organization.entity.OrganizationAuditLogEntity;
import com.cool.modules.organization.service.OrganizationAuditLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 组织架构审计日志管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "组织架构审计日志管理", description = "组织架构审计日志查询和统计功能")
@CoolRestController(api = { "page", "info" })
public class AdminOrganizationAuditLogController
        extends BaseController<OrganizationAuditLogService, OrganizationAuditLogEntity> {

    @Autowired
    private OrganizationAuditLogService auditLogService;

    @Override
    protected void init(HttpServletRequest request, cn.hutool.json.JSONObject requestParams) {
        // 引入TableDef以实现类型安全的查询
        com.cool.modules.organization.entity.table.OrganizationAuditLogEntityTableDef auditLog = com.cool.modules.organization.entity.table.OrganizationAuditLogEntityTableDef.ORGANIZATION_AUDIT_LOG_ENTITY;

        setListOption(
                createOp()
                        .fieldEq(auditLog.USER_ID, auditLog.OPERATION_TYPE)
                        .keyWordLikeFields(auditLog.USERNAME, auditLog.OPERATION_DESCRIPTION,
                                auditLog.TARGET_ENTITY_TYPE));

    }

    /**
     * 获取用户操作日志
     */
    @Operation(summary = "获取用户操作日志")
    @GetMapping("/user/{userId}/logs")
    public R getUserOperationLogs(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") int limit) {

        List<OrganizationAuditLogEntity> logs = auditLogService.getUserOperationLogs(userId, limit);
        return R.ok(logs);
    }

    /**
     * 获取组织形态切换统计
     */
    @Operation(summary = "获取组织形态切换统计")
    @GetMapping("/statistics/mode-switch")
    public R getModeSwitchStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days) {

        Map<String, Object> statistics = auditLogService.getModeSwitchStatistics(days);
        return R.ok(statistics);
    }

    /**
     * 获取权限验证失败统计
     */
    @Operation(summary = "获取权限验证失败统计")
    @GetMapping("/statistics/permission-failure")
    public R getPermissionFailureStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days) {

        Map<String, Object> statistics = auditLogService.getPermissionFailureStatistics(days);
        return R.ok(statistics);
    }

    /**
     * 获取异常操作统计
     */
    @Operation(summary = "获取异常操作统计")
    @GetMapping("/statistics/exception")
    public R getExceptionStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days) {

        Map<String, Object> statistics = auditLogService.getExceptionStatistics(days);
        return R.ok(statistics);
    }

    /**
     * 获取用户活动统计
     */
    @Operation(summary = "获取用户活动统计")
    @GetMapping("/statistics/user-activity")
    public R getUserActivityStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days) {

        Map<String, Object> statistics = auditLogService.getUserActivityStatistics(days);
        return R.ok(statistics);
    }

    /**
     * 获取系统安全事件
     */
    @Operation(summary = "获取系统安全事件")
    @GetMapping("/security-events")
    public R getSecurityEvents(
            @Parameter(description = "查询天数") @RequestParam(defaultValue = "7") int days) {

        List<OrganizationAuditLogEntity> events = auditLogService.getSecurityEvents(days);
        return R.ok(events);
    }

    /**
     * 根据操作类型查询日志
     */
    @Operation(summary = "根据操作类型查询日志")
    @GetMapping("/logs/by-operation")
    public R getLogsByOperationType(
            @Parameter(description = "操作类型") @RequestParam String operationType,
            @Parameter(description = "查询天数") @RequestParam(defaultValue = "30") int days,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") int limit) {

        List<OrganizationAuditLogEntity> logs = auditLogService.getLogsByOperationType(operationType, days, limit);
        return R.ok(logs);
    }

    /**
     * 获取项目相关操作日志
     */
    @Operation(summary = "获取项目相关操作日志")
    @GetMapping("/project/{projectId}/logs")
    public R getProjectOperationLogs(
            @Parameter(description = "项目ID") @PathVariable Long projectId,
            @Parameter(description = "查询天数") @RequestParam(defaultValue = "30") int days,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") int limit) {

        List<OrganizationAuditLogEntity> logs = auditLogService.getProjectOperationLogs(projectId, days, limit);
        return R.ok(logs);
    }

    /**
     * 清理过期日志
     */
    @Operation(summary = "清理过期日志")
    @PostMapping("/clean-expired")
    public R cleanExpiredLogs(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "90") int days,
            HttpServletRequest request) {

        int cleanedCount = auditLogService.cleanExpiredLogs(days);

        // 记录清理操作到审计日志
        auditLogService.logDataOperation(
                getCurrentUserId(), getCurrentUsername(), "LOG_CLEANUP",
                "AUDIT_LOG", null, null, "清理了 " + cleanedCount + " 条过期日志",
                request, true, null, null);

        return R.ok("成功清理 " + cleanedCount + " 条过期日志");
    }

    /**
     * 导出审计日志
     */
    @Operation(summary = "导出审计日志")
    @PostMapping("/export")
    public R exportAuditLogs(
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime,
            @Parameter(description = "操作类型") @RequestParam(required = false) String operationType,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            HttpServletRequest request) {

        // 记录导出操作到审计日志
        auditLogService.logDataOperation(
                getCurrentUserId(), getCurrentUsername(), "LOG_EXPORT",
                "AUDIT_LOG", null, null,
                String.format("导出条件: startTime=%s, endTime=%s, operationType=%s, userId=%s",
                        startTime, endTime, operationType, userId),
                request, true, null, null);

        return R.ok("审计日志导出功能待实现");
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        // 从当前会话获取用户ID，这里需要根据实际的用户会话管理实现
        return 1L; // 临时返回，实际应该从SecurityContext或Session获取
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUsername() {
        // 从当前会话获取用户名，这里需要根据实际的用户会话管理实现
        return "admin"; // 临时返回，实际应该从SecurityContext或Session获取
    }
}