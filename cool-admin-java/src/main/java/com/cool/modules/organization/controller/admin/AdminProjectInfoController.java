package com.cool.modules.organization.controller.admin;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.CrudOption;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.cool.modules.organization.entity.table.ProjectInfoEntityTableDef;
import com.cool.modules.organization.service.DualDimensionDataPermissionService;
import com.cool.modules.organization.service.ProjectInfoService;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 项目信息管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "项目信息管理", description = "项目CRUD、项目统计等功能")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info", "options"})
@RequiredArgsConstructor
public class AdminProjectInfoController extends BaseController<ProjectInfoService, ProjectInfoEntity> {

    private final DualDimensionDataPermissionService permissionService;

    /**
     * 获取项目选项，用于下拉选择器等场景，已根据双维度数据权限过滤
     * 
     * @return R<List<Dict>> 项目选项列表
     */
    public R<List<Dict>> options() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        // 调用getAccessibleDataIds时，需明确指定组织模式为'project'
        List<Long> projectIds = permissionService.getAccessibleDataIds(userId, "project");
        if (projectIds == null || projectIds.isEmpty()) {
            return R.ok(java.util.Collections.emptyList());
        }
        // 使用 where ... in ... 方式构建查询，避免方法重载歧义
        List<ProjectInfoEntity> list = service
                .list(QueryWrapper.create().where(ProjectInfoEntityTableDef.PROJECT_INFO_ENTITY.ID.in(projectIds)));

        List<Dict> options = list.stream()
                .map(e -> Dict.create().set("label", e.getProjectName()).set("value", e.getId()))
                .collect(java.util.stream.Collectors.toList());
        return R.ok(options);
    }

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        CrudOption op = createOp()
                // 状态和优先级使用精确匹配
                .fieldEq(ProjectInfoEntityTableDef.PROJECT_INFO_ENTITY.STATUS, ProjectInfoEntityTableDef.PROJECT_INFO_ENTITY.PRIORITY)
                // 项目名称和编码使用模糊查询
                .keyWordLikeFields(ProjectInfoEntityTableDef.PROJECT_INFO_ENTITY.PROJECT_NAME,ProjectInfoEntityTableDef.PROJECT_INFO_ENTITY.PROJECT_CODE);
        setPageOption(op);
        setListOption(op);
    }

    @Override
    public R add(@RequestBody JSONObject body) {
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        body.set("creatorId", currentUserId);
        if (body.get("ownerId") == null) {
            body.set("ownerId", currentUserId);
        }
        return super.add(body);
    }
    
    @PostMapping("/create")
    @Operation(summary = "创建项目")
    public R<ProjectInfoEntity> createProject(@Valid @RequestBody ProjectInfoEntity project) {
        // 设置组织归属信息
        Long userId = CoolSecurityUtil.getCurrentUserId();
        permissionService.setEntityOrganizationInfo(project, userId);

        boolean success = service.createProject(project);

        if (success) {
            return R.ok(project);
        } else {
            return R.error("项目创建失败");
        }
    }
    
    @GetMapping("/my-projects")
    @Operation(summary = "获取我的项目列表")
    public R<List<ProjectInfoEntity>> getMyProjects() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        List<ProjectInfoEntity> projects = service.getUserProjects(userId);
        
        return R.ok(projects);
    }
    
    @GetMapping("/by-owner/{ownerId}")
    @Operation(summary = "根据负责人获取项目列表")
    public R<List<ProjectInfoEntity>> getProjectsByOwner(@PathVariable Long ownerId) {
        List<ProjectInfoEntity> projects = service.getByOwnerId(ownerId);
        
        return R.ok(projects);
    }
    
    @GetMapping("/by-creator/{creatorId}")
    @Operation(summary = "根据创建人获取项目列表")
    public R<List<ProjectInfoEntity>> getProjectsByCreator(@PathVariable Long creatorId) {
        List<ProjectInfoEntity> projects = service.getByCreatorId(creatorId);
        
        return R.ok(projects);
    }
    
    @GetMapping("/detail/{projectId}")
    @Operation(summary = "获取项目详情（包含统计信息）")
    public R<ProjectInfoEntity> getProjectDetail(@PathVariable Long projectId) {
        // 检查权限
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectAccess(userId, projectId)) {
            return R.error("无权限访问此项目");
        }
        
        ProjectInfoEntity project = service.getProjectWithStats(projectId);
        
        if (project != null) {
            return R.ok(project);
        } else {
            return R.error("项目不存在");
        }
    }
    
    @PutMapping("/status/{projectId}")
    @Operation(summary = "更新项目状态")
    public R<String> updateProjectStatus(@PathVariable Long projectId, 
                                        @RequestParam Integer status) {
        // 检查权限
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectAccess(userId, projectId)) {
            return R.error("无权限操作此项目");
        }
        
        boolean success = service.updateStatus(projectId, status);
        
        if (success) {
            return R.ok("项目状态更新成功");
        } else {
            return R.error("项目状态更新失败");
        }
    }
    
    @PutMapping("/batch-status")
    @Operation(summary = "批量更新项目状态")
    public R<String> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> projectIds = (List<Long>) params.get("projectIds");
        Integer status = (Integer) params.get("status");
        
        if (projectIds == null || projectIds.isEmpty() || status == null) {
            return R.error("参数不能为空");
        }
        
        int successCount = service.batchUpdateStatus(projectIds, status);
        
        return R.ok("成功更新 " + successCount + " 个项目状态");
    }
    
    @DeleteMapping("/soft-delete/{projectId}")
    @Operation(summary = "软删除项目")
    public R<String> softDeleteProject(@PathVariable Long projectId) {
        // 检查权限
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectAccess(userId, projectId)) {
            return R.error("无权限删除此项目");
        }
        
        boolean success = service.deleteProject(projectId);
        
        if (success) {
            return R.ok("项目删除成功");
        } else {
            return R.error("项目删除失败");
        }
    }
    
    @GetMapping("/check-code/{projectCode}")
    @Operation(summary = "检查项目编码是否可用")
    public R<Dict> checkProjectCode(@PathVariable String projectCode, 
                                   @RequestParam(required = false) Long excludeId) {
        boolean exists = service.existsByProjectCode(projectCode, excludeId);
        
        return R.ok(Dict.create()
            .set("projectCode", projectCode)
            .set("exists", exists)
            .set("available", !exists)
        );
    }
    
    @GetMapping("/statistics")
    @Operation(summary = "获取项目统计信息")
    public R<Dict> getProjectStatistics() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        
        // 获取用户参与的项目
        List<ProjectInfoEntity> userProjects = service.getUserProjects(userId);
        
        // 统计各状态项目数量
        long planningCount = userProjects.stream().filter(p -> p.getStatus() == 0).count();
        long inProgressCount = userProjects.stream().filter(p -> p.getStatus() == 1).count();
        long completedCount = userProjects.stream().filter(p -> p.getStatus() == 2).count();
        long pausedCount = userProjects.stream().filter(p -> p.getStatus() == 3).count();
        long cancelledCount = userProjects.stream().filter(p -> p.getStatus() == 4).count();
        
        // 统计各优先级项目数量
        long lowPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 1).count();
        long mediumPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 2).count();
        long highPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 3).count();
        long urgentPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 4).count();
        
        return R.ok(Dict.create()
            .set("totalCount", userProjects.size())
            .set("statusStats", Dict.create()
                .set("planning", planningCount)
                .set("inProgress", inProgressCount)
                .set("completed", completedCount)
                .set("paused", pausedCount)
                .set("cancelled", cancelledCount)
            )
            .set("priorityStats", Dict.create()
                .set("low", lowPriorityCount)
                .set("medium", mediumPriorityCount)
                .set("high", highPriorityCount)
                .set("urgent", urgentPriorityCount)
            )
        );
    }

    @GetMapping("/options")
    @Operation(summary = "获取项目下拉选项")
    public R<List<Dict>> getProjectOptions() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        List<ProjectInfoEntity> userProjects = service.getUserProjects(userId);
        List<Dict> options = userProjects.stream()
                .map(p -> Dict.create().set("value", p.getId()).set("label", p.getProjectName()))
                .toList();
        return R.ok(options);
    }

    @PostMapping("/import")
    @Operation(summary = "导入项目")
    public R<Dict> importProjects(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.error("上传文件不能为空");
        }
        try {
            Dict result = service.importProjects(file);
            return R.ok(result);
        } catch (Exception e) {
            return R.error("导入失败：" + e.getMessage());
        }
    }
}
