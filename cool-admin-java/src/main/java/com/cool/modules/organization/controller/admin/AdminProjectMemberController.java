package com.cool.modules.organization.controller.admin;

import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.CrudOption;
import com.cool.core.request.PageResult;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.entity.sys.table.BaseSysUserEntityTableDef;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.organization.dto.ProjectMemberDTO;
import com.cool.modules.organization.dto.UpdateMemberRequest;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.entity.table.UserOrganizationEntityTableDef;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.UserOrganizationService;
import com.cool.modules.organization.service.impl.UserOrganizationServiceImpl;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 项目成员管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "项目成员管理", description = "项目成员增删改查和权限管理")
@CoolRestController(api = { "add", "delete", "update", "page", "list", "info" })
@RequiredArgsConstructor
public class AdminProjectMemberController extends BaseController<UserOrganizationService, UserOrganizationEntity> {

    private final BaseSysUserService baseSysUserService;


    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 引入TableDef以实现类型安全的查询
        UserOrganizationEntityTableDef member = UserOrganizationEntityTableDef.USER_ORGANIZATION_ENTITY;
        BaseSysUserEntityTableDef user = BaseSysUserEntityTableDef.BASE_SYS_USER_ENTITY;
        //兼容projectId参数
        Long projectId = requestParams.getLong("projectId");
        if(projectId != null) {
           requestParams.set("organizationId", projectId);
        }
        // 配置分页和列表查询
        CrudOption op = createOp()
                .fieldEq(member.ORGANIZATION_ID, member.ROLE_CODE, member.STATUS)
                .keyWordLikeFields(user.NAME, user.PHONE, user.EMAIL);

        setPageOption(op);
        setListOption(op);

        // 详情查询保持默认
        setInfoOption(createOp());
    }

    /**
     * 分页查询项目成员
     */
    @Operation(summary = "分页查询项目成员")
    @PostMapping("/pageMembers")
    public R<PageResult<ProjectMemberDTO>> pageMembers(HttpServletRequest request, @RequestAttribute() JSONObject requestParams) {
        CrudOption<UserOrganizationEntity> option = (CrudOption<UserOrganizationEntity>) request.getAttribute(COOL_PAGE_OP);
        QueryWrapper queryWrapper = option.getQueryWrapper(UserOrganizationEntity.class);
        Page<ProjectMemberDTO> result = service.pageProjectMembers(new Page<>(requestParams.getInt("page", 1), requestParams.getInt("size", 20)), queryWrapper);
        return R.ok(PageResult.of(result));
    }


    /**
     * 获取项目成员列表
     */
    @Operation(summary = "获取项目成员列表", description = "根据项目ID获取项目成员列表")
    @PostMapping("/projectMembers")
    public R<List<ProjectMemberDTO>> getProjectMembers(@RequestBody @Valid ProjectIdRequest request) {
        Long projectId = request.getProjectId();
        
        // 权限检查：只有项目成员才能查看项目成员列表
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectManagePermission(userId, projectId)) {
            return R.error("您没有权限查看该项目成员");
        }

        List<ProjectMemberDTO> members = service.getProjectMembers(projectId);
        return R.ok(members);
    }

    /**
     * 添加用户到项目
     */
    @Operation(summary = "添加用户到项目", description = "将用户添加到指定项目中并分配角色")
    @PostMapping("/addMember")
    public R<String> addMemberToProject(@RequestBody @Valid AddMemberRequest request) {
        Long projectId = request.getProjectId();
        
        // 权限检查：只有项目管理员和负责人才能添加成员
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectManagePermission(userId, projectId)) {
            return R.error("您没有权限添加项目成员");
        }

        // 验证角色代码
        if (!GlobalProjectRoleEnum.isValidCode(request.getRoleCode())) {
            return R.error("无效的项目角色代码: " + request.getRoleCode());
        }

        try {
            boolean success = service.addUserToOrganization(
                    request.getUserId(),
                    projectId,
                    OrganizationModeEnum.PROJECT.getCode(),
                    request.getRoleCode(),
                    userId);

            if (success) {
                return R.ok("用户已成功添加到项目");
            } else {
                return R.error("添加用户到项目失败");
            }
        } catch (Exception e) {
            return R.error("添加用户到项目失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加用户到项目
     */
    @Operation(summary = "批量添加用户到项目", description = "批量将用户添加到指定项目中并分配角色")
    @PostMapping("/batchAddMembers")
    public R<Dict> batchAddMembersToProject(@RequestBody @Valid BatchAddMembersRequest request) {
        Long projectId = request.getProjectId();
        
        // 权限检查：只有项目管理员和负责人才能批量添加成员
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectManagePermission(userId, projectId)) {
            return R.error("您没有权限批量添加项目成员");
        }

        // 验证角色代码
        if (!GlobalProjectRoleEnum.isValidCode(request.getRoleCode())) {
            return R.error("无效的项目角色代码: " + request.getRoleCode());
        }

        try {
            // 转换过期时间
            Date expireTime = null;
            if (request.getExpireTime() != null && !request.getExpireTime().isEmpty()) {
                try {
                    expireTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(request.getExpireTime());
                } catch (Exception e) {
                    return R.error("过期时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
                }
            }
            
            // 调用扩展方法，支持过期时间和备注
            int successCount = ((UserOrganizationServiceImpl)service).batchAddUsersToOrganization(
                    request.getUserIds(),
                    projectId,
                    OrganizationModeEnum.PROJECT.getCode(),
                    request.getRoleCode(),
                    userId,
                    expireTime,
                    request.getRemark());

            return R.ok(Dict.create()
                    .set("successCount", successCount)
                    .set("totalCount", request.getUserIds().size())
                    .set("message", String.format("成功添加 %d 个用户到项目", successCount)));
        } catch (Exception e) {
            return R.error("批量添加用户到项目失败: " + e.getMessage());
        }
    }

    /**
     * 从项目中移除用户
     */
    @Operation(summary = "从项目中移除用户", description = "将用户从指定项目中移除")
    @PostMapping("/removeMember")
    public R<String> removeMemberFromProject(@RequestBody @Valid RemoveMemberRequest request) {
        Long projectId = request.getProjectId();
        
        // 权限检查：只有项目管理员和负责人才能移除成员
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectManagePermission(userId, projectId)) {
            return R.error("您没有权限移除项目成员");
        }

        // 不能移除自己
        if (request.getUserId().equals(userId)) {
            return R.error("不能移除自己");
        }

        try {
            boolean success = service.removeUserFromOrganization(
                    request.getUserId(),
                    projectId,
                    OrganizationModeEnum.PROJECT.getCode());

            if (success) {
                return R.ok("用户已成功从项目中移除");
            } else {
                return R.error("移除用户失败");
            }
        } catch (Exception e) {
            return R.error("移除用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量从项目中移除用户
     */
    @Operation(summary = "批量从项目中移除用户", description = "批量将用户从指定项目中移除")
    @PostMapping("/batchRemoveMembers")
    public R<Dict> batchRemoveMembersFromProject(@RequestBody @Valid BatchRemoveMembersRequest request) {
        Long projectId = request.getProjectId();
        
        // 权限检查：只有项目管理员和负责人才能批量移除成员
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectManagePermission(userId, projectId)) {
            return R.error("您没有权限批量移除项目成员");
        }

        // 不能移除自己
        if (request.getUserIds().contains(userId)) {
            return R.error("不能移除自己");
        }

        try {
            int successCount = service.batchRemoveUsersFromOrganization(
                    request.getUserIds(),
                    projectId,
                    OrganizationModeEnum.PROJECT.getCode());

            return R.ok(Dict.create()
                    .set("successCount", successCount)
                    .set("totalCount", request.getUserIds().size())
                    .set("message", String.format("成功移除 %d 个用户", successCount)));
        } catch (Exception e) {
            return R.error("批量移除用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户在项目中的角色
     */
    @Operation(summary = "更新项目成员信息", description = "更新成员的角色、状态、到期时间等信息")
    @PostMapping("/updateMember")
    public R<String> updateMember(@RequestBody @Valid UpdateMemberRequest request) {
        Long projectId = request.getProjectId();
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();

        if (!baseSysUserService.isSuperAdmin(currentUserId) && !service.hasProjectManagePermission(currentUserId, projectId)) {
            return R.error("您没有权限更新项目成员信息");
        }

        try {
            service.updateMemberDetails(request);
            return R.ok("成员信息已成功更新");
        } catch (Exception e) {
            return R.error("更新成员信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的项目列表
     */
    @Operation(summary = "获取用户项目列表", description = "获取当前用户参与的所有项目")
    @GetMapping("/userProjects")
    public R<List<Long>> getUserProjects() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        List<Long> projectIds = service.getUserProjectIds(userId);
        return R.ok(projectIds);
    }

    /**
     * 检查用户是否有项目管理权限
     */
    @Operation(summary = "检查项目管理权限", description = "检查当前用户是否有指定项目的管理权限")
    @PostMapping("/hasManagePermission")
    public R<Boolean> hasProjectManagePermission(@RequestBody @Valid ProjectIdRequest request) {
        Long projectId = request.getProjectId();
        Long userId = CoolSecurityUtil.getCurrentUserId();
        boolean hasPermission = service.hasProjectManagePermission(userId, projectId);
        return R.ok(hasPermission);
    }

    /**
     * 获取项目可用分配人员
     */
    @Operation(summary = "获取项目可用分配人员", description = "获取指定项目的可用分配人员列表")
    @GetMapping("/available-assignees")
    public R<List<ProjectMemberDTO>> getAvailableAssignees(
            @RequestParam(required = false) Long projectId,
            @RequestParam(required = false) List<Long> roleIds,
            @RequestParam(required = false) List<String> roleNames,
            @RequestParam(required = false) String keyword) {
        try {
            // 如果没有指定项目ID，返回空列表
            if (projectId == null) {
                return R.ok(List.of());
            }
            
            // 权限检查：只有项目成员才能查看项目成员列表
            Long userId = CoolSecurityUtil.getCurrentUserId();
            if (!service.hasProjectManagePermission(userId, projectId)) {
                return R.error("您没有权限查看该项目成员");
            }
            
            // 获取项目成员列表
            List<ProjectMemberDTO> members = service.getProjectMembers(projectId);
            
            // 根据条件过滤
            if (roleIds != null && !roleIds.isEmpty()) {
                members = members.stream()
                    .filter(member -> roleIds.contains(member.getRoleCode()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            if (roleNames != null && !roleNames.isEmpty()) {
                members = members.stream()
                    .filter(member -> roleNames.contains(member.getRoleName()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                String searchKeyword = keyword.trim().toLowerCase();
                members = members.stream()
                    .filter(member -> 
                        (member.getUserName() != null && member.getUserName().toLowerCase().contains(searchKeyword)) ||
                        (member.getUserPhone() != null && member.getUserPhone().contains(searchKeyword)) ||
                        (member.getUserEmail() != null && member.getUserEmail().toLowerCase().contains(searchKeyword))
                    )
                    .collect(java.util.stream.Collectors.toList());
            }
            
            return R.ok(members);
        } catch (Exception e) {
            return R.error("获取项目可用分配人员失败: " + e.getMessage());
        }
    }

    /**
     * 获取全局项目角色列表
     */
    @Operation(summary = "获取全局项目角色列表", description = "获取所有可用的全局项目角色")
    @GetMapping("/globalProjectRoles")
    public R<List<Dict>> getGlobalProjectRoles() {
        List<Dict> roles = List.of(
                Dict.create()
                        .set("label", GlobalProjectRoleEnum.PROJECT_OWNER.getCode())
                        .set("name", GlobalProjectRoleEnum.PROJECT_OWNER.getName())
                        .set("level", GlobalProjectRoleEnum.PROJECT_OWNER.getLevel())
                        .set("description", "项目负责人，拥有项目的完全控制权限"),
                Dict.create()
                        .set("label", GlobalProjectRoleEnum.PROJECT_ADMIN.getCode())
                        .set("name", GlobalProjectRoleEnum.PROJECT_ADMIN.getName())
                        .set("level", GlobalProjectRoleEnum.PROJECT_ADMIN.getLevel())
                        .set("description", "项目管理员，拥有项目的管理权限"),
                Dict.create()
                        .set("label", GlobalProjectRoleEnum.PROJECT_MEMBER.getCode())
                        .set("name", GlobalProjectRoleEnum.PROJECT_MEMBER.getName())
                        .set("level", GlobalProjectRoleEnum.PROJECT_MEMBER.getLevel())
                        .set("description", "项目成员，拥有项目的参与权限"),
                Dict.create()
                        .set("label", GlobalProjectRoleEnum.PROJECT_VIEWER.getCode())
                        .set("name", GlobalProjectRoleEnum.PROJECT_VIEWER.getName())
                        .set("level", GlobalProjectRoleEnum.PROJECT_VIEWER.getLevel())
                        .set("description", "项目观察者，拥有项目的只读权限"));

        return R.ok(roles);
    }

    // 请求DTO类
    public static class ProjectIdRequest {
        private Long projectId;

        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }
    }

    public static class AddMemberRequest {
        private Long projectId;
        private Long userId;
        private String roleCode;

        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getRoleCode() {
            return roleCode;
        }

        public void setRoleCode(String roleCode) {
            this.roleCode = roleCode;
        }
    }

    public static class BatchAddMembersRequest {
        private Long projectId;
        private List<Long> userIds;
        private String roleCode;
        private String expireTime;
        private String remark;

        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }

        public List<Long> getUserIds() {
            return userIds;
        }

        public void setUserIds(List<Long> userIds) {
            this.userIds = userIds;
        }

        public String getRoleCode() {
            return roleCode;
        }

        public void setRoleCode(String roleCode) {
            this.roleCode = roleCode;
        }
        
        public String getExpireTime() {
            return expireTime;
        }
        
        public void setExpireTime(String expireTime) {
            this.expireTime = expireTime;
        }
        
        public String getRemark() {
            return remark;
        }
        
        public void setRemark(String remark) {
            this.remark = remark;
        }
    }

    public static class RemoveMemberRequest {
        private Long projectId;
        private Long userId;

        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }
    }

    public static class BatchRemoveMembersRequest {
        private Long projectId;
        private List<Long> userIds;

        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }

        public List<Long> getUserIds() {
            return userIds;
        }

        public void setUserIds(List<Long> userIds) {
            this.userIds = userIds;
        }
    }

    
}