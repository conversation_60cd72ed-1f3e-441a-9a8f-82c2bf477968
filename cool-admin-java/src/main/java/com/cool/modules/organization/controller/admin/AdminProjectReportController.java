package com.cool.modules.organization.controller.admin;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.request.R;
import com.cool.modules.organization.dto.ChartsDTO;
import com.cool.modules.organization.dto.OverviewDTO;
import com.cool.modules.organization.dto.RankingDTO;
import com.cool.modules.organization.service.ProjectReportService;

import cn.hutool.core.lang.Dict;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "项目报表管理", description = "项目报表统计与分析")
@CoolRestController(api = { "overview", "ranking", "charts" })
@RequiredArgsConstructor
public class AdminProjectReportController {

    private final ProjectReportService projectReportService;

    @PostMapping("/overview")
    public R<OverviewDTO> overview(@RequestBody(required = false) Dict params) {
        return R.ok(projectReportService.overview(params));
    }

    @PostMapping("/ranking")
    public R<RankingDTO> ranking(@RequestBody(required = false) Dict params) {
        return R.ok(projectReportService.ranking(params));
    }

    @PostMapping("/charts")
    public R<ChartsDTO> charts(@RequestBody(required = false) Dict params) {
        return R.ok(projectReportService.charts(params));
    }

    @Operation(summary = "获取人员任务排名", description = "获取人员任务排名数据")
    @PostMapping("/memberTaskRanking")
    public R<List<Dict>> memberTaskRanking(@RequestBody(required = false) Dict params) {
        return R.ok(projectReportService.memberTaskRanking(params));
    }
}
