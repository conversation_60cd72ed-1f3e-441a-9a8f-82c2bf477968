package com.cool.modules.organization.controller.admin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.CrudOption;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.service.ProjectAccessService;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.table.TaskInfoEntityTableDef;
import com.cool.modules.task.service.TaskInfoService;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 项目任务管理
 */
@Tag(name = "项目任务管理", description = "根据项目ID过滤任务列表")
@CoolRestController(api = { "page", "list", "add", "update", "delete", "info" })
public class AdminProjectTaskController extends BaseController<TaskInfoService, TaskInfoEntity> {

    @Autowired
    private ProjectAccessService projectAccessService;

    /**
     * 获取用户可访问的项目列表
     */
    @Operation(summary = "获取用户可访问的项目列表")
    @GetMapping("/accessibleProjects")
    public R<Object> getAccessibleProjects() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        return R.ok(projectAccessService.getUserAccessibleProjects(userId));
    }

    @Operation(summary = "看板分页查询")
    @PostMapping("/kanbanPage")
    public R<Object> kanbanPage(@RequestBody JSONObject requestParams) {
        return R.ok(service.kanbanPage(requestParams));
    }

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 根据开发规范，Controller层只负责简单的参数传递
        // 复杂的查询逻辑、权限过滤、项目筛选等都在TaskInfoServiceImpl的page方法中处理
        TaskInfoEntityTableDef task = TaskInfoEntityTableDef.TASK_INFO_ENTITY;
        
        CrudOption<TaskInfoEntity> op = createOp()
                // 基本的字段匹配和模糊查询设置
                .fieldEq(task.TASK_STATUS, task.PRIORITY, task.TASK_CATEGORY)
                // 关键词搜索字段
                .keyWordLikeFields(task.NAME, task.DESCRIPTION, task.SCENARIO_NAME);
        
        setPageOption(op);
        setListOption(op);
    }
}