package com.cool.modules.organization.controller.admin;

import com.cool.core.request.R;
import com.cool.modules.organization.service.SecurityMonitorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 安全监控管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "安全监控管理", description = "系统安全监控和用户锁定管理")
@RestController
@RequestMapping("/admin/organization/security")
public class AdminSecurityMonitorController {

    @Autowired
    private SecurityMonitorService securityMonitorService;

    /**
     * 获取用户锁定状态
     */
    @Operation(summary = "获取用户锁定状态")
    @GetMapping("/user/{userId}/lock-status")
    public R getUserLockStatus(@Parameter(description = "用户ID") @PathVariable Long userId) {
        boolean isLocked = securityMonitorService.isUserLocked(userId);
        Map<String, Object> lockInfo = securityMonitorService.getUserLockInfo(userId);
        
        return R.ok(Map.of(
                "isLocked", isLocked,
                "lockInfo", lockInfo
        ));
    }

    /**
     * 手动锁定用户
     */
    @Operation(summary = "手动锁定用户")
    @PostMapping("/user/{userId}/lock")
    public R lockUser(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "锁定时长(分钟)") @RequestParam int durationMinutes,
            @Parameter(description = "锁定原因") @RequestParam String reason) {
        
        securityMonitorService.lockUser(userId, reason, durationMinutes);
        return R.ok("用户已被锁定");
    }

    /**
     * 手动解锁用户
     */
    @Operation(summary = "手动解锁用户")
    @PostMapping("/user/{userId}/unlock")
    public R unlockUser(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "解锁原因") @RequestParam String reason) {
        
        securityMonitorService.unlockUser(userId, reason);
        return R.ok("用户已被解锁");
    }

    /**
     * 获取安全风险统计
     */
    @Operation(summary = "获取安全风险统计")
    @GetMapping("/risk-statistics")
    public R getRiskStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") int days) {
        
        Map<String, Object> statistics = securityMonitorService.getRiskStatistics(days);
        return R.ok(statistics);
    }

    /**
     * 获取高风险用户列表
     */
    @Operation(summary = "获取高风险用户列表")
    @GetMapping("/high-risk-users")
    public R getHighRiskUsers(
            @Parameter(description = "查询天数") @RequestParam(defaultValue = "7") int days,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "50") int limit) {
        
        List<Map<String, Object>> highRiskUsers = securityMonitorService.getHighRiskUsers(days, limit);
        return R.ok(highRiskUsers);
    }

    /**
     * 获取用户权限失败记录
     */
    @Operation(summary = "获取用户权限失败记录")
    @GetMapping("/user/{userId}/permission-failures")
    public R getUserPermissionFailures(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "查询天数") @RequestParam(defaultValue = "7") int days) {
        
        List<Map<String, Object>> failures = securityMonitorService.getUserPermissionFailures(userId, days);
        return R.ok(failures);
    }

    /**
     * 获取系统安全事件概览
     */
    @Operation(summary = "获取系统安全事件概览")
    @GetMapping("/security-overview")
    public R getSecurityOverview(
            @Parameter(description = "查询天数") @RequestParam(defaultValue = "7") int days) {
        
        Map<String, Object> overview = securityMonitorService.getSecurityOverview(days);
        return R.ok(overview);
    }

    /**
     * 获取实时安全警报
     */
    @Operation(summary = "获取实时安全警报")
    @GetMapping("/alerts")
    public R getSecurityAlerts(
            @Parameter(description = "警报级别") @RequestParam(required = false) String level,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "20") int limit) {
        
        List<Map<String, Object>> alerts = securityMonitorService.getSecurityAlerts(level, limit);
        return R.ok(alerts);
    }

    /**
     * 清除安全警报
     */
    @Operation(summary = "清除安全警报")
    @PostMapping("/alerts/{alertId}/clear")
    public R clearSecurityAlert(@Parameter(description = "警报ID") @PathVariable String alertId) {
        securityMonitorService.clearSecurityAlert(alertId);
        return R.ok("安全警报已清除");
    }

    /**
     * 获取IP访问统计
     */
    @Operation(summary = "获取IP访问统计")
    @GetMapping("/ip-statistics")
    public R getIpStatistics(
            @Parameter(description = "查询天数") @RequestParam(defaultValue = "7") int days,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") int limit) {
        
        List<Map<String, Object>> ipStats = securityMonitorService.getIpStatistics(days, limit);
        return R.ok(ipStats);
    }

    /**
     * 添加IP黑名单
     */
    @Operation(summary = "添加IP黑名单")
    @PostMapping("/ip-blacklist")
    public R addIpToBlacklist(
            @Parameter(description = "IP地址") @RequestParam String ipAddress,
            @Parameter(description = "封禁原因") @RequestParam String reason,
            @Parameter(description = "封禁时长(小时)") @RequestParam(defaultValue = "24") int durationHours) {
        
        securityMonitorService.addIpToBlacklist(ipAddress, reason, durationHours);
        return R.ok("IP已添加到黑名单");
    }

    /**
     * 移除IP黑名单
     */
    @Operation(summary = "移除IP黑名单")
    @PostMapping("/ip-blacklist/{ipAddress}/remove")
    public R removeIpFromBlacklist(@Parameter(description = "IP地址") @PathVariable String ipAddress) {
        securityMonitorService.removeIpFromBlacklist(ipAddress);
        return R.ok("IP已从黑名单移除");
    }

    /**
     * 获取IP黑名单列表
     */
    @Operation(summary = "获取IP黑名单列表")
    @GetMapping("/ip-blacklist")
    public R getIpBlacklist() {
        List<Map<String, Object>> blacklist = securityMonitorService.getIpBlacklist();
        return R.ok(blacklist);
    }
}