package com.cool.modules.organization.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数据迁移结果DTO
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Data
public class MigrationResultDTO {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 迁移的用户数量
     */
    private Integer migratedUserCount;
    
    /**
     * 初始化的用户模式数量
     */
    private Integer initializedModeCount;
    
    /**
     * 创建的项目角色数量
     */
    private Integer createdRoleCount;
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 详细信息
     */
    private List<String> details;
    
    /**
     * 错误信息
     */
    private List<String> errors;
    
    /**
     * 是否有效（用于验证）
     */
    private Boolean valid;
    
    /**
     * 验证详情
     */
    private String validationDetails;
    
    /**
     * 迁移状态
     */
    private String status;
    
    /**
     * 进度百分比
     */
    private Integer progress;
}