package com.cool.modules.organization.dto;

import lombok.Data;
import java.util.Date;

/**
 * 项目访问信息DTO
 */
@Data
public class ProjectAccessDTO {
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 项目编码
     */
    private String projectCode;
    
    /**
     * 用户在项目中的角色
     */
    private String userRole;
    
    /**
     * 加入时间
     */
    private Date joinTime;
}