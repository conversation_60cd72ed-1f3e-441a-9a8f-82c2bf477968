package com.cool.modules.organization.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织架构审计日志实体
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "organization_audit_log", comment = "组织架构审计日志")
public class OrganizationAuditLogEntity extends BaseEntity<OrganizationAuditLogEntity> {
    
    @ColumnDefine(comment = "用户ID", notNull = true)
    private Long userId;
    
    @ColumnDefine(comment = "用户名", length = 100)
    private String username;
    
    @ColumnDefine(comment = "操作类型", length = 50, notNull = true)
    private String operationType;
    
    @ColumnDefine(comment = "操作描述", length = 500)
    private String operationDescription;
    
    @ColumnDefine(comment = "目标实体类型", length = 50)
    private String targetEntityType;
    
    @ColumnDefine(comment = "目标实体ID")
    private Long targetEntityId;
    
    @ColumnDefine(comment = "操作前数据", type = "text")
    private String beforeData;
    
    @ColumnDefine(comment = "操作后数据", type = "text")
    private String afterData;
    
    @ColumnDefine(comment = "请求IP", length = 50)
    private String requestIp;
    
    @ColumnDefine(comment = "用户代理", length = 500)
    private String userAgent;
    
    @ColumnDefine(comment = "请求URL", length = 500)
    private String requestUrl;
    
    @ColumnDefine(comment = "请求方法", length = 10)
    private String requestMethod;
    
    @ColumnDefine(comment = "操作结果", length = 20, defaultValue = "SUCCESS")
    private String operationResult;
    
    @ColumnDefine(comment = "错误信息", type = "text")
    private String errorMessage;
    
    @ColumnDefine(comment = "执行时间(毫秒)")
    private Long executionTime;
    
    @ColumnDefine(comment = "组织形态", length = 20)
    private String organizationMode;
    
    @ColumnDefine(comment = "项目ID")
    private Long projectId;
    
    @ColumnDefine(comment = "部门ID")
    private Long departmentId;
}