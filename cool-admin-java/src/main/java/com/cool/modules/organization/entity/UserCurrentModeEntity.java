package com.cool.modules.organization.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;

import java.util.Date;

/**
 * 用户当前组织模式实体
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Getter
@Setter
@Table(value = "org_user_current_mode", comment = "用户当前组织模式表")
public class UserCurrentModeEntity extends BaseEntity<UserCurrentModeEntity> {
    
    /**
     * 用户ID
     */
    @Index
    @ColumnDefine(comment = "用户ID", type = "bigint", notNull = true)
    private Long userId;
    
    /**
     * 当前组织模式
     */
    @ColumnDefine(comment = "当前组织模式", length = 20, notNull = true, defaultValue = "'DEPARTMENT'")
    private String currentMode;
    
    /**
     * 最后切换时间
     */
    @ColumnDefine(comment = "最后切换时间")
    private Date lastSwitchTime;
    
    /**
     * 切换次数
     */
    @ColumnDefine(comment = "切换次数", type = "int", defaultValue = "0")
    private Integer switchCount;
    
    /**
     * 切换原因
     */
    @ColumnDefine(comment = "切换原因", length = 500)
    private String reason;
    
    /**
     * 客户端IP地址
     */
    @ColumnDefine(comment = "客户端IP地址", length = 50)
    private String ipAddress;
    
    /**
     * 用户代理信息
     */
    @ColumnDefine(comment = "用户代理信息", length = 500)
    private String userAgent;
    
    /**
     * 是否强制切换
     */
    @ColumnDefine(comment = "是否强制切换", defaultValue = "false")
    private Boolean forceSwitch;
    
    /**
     * 切换状态
     */
    @ColumnDefine(comment = "切换状态 0:失败 1:成功", type = "tinyint", defaultValue = "1")
    private Integer switchStatus;
    
    /**
     * 错误信息
     */
    @ColumnDefine(comment = "错误信息", length = 1000)
    private String errorMessage;
    
    /**
     * 备注
     */
    @ColumnDefine(comment = "备注", length = 500)
    private String remark;
    
    // 以下为查询时填充的字段，不存储到数据库
    
    /**
     * 用户姓名（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String userName;
    
    /**
     * 用户账号（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String userAccount;
    
    /**
     * 当前模式名称（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String currentModeName;
    
    /**
     * 是否为当前活跃模式
     */
    @Column(ignore = true)
    private Boolean isActive;
    
    /**
     * 模式持续时间（秒）
     */
    @Column(ignore = true)
    private Long modeDuration;
}