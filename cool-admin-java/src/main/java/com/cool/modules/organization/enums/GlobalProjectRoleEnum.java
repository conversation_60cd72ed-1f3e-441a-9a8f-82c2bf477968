package com.cool.modules.organization.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 全局项目角色枚举
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Getter
@AllArgsConstructor
public enum GlobalProjectRoleEnum {

    /**
     * 项目负责人 - 拥有项目的完全控制权限
     */
    PROJECT_OWNER("PROJECT_OWNER", "项目负责人", "拥有项目的完全控制权限，可以删除项目、管理所有成员", 4),

    /**
     * 项目管理员 - 拥有项目管理权限
     */
    PROJECT_ADMIN("PROJECT_ADMIN", "项目管理员", "拥有项目管理权限，可以编辑项目、管理成员（除负责人外）", 3),

    /**
     * 项目成员 - 拥有项目参与权限
     */
    PROJECT_MEMBER("PROJECT_MEMBER", "项目成员", "拥有项目参与权限，可以查看项目信息、创建和编辑任务", 2),

    /**
     * 项目观察者 - 拥有项目只读权限
     */
    PROJECT_VIEWER("PROJECT_VIEWER", "项目观察者", "拥有项目只读权限，只能查看项目信息和报表", 1);

    /**
     * 角色代码
     */
    private final String code;

    /**
     * 角色名称
     */
    private final String name;

    /**
     * 角色描述
     */
    private final String description;

    /**
     * 角色级别（数字越大权限越高）
     */
    private final Integer level;

    /**
     * 根据代码获取枚举
     * 
     * @param code 角色代码
     * @return 项目角色枚举
     */
    public static GlobalProjectRoleEnum getByCode(String code) {
        for (GlobalProjectRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return PROJECT_VIEWER; // 默认返回观察者角色
    }

    /**
     * 检查是否为有效的角色代码
     * 
     * @param code 角色代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        for (GlobalProjectRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查角色是否有管理权限
     * 
     * @param code 角色代码
     * @return 是否有管理权限
     */
    public static boolean hasManagePermission(String code) {
        GlobalProjectRoleEnum role = getByCode(code);
        return role.getLevel() >= PROJECT_ADMIN.getLevel();
    }

    /**
     * 检查角色是否有删除权限
     * 
     * @param code 角色代码
     * @return 是否有删除权限
     */
    public static boolean hasDeletePermission(String code) {
        GlobalProjectRoleEnum role = getByCode(code);
        return role.getLevel() >= PROJECT_OWNER.getLevel();
    }

    /**
     * 检查角色是否有编辑权限
     * 
     * @param code 角色代码
     * @return 是否有编辑权限
     */
    public static boolean hasEditPermission(String code) {
        GlobalProjectRoleEnum role = getByCode(code);
        return role.getLevel() >= PROJECT_MEMBER.getLevel();
    }

    /**
     * 比较两个角色的权限级别
     * 
     * @param code1 角色代码1
     * @param code2 角色代码2
     * @return code1的权限是否高于或等于code2
     */
    public static boolean hasHigherOrEqualPermission(String code1, String code2) {
        GlobalProjectRoleEnum role1 = getByCode(code1);
        GlobalProjectRoleEnum role2 = getByCode(code2);
        return role1.getLevel() >= role2.getLevel();
    }

    /**
     * 获取所有角色的代码列表
     * 
     * @return 角色代码列表
     */
    public static String[] getAllCodes() {
        GlobalProjectRoleEnum[] roles = values();
        String[] codes = new String[roles.length];
        for (int i = 0; i < roles.length; i++) {
            codes[i] = roles[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有角色的名称列表
     * 
     * @return 角色名称列表
     */
    public static String[] getAllNames() {
        GlobalProjectRoleEnum[] roles = values();
        String[] names = new String[roles.length];
        for (int i = 0; i < roles.length; i++) {
            names[i] = roles[i].getName();
        }
        return names;
    }
}