package com.cool.modules.organization.exception;

import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.service.OrganizationAuditLogService;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 双维度组织架构异常处理器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@RestControllerAdvice
@Order(1) // 优先级高于全局异常处理器
@RequiredArgsConstructor
public class DualDimensionExceptionHandler {

        private final OrganizationAuditLogService auditLogService;

        /**
         * 处理组织形态异常
         */
        @ExceptionHandler(OrganizationModeException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public R<String> handleOrganizationModeException(OrganizationModeException e, HttpServletRequest request) {
                Long userId = getCurrentUserId();
                String username = getCurrentUsername();

                log.error("组织形态异常 - URL: {}, 用户: {}, 错误码: {}, 消息: {}",
                                request.getRequestURI(), username, e.getErrorCode(), e.getMessage(), e);

                // 记录异常审计日志
                recordExceptionAuditLog(userId, username, "ORGANIZATION_MODE_ERROR",
                                "组织形态操作异常", "ORGANIZATION_MODE", null, request, e.getMessage());

                return R.error(1002, "组织形态操作失败: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理组织形态切换异常
         */
        @ExceptionHandler(OrganizationModeException.SwitchException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public R<String> handleModeSwitchException(OrganizationModeException.SwitchException e,
                        HttpServletRequest request) {
                Long userId = getCurrentUserId();
                String username = getCurrentUsername();

                log.error("组织形态切换异常 - URL: {}, 用户: {}, 错误码: {}, 消息: {}",
                                request.getRequestURI(), username, e.getErrorCode(), e.getMessage(), e);

                // 记录异常审计日志
                recordExceptionAuditLog(userId, username, "MODE_SWITCH_ERROR",
                                "组织形态切换异常", "ORGANIZATION_MODE", null, request, e.getMessage());

                return R.error(1003, "组织形态切换失败: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理双维度权限异常
         */
        @ExceptionHandler(DualDimensionPermissionException.class)
        @ResponseStatus(HttpStatus.FORBIDDEN)
        public R<String> handleDualDimensionPermissionException(DualDimensionPermissionException e,
                        HttpServletRequest request) {
                Long userId = e.getUserId() != null ? e.getUserId() : getCurrentUserId();
                String username = getCurrentUsername();

                log.warn("双维度权限异常 - URL: {}, 用户: {}, 用户ID: {}, 实体类型: {}, 实体ID: {}, 错误码: {}, 消息: {}",
                                request.getRequestURI(), username, userId, e.getEntityType(), e.getEntityId(),
                                e.getErrorCode(), e.getMessage());

                // 记录权限异常审计日志
                recordExceptionAuditLog(userId, username, "PERMISSION_DENIED",
                                "双维度权限验证失败", e.getEntityType(), e.getEntityId(), request, e.getMessage());

                return R.error(1004, "权限不足: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("entityType", e.getEntityType())
                                .put("entityId", e.getEntityId())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理数据权限异常
         */
        @ExceptionHandler(DualDimensionPermissionException.DataPermissionException.class)
        @ResponseStatus(HttpStatus.FORBIDDEN)
        public R<String> handleDataPermissionException(DualDimensionPermissionException.DataPermissionException e,
                        HttpServletRequest request) {
                Long userId = e.getUserId() != null ? e.getUserId() : getCurrentUserId();
                String username = getCurrentUsername();

                log.warn("数据权限异常 - URL: {}, 用户: {}, 用户ID: {}, 实体类型: {}, 实体ID: {}, 消息: {}",
                                request.getRequestURI(), username, userId, e.getEntityType(), e.getEntityId(),
                                e.getMessage());

                // 记录数据权限异常审计日志
                recordExceptionAuditLog(userId, username, "DATA_PERMISSION_DENIED",
                                "数据权限验证失败", e.getEntityType(), e.getEntityId(), request, e.getMessage());

                return R.error(1005, "数据访问权限不足: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("entityType", e.getEntityType())
                                .put("entityId", e.getEntityId())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理项目权限异常
         */
        @ExceptionHandler(DualDimensionPermissionException.ProjectPermissionException.class)
        @ResponseStatus(HttpStatus.FORBIDDEN)
        public R<String> handleProjectPermissionException(DualDimensionPermissionException.ProjectPermissionException e,
                        HttpServletRequest request) {
                Long userId = e.getUserId() != null ? e.getUserId() : getCurrentUserId();
                String username = getCurrentUsername();

                log.warn("项目权限异常 - URL: {}, 用户: {}, 用户ID: {}, 项目ID: {}, 消息: {}",
                                request.getRequestURI(), username, userId, e.getEntityId(), e.getMessage());

                // 记录项目权限异常审计日志
                recordExceptionAuditLog(userId, username, "PROJECT_PERMISSION_DENIED",
                                "项目权限验证失败", "PROJECT", e.getEntityId(), request, e.getMessage());

                return R.error(1006, "项目访问权限不足: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("projectId", e.getEntityId())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理数据迁移异常
         */
        @ExceptionHandler(OrganizationModeException.MigrationException.class)
        @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
        public R<String> handleMigrationException(OrganizationModeException.MigrationException e,
                        HttpServletRequest request) {
                Long userId = getCurrentUserId();
                String username = getCurrentUsername();

                log.error("数据迁移异常 - URL: {}, 用户: {}, 错误码: {}, 消息: {}",
                                request.getRequestURI(), username, e.getErrorCode(), e.getMessage(), e);

                // 记录异常审计日志
                recordExceptionAuditLog(userId, username, "DATA_MIGRATION_ERROR",
                                "数据迁移异常", "DATA_MIGRATION", null, request, e.getMessage());

                return R.error(1007, "数据迁移失败: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理项目管理异常
         */
        @ExceptionHandler(ProjectManagementException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public R<String> handleProjectManagementException(ProjectManagementException e, HttpServletRequest request) {
                Long userId = getCurrentUserId();
                String username = getCurrentUsername();

                log.error("项目管理异常 - URL: {}, 用户: {}, 项目ID: {}, 错误码: {}, 消息: {}",
                                request.getRequestURI(), username, e.getProjectId(), e.getErrorCode(), e.getMessage(),
                                e);

                // 记录异常审计日志
                recordExceptionAuditLog(userId, username, "PROJECT_MANAGEMENT_ERROR",
                                "项目管理操作异常", "PROJECT", e.getProjectId(), request, e.getMessage());

                return R.error(1008, "项目操作失败: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("projectId", e.getProjectId())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理项目不存在异常
         */
        @ExceptionHandler(ProjectManagementException.ProjectNotFoundException.class)
        @ResponseStatus(HttpStatus.NOT_FOUND)
        public R<String> handleProjectNotFoundException(ProjectManagementException.ProjectNotFoundException e,
                        HttpServletRequest request) {
                Long userId = getCurrentUserId();
                String username = getCurrentUsername();

                log.warn("项目不存在异常 - URL: {}, 用户: {}, 项目ID: {}, 消息: {}",
                                request.getRequestURI(), username, e.getProjectId(), e.getMessage());

                // 记录异常审计日志
                recordExceptionAuditLog(userId, username, "PROJECT_NOT_FOUND",
                                "项目不存在", "PROJECT", e.getProjectId(), request, e.getMessage());

                return R.error(1009, "项目不存在: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("projectId", e.getProjectId())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 处理项目成员异常
         */
        @ExceptionHandler(ProjectManagementException.ProjectMemberException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public R<String> handleProjectMemberException(ProjectManagementException.ProjectMemberException e,
                        HttpServletRequest request) {
                Long userId = getCurrentUserId();
                String username = getCurrentUsername();

                log.warn("项目成员异常 - URL: {}, 用户: {}, 项目ID: {}, 目标用户ID: {}, 消息: {}",
                                request.getRequestURI(), username, e.getProjectId(), e.getUserId(), e.getMessage());

                // 记录异常审计日志
                recordExceptionAuditLog(userId, username, "PROJECT_MEMBER_ERROR",
                                "项目成员操作异常", "PROJECT_MEMBER", e.getProjectId(), request, e.getMessage());

                return R.error(1010, "项目成员操作失败: " + e.getMessage())
                                .put("errorCode", e.getErrorCode())
                                .put("projectId", e.getProjectId())
                                .put("targetUserId", e.getUserId())
                                .put("timestamp", System.currentTimeMillis());
        }

        /**
         * 获取当前用户ID
         */
        private Long getCurrentUserId() {
                try {
                        return CoolSecurityUtil.getCurrentUserId();
                } catch (Exception e) {
                        log.warn("获取当前用户ID失败", e);
                        return null;
                }
        }

        /**
         * 获取当前用户名
         */
        private String getCurrentUsername() {
                try {
                        return CoolSecurityUtil.getAdminUsername();
                } catch (Exception e) {
                        log.warn("获取当前用户名失败", e);
                        return "unknown";
                }
        }

        /**
         * 记录异常审计日志
         */
        private void recordExceptionAuditLog(Long userId, String username, String operationType,
                        String description, String entityType, Long entityId,
                        HttpServletRequest request, String errorMessage) {
                try {
                        auditLogService.logDataOperation(userId, username, operationType,
                                        entityType, entityId, null, null, request, false, errorMessage, null);
                } catch (Exception e) {
                        log.error("记录异常审计日志失败", e);
                }
        }
}