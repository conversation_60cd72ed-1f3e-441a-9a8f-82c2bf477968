package com.cool.modules.organization.exception;

/**
 * 双维度权限异常
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public class DualDimensionPermissionException extends RuntimeException {
    
    private final String errorCode;
    private final String entityType;
    private final Long entityId;
    private final Long userId;
    
    public DualDimensionPermissionException(String message) {
        super(message);
        this.errorCode = "DUAL_PERMISSION_ERROR";
        this.entityType = null;
        this.entityId = null;
        this.userId = null;
    }
    
    public DualDimensionPermissionException(String errorCode, String message, String entityType, Long entityId, Long userId) {
        super(message);
        this.errorCode = errorCode;
        this.entityType = entityType;
        this.entityId = entityId;
        this.userId = userId;
    }
    
    public DualDimensionPermissionException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "DUAL_PERMISSION_ERROR";
        this.entityType = null;
        this.entityId = null;
        this.userId = null;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getEntityType() {
        return entityType;
    }
    
    public Long getEntityId() {
        return entityId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    /**
     * 数据权限异常
     */
    public static class DataPermissionException extends DualDimensionPermissionException {
        public DataPermissionException(String message, String entityType, Long entityId, Long userId) {
            super("DATA_PERMISSION_DENIED", message, entityType, entityId, userId);
        }
    }
    
    /**
     * 项目权限异常
     */
    public static class ProjectPermissionException extends DualDimensionPermissionException {
        public ProjectPermissionException(String message, Long projectId, Long userId) {
            super("PROJECT_PERMISSION_DENIED", message, "PROJECT", projectId, userId);
        }
    }
    
    /**
     * 部门权限异常
     */
    public static class DepartmentPermissionException extends DualDimensionPermissionException {
        public DepartmentPermissionException(String message, Long departmentId, Long userId) {
            super("DEPARTMENT_PERMISSION_DENIED", message, "DEPARTMENT", departmentId, userId);
        }
    }
}