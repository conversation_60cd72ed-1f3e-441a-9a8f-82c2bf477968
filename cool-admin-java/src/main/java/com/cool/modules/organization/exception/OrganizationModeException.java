package com.cool.modules.organization.exception;

/**
 * 组织形态相关异常
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public class OrganizationModeException extends RuntimeException {
    
    private final String errorCode;
    
    public OrganizationModeException(String message) {
        super(message);
        this.errorCode = "ORG_MODE_ERROR";
    }
    
    public OrganizationModeException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public OrganizationModeException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "ORG_MODE_ERROR";
    }
    
    public OrganizationModeException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 组织形态切换异常
     */
    public static class SwitchException extends OrganizationModeException {
        public SwitchException(String message) {
            super("MODE_SWITCH_ERROR", message);
        }
        
        public SwitchException(String message, Throwable cause) {
            super("MODE_SWITCH_ERROR", message, cause);
        }
    }
    
    /**
     * 权限验证异常
     */
    public static class PermissionException extends OrganizationModeException {
        public PermissionException(String message) {
            super("MODE_PERMISSION_ERROR", message);
        }
        
        public PermissionException(String message, Throwable cause) {
            super("MODE_PERMISSION_ERROR", message, cause);
        }
    }
    
    /**
     * 数据迁移异常
     */
    public static class MigrationException extends OrganizationModeException {
        public MigrationException(String message) {
            super("DATA_MIGRATION_ERROR", message);
        }
        
        public MigrationException(String message, Throwable cause) {
            super("DATA_MIGRATION_ERROR", message, cause);
        }
    }
}