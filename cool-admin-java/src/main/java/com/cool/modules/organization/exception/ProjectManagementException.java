package com.cool.modules.organization.exception;

/**
 * 项目管理相关异常
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
public class ProjectManagementException extends RuntimeException {
    
    private final String errorCode;
    private final Long projectId;
    private final Long userId;
    
    public ProjectManagementException(String message) {
        super(message);
        this.errorCode = "PROJECT_MANAGEMENT_ERROR";
        this.projectId = null;
        this.userId = null;
    }
    
    public ProjectManagementException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.projectId = null;
        this.userId = null;
    }
    
    public ProjectManagementException(String errorCode, String message, Long projectId, Long userId) {
        super(message);
        this.errorCode = errorCode;
        this.projectId = projectId;
        this.userId = userId;
    }
    
    public ProjectManagementException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "PROJECT_MANAGEMENT_ERROR";
        this.projectId = null;
        this.userId = null;
    }
    
    public ProjectManagementException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.projectId = null;
        this.userId = null;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public Long getProjectId() {
        return projectId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    /**
     * 项目不存在异常
     */
    public static class ProjectNotFoundException extends ProjectManagementException {
        public ProjectNotFoundException(String message, Long projectId) {
            super("PROJECT_NOT_FOUND", message, projectId, null);
        }
    }
    
    /**
     * 项目状态异常
     */
    public static class ProjectStatusException extends ProjectManagementException {
        public ProjectStatusException(String message, Long projectId) {
            super("PROJECT_STATUS_ERROR", message, projectId, null);
        }
    }
    
    /**
     * 项目成员异常
     */
    public static class ProjectMemberException extends ProjectManagementException {
        public ProjectMemberException(String message, Long projectId, Long userId) {
            super("PROJECT_MEMBER_ERROR", message, projectId, userId);
        }
    }
    
    /**
     * 项目创建异常
     */
    public static class ProjectCreationException extends ProjectManagementException {
        public ProjectCreationException(String message) {
            super("PROJECT_CREATION_ERROR", message);
        }
        
        public ProjectCreationException(String message, Throwable cause) {
            super("PROJECT_CREATION_ERROR", message, cause);
        }
    }
}