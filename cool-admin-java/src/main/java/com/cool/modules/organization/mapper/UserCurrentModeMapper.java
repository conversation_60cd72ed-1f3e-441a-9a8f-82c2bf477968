package com.cool.modules.organization.mapper;

import com.mybatisflex.core.BaseMapper;
import com.cool.modules.organization.entity.UserCurrentModeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 用户当前组织模式Mapper
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Mapper
public interface UserCurrentModeMapper extends BaseMapper<UserCurrentModeEntity> {
    
    /**
     * 根据用户ID获取当前组织模式
     * 
     * @param userId 用户ID
     * @return 当前组织模式
     */
    @Select("SELECT current_mode FROM org_user_current_mode WHERE user_id = #{userId} AND is_deleted = 0")
    String getCurrentModeByUserId(@Param("userId") Long userId);
    
    /**
     * 更新用户当前组织模式
     * 
     * @param userId 用户ID
     * @param currentMode 当前组织模式
     * @return 影响行数
     */
    @Update("UPDATE org_user_current_mode SET current_mode = #{currentMode}, " +
            "last_switch_time = NOW(), switch_count = switch_count + 1, update_time = NOW() " +
            "WHERE user_id = #{userId} AND is_deleted = 0")
    int updateCurrentModeByUserId(@Param("userId") Long userId, @Param("currentMode") String currentMode);
    
    /**
     * 根据用户ID获取切换次数
     * 
     * @param userId 用户ID
     * @return 切换次数
     */
    @Select("SELECT COALESCE(switch_count, 0) FROM org_user_current_mode WHERE user_id = #{userId} AND is_deleted = 0")
    Integer getSwitchCountByUserId(@Param("userId") Long userId);
    
    /**
     * 批量初始化用户组织模式
     * 
     * @param defaultMode 默认模式
     * @return 影响行数
     */
    @Update("INSERT INTO org_user_current_mode (user_id, current_mode, last_switch_time, switch_count, create_time, update_time, is_deleted) " +
            "SELECT id, #{defaultMode}, NOW(), 0, NOW(), NOW(), 0 FROM base_sys_user " +
            "WHERE id NOT IN (SELECT user_id FROM org_user_current_mode WHERE is_deleted = 0) " +
            "AND is_deleted = 0")
    int batchInitUserMode(@Param("defaultMode") String defaultMode);
    
    /**
     * 清理过期的模式记录
     * 
     * @param expireDays 过期天数
     * @return 影响行数
     */
    @Update("UPDATE org_user_current_mode SET is_deleted = 1, update_time = NOW() " +
            "WHERE last_switch_time < DATE_SUB(NOW(), INTERVAL #{expireDays} DAY) " +
            "AND is_deleted = 0")
    int cleanExpiredModeRecords(@Param("expireDays") int expireDays);
}