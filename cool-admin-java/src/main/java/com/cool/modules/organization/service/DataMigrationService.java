package com.cool.modules.organization.service;

import com.cool.modules.organization.dto.MigrationResultDTO;

/**
 * 数据迁移服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface DataMigrationService {
    
    /**
     * 执行完整的数据迁移
     * 
     * @return 迁移结果
     */
    MigrationResultDTO migrateAllData();
    
    /**
     * 迁移现有用户数据到双维度架构
     * 
     * @return 迁移的用户数量
     */
    int migrateDepartmentUsers();
    
    /**
     * 初始化用户组织形态
     * 
     * @return 初始化的用户数量
     */
    int initializeUserModes();
    
    /**
     * 验证数据迁移的完整性
     * 
     * @return 验证结果
     */
    MigrationResultDTO validateMigration();
    
    /**
     * 回滚数据迁移
     * 
     * @return 是否成功
     */
    boolean rollbackMigration();
    
    /**
     * 获取迁移状态
     * 
     * @return 迁移状态信息
     */
    MigrationResultDTO getMigrationStatus();
}