package com.cool.modules.organization.service;

import cn.hutool.core.lang.Dict;
import com.cool.modules.base.entity.sys.BaseSysMenuEntity;

import java.util.List;
import java.util.Map;

/**
 * 双维度权限服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface DualDimensionPermsService {
    
    /**
     * 获取用户在当前组织形态下的权限菜单
     * 
     * @param adminUserId 用户ID
     * @return 权限菜单信息
     */
    Dict permmenu(Long adminUserId);
    
    /**
     * 获取用户在项目维度下的菜单
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<BaseSysMenuEntity> getProjectMenus(Long userId);
    
    /**
     * 获取用户在项目维度下的权限
     * 
     * @param userId 用户ID
     * @return 权限数组
     */
    String[] getProjectPerms(Long userId);
    
    /**
     * 获取用户在项目维度下的角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getProjectRoleIds(Long userId);
    
    /**
     * 检查用户是否有指定权限
     * 
     * @param userId 用户ID
     * @param permission 权限标识
     * @param context 权限上下文
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permission, Map<String, Object> context);
    
    /**
     * 获取用户在当前组织形态下的菜单
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<String> getUserMenus(Long userId);
    
    /**
     * 根据项目角色代码获取对应的系统角色ID
     * 
     * @param projectRoleCode 项目角色代码
     * @return 系统角色ID
     */
    Long getSystemRoleIdByProjectRole(String projectRoleCode);
    
    /**
     * 刷新用户权限缓存
     * 
     * @param userId 用户ID
     */
    void refreshUserPermissionCache(Long userId);
    
    /**
     * 检查用户是否有项目访问权限
     * 
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return 是否有权限
     */
    boolean hasProjectAccess(Long userId, Long projectId);
    
    /**
     * 获取用户在指定项目中的角色
     * 
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return 角色代码
     */
    String getUserProjectRole(Long userId, Long projectId);
}