package com.cool.modules.organization.service;

import com.cool.core.base.BaseService;
import com.cool.modules.organization.entity.OrganizationAuditLogEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 组织架构审计日志服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface OrganizationAuditLogService extends BaseService<OrganizationAuditLogEntity> {
    
    /**
     * 记录组织形态切换日志
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param fromMode 原组织形态
     * @param toMode 目标组织形态
     * @param reason 切换原因
     * @param request HTTP请求
     * @param success 是否成功
     * @param errorMessage 错误信息（如果失败）
     * @param executionTime 执行时间
     */
    void logModeSwitch(Long userId, String username, OrganizationModeEnum fromMode, 
                      OrganizationModeEnum toMode, String reason, HttpServletRequest request,
                      boolean success, String errorMessage, Long executionTime);
    
    /**
     * 记录权限验证日志
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param operationType 操作类型
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param request HTTP请求
     * @param success 是否成功
     * @param errorMessage 错误信息（如果失败）
     */
    void logPermissionCheck(Long userId, String username, String operationType, 
                           String entityType, Long entityId, HttpServletRequest request,
                           boolean success, String errorMessage);
    
    /**
     * 记录数据操作日志
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param operationType 操作类型
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param beforeData 操作前数据
     * @param afterData 操作后数据
     * @param request HTTP请求
     * @param success 是否成功
     * @param errorMessage 错误信息（如果失败）
     * @param executionTime 执行时间
     */
    void logDataOperation(Long userId, String username, String operationType, 
                         String entityType, Long entityId, String beforeData, String afterData,
                         HttpServletRequest request, boolean success, String errorMessage, Long executionTime);
    
    /**
     * 获取用户操作日志
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 操作日志列表
     */
    List<OrganizationAuditLogEntity> getUserOperationLogs(Long userId, int limit);
    
    /**
     * 获取组织形态切换统计
     * 
     * @param days 统计天数
     * @return 统计结果
     */
    Map<String, Object> getModeSwitchStatistics(int days);
    
    /**
     * 获取权限验证失败统计
     * 
     * @param days 统计天数
     * @return 统计结果
     */
    Map<String, Object> getPermissionFailureStatistics(int days);
    
    /**
     * 清理过期日志
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredLogs(int days);
    
    /**
     * 获取异常操作统计
     * 
     * @param days 统计天数
     * @return 统计结果
     */
    Map<String, Object> getExceptionStatistics(int days);
    
    /**
     * 获取用户活动统计
     * 
     * @param days 统计天数
     * @return 统计结果
     */
    Map<String, Object> getUserActivityStatistics(int days);
    
    /**
     * 获取系统安全事件
     * 
     * @param days 查询天数
     * @return 安全事件列表
     */
    List<OrganizationAuditLogEntity> getSecurityEvents(int days);
    
    /**
     * 根据操作类型查询日志
     * 
     * @param operationType 操作类型
     * @param days 查询天数
     * @param limit 限制数量
     * @return 日志列表
     */
    List<OrganizationAuditLogEntity> getLogsByOperationType(String operationType, int days, int limit);
    
    /**
     * 获取项目相关操作日志
     * 
     * @param projectId 项目ID
     * @param days 查询天数
     * @param limit 限制数量
     * @return 日志列表
     */
    List<OrganizationAuditLogEntity> getProjectOperationLogs(Long projectId, int days, int limit);
}