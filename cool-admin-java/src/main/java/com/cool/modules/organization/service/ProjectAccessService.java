package com.cool.modules.organization.service;

import com.cool.modules.organization.dto.ProjectAccessDTO;
import java.util.List;

/**
 * 项目访问权限服务接口
 */
public interface ProjectAccessService {
    
    /**
     * 获取用户可访问的项目列表
     * @param userId 用户ID
     * @return 项目访问信息列表
     */
    List<ProjectAccessDTO> getUserAccessibleProjects(Long userId);
    
    /**
     * 验证用户是否有项目访问权限
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return 是否有权限
     */
    boolean hasProjectAccess(Long userId, Long projectId);
}