package com.cool.modules.organization.service;

import com.cool.core.base.BaseService;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.lang.Dict;
import java.util.List;

/**
 * 项目信息服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface ProjectInfoService extends BaseService<ProjectInfoEntity> {
    
    /**
     * 根据项目编码获取项目信息
     * 
     * @param projectCode 项目编码
     * @return 项目信息
     */
    ProjectInfoEntity getByProjectCode(String projectCode);
    
    /**
     * 检查项目编码是否存在
     * 
     * @param projectCode 项目编码
     * @param excludeId 排除的项目ID
     * @return 是否存在
     */
    boolean existsByProjectCode(String projectCode, Long excludeId);
    
    /**
     * 根据负责人ID获取项目列表
     * 
     * @param ownerId 负责人ID
     * @return 项目列表
     */
    List<ProjectInfoEntity> getByOwnerId(Long ownerId);
    
    /**
     * 根据创建人ID获取项目列表
     * 
     * @param creatorId 创建人ID
     * @return 项目列表
     */
    List<ProjectInfoEntity> getByCreatorId(Long creatorId);
    
    /**
     * 获取用户参与的项目列表
     * 
     * @param userId 用户ID
     * @return 项目列表
     */
    List<ProjectInfoEntity> getUserProjects(Long userId);
    
    /**
     * 创建项目
     * 
     * @param project 项目信息
     * @return 是否成功
     */
    boolean createProject(ProjectInfoEntity project);
    
    /**
     * 更新项目状态
     * 
     * @param projectId 项目ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Long projectId, Integer status);
    
    /**
     * 批量更新项目状态
     * 
     * @param projectIds 项目ID列表
     * @param status 状态
     * @return 成功更新的数量
     */
    int batchUpdateStatus(List<Long> projectIds, Integer status);
    
    /**
     * 删除项目（软删除）
     * 
     * @param projectId 项目ID
     * @return 是否成功
     */
    boolean deleteProject(Long projectId);
    
    /**
     * 检查用户是否有项目访问权限
     * 
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return 是否有权限
     */
    boolean hasProjectAccess(Long userId, Long projectId);
    
    /**
     * 获取项目统计信息
     * 
     * @param projectId 项目ID
     * @return 统计信息
     */
    ProjectInfoEntity getProjectWithStats(Long projectId);

    /**
     * 导入项目
     * @param file
     * @return
     */
    Dict importProjects(MultipartFile file);
}
