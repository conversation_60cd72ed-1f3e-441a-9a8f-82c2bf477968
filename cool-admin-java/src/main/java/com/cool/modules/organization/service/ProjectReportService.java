package com.cool.modules.organization.service;

import java.util.List;

import cn.hutool.core.lang.Dict;
import com.cool.modules.organization.dto.ChartsDTO;
import com.cool.modules.organization.dto.OverviewDTO;
import com.cool.modules.organization.dto.RankingDTO;

public interface ProjectReportService {
    OverviewDTO overview(Dict params);
    RankingDTO ranking(Dict params);
    ChartsDTO charts(Dict params);
    List<Dict> memberTaskRanking(Dict params);
    // Export method will be handled differently, likely returning a file stream
}
