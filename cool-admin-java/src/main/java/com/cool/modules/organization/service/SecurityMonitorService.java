package com.cool.modules.organization.service;

import java.util.List;
import java.util.Map;

/**
 * 安全监控服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
public interface SecurityMonitorService {
    
    /**
     * 检测异常权限访问
     * 
     * @param userId 用户ID
     * @param hours 检测时间范围（小时）
     * @return 是否存在异常访问
     */
    boolean detectAbnormalPermissionAccess(Long userId, int hours);
    
    /**
     * 检测频繁组织形态切换
     * 
     * @param userId 用户ID
     * @param hours 检测时间范围（小时）
     * @param threshold 阈值
     * @return 是否存在频繁切换
     */
    boolean detectFrequentModeSwitch(Long userId, int hours, int threshold);
    
    /**
     * 获取安全风险用户列表
     * 
     * @param days 检测天数
     * @return 风险用户列表
     */
    List<Map<String, Object>> getRiskUsers(int days);
    
    /**
     * 获取安全事件摘要
     * 
     * @param days 统计天数
     * @return 安全事件摘要
     */
    Map<String, Object> getSecurityEventSummary(int days);
    
    /**
     * 触发安全警报
     * 
     * @param userId 用户ID
     * @param eventType 事件类型
     * @param description 事件描述
     * @param severity 严重程度 (LOW, MEDIUM, HIGH, CRITICAL)
     */
    void triggerSecurityAlert(Long userId, String eventType, String description, String severity);
    
    /**
     * 检查用户是否被锁定
     * 
     * @param userId 用户ID
     * @return 是否被锁定
     */
    boolean isUserLocked(Long userId);
    
    /**
     * 锁定用户
     * 
     * @param userId 用户ID
     * @param reason 锁定原因
     * @param durationMinutes 锁定时长（分钟）
     */
    void lockUser(Long userId, String reason, int durationMinutes);
    
    /**
     * 解锁用户
     * 
     * @param userId 用户ID
     * @param reason 解锁原因
     */
    void unlockUser(Long userId, String reason);
    
    /**
     * 获取用户锁定信息
     * 
     * @param userId 用户ID
     * @return 锁定信息
     */
    Map<String, Object> getUserLockInfo(Long userId);
    
    /**
     * 获取安全风险统计
     * 
     * @param days 统计天数
     * @return 风险统计
     */
    Map<String, Object> getRiskStatistics(int days);
    
    /**
     * 获取高风险用户列表
     * 
     * @param days 查询天数
     * @param limit 限制数量
     * @return 高风险用户列表
     */
    List<Map<String, Object>> getHighRiskUsers(int days, int limit);
    
    /**
     * 获取用户权限失败记录
     * 
     * @param userId 用户ID
     * @param days 查询天数
     * @return 权限失败记录
     */
    List<Map<String, Object>> getUserPermissionFailures(Long userId, int days);
    
    /**
     * 获取系统安全事件概览
     * 
     * @param days 查询天数
     * @return 安全事件概览
     */
    Map<String, Object> getSecurityOverview(int days);
    
    /**
     * 获取实时安全警报
     * 
     * @param level 警报级别
     * @param limit 限制数量
     * @return 安全警报列表
     */
    List<Map<String, Object>> getSecurityAlerts(String level, int limit);
    
    /**
     * 清除安全警报
     * 
     * @param alertId 警报ID
     */
    void clearSecurityAlert(String alertId);
    
    /**
     * 获取IP访问统计
     * 
     * @param days 查询天数
     * @param limit 限制数量
     * @return IP访问统计
     */
    List<Map<String, Object>> getIpStatistics(int days, int limit);
    
    /**
     * 添加IP到黑名单
     * 
     * @param ipAddress IP地址
     * @param reason 封禁原因
     * @param durationHours 封禁时长（小时）
     */
    void addIpToBlacklist(String ipAddress, String reason, int durationHours);
    
    /**
     * 从黑名单移除IP
     * 
     * @param ipAddress IP地址
     */
    void removeIpFromBlacklist(String ipAddress);
    
    /**
     * 获取IP黑名单列表
     * 
     * @return IP黑名单列表
     */
    List<Map<String, Object>> getIpBlacklist();
}