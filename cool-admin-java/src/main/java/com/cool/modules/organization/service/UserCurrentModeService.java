package com.cool.modules.organization.service;

import com.cool.core.base.BaseService;
import com.cool.modules.organization.entity.UserCurrentModeEntity;

/**
 * 用户当前组织模式服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface UserCurrentModeService extends BaseService<UserCurrentModeEntity> {
    
    /**
     * 根据用户ID获取当前模式记录
     * 
     * @param userId 用户ID
     * @return 当前模式记录
     */
    UserCurrentModeEntity getByUserId(Long userId);
    
    /**
     * 根据用户ID获取当前组织模式
     * 
     * @param userId 用户ID
     * @return 当前组织模式
     */
    String getCurrentModeByUserId(Long userId);
    
    /**
     * 更新用户当前组织模式
     * 
     * @param userId 用户ID
     * @param currentMode 当前组织模式
     * @return 是否成功
     */
    boolean updateCurrentModeByUserId(Long userId, String currentMode);
    
    /**
     * 根据用户ID获取切换次数
     * 
     * @param userId 用户ID
     * @return 切换次数
     */
    Integer getSwitchCountByUserId(Long userId);
    
    /**
     * 批量初始化用户组织模式
     * 
     * @param defaultMode 默认模式
     * @return 初始化的用户数量
     */
    int batchInitUserMode(String defaultMode);
    
    /**
     * 清理过期的模式记录
     * 
     * @param expireDays 过期天数
     * @return 清理的记录数量
     */
    int cleanExpiredModeRecords(int expireDays);
    
    /**
     * 创建或更新用户模式记录
     * 
     * @param entity 用户模式实体
     * @return 是否成功
     */
    boolean saveOrUpdateUserMode(UserCurrentModeEntity entity);
}