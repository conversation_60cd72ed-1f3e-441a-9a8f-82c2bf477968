package com.cool.modules.organization.service.impl;

import com.cool.modules.base.entity.sys.BaseSysRoleEntity;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysRoleService;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.organization.dto.MigrationResultDTO;
import com.cool.modules.organization.entity.UserCurrentModeEntity;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.DataMigrationService;
import com.cool.modules.organization.service.UserCurrentModeService;
import com.cool.modules.organization.service.UserOrganizationService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据迁移服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataMigrationServiceImpl implements DataMigrationService {
    
    private final BaseSysUserService baseSysUserService;
    private final BaseSysRoleService baseSysRoleService;
    private final UserOrganizationService userOrganizationService;
    private final UserCurrentModeService userCurrentModeService;
    
    // 迁移状态缓存
    private static final Map<String, MigrationStatus> migrationStatusMap = new ConcurrentHashMap<>();
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MigrationResultDTO migrateAllData() {
        MigrationResultDTO result = new MigrationResultDTO();
        result.setStartTime(new Date());
        result.setDetails(new ArrayList<>());
        result.setErrors(new ArrayList<>());
        
        try {
            log.info("开始执行双维度组织架构数据迁移...");
            migrationStatusMap.put("full", MigrationStatus.IN_PROGRESS);
            
            // 1. 创建全局项目角色
            int createdRoles = createGlobalProjectRoles();
            result.setCreatedRoleCount(createdRoles);
            result.getDetails().add("创建了 " + createdRoles + " 个全局项目角色");
            
            // 2. 迁移现有用户数据
            int migratedUsers = migrateDepartmentUsers();
            result.setMigratedUserCount(migratedUsers);
            result.getDetails().add("迁移了 " + migratedUsers + " 个用户的部门数据");
            
            // 3. 初始化用户组织形态
            int initializedModes = initializeUserModes();
            result.setInitializedModeCount(initializedModes);
            result.getDetails().add("初始化了 " + initializedModes + " 个用户的组织形态");
            
            result.setEndTime(new Date());
            result.setDuration(result.getEndTime().getTime() - result.getStartTime().getTime());
            result.setSuccess(true);
            result.setMessage("数据迁移成功完成");
            result.setStatus("COMPLETED");
            result.setProgress(100);
            
            migrationStatusMap.put("full", MigrationStatus.COMPLETED);
            log.info("双维度组织架构数据迁移完成，耗时: {}ms", result.getDuration());
            
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            result.setSuccess(false);
            result.setMessage("数据迁移失败: " + e.getMessage());
            result.getErrors().add(e.getMessage());
            result.setStatus("FAILED");
            migrationStatusMap.put("full", MigrationStatus.FAILED);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int migrateDepartmentUsers() {
        log.info("开始迁移现有用户的部门数据...");
        
        try {
            // 获取所有有部门的用户
            List<BaseSysUserEntity> users = baseSysUserService.list(
                QueryWrapper.create()
                    .isNotNull(BaseSysUserEntity::getDepartmentId)
                    .eq(BaseSysUserEntity::getStatus, 1));
            
            int migratedCount = 0;
            
            for (BaseSysUserEntity user : users) {
                // 检查是否已经存在部门组织关系
                boolean exists = userOrganizationService.count(
                    QueryWrapper.create()
                        .eq(UserOrganizationEntity::getUserId, user.getId())
                        .eq(UserOrganizationEntity::getOrganizationType, OrganizationModeEnum.DEPARTMENT.getCode())
                        .eq(UserOrganizationEntity::getOrganizationId, user.getDepartmentId())
                ) > 0;
                
                if (!exists) {
                    // 创建部门组织关系
                    UserOrganizationEntity orgRelation = new UserOrganizationEntity();
                    orgRelation.setUserId(user.getId());
                    orgRelation.setOrganizationType(OrganizationModeEnum.DEPARTMENT.getCode());
                    orgRelation.setOrganizationId(user.getDepartmentId());
                    orgRelation.setRoleCode("DEPT_MEMBER"); // 默认部门成员角色
                    orgRelation.setStatus(1);
                    orgRelation.setJoinTime(user.getCreateTime() != null ? user.getCreateTime() : new Date());
                    orgRelation.setAssignerId(1L); // 系统迁移
                    orgRelation.setAssignTime(new Date());
                    orgRelation.setRemark("系统数据迁移自动创建");
                    
                    userOrganizationService.save(orgRelation);
                    migratedCount++;
                }
            }
            
            log.info("成功迁移 {} 个用户的部门数据", migratedCount);
            return migratedCount;
            
        } catch (Exception e) {
            log.error("迁移用户部门数据失败", e);
            throw new RuntimeException("迁移用户部门数据失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int initializeUserModes() {
        log.info("开始初始化用户组织形态...");
        
        try {
            // 批量初始化用户组织模式为部门维度
            int initializedCount = userCurrentModeService.batchInitUserMode(
                OrganizationModeEnum.DEPARTMENT.getCode());
            
            log.info("成功初始化 {} 个用户的组织形态", initializedCount);
            return initializedCount;
            
        } catch (Exception e) {
            log.error("初始化用户组织形态失败", e);
            throw new RuntimeException("初始化用户组织形态失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建全局项目角色
     */
    private int createGlobalProjectRoles() {
        log.info("开始创建全局项目角色...");
        
        int createdCount = 0;
        
        for (GlobalProjectRoleEnum roleEnum : GlobalProjectRoleEnum.values()) {
            BaseSysRoleEntity existingRole = baseSysRoleService.getOne(
                QueryWrapper.create().eq(BaseSysRoleEntity::getLabel, roleEnum.getCode()));
            
            if (existingRole == null) {
                BaseSysRoleEntity role = new BaseSysRoleEntity();
                role.setLabel(roleEnum.getCode());
                role.setName(roleEnum.getName());
                role.setRemark(roleEnum.getDescription());
                role.setUserId(1L); // 系统创建
                role.setRelevance(1); // 设置为系统角色
                
                baseSysRoleService.save(role);
                createdCount++;
                log.info("创建项目角色: {} - {}", roleEnum.getCode(), roleEnum.getName());
            }
        }
        
        log.info("成功创建 {} 个全局项目角色", createdCount);
        return createdCount;
    }
    
    @Override
    public MigrationResultDTO validateMigration() {
        MigrationResultDTO result = new MigrationResultDTO();
        result.setStartTime(new Date());
        result.setDetails(new ArrayList<>());
        result.setErrors(new ArrayList<>());
        
        try {
            log.info("开始验证数据迁移完整性...");
            
            // 1. 验证全局项目角色是否创建完整
            boolean rolesValid = validateGlobalProjectRoles(result);
            
            // 2. 验证用户部门数据迁移
            boolean usersValid = validateUserDepartmentMigration(result);
            
            // 3. 验证用户组织形态初始化
            boolean modesValid = validateUserModeInitialization(result);
            
            result.setValid(rolesValid && usersValid && modesValid);
            result.setSuccess(true);
            result.setMessage("数据迁移验证完成");
            
            if (result.getValid()) {
                result.setValidationDetails("所有数据迁移验证通过");
            } else {
                result.setValidationDetails("部分数据迁移验证失败，请检查详细信息");
            }
            
        } catch (Exception e) {
            log.error("数据一致性验证失败", e);
            result.setValid(false);
            result.setSuccess(false);
            result.setMessage("数据一致性验证失败: " + e.getMessage());
            result.getErrors().add(e.getMessage());
        }
        
        result.setEndTime(new Date());
        result.setDuration(result.getEndTime().getTime() - result.getStartTime().getTime());
        
        return result;
    }
    
    /**
     * 验证全局项目角色
     */
    private boolean validateGlobalProjectRoles(MigrationResultDTO result) {
        int expectedRoleCount = GlobalProjectRoleEnum.values().length;
        int actualRoleCount = 0;
        
        for (GlobalProjectRoleEnum roleEnum : GlobalProjectRoleEnum.values()) {
            BaseSysRoleEntity role = baseSysRoleService.getOne(
                QueryWrapper.create().eq(BaseSysRoleEntity::getLabel, roleEnum.getCode()));
            
            if (role != null) {
                actualRoleCount++;
            } else {
                result.getErrors().add("缺少全局项目角色: " + roleEnum.getCode());
            }
        }
        
        result.getDetails().add("全局项目角色验证: " + actualRoleCount + "/" + expectedRoleCount);
        return actualRoleCount == expectedRoleCount;
    }
    
    /**
     * 验证用户部门数据迁移
     */
    private boolean validateUserDepartmentMigration(MigrationResultDTO result) {
        // 获取有部门的用户数量
        long usersWithDept = baseSysUserService.count(
            QueryWrapper.create()
                .isNotNull(BaseSysUserEntity::getDepartmentId)
                .eq(BaseSysUserEntity::getStatus, 1));
        
        // 获取已迁移的用户组织关系数量
        long migratedRelations = userOrganizationService.count(
            QueryWrapper.create()
                .eq(UserOrganizationEntity::getOrganizationType, OrganizationModeEnum.DEPARTMENT.getCode()));
        
        result.getDetails().add("用户部门数据迁移验证: " + migratedRelations + "/" + usersWithDept);
        
        if (migratedRelations < usersWithDept) {
            result.getErrors().add("部分用户的部门数据未迁移完整");
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证用户组织形态初始化
     */
    private boolean validateUserModeInitialization(MigrationResultDTO result) {
        // 获取活跃用户数量
        long activeUsers = baseSysUserService.count(
            QueryWrapper.create().eq(BaseSysUserEntity::getStatus, 1));
        
        // 获取已初始化组织形态的用户数量
        long initializedModes = userCurrentModeService.count();
        
        result.getDetails().add("用户组织形态初始化验证: " + initializedModes + "/" + activeUsers);
        
        if (initializedModes < activeUsers) {
            result.getErrors().add("部分用户的组织形态未初始化");
            return false;
        }
        
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackMigration() {
        log.info("开始回滚数据迁移...");
        
        try {
            // 1. 删除用户组织关系（部门维度）
            userOrganizationService.remove(
                QueryWrapper.create()
                    .eq(UserOrganizationEntity::getOrganizationType, OrganizationModeEnum.DEPARTMENT.getCode())
                    .eq(UserOrganizationEntity::getRemark, "系统数据迁移自动创建"));
            
            // 2. 删除用户当前组织形态记录
            userCurrentModeService.remove(
                QueryWrapper.create()
                    .eq(UserCurrentModeEntity::getRemark, "系统初始化"));
            
            // 3. 删除全局项目角色（可选，因为可能已经被使用）
            // 这里不删除角色，避免影响已有的权限分配
            
            migrationStatusMap.put("full", MigrationStatus.ROLLBACK);
            log.info("数据迁移回滚完成");
            return true;
            
        } catch (Exception e) {
            log.error("数据迁移回滚失败", e);
            migrationStatusMap.put("full", MigrationStatus.FAILED);
            return false;
        }
    }
    
    @Override
    public MigrationResultDTO getMigrationStatus() {
        MigrationResultDTO result = new MigrationResultDTO();
        
        MigrationStatus status = migrationStatusMap.getOrDefault("full", MigrationStatus.NOT_STARTED);
        
        result.setStatus(status.name());
        result.setSuccess(status == MigrationStatus.COMPLETED);
        
        switch (status) {
            case NOT_STARTED:
                result.setMessage("数据迁移尚未开始");
                result.setProgress(0);
                break;
            case IN_PROGRESS:
                result.setMessage("数据迁移正在进行中");
                result.setProgress(50);
                break;
            case COMPLETED:
                result.setMessage("数据迁移已完成");
                result.setProgress(100);
                break;
            case FAILED:
                result.setMessage("数据迁移失败");
                result.setProgress(0);
                break;
            case ROLLBACK:
                result.setMessage("数据迁移已回滚");
                result.setProgress(0);
                break;
        }
        
        return result;
    }
    
    /**
     * 迁移状态枚举
     */
    private enum MigrationStatus {
        NOT_STARTED,
        IN_PROGRESS,
        COMPLETED,
        FAILED,
        ROLLBACK
    }
}