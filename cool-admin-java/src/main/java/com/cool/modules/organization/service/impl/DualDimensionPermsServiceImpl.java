package com.cool.modules.organization.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.entity.sys.BaseSysMenuEntity;
import com.cool.modules.base.entity.sys.BaseSysRoleEntity;
import com.cool.modules.base.service.sys.BaseSysPermsService;
import com.cool.modules.base.service.sys.BaseSysRoleService;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.DualDimensionPermsService;
import com.cool.modules.organization.service.OrganizationModeService;
import com.cool.modules.organization.service.UserOrganizationService;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 双维度权限服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
public class DualDimensionPermsServiceImpl implements DualDimensionPermsService {
    
    private final BaseSysPermsService baseSysPermsService;
    private final OrganizationModeService organizationModeService;
    private final UserOrganizationService userOrganizationService;
    private final BaseSysRoleService baseSysRoleService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    public DualDimensionPermsServiceImpl(
            @Lazy BaseSysPermsService baseSysPermsService,
            OrganizationModeService organizationModeService,
            UserOrganizationService userOrganizationService,
            BaseSysRoleService baseSysRoleService,
            RedisTemplate<String, Object> redisTemplate) {
        this.baseSysPermsService = baseSysPermsService;
        this.organizationModeService = organizationModeService;
        this.userOrganizationService = userOrganizationService;
        this.baseSysRoleService = baseSysRoleService;
        this.redisTemplate = redisTemplate;
    }
    
    private static final String CACHE_KEY_PREFIX = "dual:perms:";
    private static final int CACHE_EXPIRE_MINUTES = 10;
    
    @Override
    public Dict permmenu(Long adminUserId) {
        if (adminUserId == null) {
            return Dict.create().set("menus", new ArrayList<>()).set("perms", new String[0]);
        }
        
        try {
            // 获取用户当前组织形态
            String currentMode = organizationModeService.getCurrentMode(adminUserId);
            
            // 根据组织形态获取对应的菜单和权限
            List<BaseSysMenuEntity> menus;
            String[] perms;
            
            if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
                // 部门形态：使用现有逻辑
                menus = baseSysPermsService.getMenus(adminUserId);
                perms = baseSysPermsService.getPerms(adminUserId);
            } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
                // 项目形态：获取项目相关菜单和权限
                menus = getProjectMenus(adminUserId);
                perms = getProjectPerms(adminUserId);
            } else {
                // 默认使用部门形态
                menus = baseSysPermsService.getMenus(adminUserId);
                perms = baseSysPermsService.getPerms(adminUserId);
            }
            
            return Dict.create().set("menus", menus).set("perms", perms);
            
        } catch (Exception e) {
            log.error("获取双维度权限菜单失败，用户ID: {}", adminUserId, e);
            // 出错时回退到部门权限
            List<BaseSysMenuEntity> menus = baseSysPermsService.getMenus(adminUserId);
            String[] perms = baseSysPermsService.getPerms(adminUserId);
            return Dict.create().set("menus", menus).set("perms", perms);
        }
    }
    
    @Override
    public List<BaseSysMenuEntity> getProjectMenus(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        try {
            // 获取用户在项目维度的角色ID列表
            List<Long> projectRoleIds = getProjectRoleIds(userId);
            
            if (projectRoleIds.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 使用现有的getMenus方法，但传入项目角色ID
            return baseSysPermsService.getMenus(projectRoleIds.toArray(new Long[0]));
            
        } catch (Exception e) {
            log.error("获取项目菜单失败，用户ID: {}", userId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public String[] getProjectPerms(Long userId) {
        if (userId == null) {
            return new String[0];
        }
        
        try {
            // 获取用户在项目维度的角色ID列表
            List<Long> projectRoleIds = getProjectRoleIds(userId);
            
            if (projectRoleIds.isEmpty()) {
                return new String[0];
            }
            
            // 使用现有的getPerms方法，但传入项目角色ID
            return baseSysPermsService.getPerms(projectRoleIds.toArray(new Long[0]));
            
        } catch (Exception e) {
            log.error("获取项目权限失败，用户ID: {}", userId, e);
            return new String[0];
        }
    }
    
    @Override
    public List<Long> getProjectRoleIds(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "project:roles:" + userId;
        
        try {
            // 先从缓存获取
            @SuppressWarnings("unchecked")
            List<Long> cachedRoleIds = (List<Long>) redisTemplate.opsForValue().get(cacheKey);
            if (cachedRoleIds != null) {
                return cachedRoleIds;
            }
            
            // 获取用户在项目维度的所有角色
            List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
                userId, OrganizationModeEnum.PROJECT.getCode());
            
            // 根据项目角色代码获取对应的系统角色ID
            List<Long> roleIds = projectRoles.stream()
                .filter(role -> role.getStatus() == 1) // 只取启用的角色
                .filter(role -> role.getExpireTime() == null || role.getExpireTime().after(new Date())) // 未过期的角色
                .map(role -> getSystemRoleIdByProjectRole(role.getRoleCode()))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
            
            // 缓存结果
            redisTemplate.opsForValue().set(cacheKey, roleIds, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            return roleIds;
            
        } catch (Exception e) {
            log.error("获取项目角色ID失败，用户ID: {}", userId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public Long getSystemRoleIdByProjectRole(String projectRoleCode) {
        if (StrUtil.isBlank(projectRoleCode)) {
            return null;
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "role:mapping:" + projectRoleCode;
        
        try {
            // 先从缓存获取
            Long cachedRoleId = (Long) redisTemplate.opsForValue().get(cacheKey);
            if (cachedRoleId != null) {
                return cachedRoleId;
            }
            
            // 根据项目角色代码获取对应的系统角色ID
            BaseSysRoleEntity role = baseSysRoleService.getOne(
                QueryWrapper.create().eq(BaseSysRoleEntity::getLabel, projectRoleCode));
            
            Long roleId = role != null ? role.getId() : null;
            
            // 缓存结果
            if (roleId != null) {
                redisTemplate.opsForValue().set(cacheKey, roleId, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            }
            
            return roleId;
            
        } catch (Exception e) {
            log.error("获取系统角色ID失败，项目角色代码: {}", projectRoleCode, e);
            return null;
        }
    }
    
    @Override
    public boolean hasPermission(Long userId, String permission, Map<String, Object> context) {
        if (userId == null || StrUtil.isBlank(permission)) {
            return false;
        }
        
        try {
            // 系统管理员拥有所有权限
            if (isSystemAdmin(userId)) {
                return true;
            }
            
            String currentMode = organizationModeService.getCurrentMode(userId);
            
            if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
                return hasDepartmentPermission(userId, permission, context);
            } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
                return hasProjectPermission(userId, permission, context);
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("权限验证失败，用户ID: {}, 权限: {}", userId, permission, e);
            return false;
        }
    }
    
    private boolean isSystemAdmin(Long userId) {
        try {
            String currentUser = CoolSecurityUtil.getAdminUsername();
            return "admin".equals(currentUser);
        } catch (Exception e) {
            return false;
        }
    }
    
    private boolean hasDepartmentPermission(Long userId, String permission, Map<String, Object> context) {
        // 使用现有的部门权限验证逻辑
        String[] userPerms = baseSysPermsService.getPerms(userId);
        return Arrays.asList(userPerms).contains(permission);
    }
    
    private boolean hasProjectPermission(Long userId, String permission, Map<String, Object> context) {
        Long projectId = context != null ? (Long) context.get("projectId") : null;
        
        if (projectId == null) {
            // 没有项目上下文，检查全局项目权限
            return hasGlobalProjectPermission(userId, permission);
        }
        
        // 有项目上下文，检查项目内权限
        String userRole = getUserProjectRole(userId, projectId);
        if (userRole == null) {
            return false;
        }
        
        return hasRolePermission(userRole, permission);
    }
    
    private boolean hasGlobalProjectPermission(Long userId, String permission) {
        String[] userPerms = getProjectPerms(userId);
        return Arrays.asList(userPerms).contains(permission);
    }
    
    private boolean hasRolePermission(String roleCode, String permission) {
        // 根据角色级别判断权限
        if (GlobalProjectRoleEnum.PROJECT_OWNER.getCode().equals(roleCode)) {
            return true; // 项目负责人拥有所有权限
        } else if (GlobalProjectRoleEnum.PROJECT_ADMIN.getCode().equals(roleCode)) {
            return !permission.contains("delete:project"); // 项目管理员不能删除项目
        } else if (GlobalProjectRoleEnum.PROJECT_MEMBER.getCode().equals(roleCode)) {
            return permission.contains("view") || permission.contains("edit:task"); // 项目成员只能查看和编辑任务
        } else if (GlobalProjectRoleEnum.PROJECT_VIEWER.getCode().equals(roleCode)) {
            return permission.contains("view"); // 项目观察者只能查看
        }
        
        return false;
    }
    
    @Override
    public List<String> getUserMenus(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        try {
            String currentMode = organizationModeService.getCurrentMode(userId);
            
            if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
                return getDepartmentModeMenus(userId);
            } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
                return getProjectModeMenus(userId);
            }
            
            return new ArrayList<>();
            
        } catch (Exception e) {
            log.error("获取用户菜单失败，用户ID: {}", userId, e);
            return new ArrayList<>();
        }
    }
    
    private List<String> getDepartmentModeMenus(Long userId) {
        List<BaseSysMenuEntity> menus = baseSysPermsService.getMenus(userId);
        return menus.stream()
            .filter(menu -> menu.getType() != 2) // 过滤掉按钮权限
            .map(BaseSysMenuEntity::getRouter)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    private List<String> getProjectModeMenus(Long userId) {
        List<BaseSysMenuEntity> menus = getProjectMenus(userId);
        return menus.stream()
            .filter(menu -> menu.getType() != 2) // 过滤掉按钮权限
            .map(BaseSysMenuEntity::getRouter)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    @Override
    public void refreshUserPermissionCache(Long userId) {
        if (userId == null) {
            return;
        }
        
        try {
            // 清理相关缓存
            String[] cacheKeys = {
                CACHE_KEY_PREFIX + "project:roles:" + userId,
                CACHE_KEY_PREFIX + "menus:" + userId,
                CACHE_KEY_PREFIX + "perms:" + userId
            };
            
            for (String key : cacheKeys) {
                redisTemplate.delete(key);
            }
            
            log.debug("已清理用户 {} 的双维度权限缓存", userId);
            
        } catch (Exception e) {
            log.error("清理用户权限缓存失败，用户ID: {}", userId, e);
        }
    }
    
    @Override
    public boolean hasProjectAccess(Long userId, Long projectId) {
        if (userId == null || projectId == null) {
            return false;
        }
        
        try {
            // 检查用户是否是项目成员
            List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
                userId, OrganizationModeEnum.PROJECT.getCode());
            
            return projectRoles.stream()
                .anyMatch(role -> projectId.equals(role.getOrganizationId()) && role.getStatus() == 1);
                
        } catch (Exception e) {
            log.error("检查项目访问权限失败，用户ID: {}, 项目ID: {}", userId, projectId, e);
            return false;
        }
    }
    
    @Override
    public String getUserProjectRole(Long userId, Long projectId) {
        if (userId == null || projectId == null) {
            return null;
        }
        
        try {
            List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
                userId, OrganizationModeEnum.PROJECT.getCode());
            
            return projectRoles.stream()
                .filter(role -> projectId.equals(role.getOrganizationId()) && role.getStatus() == 1)
                .map(UserOrganizationEntity::getRoleCode)
                .findFirst()
                .orElse(null);
                
        } catch (Exception e) {
            log.error("获取用户项目角色失败，用户ID: {}, 项目ID: {}", userId, projectId, e);
            return null;
        }
    }
}