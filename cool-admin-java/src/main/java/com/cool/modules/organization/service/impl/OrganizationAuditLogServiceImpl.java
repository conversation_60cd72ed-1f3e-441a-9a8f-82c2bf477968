package com.cool.modules.organization.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.organization.entity.OrganizationAuditLogEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.mapper.OrganizationAuditLogMapper;
import com.cool.modules.organization.service.OrganizationAuditLogService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组织架构审计日志服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
public class OrganizationAuditLogServiceImpl
        extends BaseServiceImpl<OrganizationAuditLogMapper, OrganizationAuditLogEntity>
        implements OrganizationAuditLogService {

    @Override
    @Async
    public void logModeSwitch(Long userId, String username, OrganizationModeEnum fromMode,
            OrganizationModeEnum toMode, String reason, HttpServletRequest request,
            boolean success, String errorMessage, Long executionTime) {
        try {
            OrganizationAuditLogEntity audit = createBaseLog(userId, username, request);
            audit.setOperationType("MODE_SWITCH");
            audit.setOperationDescription(String.format("组织形态切换: %s -> %s, 原因: %s",
                    fromMode.getName(), toMode.getName(), reason));
            audit.setTargetEntityType("ORGANIZATION_MODE");
            audit.setBeforeData(fromMode.getCode());
            audit.setAfterData(toMode.getCode());
            audit.setOperationResult(success ? "SUCCESS" : "FAILURE");
            audit.setErrorMessage(errorMessage);
            audit.setExecutionTime(executionTime);
            audit.setOrganizationMode(toMode.getCode());

            save(audit);

            log.info("记录组织形态切换日志: 用户={}, {}=>{}, 结果={}",
                    username, fromMode.getName(), toMode.getName(), success ? "成功" : "失败");
        } catch (Exception e) {
            log.error("记录组织形态切换日志失败", e);
        }
    }

    @Override
    @Async
    public void logPermissionCheck(Long userId, String username, String operationType,
            String entityType, Long entityId, HttpServletRequest request,
            boolean success, String errorMessage) {
        try {
            OrganizationAuditLogEntity audit = createBaseLog(userId, username, request);
            audit.setOperationType("PERMISSION_CHECK");
            audit.setOperationDescription(String.format("权限验证: %s %s", operationType, entityType));
            audit.setTargetEntityType(entityType);
            audit.setTargetEntityId(entityId);
            audit.setOperationResult(success ? "SUCCESS" : "FAILURE");
            audit.setErrorMessage(errorMessage);

            save(audit);

            if (!success) {
                log.warn("权限验证失败: 用户={}, 操作={}, 实体={}#{}, 错误={}",
                        username, operationType, entityType, entityId, errorMessage);
            }
        } catch (Exception e) {
            log.error("记录权限验证日志失败", e);
        }
    }

    @Override
    @Async
    public void logDataOperation(Long userId, String username, String operationType,
            String entityType, Long entityId, String beforeData, String afterData,
            HttpServletRequest request, boolean success, String errorMessage, Long executionTime) {
        try {
            OrganizationAuditLogEntity audit = createBaseLog(userId, username, request);
            audit.setOperationType(operationType);
            audit.setOperationDescription(String.format("数据操作: %s %s", operationType, entityType));
            audit.setTargetEntityType(entityType);
            audit.setTargetEntityId(entityId);
            audit.setBeforeData(beforeData);
            audit.setAfterData(afterData);
            audit.setOperationResult(success ? "SUCCESS" : "FAILURE");
            audit.setErrorMessage(errorMessage);
            audit.setExecutionTime(executionTime);

            save(audit);

            log.info("记录数据操作日志: 用户={}, 操作={}, 实体={}#{}, 结果={}",
                    username, operationType, entityType, entityId, success ? "成功" : "失败");
        } catch (Exception e) {
            log.error("记录数据操作日志失败", e);
        }
    }

    @Override
    public List<OrganizationAuditLogEntity> getUserOperationLogs(Long userId, int limit) {
        return list(QueryWrapper.create()
                .eq("userId", userId)
                .orderBy("createTime", false)
                .limit(limit));
    }

    @Override
    public Map<String, Object> getModeSwitchStatistics(int days) {
        Date startDate = DateUtil.offsetDay(new Date(), -days);

        List<OrganizationAuditLogEntity> logs = list(QueryWrapper.create()
                .eq("operationType", "MODE_SWITCH")
                .ge("createTime", startDate));

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalSwitches", logs.size());
        statistics.put("successSwitches",
                logs.stream().mapToLong(l -> "SUCCESS".equals(l.getOperationResult()) ? 1 : 0).sum());
        statistics.put("failureSwitches",
                logs.stream().mapToLong(l -> "FAILURE".equals(l.getOperationResult()) ? 1 : 0).sum());

        // 按天统计
        Map<String, Long> dailyStats = new HashMap<>();
        logs.forEach(log -> {
            String day = DateUtil.formatDate(log.getCreateTime());
            dailyStats.merge(day, 1L, Long::sum);
        });
        statistics.put("dailyStatistics", dailyStats);

        return statistics;
    }

    @Override
    public Map<String, Object> getPermissionFailureStatistics(int days) {
        Date startDate = DateUtil.offsetDay(new Date(), -days);

        List<OrganizationAuditLogEntity> logs = list(QueryWrapper.create()
                .eq("operation_type", "PERMISSION_CHECK")
                .eq("operation_result", "FAILURE")
                .ge("create_time", startDate));

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalFailures", logs.size());

        // 按实体类型统计
        Map<String, Long> entityTypeStats = new HashMap<>();
        logs.forEach(log -> {
            String entityType = log.getTargetEntityType();
            entityTypeStats.merge(entityType, 1L, Long::sum);
        });
        statistics.put("entityTypeStatistics", entityTypeStats);

        // 按用户统计
        Map<String, Long> userStats = new HashMap<>();
        logs.forEach(log -> {
            String username = log.getUsername();
            userStats.merge(username, 1L, Long::sum);
        });
        statistics.put("userStatistics", userStats);

        return statistics;
    }

    @Override
    public int cleanExpiredLogs(int days) {
        Date expireDate = DateUtil.offsetDay(new Date(), -days);

        int count = (int) count(QueryWrapper.create()
                .lt("createTime", expireDate));

        if (count > 0) {
            remove(QueryWrapper.create()
                    .lt("createTime", expireDate));

            log.info("清理过期审计日志: {} 条", count);
        }

        return count;
    }

    @Override
    public Map<String, Object> getExceptionStatistics(int days) {
        Date startDate = DateUtil.offsetDay(new Date(), -days);

        List<OrganizationAuditLogEntity> logs = list(QueryWrapper.create()
                .eq("operation_result", "FAILURE")
                .ge("create_time", startDate));

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalExceptions", logs.size());

        // 按异常类型统计
        Map<String, Long> exceptionTypeStats = new HashMap<>();
        logs.forEach(log -> {
            String operationType = log.getOperationType();
            exceptionTypeStats.merge(operationType, 1L, Long::sum);
        });
        statistics.put("exceptionTypeStatistics", exceptionTypeStats);

        // 按实体类型统计
        Map<String, Long> entityTypeStats = new HashMap<>();
        logs.forEach(log -> {
            String entityType = log.getTargetEntityType();
            if (entityType != null) {
                entityTypeStats.merge(entityType, 1L, Long::sum);
            }
        });
        statistics.put("entityTypeStatistics", entityTypeStats);

        // 按天统计
        Map<String, Long> dailyStats = new HashMap<>();
        logs.forEach(log -> {
            String day = DateUtil.formatDate(log.getCreateTime());
            dailyStats.merge(day, 1L, Long::sum);
        });
        statistics.put("dailyStatistics", dailyStats);

        return statistics;
    }

    @Override
    public Map<String, Object> getUserActivityStatistics(int days) {
        Date startDate = DateUtil.offsetDay(new Date(), -days);

        List<OrganizationAuditLogEntity> logs = list(QueryWrapper.create()
                .ge("createTime", startDate));

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalActivities", logs.size());

        // 按用户统计活动数量
        Map<String, Long> userActivityStats = new HashMap<>();
        logs.forEach(log -> {
            String username = log.getUsername();
            if (username != null) {
                userActivityStats.merge(username, 1L, Long::sum);
            }
        });
        statistics.put("userActivityStatistics", userActivityStats);

        // 按操作类型统计
        Map<String, Long> operationTypeStats = new HashMap<>();
        logs.forEach(log -> {
            String operationType = log.getOperationType();
            operationTypeStats.merge(operationType, 1L, Long::sum);
        });
        statistics.put("operationTypeStatistics", operationTypeStats);

        // 活跃用户数量
        long activeUsers = logs.stream()
                .map(OrganizationAuditLogEntity::getUserId)
                .distinct()
                .count();
        statistics.put("activeUsers", activeUsers);

        return statistics;
    }

    @Override
    public List<OrganizationAuditLogEntity> getSecurityEvents(int days) {
        Date startDate = DateUtil.offsetDay(new Date(), -days);

        return list(QueryWrapper.create()
                .in("operationType", "PERMISSION_DENIED", "DATA_PERMISSION_DENIED",
                        "PROJECT_PERMISSION_DENIED", "MODE_SWITCH_ERROR", "ORGANIZATION_MODE_ERROR")
                .ge("createTime", startDate)
                .orderBy("createTime", false));
    }

    @Override
    public List<OrganizationAuditLogEntity> getLogsByOperationType(String operationType, int days, int limit) {
        Date startDate = DateUtil.offsetDay(new Date(), -days);

        return list(QueryWrapper.create()
                .eq("operationType", operationType)
                .ge("createTime", startDate)
                .orderBy("createTime", false)
                .limit(limit));
    }

    @Override
    public List<OrganizationAuditLogEntity> getProjectOperationLogs(Long projectId, int days, int limit) {
        Date startDate = DateUtil.offsetDay(new Date(), -days);

        return list(QueryWrapper.create()
                .and(wrapper -> {
                    wrapper.eq("target_entity_type", "PROJECT").eq("target_entity_id", projectId);
                })
                .or(wrapper -> {
                    wrapper.eq("project_id", projectId);
                })
                .ge("create_time", startDate)
                .orderBy("create_time", false)
                .limit(limit));
    }

    /**
     * 创建基础日志对象
     */
    private OrganizationAuditLogEntity createBaseLog(Long userId, String username, HttpServletRequest request) {
        OrganizationAuditLogEntity log = new OrganizationAuditLogEntity();
        log.setUserId(userId);
        log.setUsername(username);
        log.setCreateTime(new Date());

        if (request != null) {
            log.setRequestIp(getClientIp(request));
            log.setUserAgent(request.getHeader("User-Agent"));
            log.setRequestUrl(request.getRequestURL().toString());
            log.setRequestMethod(request.getMethod());
        }

        return log;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个
        if (StrUtil.isNotBlank(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }
}