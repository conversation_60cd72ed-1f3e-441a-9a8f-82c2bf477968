package com.cool.modules.organization.service.impl;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;

import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.service.sys.BaseSysPermsService;
import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.cool.modules.organization.dto.OrganizationModeSwitchDTO;
import com.cool.modules.organization.entity.UserCurrentModeEntity;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.exception.OrganizationModeException;
import com.cool.modules.organization.mapper.UserCurrentModeMapper;
import com.cool.modules.organization.service.OrganizationAuditLogService;
import com.cool.modules.organization.service.OrganizationModeService;
import com.cool.modules.organization.service.UserOrganizationService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 组织形态管理服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationModeServiceImpl implements OrganizationModeService {
    
    private final UserCurrentModeMapper userCurrentModeMapper;
    private final UserOrganizationService userOrganizationService;
    private final BaseSysPermsService baseSysPermsService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final OrganizationAuditLogService auditLogService;
    
    private static final String CACHE_KEY_PREFIX = "user:permission:scope:";
    private static final String MODE_CACHE_KEY_PREFIX = "user:current:mode:";
    private static final int CACHE_EXPIRE_MINUTES = 10;
    
    @Override
    public String getCurrentMode(Long userId) {
        if (userId == null) {
            return OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        // 先从缓存获取
        String cacheKey = MODE_CACHE_KEY_PREFIX + userId;
        String cachedMode = (String) redisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotBlank(cachedMode)) {
            return cachedMode;
        }
        
        // 从数据库获取
        String currentMode = userCurrentModeMapper.getCurrentModeByUserId(userId);
        if (StrUtil.isBlank(currentMode)) {
            // 如果没有记录，初始化为部门模式
            initUserMode(userId, OrganizationModeEnum.DEPARTMENT.getCode());
            currentMode = OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, currentMode, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return currentMode;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean switchMode(OrganizationModeSwitchDTO switchDTO) {
        Long userId = switchDTO.getUserId();
        String targetMode = switchDTO.getTargetMode();
        long startTime = System.currentTimeMillis();
        
        // 获取当前模式用于审计日志
        String currentMode = getCurrentMode(userId);
        OrganizationModeEnum fromMode = OrganizationModeEnum.fromCode(currentMode);
        OrganizationModeEnum toMode = OrganizationModeEnum.fromCode(targetMode);
        
        // 获取当前请求信息
        HttpServletRequest request = getCurrentRequest();
        String username = getCurrentUsername();
        
        // 验证目标模式
        if (!isValidMode(targetMode)) {
            String errorMsg = "无效的组织模式: " + targetMode;
            // 记录失败日志
            auditLogService.logModeSwitch(userId, username, fromMode, toMode, 
                    switchDTO.getReason(), request, false, errorMsg, 
                    System.currentTimeMillis() - startTime);
            throw new OrganizationModeException.SwitchException(errorMsg);
        }
        
        // 检查是否可以切换
        if (!switchDTO.getForceSwitch() && !canSwitchToMode(userId, targetMode)) {
            String errorMsg = "用户无权限切换到指定组织模式";
            // 记录失败日志
            auditLogService.logModeSwitch(userId, username, fromMode, toMode, 
                    switchDTO.getReason(), request, false, errorMsg, 
                    System.currentTimeMillis() - startTime);
            throw new OrganizationModeException.PermissionException(errorMsg);
        }
        
        try {
            // 更新数据库
            int result = userCurrentModeMapper.updateCurrentModeByUserId(userId, targetMode);
            if (result == 0) {
                // 如果没有记录，创建新记录
                UserCurrentModeEntity entity = new UserCurrentModeEntity();
                entity.setUserId(userId);
                entity.setCurrentMode(targetMode);
                entity.setLastSwitchTime(new Date());
                entity.setSwitchCount(1);
                entity.setRemark(switchDTO.getReason());
                userCurrentModeMapper.insert(entity);
            } else {
                // 更新切换时间和次数
                UserCurrentModeEntity existing = userCurrentModeMapper.selectOneByQuery(
                    QueryWrapper.create().eq("user_id", userId));
                if (existing != null) {
                    existing.setLastSwitchTime(new Date());
                    existing.setSwitchCount(existing.getSwitchCount() + 1);
                    existing.setRemark(switchDTO.getReason());
                    userCurrentModeMapper.update(existing);
                }
            }
            
            // 清理相关缓存
            refreshUserPermissionCache(userId);
            
            // 记录成功日志
            auditLogService.logModeSwitch(userId, username, fromMode, toMode, 
                    switchDTO.getReason(), request, true, null, 
                    System.currentTimeMillis() - startTime);
            
            log.info("用户 {} 成功切换组织模式到 {}, 原因: {}", userId, targetMode, switchDTO.getReason());
            return true;
            
        } catch (Exception e) {
            // 记录失败日志
            auditLogService.logModeSwitch(userId, username, fromMode, toMode, 
                    switchDTO.getReason(), request, false, e.getMessage(), 
                    System.currentTimeMillis() - startTime);
            
            log.error("用户 {} 切换组织模式失败", userId, e);
            throw new OrganizationModeException.SwitchException("切换组织模式失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean canSwitchToMode(Long userId, String targetMode) {
        if (!isValidMode(targetMode)) {
            return false;
        }
        
        // 系统管理员可以切换到任何模式
        String currentUser = CoolSecurityUtil.getAdminUsername();
        if ("admin".equals(currentUser)) {
            return true;
        }
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(targetMode)) {
            // 所有用户都可以切换到部门模式
            return true;
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(targetMode)) {
            // 检查用户是否参与了任何项目
            List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
                userId, OrganizationModeEnum.PROJECT.getCode());
            return !projectRoles.isEmpty();
        }
        
        return false;
    }
    
    @Override
    public DataPermissionScopeDTO getUserPermissionScope(Long userId) {
        if (userId == null) {
            return new DataPermissionScopeDTO().setIsUnlimited(false);
        }
        
        String cacheKey = CACHE_KEY_PREFIX + userId;
        DataPermissionScopeDTO cachedScope = (DataPermissionScopeDTO) redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedScope != null) {
            return cachedScope;
        }
        
        DataPermissionScopeDTO scope = calculateUserPermissionScope(userId);
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, scope, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return scope;
    }
    
    private DataPermissionScopeDTO calculateUserPermissionScope(Long userId) {
        DataPermissionScopeDTO scope = new DataPermissionScopeDTO();
        scope.setUserId(userId);
        scope.setCacheTimestamp(System.currentTimeMillis());
        
        // 检查是否系统管理员
        String currentUser = CoolSecurityUtil.getAdminUsername();
        boolean isSystemAdmin = "admin".equals(currentUser);
        scope.setIsSystemAdmin(isSystemAdmin);
        scope.setIsUnlimited(isSystemAdmin);
        
        if (isSystemAdmin) {
            scope.setDescription("系统管理员，拥有所有权限");
            return scope;
        }
        
        // 获取当前组织模式
        String currentMode = getCurrentMode(userId);
        scope.setOrganizationMode(currentMode);
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
            // 部门模式：获取部门权限
            Long[] departmentIds = baseSysPermsService.loginDepartmentIds();
            scope.setDepartmentIds(departmentIds != null ? Arrays.asList(departmentIds) : null);
            scope.setDescription("部门模式，可访问 " + (departmentIds != null ? departmentIds.length : 0) + " 个部门");
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
            // 项目模式：获取项目权限
            List<Long> projectIds = userOrganizationService.getUserProjectIds(userId);
            scope.setProjectIds(projectIds);
            scope.setDescription("项目模式，可访问 " + projectIds.size() + " 个项目");
        }
        
        return scope;
    }
    
    @Override
    public void refreshUserPermissionCache(Long userId) {
        if (userId == null) {
            return;
        }
        
        String permissionCacheKey = CACHE_KEY_PREFIX + userId;
        String modeCacheKey = MODE_CACHE_KEY_PREFIX + userId;
        
        redisTemplate.delete(permissionCacheKey);
        redisTemplate.delete(modeCacheKey);
        
        log.debug("已清理用户 {} 的权限缓存", userId);
    }
    
    @Override
    public void clearAllPermissionCache() {
        try {
            // 清理权限范围缓存
            redisTemplate.delete(redisTemplate.keys(CACHE_KEY_PREFIX + "*"));
            // 清理模式缓存
            redisTemplate.delete(redisTemplate.keys(MODE_CACHE_KEY_PREFIX + "*"));
            
            log.info("已清理所有用户权限缓存");
        } catch (Exception e) {
            log.error("清理权限缓存失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initUserMode(Long userId, String defaultMode) {
        if (userId == null) {
            return;
        }
        
        if (!isValidMode(defaultMode)) {
            defaultMode = OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        // 检查是否已存在记录
        UserCurrentModeEntity existing = userCurrentModeMapper.selectOneByQuery(
            QueryWrapper.create().eq("user_id", userId));
        
        if (existing == null) {
            UserCurrentModeEntity entity = new UserCurrentModeEntity();
            entity.setUserId(userId);
            entity.setCurrentMode(defaultMode);
            entity.setLastSwitchTime(new Date());
            entity.setSwitchCount(0);
            entity.setRemark("系统初始化");
            userCurrentModeMapper.insert(entity);
            
            log.debug("为用户 {} 初始化组织模式: {}", userId, defaultMode);
        }
    }
    
    @Override
    public Integer getUserSwitchCount(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        UserCurrentModeEntity entity = userCurrentModeMapper.selectOneByQuery(
            QueryWrapper.create().eq("user_id", userId));
        
        return entity != null ? entity.getSwitchCount() : 0;
    }
    
    @Override
    public boolean isValidMode(String mode) {
        return OrganizationModeEnum.isValidCode(mode);
    }
    
    /**
     * 获取当前HTTP请求
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取当前用户名
     */
    private String getCurrentUsername() {
        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            if (currentUserId != null) {
                // 这里可以通过用户服务获取用户名，暂时返回用户ID
                return "User#" + currentUserId;
            }
            return "Unknown";
        } catch (Exception e) {
            return "Unknown";
        }
    }
}
