package com.cool.modules.organization.service.impl;

import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.organization.dto.ProjectAccessDTO;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.ProjectAccessService;
import com.cool.modules.organization.service.ProjectInfoService;
import com.cool.modules.organization.service.UserOrganizationService;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目访问权限服务实现
 */
@Service
@RequiredArgsConstructor
public class ProjectAccessServiceImpl implements ProjectAccessService {
    
    private final UserOrganizationService userOrganizationService;
    private final ProjectInfoService projectInfoService;
    private final BaseSysUserService baseSysUserService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    // @Cacheable(value = "user:accessible:projects", key = "#userId")
    public List<ProjectAccessDTO> getUserAccessibleProjects(Long userId) {
        // 检查用户是否为超级管理员
        if (baseSysUserService.isSuperAdmin(userId)) {
            // 超级管理员可以访问所有项目
            return getAllProjects(userId);
        }
        
        // 获取用户在项目维度的组织关系
        List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
            userId, OrganizationModeEnum.PROJECT.getCode());
        
        if (projectRoles.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取项目基本信息
        List<Long> projectIds = projectRoles.stream()
            .filter(role -> role.getStatus() == 1)
            .filter(role -> role.getExpireTime() == null || role.getExpireTime().after(new Date()))
            .map(UserOrganizationEntity::getOrganizationId)
            .distinct()
            .collect(Collectors.toList());
        
        if (projectIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<ProjectInfoEntity> projects = projectInfoService.listByIds(projectIds);
        
        // 构建返回数据
        return projects.stream()
            .map(project -> {
                ProjectAccessDTO dto = new ProjectAccessDTO();
                dto.setProjectId(project.getId());
                dto.setProjectName(project.getProjectName());
                dto.setProjectCode(project.getProjectCode());
                
                // 获取用户在该项目中的角色
                String userRole = projectRoles.stream()
                    .filter(role -> role.getOrganizationId().equals(project.getId()))
                    .map(UserOrganizationEntity::getRoleCode)
                    .findFirst()
                    .orElse(null);
                dto.setUserRole(userRole);
                
                // 获取加入时间
                Date joinTime = projectRoles.stream()
                    .filter(role -> role.getOrganizationId().equals(project.getId()))
                    .map(UserOrganizationEntity::getJoinTime)
                    .findFirst()
                    .orElse(null);
                dto.setJoinTime(joinTime);
                
                return dto;
            })
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean hasProjectAccess(Long userId, Long projectId) {
        if (projectId == null) {
            return true; // 不筛选项目时默认有权限
        }
        
        // 超级管理员拥有所有项目的访问权限
        if (baseSysUserService.isSuperAdmin(userId)) {
            return true;
        }
        
        List<ProjectAccessDTO> accessibleProjects = getUserAccessibleProjects(userId);
        return accessibleProjects.stream()
            .anyMatch(project -> project.getProjectId().equals(projectId));
    }
    
    /**
     * 清除用户项目缓存
     */
    public void clearUserProjectCache(Long userId) {
        String cacheKey = "user:accessible:projects:" + userId;
        redisTemplate.delete(cacheKey);
    }
    
    /**
     * 获取所有项目（超级管理员使用）
     * 
     * @param userId 用户ID
     * @return 所有项目的访问信息列表
     */
    private List<ProjectAccessDTO> getAllProjects(Long userId) {
        // 获取所有项目
        List<ProjectInfoEntity> allProjects = projectInfoService.list();
        
        // 构建返回数据
        return allProjects.stream()
            .map(project -> {
                ProjectAccessDTO dto = new ProjectAccessDTO();
                dto.setProjectId(project.getId());
                dto.setProjectName(project.getProjectName());
                dto.setProjectCode(project.getProjectCode());
                dto.setUserRole("admin"); // 设置为超级管理员角色
                dto.setJoinTime(project.getCreateTime()); // 使用项目创建时间作为加入时间
                
                return dto;
            })
            .collect(Collectors.toList());
    }
}