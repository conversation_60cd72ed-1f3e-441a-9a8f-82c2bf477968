package com.cool.modules.organization.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.cool.core.util.CoolSecurityUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.organization.dto.ChartDataPointDTO;
import com.cool.modules.organization.dto.ChartsDTO;
import com.cool.modules.organization.dto.MemberRankDTO;
import com.cool.modules.organization.dto.OverviewDTO;
import com.cool.modules.organization.dto.ProjectRankDTO;
import com.cool.modules.organization.dto.RankingDTO;
import com.cool.modules.organization.dto.TrendChartDataDTO;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.ProjectInfoService;
import com.cool.modules.organization.service.ProjectReportService;
import com.cool.modules.organization.service.UserOrganizationService;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;

import cn.hutool.core.lang.Dict;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectReportServiceImpl implements ProjectReportService {

    private final ProjectInfoService projectInfoService;
    private final TaskInfoService taskInfoService;
    private final TaskExecutionService taskExecutionService;
    private final BaseSysUserService baseSysUserService;
    private final UserOrganizationService userOrganizationService;

    // 在类的开头添加新的方法来获取历史数据
    private OverviewDTO getHistoricalOverview(Date startDate, Date endDate, Long currentUserId, boolean isAdmin, Dict params) {
        OverviewDTO dto = new OverviewDTO();
        
        try {
            // 获取历史时间段内的项目
            List<ProjectInfoEntity> historicalProjects = getUserProjectsByDateRange(currentUserId, isAdmin, startDate, endDate, params);
            int totalProjects = historicalProjects.size();
            
            // 统计历史任务信息
            int totalTasks = 0;
            int completedTasks = 0;
            Set<Long> uniqueMembers = new HashSet<>();
            
            for (ProjectInfoEntity project : historicalProjects) {
                // 统计项目成员
                List<UserOrganizationEntity> members = userOrganizationService.getByOrganizationIdAndType(
                    project.getId(), OrganizationModeEnum.PROJECT.getCode());
                if (members != null) {
                    for (UserOrganizationEntity member : members) {
                        uniqueMembers.add(member.getUserId());
                    }
                }
                
                // 统计历史任务（在指定时间范围内创建的任务）
                List<TaskInfoEntity> projectTasks = taskInfoService.list(
                    com.mybatisflex.core.query.QueryWrapper.create()
                        .eq("project_id", project.getId())
                        .between("create_time", startDate, endDate));
                
                totalTasks += projectTasks.size();
                for (TaskInfoEntity task : projectTasks) {
                    if (task.getTaskStatus() != null && (task.getTaskStatus() == 3 || task.getTaskStatus() == 4)) {
                        completedTasks++;
                    }
                }
            }
            
            int efficiency = totalTasks > 0 ? (int) Math.round((double) completedTasks / totalTasks * 100) : 0;
            
            dto.setTotalProjects(totalProjects);
            dto.setTotalTasks(totalTasks);
            dto.setTotalMembers(uniqueMembers.size());
            dto.setEfficiency(efficiency);
            
        } catch (Exception e) {
            log.error("获取历史概览数据失败", e);
            dto.setTotalProjects(0);
            dto.setTotalTasks(0);
            dto.setTotalMembers(0);
            dto.setEfficiency(0);
        }
        
        return dto;
    }

    private List<ProjectInfoEntity> getUserProjectsByDateRange(Long currentUserId, boolean isAdmin, Date startDate, Date endDate) {
        if (isAdmin) {
            return projectInfoService.list(
                com.mybatisflex.core.query.QueryWrapper.create()
                    .between("create_time", startDate, endDate));
        } else {
            List<UserOrganizationEntity> userOrgs = userOrganizationService.list(
                com.mybatisflex.core.query.QueryWrapper.create()
                    .eq("user_id", currentUserId)
                    .eq("organization_type", OrganizationModeEnum.PROJECT.getCode()));
            
            if (userOrgs.isEmpty()) {
                return new ArrayList<>();
            }
            
            List<Long> projectIds = userOrgs.stream()
                .map(UserOrganizationEntity::getOrganizationId)
                .collect(Collectors.toList());
            
            return projectInfoService.list(
                com.mybatisflex.core.query.QueryWrapper.create()
                    .in("id", projectIds)
                    .between("create_time", startDate, endDate));
        }
    }
    
    /**
     * 获取历史时间段内的项目（支持筛选参数）
     */
    private List<ProjectInfoEntity> getUserProjectsByDateRange(Long userId, boolean isAdmin, Date startDate, Date endDate, Dict params) {
        List<ProjectInfoEntity> allProjects = getUserProjects(userId, isAdmin, params);
        
        // 筛选在指定时间范围内创建的项目
        return allProjects.stream()
            .filter(project -> {
                Date createTime = project.getCreateTime();
                return createTime != null && 
                       !createTime.before(startDate) && 
                       !createTime.after(endDate);
            })
            .collect(Collectors.toList());
    }

    @Override
    public OverviewDTO overview(Dict params) {
        OverviewDTO dto = new OverviewDTO();

        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            String currentUser = CoolSecurityUtil.getAdminUsername();
            boolean isAdmin = "admin".equals(currentUser);

            // 获取当前数据
            List<ProjectInfoEntity> userProjects = getUserProjects(currentUserId, isAdmin, params);
            
            // 处理日期范围筛选
            if (params != null && params.containsKey("dateRange")) {
                Object dateRangeObj = params.get("dateRange");
                if (dateRangeObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> dateRange = (List<String>) dateRangeObj;
                    if (dateRange.size() == 2) {
                        try {
                            Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                            Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                            
                            // 筛选在日期范围内有任务的项目
                            List<Long> projectIds = userProjects.stream()
                                .map(ProjectInfoEntity::getId)
                                .collect(Collectors.toList());
                            
                            List<TaskInfoEntity> tasksInRange = taskInfoService.list(
                                com.mybatisflex.core.query.QueryWrapper.create()
                                    .in("project_id", projectIds)
                                    .between("create_time", startDate, endDate)
                            );
                            
                            Set<Long> projectIdsInRange = tasksInRange.stream()
                                .map(TaskInfoEntity::getProjectId)
                                .collect(Collectors.toSet());
                            
                            userProjects = userProjects.stream()
                                .filter(project -> projectIdsInRange.contains(project.getId()))
                                .collect(Collectors.toList());
                        } catch (Exception e) {
                            log.warn("日期范围解析失败: {}", dateRange, e);
                        }
                    }
                }
            }
            
            int totalProjects = userProjects.size();
            
            // 统计当前任务信息
            int totalTasks = 0;
            int completedTasks = 0;
            Set<Long> uniqueMembers = new HashSet<>();
            
            for (ProjectInfoEntity project : userProjects) {
                // 统计项目成员
                List<UserOrganizationEntity> members = userOrganizationService.getByOrganizationIdAndType(
                    project.getId(), OrganizationModeEnum.PROJECT.getCode());
                if (members != null) {
                    for (UserOrganizationEntity member : members) {
                        uniqueMembers.add(member.getUserId());
                    }
                }
                
                // 统计项目任务
            com.mybatisflex.core.query.QueryWrapper taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                .eq("project_id", project.getId());
            
            // 如果有日期范围，添加任务创建时间筛选
            if (params != null && params.containsKey("dateRange")) {
                Object dateRangeObj = params.get("dateRange");
                if (dateRangeObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> dateRange = (List<String>) dateRangeObj;
                    if (dateRange.size() == 2) {
                        try {
                            Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                            Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                            taskQuery.between("create_time", startDate, endDate);
                        } catch (Exception e) {
                            log.warn("任务日期范围解析失败: {}", dateRange, e);
                        }
                    }
                }
            }
            
            List<TaskInfoEntity> projectTasks = taskInfoService.list(taskQuery);
            
            totalTasks += projectTasks.size();
            for (TaskInfoEntity task : projectTasks) {
                if (task.getTaskStatus() != null && (task.getTaskStatus() == 3 || task.getTaskStatus() == 4)) {
                    completedTasks++;
                }
            }
            }
            
            int efficiency = totalTasks > 0 ? (int) Math.round((double) completedTasks / totalTasks * 100) : 0;
            
            // 设置当前数据
            dto.setTotalProjects(totalProjects);
            dto.setTotalTasks(totalTasks);
            dto.setTotalMembers(uniqueMembers.size());
            dto.setEfficiency(efficiency);
            
            // 计算真实的增长率（与上个月对比）
            Calendar cal = Calendar.getInstance();
            Date currentDate = cal.getTime();
            cal.add(Calendar.MONTH, -1);
            Date lastMonthEnd = cal.getTime();
            cal.setTime(currentDate);
            cal.add(Calendar.MONTH, -1);
            Date lastMonthStart = cal.getTime();
            
            // 获取上个月的数据
            OverviewDTO lastMonthData = getHistoricalOverview(lastMonthStart, lastMonthEnd, currentUserId, isAdmin, params);
            
            // 计算增长率
            dto.setProjectGrowth(calculateGrowthRate(lastMonthData.getTotalProjects(), totalProjects));
            dto.setTaskGrowth(calculateGrowthRate(lastMonthData.getTotalTasks(), totalTasks));
            dto.setMemberGrowth(calculateGrowthRate(lastMonthData.getTotalMembers(), uniqueMembers.size()));
            dto.setEfficiencyGrowth(calculateGrowthRate(lastMonthData.getEfficiency(), efficiency));
            
        } catch (Exception e) {
            log.error("获取项目概览数据失败", e);
            // 返回默认值
            dto.setTotalProjects(0);
            dto.setTotalTasks(0);
            dto.setTotalMembers(0);
            dto.setEfficiency(0);
            dto.setProjectGrowth(0);
            dto.setTaskGrowth(0);
            dto.setMemberGrowth(0);
            dto.setEfficiencyGrowth(0);
        }
        
        return dto;
    }

    private int calculateGrowthRate(int oldValue, int newValue) {
        if (oldValue == 0) {
            return newValue > 0 ? 100 : 0;
        }
        return (int) Math.round(((double) (newValue - oldValue) / oldValue) * 100);
    }

    @Override
    public RankingDTO ranking(Dict params) {
        RankingDTO dto = new RankingDTO();

        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            String currentUser = CoolSecurityUtil.getAdminUsername();
            boolean isAdmin = "admin".equals(currentUser);

            // 获取用户参与的项目
            List<ProjectInfoEntity> userProjects = getUserProjects(currentUserId, isAdmin, params);
            
            // 处理日期范围筛选
            if (params != null && params.containsKey("dateRange")) {
                Object dateRangeObj = params.get("dateRange");
                if (dateRangeObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> dateRange = (List<String>) dateRangeObj;
                    if (dateRange.size() == 2) {
                        try {
                            Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                            Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                            
                            // 筛选在日期范围内有任务的项目
                            List<Long> projectIds = userProjects.stream()
                                .map(ProjectInfoEntity::getId)
                                .collect(Collectors.toList());
                            
                            List<TaskInfoEntity> tasksInRange = taskInfoService.list(
                                com.mybatisflex.core.query.QueryWrapper.create()
                                    .in("project_id", projectIds)
                                    .between("create_time", startDate, endDate)
                            );
                            
                            Set<Long> projectIdsInRange = tasksInRange.stream()
                                .map(TaskInfoEntity::getProjectId)
                                .collect(Collectors.toSet());
                            
                            userProjects = userProjects.stream()
                                .filter(project -> projectIdsInRange.contains(project.getId()))
                                .collect(Collectors.toList());
                        } catch (Exception e) {
                            log.warn("日期范围解析失败: {}", dateRange, e);
                        }
                    }
                }
            }
            
            // 项目完成度排行（根据项目完成的任务数计算）
         List<ProjectRankDTO> projectRanks = new ArrayList<>();
         for (ProjectInfoEntity project : userProjects) {
             ProjectRankDTO rank = new ProjectRankDTO();
             rank.setProjectName(project.getProjectName());
             
             // 计算项目完成度
             com.mybatisflex.core.query.QueryWrapper taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                 .eq("project_id", project.getId());
             
             // 如果有日期范围，添加任务创建时间筛选
             if (params != null && params.containsKey("dateRange")) {
                 Object dateRangeObj = params.get("dateRange");
                 if (dateRangeObj instanceof List) {
                     @SuppressWarnings("unchecked")
                     List<String> dateRange = (List<String>) dateRangeObj;
                     if (dateRange.size() == 2) {
                         try {
                             Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                             Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                             taskQuery.between("create_time", startDate, endDate);
                         } catch (Exception e) {
                             log.warn("任务日期范围解析失败: {}", dateRange, e);
                         }
                     }
                 }
             }
             
             List<TaskInfoEntity> projectTasks = taskInfoService.list(taskQuery);
             
             long totalTasks = projectTasks.size();
             long completedTasks = 0;
             for (TaskInfoEntity task : projectTasks) {
                 if (task.getTaskStatus() != null && (task.getTaskStatus() == 3 || task.getTaskStatus() == 4)) {
                     completedTasks++;
                 }
             }
             
             int progress = totalTasks > 0 ? (int) Math.round((double) completedTasks / totalTasks * 100) : 0;
             rank.setProgress(progress);
             projectRanks.add(rank);
         }
         
         // 按完成率排序并取前10名
         projectRanks.sort((a, b) -> Integer.compare(b.getProgress(), a.getProgress()));
         projectRanks = projectRanks.stream().limit(10).collect(Collectors.toList());
         
         // 设置排名
         for (int i = 0; i < projectRanks.size(); i++) {
             projectRanks.get(i).setRank(i + 1);
         }
            dto.setProjects(projectRanks);
            
            // 成员效率排行（根据成员完成的任务数计算）
         List<MemberRankDTO> memberRanks = new ArrayList<>();
         Set<Long> uniqueMembers = new HashSet<>();
         
         // 收集所有项目成员
         for (ProjectInfoEntity project : userProjects) {
             List<UserOrganizationEntity> members = userOrganizationService.getByOrganizationIdAndType(
                 project.getId(), OrganizationModeEnum.PROJECT.getCode());
             if (members != null) {
                 for (UserOrganizationEntity member : members) {
                     uniqueMembers.add(member.getUserId());
                 }
             }
         }
         
         // 为所有成员生成数据
         for (Long memberId : uniqueMembers) {
             BaseSysUserEntity user = baseSysUserService.getById(memberId);
             if (user != null) {
                 MemberRankDTO memberRank = new MemberRankDTO();
                 memberRank.setMemberName(user.getName());
                 
                 // 统计该成员完成的任务数
                 List<TaskExecutionEntity> executions = taskExecutionService.list(
                     com.mybatisflex.core.query.QueryWrapper.create()
                         .eq("assignee_id", memberId)
                         .eq("execution_status", "COMPLETED"));
                 
                 memberRank.setCompletedTasks(executions.size());
                 
                 // 计算效率分（根据任务完成情况计算）
                 long totalTasks = taskExecutionService.count(
                     com.mybatisflex.core.query.QueryWrapper.create()
                         .eq("assignee_id", memberId));
                 int efficiency = totalTasks > 0 ? (int) Math.round((double) executions.size() / totalTasks * 100) : 0;
                 memberRank.setEfficiency(efficiency);
                 
                 memberRanks.add(memberRank);
             }
         }
         
         // 按效率排序并取前10名
         memberRanks.sort((a, b) -> Integer.compare(b.getEfficiency(), a.getEfficiency()));
         memberRanks = memberRanks.stream().limit(10).collect(Collectors.toList());
         
         // 设置排名
         for (int i = 0; i < memberRanks.size(); i++) {
             memberRanks.get(i).setRank(i + 1);
         }
            dto.setMembers(memberRanks);
            
            // 人员任务排名
            List<Dict> memberTaskRanks = new ArrayList<>();
            int taskRankIndex = 1;
            
            for (Long memberId : uniqueMembers) {
                if (taskRankIndex > 10) break;
                
                BaseSysUserEntity user = baseSysUserService.getById(memberId);
                if (user != null) {
                    Dict memberTaskRank = Dict.create();
                    memberTaskRank.set("rank", taskRankIndex);
                    memberTaskRank.set("memberName", user.getName());
                    
                    // 统计该成员的任务情况
                    com.mybatisflex.core.query.QueryWrapper taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                        .eq("assignee_id", memberId);
                    
                    // 如果有日期范围，添加任务创建时间筛选
                    if (params != null && params.containsKey("dateRange")) {
                        Object dateRangeObj = params.get("dateRange");
                        if (dateRangeObj instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<String> dateRange = (List<String>) dateRangeObj;
                            if (dateRange.size() == 2) {
                                try {
                                    Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                                    Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                                    taskQuery.between("create_time", startDate, endDate);
                                } catch (Exception e) {
                                    log.warn("任务日期范围解析失败: {}", dateRange, e);
                                }
                            }
                        }
                    }
                    
                    List<TaskExecutionEntity> allTasks = taskExecutionService.list(taskQuery);
                    long totalTasks = allTasks.size();
                    long completedTasks = 0;
                    long inProgressTasks = 0;
                    
                    for (TaskExecutionEntity task : allTasks) {
                        if ("COMPLETED".equals(task.getExecutionStatus())) {
                            completedTasks++;
                        } else if ("IN_PROGRESS".equals(task.getExecutionStatus())) {
                            inProgressTasks++;
                        }
                    }
                    
                    int completionRate = totalTasks > 0 ? (int) Math.round((double) completedTasks / totalTasks * 100) : 0;
                    
                    memberTaskRank.set("totalTasks", totalTasks);
                    memberTaskRank.set("completedTasks", completedTasks);
                    memberTaskRank.set("inProgressTasks", inProgressTasks);
                    memberTaskRank.set("completionRate", completionRate);
                    
                    memberTaskRanks.add(memberTaskRank);
                    taskRankIndex++;
                }
            }
            
            // 按完成率排序
            memberTaskRanks.sort((a, b) -> {
                Integer rateA = a.getInt("completionRate");
                Integer rateB = b.getInt("completionRate");
                return rateB.compareTo(rateA); // 降序排列
            });
            
            // 重新设置排名
            for (int i = 0; i < memberTaskRanks.size(); i++) {
                memberTaskRanks.get(i).set("rank", i + 1);
            }
            
            dto.setMemberTasks(memberTaskRanks);
            
        } catch (Exception e) {
            log.error("获取项目排行数据失败", e);
            dto.setProjects(new ArrayList<>());
            dto.setMembers(new ArrayList<>());
            dto.setMemberTasks(new ArrayList<>());
        }
        
        return dto;
    }

    @Override
    public List<Dict> memberTaskRanking(Dict params) {
        List<Dict> memberTaskRanks = new ArrayList<>();
        
        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            String currentUser = CoolSecurityUtil.getAdminUsername();
            boolean isAdmin = "admin".equals(currentUser);

            // 获取用户参与的项目
            List<ProjectInfoEntity> userProjects = getUserProjects(currentUserId, isAdmin, params);
            
            // 处理日期范围筛选
            if (params != null && params.containsKey("dateRange")) {
                Object dateRangeObj = params.get("dateRange");
                if (dateRangeObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> dateRange = (List<String>) dateRangeObj;
                    if (dateRange.size() == 2) {
                        try {
                            Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                            Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                            
                            // 筛选在日期范围内有活动的项目
                            userProjects = userProjects.stream()
                                .filter(project -> {
                                    Date createTime = project.getCreateTime();
                                    return createTime != null && 
                                           !createTime.after(endDate) && 
                                           !createTime.before(startDate);
                                })
                                .collect(Collectors.toList());
                        } catch (Exception e) {
                            log.warn("日期范围解析失败: {}", dateRange, e);
                        }
                    }
                }
            }
            
            // 收集所有项目成员
            Set<Long> uniqueMembers = new HashSet<>();
            for (ProjectInfoEntity project : userProjects) {
                List<UserOrganizationEntity> members = userOrganizationService.getByOrganizationIdAndType(
                    project.getId(), OrganizationModeEnum.PROJECT.getCode());
                if (members != null) {
                    for (UserOrganizationEntity member : members) {
                        uniqueMembers.add(member.getUserId());
                    }
                }
            }
            
            // 生成人员任务排名数据
            int taskRankIndex = 1;
            
            for (Long memberId : uniqueMembers) {
                BaseSysUserEntity user = baseSysUserService.getById(memberId);
                if (user != null) {
                    Dict memberTaskRank = Dict.create();
                    memberTaskRank.set("rank", taskRankIndex);
                    memberTaskRank.set("memberName", user.getName());
                    
                    // 统计该成员的任务情况
                    com.mybatisflex.core.query.QueryWrapper taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                        .eq("assignee_id", memberId);
                    
                    // 如果有日期范围，添加任务创建时间筛选
                    if (params != null && params.containsKey("dateRange")) {
                        Object dateRangeObj = params.get("dateRange");
                        if (dateRangeObj instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<String> dateRange = (List<String>) dateRangeObj;
                            if (dateRange.size() == 2) {
                                try {
                                    Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                                    Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                                    taskQuery.between("create_time", startDate, endDate);
                                } catch (Exception e) {
                                    log.warn("任务日期范围解析失败: {}", dateRange, e);
                                }
                            }
                        }
                    }
                    
                    List<TaskExecutionEntity> allTasks = taskExecutionService.list(taskQuery);
                    int totalTasks = allTasks.size();
                    int completedTasks = 0;
                    int inProgressTasks = 0;
                    
                    for (TaskExecutionEntity task : allTasks) {
                        if ("COMPLETED".equals(task.getExecutionStatus())) {
                            completedTasks++;
                        } else if ("IN_PROGRESS".equals(task.getExecutionStatus())) {
                            inProgressTasks++;
                        }
                    }
                    
                    int completionRate = totalTasks > 0 ? (int) Math.round((double) completedTasks / totalTasks * 100) : 0;
                    
                    memberTaskRank.set("totalTasks", totalTasks);
                    memberTaskRank.set("completedTasks", completedTasks);
                    memberTaskRank.set("inProgressTasks", inProgressTasks);
                    memberTaskRank.set("completionRate", completionRate);
                    
                    memberTaskRanks.add(memberTaskRank);
                    taskRankIndex++;
                }
            }
            
            // 按完成率排序
            memberTaskRanks.sort((a, b) -> {
                Integer rateA = a.getInt("completionRate");
                Integer rateB = b.getInt("completionRate");
                return rateB.compareTo(rateA); // 降序排列
            });
            
            // 重新设置排名
            for (int i = 0; i < memberTaskRanks.size(); i++) {
                memberTaskRanks.get(i).set("rank", i + 1);
            }
            
        } catch (Exception e) {
            log.error("获取人员任务排名数据失败", e);
            memberTaskRanks = new ArrayList<>();
        }
        
        return memberTaskRanks;
    }

    @Override
    public ChartsDTO charts(Dict params) {
        ChartsDTO dto = new ChartsDTO();

        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            String currentUser = CoolSecurityUtil.getAdminUsername();
            boolean isAdmin = "admin".equals(currentUser);

            // 获取用户参与的项目
            List<ProjectInfoEntity> userProjects = getUserProjects(currentUserId, isAdmin, params);
            
            // 处理日期范围筛选
            if (params != null && params.containsKey("dateRange")) {
                Object dateRangeObj = params.get("dateRange");
                if (dateRangeObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> dateRange = (List<String>) dateRangeObj;
                    if (dateRange.size() == 2) {
                        try {
                            Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                            Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                            
                            // 筛选在日期范围内有活动的项目
                            userProjects = userProjects.stream()
                                .filter(project -> {
                                    Date createTime = project.getCreateTime();
                                    return createTime != null && 
                                           !createTime.after(endDate) && 
                                           !createTime.before(startDate);
                                })
                                .collect(Collectors.toList());
                        } catch (Exception e) {
                            log.warn("日期范围解析失败: {}", dateRange, e);
                        }
                    }
                }
            }
            
            // 任务分析
            Integer taskCompleted = 0, taskInProgress = 0, taskPending = 0, taskClosed = 0;
            
            for (ProjectInfoEntity project : userProjects) {
                com.mybatisflex.core.query.QueryWrapper taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                    .eq("project_id", project.getId());
                
                // 如果有日期范围，添加任务创建时间筛选
                if (params != null && params.containsKey("dateRange")) {
                    Object dateRangeObj = params.get("dateRange");
                    if (dateRangeObj instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<String> dateRange = (List<String>) dateRangeObj;
                        if (dateRange.size() == 2) {
                            try {
                                Date startDate = java.sql.Date.valueOf(dateRange.get(0));
                                Date endDate = java.sql.Date.valueOf(dateRange.get(1));
                                taskQuery.between("create_time", startDate, endDate);
                            } catch (Exception e) {
                                log.warn("任务日期范围解析失败: {}", dateRange, e);
                            }
                        }
                    }
                }
                
                List<TaskInfoEntity> projectTasks = taskInfoService.list(taskQuery);
                
                for (TaskInfoEntity task : projectTasks) {
                    if (task.getTaskStatus() == null) {
                        taskPending++;
                    } else {
                        switch (task.getTaskStatus()) {
                            case 0: // 待分配
                                taskPending++;
                                break;
                            case 1: // 待执行
                                taskInProgress++;
                                break;
                            case 2: // 执行中
                                taskInProgress++;
                                break;
                            case 3: // 已完成
                                taskCompleted++;
                                break;
                            case 4: // 已关闭
                                taskClosed++;
                                break;
                            default:
                                taskPending++;
                                break;
                        }
                    }
                }
            }
            
            // 饼图格式数据
            List<ChartDataPointDTO> progress = Arrays.asList(
                new ChartDataPointDTO("已完成", taskCompleted),
                new ChartDataPointDTO("进行中", taskInProgress),
                new ChartDataPointDTO("待处理", taskPending),
                new ChartDataPointDTO("已关闭", taskClosed)
            );
            
            // 柱状图格式数据
            Map<String, Object> barChartData = new HashMap<>();
            barChartData.put("xAxis", Arrays.asList("已完成", "进行中", "待处理", "已关闭"));
            barChartData.put("series", Arrays.asList(taskCompleted, taskInProgress, taskPending, taskClosed));
            
            // 同时返回饼图和柱状图数据
            dto.setProgress(progress);
            dto.setBarChartData(barChartData);
            
            // 任务状态分布数据已移除，因为前端不再需要
            
            // 团队效率趋势 - 使用真实数据
            TrendChartDataDTO trend = generateRealTrendData(userProjects, params);
            dto.setTrend(trend);
            
        } catch (Exception e) {
            log.error("获取图表数据失败", e);
            // 返回默认值
            dto.setProgress(Arrays.asList(
                new ChartDataPointDTO("已完成", 0),
                new ChartDataPointDTO("进行中", 0),
                new ChartDataPointDTO("待处理", 0)
            ));
            
            TrendChartDataDTO defaultTrend = new TrendChartDataDTO();
            defaultTrend.setDates(Arrays.asList("07-01", "07-02", "07-03", "07-04", "07-05", "07-06", "07-07"));
            defaultTrend.setCompleted(Arrays.asList(0, 0, 0, 0, 0, 0, 0));
            defaultTrend.setCreated(Arrays.asList(0, 0, 0, 0, 0, 0, 0));
            defaultTrend.setEfficiency(Arrays.asList(0, 0, 0, 0, 0, 0, 0));
            dto.setTrend(defaultTrend);
        }
        
        return dto;
    }

    private TrendChartDataDTO generateRealTrendData(List<ProjectInfoEntity> userProjects, Dict params) {
        TrendChartDataDTO trend = new TrendChartDataDTO();
        
        if (userProjects.isEmpty()) {
            // 返回空数据
            trend.setDates(Arrays.asList("今日"));
            trend.setCompleted(Arrays.asList(0));
            trend.setCreated(Arrays.asList(0));
            trend.setEfficiency(Arrays.asList(0));
            return trend;
        }
        
        // 获取日期范围参数
        String period = "month";
        if (params != null) {
            String periodParam = params.getStr("trendPeriod");
            if (periodParam != null && !periodParam.isEmpty()) {
                period = periodParam;
            }
        }
        int days = "week".equals(period) ? 7 : "month".equals(period) ? 30 : 90;
        
        List<String> dates = new ArrayList<>();
        List<Integer> completedData = new ArrayList<>();
        List<Integer> createdData = new ArrayList<>();
        List<Integer> efficiencyData = new ArrayList<>();
        
        // 收集所有项目ID
        List<Long> projectIds = userProjects.stream()
            .map(ProjectInfoEntity::getId)
            .collect(Collectors.toList());
        
        // 生成日期列表并批量查询数据
        LocalDate today = LocalDate.now();
        Date startDate = Date.from(today.minusDays(days - 1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        // 如果有指定的日期范围参数，使用指定范围
        if (params != null && params.containsKey("dateRange")) {
            Object dateRangeObj = params.get("dateRange");
            if (dateRangeObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> dateRange = (List<String>) dateRangeObj;
                if (dateRange.size() == 2) {
                    try {
                        startDate = java.sql.Date.valueOf(dateRange.get(0));
                        endDate = java.sql.Date.valueOf(dateRange.get(1));
                        
                        // 更新today为结束日期
                        today = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        // 重新计算days为指定日期范围内的天数
                        days = (int) ChronoUnit.DAYS.between(startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), today) + 1;
                    } catch (Exception e) {
                        log.warn("趋势数据日期范围解析失败: {}", dateRange, e);
                    }
                }
            }
        }
        
        // 批量查询所有相关任务
        List<TaskInfoEntity> allTasks = taskInfoService.list(
            com.mybatisflex.core.query.QueryWrapper.create()
                .in("project_id", projectIds)
                .between("create_time", startDate, endDate));
        
        List<TaskInfoEntity> completedTasks = taskInfoService.list(
            com.mybatisflex.core.query.QueryWrapper.create()
                .in("project_id", projectIds)
                .in("task_status", Arrays.asList(3, 4))
                .between("update_time", startDate, endDate));
        
        // 按日期分组统计
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            dates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            
            Date dayStart = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date dayEnd = Date.from(date.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            
            // 统计当天创建的任务
            long createdCount = allTasks.stream()
                .filter(task -> task.getCreateTime() != null && 
                               !task.getCreateTime().before(dayStart) && 
                               task.getCreateTime().before(dayEnd))
                .count();
            
            // 统计当天完成的任务
            long completedCount = completedTasks.stream()
                .filter(task -> task.getUpdateTime() != null && 
                               !task.getUpdateTime().before(dayStart) && 
                               task.getUpdateTime().before(dayEnd))
                .count();
            
            // 统计截止当天的总任务数
            long totalTasks = allTasks.stream()
                .filter(task -> task.getCreateTime() != null && 
                               task.getCreateTime().before(dayEnd))
                .count();
            
            // 计算当天的效率
            int efficiency = totalTasks > 0 ? (int) Math.round((double) completedCount / totalTasks * 100) : 0;
            
            completedData.add(Math.toIntExact(completedCount));
            createdData.add(Math.toIntExact(createdCount));
            efficiencyData.add(Math.min(100, efficiency));
        }
        
        trend.setDates(dates);
        trend.setCompleted(completedData);
        trend.setCreated(createdData);
        trend.setEfficiency(efficiencyData);
        
        return trend;
    }
    
    /**
     * 获取用户参与的项目
     */
    private List<ProjectInfoEntity> getUserProjects(Long userId, boolean isAdmin) {
        if (isAdmin) {
            // 管理员可以看到所有项目
            return projectInfoService.list();
        } else {
            // 普通用户只能看到自己参与的项目
            return projectInfoService.getUserProjects(userId);
        }
    }

    /**
     * 获取用户参与的项目（支持筛选参数）
     */
    private List<ProjectInfoEntity> getUserProjects(Long userId, boolean isAdmin, Dict params) {
        List<ProjectInfoEntity> allProjects = getUserProjects(userId, isAdmin);

        // 如果指定了项目ID筛选
        if (params != null && params.containsKey("projectIds")) {
            Object projectIdsObj = params.get("projectIds");
            if (projectIdsObj != null) {
                List<Long> projectIds = new ArrayList<>();
                
                // 处理不同类型的projectIds参数
                if (projectIdsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<?> rawList = (List<?>) projectIdsObj;
                    for (Object item : rawList) {
                        if (item instanceof Number) {
                            projectIds.add(((Number) item).longValue());
                        } else if (item instanceof String) {
                            try {
                                projectIds.add(Long.parseLong((String) item));
                            } catch (NumberFormatException e) {
                                log.warn("无法解析项目ID: {}", item);
                            }
                        }
                    }
                } else if (projectIdsObj instanceof String) {
                    // 处理逗号分隔的字符串
                    String[] ids = ((String) projectIdsObj).split(",");
                    for (String id : ids) {
                        try {
                            projectIds.add(Long.parseLong(id.trim()));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析项目ID: {}", id);
                        }
                    }
                }
                
                if (!projectIds.isEmpty()) {
                    log.info("筛选项目ID: {}", projectIds);
                    return allProjects.stream()
                        .filter(project -> projectIds.contains(project.getId()))
                        .collect(Collectors.toList());
                }
            }
        }

        return allProjects;
    }
}
