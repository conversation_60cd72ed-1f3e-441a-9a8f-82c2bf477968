package com.cool.modules.organization.service.impl;

import cn.hutool.core.date.DateUtil;
import com.cool.modules.organization.entity.OrganizationAuditLogEntity;
import com.cool.modules.organization.service.OrganizationAuditLogService;
import com.cool.modules.organization.service.SecurityMonitorService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 安全监控服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityMonitorServiceImpl implements SecurityMonitorService {
    
    private final OrganizationAuditLogService auditLogService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String USER_LOCK_KEY_PREFIX = "security:user_lock:";
    private static final String SECURITY_ALERT_KEY_PREFIX = "security:alert:";
    
    @Override
    public boolean detectAbnormalPermissionAccess(Long userId, int hours) {
        Date startTime = DateUtil.offsetHour(new Date(), -hours);
        
        List<OrganizationAuditLogEntity> logs = auditLogService.list(QueryWrapper.create()
                .eq("user_id", userId)
                .eq("operation_result", "FAILURE")
                .in("operation_type", "PERMISSION_DENIED", "DATA_PERMISSION_DENIED", "PROJECT_PERMISSION_DENIED")
                .ge("create_time", startTime));
        
        // 如果在指定时间内有超过5次权限失败，认为是异常访问
        boolean isAbnormal = logs.size() > 5;
        
        if (isAbnormal) {
            log.warn("检测到用户 {} 在 {} 小时内有 {} 次权限访问失败", userId, hours, logs.size());
            triggerSecurityAlert(userId, "ABNORMAL_PERMISSION_ACCESS", 
                    String.format("用户在%d小时内有%d次权限访问失败", hours, logs.size()), "MEDIUM");
        }
        
        return isAbnormal;
    }
    
    @Override
    public boolean detectFrequentModeSwitch(Long userId, int hours, int threshold) {
        Date startTime = DateUtil.offsetHour(new Date(), -hours);
        
        List<OrganizationAuditLogEntity> logs = auditLogService.list(QueryWrapper.create()
                .eq("userId", userId)
                .eq("operationType", "MODE_SWITCH")
                .ge("createTime", startTime));
        
        boolean isFrequent = logs.size() > threshold;
        
        if (isFrequent) {
            log.warn("检测到用户 {} 在 {} 小时内切换组织形态 {} 次，超过阈值 {}", 
                    userId, hours, logs.size(), threshold);
            triggerSecurityAlert(userId, "FREQUENT_MODE_SWITCH", 
                    String.format("用户在%d小时内切换组织形态%d次，超过阈值%d", hours, logs.size(), threshold), "LOW");
        }
        
        return isFrequent;
    }
    
    @Override
    public List<Map<String, Object>> getRiskUsers(int days) {
        Date startTime = DateUtil.offsetDay(new Date(), -days);
        
        List<OrganizationAuditLogEntity> failureLogs = auditLogService.list(QueryWrapper.create()
                .eq("operation_result", "FAILURE")
                .ge("create_time", startTime));
        
        // 按用户统计失败次数
        Map<Long, List<OrganizationAuditLogEntity>> userFailureMap = failureLogs.stream()
                .filter(log -> log.getUserId() != null)
                .collect(Collectors.groupingBy(OrganizationAuditLogEntity::getUserId));
        
        List<Map<String, Object>> riskUsers = new ArrayList<>();
        
        userFailureMap.forEach((userId, logs) -> {
            if (logs.size() > 10) { // 失败次数超过10次的用户
                Map<String, Object> riskUser = new HashMap<>();
                riskUser.put("userId", userId);
                riskUser.put("username", logs.get(0).getUsername());
                riskUser.put("failureCount", logs.size());
                
                // 统计失败类型
                Map<String, Long> failureTypes = logs.stream()
                        .collect(Collectors.groupingBy(
                                OrganizationAuditLogEntity::getOperationType,
                                Collectors.counting()));
                riskUser.put("failureTypes", failureTypes);
                
                // 计算风险等级
                String riskLevel = calculateRiskLevel(logs.size(), failureTypes);
                riskUser.put("riskLevel", riskLevel);
                
                riskUsers.add(riskUser);
            }
        });
        
        // 按失败次数排序
        riskUsers.sort((a, b) -> Integer.compare(
                (Integer) b.get("failureCount"), 
                (Integer) a.get("failureCount")));
        
        return riskUsers;
    }
    
    @Override
    public Map<String, Object> getSecurityEventSummary(int days) {
        Date startTime = DateUtil.offsetDay(new Date(), -days);
        
        List<OrganizationAuditLogEntity> securityEvents = auditLogService.list(QueryWrapper.create()
                .in("operationType", "PERMISSION_DENIED", "DATA_PERMISSION_DENIED", 
                    "PROJECT_PERMISSION_DENIED", "MODE_SWITCH_ERROR", "ORGANIZATION_MODE_ERROR")
                .ge("createTime", startTime));
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalEvents", securityEvents.size());
        
        // 按事件类型统计
        Map<String, Long> eventTypeStats = securityEvents.stream()
                .collect(Collectors.groupingBy(
                        OrganizationAuditLogEntity::getOperationType,
                        Collectors.counting()));
        summary.put("eventTypeStatistics", eventTypeStats);
        
        // 按天统计
        Map<String, Long> dailyStats = securityEvents.stream()
                .collect(Collectors.groupingBy(
                        log -> DateUtil.formatDate(log.getCreateTime()),
                        Collectors.counting()));
        summary.put("dailyStatistics", dailyStats);
        
        // 受影响用户数
        long affectedUsers = securityEvents.stream()
                .map(OrganizationAuditLogEntity::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        summary.put("affectedUsers", affectedUsers);
        
        // 高风险事件数量
        long highRiskEvents = securityEvents.stream()
                .filter(log -> "PERMISSION_DENIED".equals(log.getOperationType()) || 
                              "DATA_PERMISSION_DENIED".equals(log.getOperationType()))
                .count();
        summary.put("highRiskEvents", highRiskEvents);
        
        return summary;
    }
    
    @Override
    public void triggerSecurityAlert(Long userId, String eventType, String description, String severity) {
        try {
            Map<String, Object> alert = new HashMap<>();
            alert.put("userId", userId);
            alert.put("eventType", eventType);
            alert.put("description", description);
            alert.put("severity", severity);
            alert.put("timestamp", System.currentTimeMillis());
            
            String alertKey = SECURITY_ALERT_KEY_PREFIX + eventType + ":" + userId + ":" + System.currentTimeMillis();
            
            // 将警报存储到Redis，保留24小时
            redisTemplate.opsForValue().set(alertKey, alert, 24, TimeUnit.HOURS);
            
            log.warn("安全警报触发 - 用户: {}, 事件类型: {}, 严重程度: {}, 描述: {}", 
                    userId, eventType, severity, description);
            
            // 根据严重程度决定是否需要锁定用户
            if ("CRITICAL".equals(severity)) {
                lockUser(userId, "触发严重安全警报: " + description, 60); // 锁定1小时
            } else if ("HIGH".equals(severity)) {
                lockUser(userId, "触发高级安全警报: " + description, 30); // 锁定30分钟
            }
            
        } catch (Exception e) {
            log.error("触发安全警报失败", e);
        }
    }
    
    @Override
    public boolean isUserLocked(Long userId) {
        String lockKey = USER_LOCK_KEY_PREFIX + userId;
        return Boolean.TRUE.equals(redisTemplate.hasKey(lockKey));
    }
    
    @Override
    public void lockUser(Long userId, String reason, int durationMinutes) {
        try {
            String lockKey = USER_LOCK_KEY_PREFIX + userId;
            
            Map<String, Object> lockInfo = new HashMap<>();
            lockInfo.put("userId", userId);
            lockInfo.put("reason", reason);
            lockInfo.put("lockTime", System.currentTimeMillis());
            lockInfo.put("durationMinutes", durationMinutes);
            
            redisTemplate.opsForValue().set(lockKey, lockInfo, durationMinutes, TimeUnit.MINUTES);
            
            log.warn("用户 {} 已被锁定 {} 分钟，原因: {}", userId, durationMinutes, reason);
            
            // 记录锁定操作到审计日志
            auditLogService.logDataOperation(null, "SYSTEM", "USER_LOCK", 
                    "USER", userId, null, reason, null, true, null, null);
            
        } catch (Exception e) {
            log.error("锁定用户失败", e);
        }
    }
    
    @Override
    public void unlockUser(Long userId, String reason) {
        try {
            String lockKey = USER_LOCK_KEY_PREFIX + userId;
            
            if (Boolean.TRUE.equals(redisTemplate.hasKey(lockKey))) {
                redisTemplate.delete(lockKey);
                
                log.info("用户 {} 已被解锁，原因: {}", userId, reason);
                
                // 记录解锁操作到审计日志
                auditLogService.logDataOperation(null, "SYSTEM", "USER_UNLOCK", 
                        "USER", userId, null, reason, null, true, null, null);
            }
            
        } catch (Exception e) {
            log.error("解锁用户失败", e);
        }
    }
    
    @Override
    public Map<String, Object> getUserLockInfo(Long userId) {
        try {
            String lockKey = USER_LOCK_KEY_PREFIX + userId;
            Object lockInfo = redisTemplate.opsForValue().get(lockKey);
            
            if (lockInfo != null) {
                return (Map<String, Object>) lockInfo;
            }
            
            return Map.of("locked", false);
        } catch (Exception e) {
            log.error("获取用户锁定信息失败", e);
            return Map.of("locked", false, "error", e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> getRiskStatistics(int days) {
        // 实现风险统计逻辑
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalRiskEvents", 0);
        statistics.put("highRiskUsers", 0);
        statistics.put("blockedIps", 0);
        statistics.put("permissionFailures", 0);
        return statistics;
    }
    
    @Override
    public List<Map<String, Object>> getHighRiskUsers(int days, int limit) {
        // 实现高风险用户查询逻辑
        return List.of();
    }
    
    @Override
    public List<Map<String, Object>> getUserPermissionFailures(Long userId, int days) {
        // 实现用户权限失败记录查询逻辑
        return List.of();
    }
    
    @Override
    public Map<String, Object> getSecurityOverview(int days) {
        // 实现安全事件概览逻辑
        Map<String, Object> overview = new HashMap<>();
        overview.put("totalEvents", 0);
        overview.put("criticalEvents", 0);
        overview.put("lockedUsers", 0);
        overview.put("blockedIps", 0);
        return overview;
    }
    
    @Override
    public List<Map<String, Object>> getSecurityAlerts(String level, int limit) {
        // 实现安全警报查询逻辑
        return List.of();
    }
    
    @Override
    public void clearSecurityAlert(String alertId) {
        // 实现清除安全警报逻辑
        log.info("清除安全警报: {}", alertId);
    }
    
    @Override
    public List<Map<String, Object>> getIpStatistics(int days, int limit) {
        // 实现IP访问统计逻辑
        return List.of();
    }
    
    @Override
    public void addIpToBlacklist(String ipAddress, String reason, int durationHours) {
        try {
            String blacklistKey = "security:ip_blacklist:" + ipAddress;
            Map<String, Object> blacklistInfo = new HashMap<>();
            blacklistInfo.put("reason", reason);
            blacklistInfo.put("addTime", System.currentTimeMillis());
            blacklistInfo.put("durationHours", durationHours);
            
            redisTemplate.opsForValue().set(blacklistKey, blacklistInfo, durationHours, TimeUnit.HOURS);
            
            log.warn("IP {} 已添加到黑名单，原因: {}, 时长: {} 小时", ipAddress, reason, durationHours);
        } catch (Exception e) {
            log.error("添加IP到黑名单失败", e);
        }
    }
    
    @Override
    public void removeIpFromBlacklist(String ipAddress) {
        try {
            String blacklistKey = "security:ip_blacklist:" + ipAddress;
            redisTemplate.delete(blacklistKey);
            
            log.info("IP {} 已从黑名单移除", ipAddress);
        } catch (Exception e) {
            log.error("从黑名单移除IP失败", e);
        }
    }
    
    @Override
    public List<Map<String, Object>> getIpBlacklist() {
        // 实现IP黑名单查询逻辑
        return List.of();
    }
    
    /**
     * 计算风险等级
     */
    private String calculateRiskLevel(int failureCount, Map<String, Long> failureTypes) {
        if (failureCount > 50) {
            return "CRITICAL";
        } else if (failureCount > 30) {
            return "HIGH";
        } else if (failureCount > 15) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }
}