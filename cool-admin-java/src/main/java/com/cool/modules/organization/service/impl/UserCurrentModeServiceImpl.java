package com.cool.modules.organization.service.impl;

import cn.hutool.core.util.StrUtil;
import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.organization.entity.UserCurrentModeEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.mapper.UserCurrentModeMapper;
import com.cool.modules.organization.service.UserCurrentModeService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 用户当前组织模式服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserCurrentModeServiceImpl extends BaseServiceImpl<UserCurrentModeMapper, UserCurrentModeEntity> 
        implements UserCurrentModeService {
    
    @Override
    public UserCurrentModeEntity getByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        
        return getOne(QueryWrapper.create()
                .eq(UserCurrentModeEntity::getUserId, userId)
                .orderBy(UserCurrentModeEntity::getUpdateTime, false));
    }
    
    @Override
    public String getCurrentModeByUserId(Long userId) {
        if (userId == null) {
            return OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        String currentMode = mapper.getCurrentModeByUserId(userId);
        return StrUtil.isNotBlank(currentMode) ? currentMode : OrganizationModeEnum.DEPARTMENT.getCode();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCurrentModeByUserId(Long userId, String currentMode) {
        if (userId == null || StrUtil.isBlank(currentMode)) {
            return false;
        }
        
        try {
            int result = mapper.updateCurrentModeByUserId(userId, currentMode);
            if (result > 0) {
                log.debug("用户 {} 组织模式更新为 {}", userId, currentMode);
                return true;
            }
            
            // 如果更新失败，可能是记录不存在，尝试创建新记录
            UserCurrentModeEntity entity = new UserCurrentModeEntity();
            entity.setUserId(userId);
            entity.setCurrentMode(currentMode);
            entity.setLastSwitchTime(new Date());
            entity.setSwitchCount(1);
            entity.setRemark("系统自动创建");
            
            boolean saved = save(entity);
            if (saved) {
                log.debug("为用户 {} 创建组织模式记录: {}", userId, currentMode);
            }
            return saved;
            
        } catch (Exception e) {
            log.error("更新用户 {} 组织模式失败", userId, e);
            return false;
        }
    }
    
    @Override
    public Integer getSwitchCountByUserId(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        Integer count = mapper.getSwitchCountByUserId(userId);
        return count != null ? count : 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInitUserMode(String defaultMode) {
        if (StrUtil.isBlank(defaultMode)) {
            defaultMode = OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        try {
            int result = mapper.batchInitUserMode(defaultMode);
            log.info("批量初始化用户组织模式完成，影响 {} 个用户", result);
            return result;
        } catch (Exception e) {
            log.error("批量初始化用户组织模式失败", e);
            return 0;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredModeRecords(int expireDays) {
        if (expireDays <= 0) {
            expireDays = 90; // 默认清理90天前的记录
        }
        
        try {
            int result = mapper.cleanExpiredModeRecords(expireDays);
            log.info("清理过期组织模式记录完成，清理 {} 条记录", result);
            return result;
        } catch (Exception e) {
            log.error("清理过期组织模式记录失败", e);
            return 0;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateUserMode(UserCurrentModeEntity entity) {
        if (entity == null || entity.getUserId() == null) {
            return false;
        }
        
        try {
            // 检查是否已存在记录
            UserCurrentModeEntity existing = getByUserId(entity.getUserId());
            
            if (existing != null) {
                // 更新现有记录
                existing.setCurrentMode(entity.getCurrentMode());
                existing.setLastSwitchTime(entity.getLastSwitchTime());
                existing.setSwitchCount(existing.getSwitchCount() + 1);
                existing.setReason(entity.getReason());
                existing.setIpAddress(entity.getIpAddress());
                existing.setUserAgent(entity.getUserAgent());
                existing.setForceSwitch(entity.getForceSwitch());
                existing.setSwitchStatus(entity.getSwitchStatus());
                existing.setErrorMessage(entity.getErrorMessage());
                existing.setRemark(entity.getRemark());
                
                return updateById(existing);
            } else {
                // 创建新记录
                entity.setSwitchCount(1);
                if (entity.getLastSwitchTime() == null) {
                    entity.setLastSwitchTime(new Date());
                }
                if (entity.getSwitchStatus() == null) {
                    entity.setSwitchStatus(1);
                }
                
                return save(entity);
            }
        } catch (Exception e) {
            log.error("保存或更新用户组织模式记录失败", e);
            return false;
        }
    }
}