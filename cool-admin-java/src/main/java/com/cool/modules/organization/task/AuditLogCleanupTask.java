package com.cool.modules.organization.task;

import com.cool.modules.organization.service.OrganizationAuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 审计日志清理定时任务
 * 
 * <AUTHOR> Admin
 * @since 2025-01-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "cool.organization.audit.cleanup.enabled", havingValue = "true", matchIfMissing = true)
public class AuditLogCleanupTask {
    
    private final OrganizationAuditLogService auditLogService;
    
    /**
     * 每天凌晨2点执行审计日志清理
     * 默认保留90天的日志
     */
    @Scheduled(cron = "${cool.organization.audit.cleanup.cron:0 0 2 * * ?}")
    public void cleanupExpiredLogs() {
        try {
            log.info("开始执行审计日志清理任务");
            
            // 从配置中获取保留天数，默认90天
            int retentionDays = getRetentionDays();
            
            int cleanedCount = auditLogService.cleanExpiredLogs(retentionDays);
            
            if (cleanedCount > 0) {
                log.info("审计日志清理任务完成，清理了 {} 条过期日志，保留天数: {}", cleanedCount, retentionDays);
            } else {
                log.info("审计日志清理任务完成，没有需要清理的过期日志，保留天数: {}", retentionDays);
            }
            
        } catch (Exception e) {
            log.error("审计日志清理任务执行失败", e);
        }
    }
    
    /**
     * 获取日志保留天数配置
     */
    private int getRetentionDays() {
        // 可以从配置文件中读取，这里先使用默认值
        String retentionDaysStr = System.getProperty("cool.organization.audit.retention.days", "90");
        try {
            return Integer.parseInt(retentionDaysStr);
        } catch (NumberFormatException e) {
            log.warn("审计日志保留天数配置无效: {}, 使用默认值90天", retentionDaysStr);
            return 90;
        }
    }
}