package com.cool.modules.organization.task;

import com.cool.modules.organization.service.OrganizationAuditLogService;
import com.cool.modules.organization.service.SecurityMonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 安全监控定时任务
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SecurityMonitorTask {

    private final SecurityMonitorService securityMonitorService;
    private final OrganizationAuditLogService auditLogService;

    /**
     * 每小时执行一次安全风险检测
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlySecurityCheck() {
        try {
            log.info("开始执行每小时安全检查");
            
            // 获取最近1小时的风险用户
            var riskUsers = securityMonitorService.getRiskUsers(1);
            
            for (var riskUser : riskUsers) {
                Long userId = (Long) riskUser.get("userId");
                String riskLevel = (String) riskUser.get("riskLevel");
                Integer failureCount = (Integer) riskUser.get("failureCount");
                
                // 检测异常权限访问
                if (securityMonitorService.detectAbnormalPermissionAccess(userId, 1)) {
                    log.warn("检测到用户 {} 存在异常权限访问行为", userId);
                }
                
                // 检测频繁组织形态切换
                if (securityMonitorService.detectFrequentModeSwitch(userId, 1, 5)) {
                    log.warn("检测到用户 {} 频繁切换组织形态", userId);
                }
                
                // 根据风险等级触发相应的安全措施
                if ("CRITICAL".equals(riskLevel)) {
                    securityMonitorService.triggerSecurityAlert(userId, "HIGH_RISK_USER", 
                            "用户在1小时内有" + failureCount + "次失败操作", "CRITICAL");
                } else if ("HIGH".equals(riskLevel)) {
                    securityMonitorService.triggerSecurityAlert(userId, "MEDIUM_RISK_USER", 
                            "用户在1小时内有" + failureCount + "次失败操作", "HIGH");
                }
            }
            
            log.info("每小时安全检查完成，检查了 {} 个风险用户", riskUsers.size());
            
        } catch (Exception e) {
            log.error("每小时安全检查执行失败", e);
        }
    }

    /**
     * 每天凌晨2点执行日志清理
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyLogCleanup() {
        try {
            log.info("开始执行每日日志清理");
            
            // 清理90天前的审计日志
            int cleanedCount = auditLogService.cleanExpiredLogs(90);
            
            log.info("每日日志清理完成，清理了 {} 条过期日志", cleanedCount);
            
            // 记录清理操作到审计日志
            auditLogService.logDataOperation(
                    null, "SYSTEM", "LOG_CLEANUP",
                    "AUDIT_LOG", null, null, "定时清理了 " + cleanedCount + " 条过期日志",
                    null, true, null, null
            );
            
        } catch (Exception e) {
            log.error("每日日志清理执行失败", e);
        }
    }

    /**
     * 每天早上8点生成安全报告
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void dailySecurityReport() {
        try {
            log.info("开始生成每日安全报告");
            
            // 获取过去24小时的安全事件摘要
            var securitySummary = securityMonitorService.getSecurityEventSummary(1);
            
            int totalEvents = (Integer) securitySummary.get("totalEvents");
            long affectedUsers = (Long) securitySummary.get("affectedUsers");
            long highRiskEvents = (Long) securitySummary.get("highRiskEvents");
            
            log.info("过去24小时安全事件摘要: 总事件数={}, 受影响用户数={}, 高风险事件数={}", 
                    totalEvents, affectedUsers, highRiskEvents);
            
            // 如果有高风险事件，触发警报
            if (highRiskEvents > 10) {
                securityMonitorService.triggerSecurityAlert(null, "HIGH_RISK_EVENTS", 
                        "过去24小时内发生了" + highRiskEvents + "个高风险事件", "HIGH");
            }
            
            // 记录报告生成到审计日志
            auditLogService.logDataOperation(
                    null, "SYSTEM", "SECURITY_REPORT",
                    "SYSTEM", null, null, 
                    String.format("生成安全报告: 总事件数=%d, 受影响用户数=%d, 高风险事件数=%d", 
                            totalEvents, affectedUsers, highRiskEvents),
                    null, true, null, null
            );
            
            log.info("每日安全报告生成完成");
            
        } catch (Exception e) {
            log.error("每日安全报告生成失败", e);
        }
    }

    /**
     * 每周日凌晨1点执行深度安全分析
     */
    @Scheduled(cron = "0 0 1 ? * SUN")
    public void weeklySecurityAnalysis() {
        try {
            log.info("开始执行每周安全分析");
            
            // 获取过去7天的风险用户
            var weeklyRiskUsers = securityMonitorService.getRiskUsers(7);
            
            // 获取过去7天的安全事件摘要
            var weeklySummary = securityMonitorService.getSecurityEventSummary(7);
            
            int totalEvents = (Integer) weeklySummary.get("totalEvents");
            long affectedUsers = (Long) weeklySummary.get("affectedUsers");
            
            log.info("过去7天安全分析结果: 风险用户数={}, 总事件数={}, 受影响用户数={}", 
                    weeklyRiskUsers.size(), totalEvents, affectedUsers);
            
            // 如果风险用户过多，触发系统级警报
            if (weeklyRiskUsers.size() > 20) {
                securityMonitorService.triggerSecurityAlert(null, "SYSTEM_SECURITY_RISK", 
                        "过去7天内发现" + weeklyRiskUsers.size() + "个风险用户，需要关注", "MEDIUM");
            }
            
            // 记录分析结果到审计日志
            auditLogService.logDataOperation(
                    null, "SYSTEM", "WEEKLY_SECURITY_ANALYSIS",
                    "SYSTEM", null, null, 
                    String.format("每周安全分析: 风险用户数=%d, 总事件数=%d, 受影响用户数=%d", 
                            weeklyRiskUsers.size(), totalEvents, affectedUsers),
                    null, true, null, null
            );
            
            log.info("每周安全分析完成");
            
        } catch (Exception e) {
            log.error("每周安全分析执行失败", e);
        }
    }

    /**
     * 每5分钟检查一次用户锁定状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void checkUserLockStatus() {
        try {
            // 这里可以实现检查即将到期的用户锁定，提前发送通知等逻辑
            log.debug("检查用户锁定状态");
            
        } catch (Exception e) {
            log.error("检查用户锁定状态失败", e);
        }
    }

    /**
     * 每30分钟清理过期的安全警报
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 1800000毫秒
    public void cleanExpiredAlerts() {
        try {
            log.debug("清理过期的安全警报");
            
            // 这里可以实现清理过期安全警报的逻辑
            // 由于警报存储在Redis中且设置了TTL，会自动过期，这里主要是记录日志
            
        } catch (Exception e) {
            log.error("清理过期安全警报失败", e);
        }
    }
}