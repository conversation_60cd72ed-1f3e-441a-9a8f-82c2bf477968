package com.cool.modules.sop.controller.admin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.core.request.R;
import com.cool.modules.sop.dto.SOPFlatExcelData;
import com.cool.modules.sop.dto.SOPImportRequest;
import com.cool.modules.sop.dto.SOPImportResult;
import com.cool.modules.sop.service.SOPFlatImportService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;


/**
 * SOP导入管理控制器
 */
@Slf4j
@Tag(name = "SOP导入管理", description = "SOP场景和步骤的Excel导入功能")
@RestController
@RequestMapping("/admin/sop/import")
public class AdminSOPImportController {

    @Autowired
    @Qualifier("sopFlatImportService")
    private SOPFlatImportService sopImportService;

    @Operation(summary = "下载导入模板", description = "下载SOP导入的Excel模板文件")
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            log.info("用户请求下载SOP导入模板");

            sopImportService.createTemplate(response);

            log.info("SOP导入模板下载成功");

        } catch (Exception e) {
            log.error("下载SOP导入模板失败: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "预览导入数据", description = "解析Excel文件并预览导入数据，不实际导入")
    @PostMapping("/preview")
    public R<SOPImportResult> previewImport(
        @Parameter(description = "Excel文件", required = true)
        @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("开始预览SOP导入，文件名: {}, 大小: {} bytes", 
                file.getOriginalFilename(), file.getSize());
            
            // 文件格式校验
            if (!isValidExcelFile(file)) {
                return R.error("文件格式不正确，请上传.xlsx格式的Excel文件");
            }
            
            // 文件大小校验
            if (file.getSize() > 10 * 1024 * 1024) { // 10MB限制
                return R.error("文件大小不能超过10MB");
            }
            
            SOPImportResult result = sopImportService.previewImport(file);
            
            log.info("SOP导入预览完成，成功: {}, 场景数: {}", 
                result.getSuccess(), 
                result.getStatistics() != null ? result.getStatistics().getTotalScenarios() : 0);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("SOP导入预览失败: {}", e.getMessage(), e);
            return R.error("预览失败: " + e.getMessage());
        }
    }

    @Operation(summary = "执行SOP文件导入", description = "通过Excel文件执行SOP场景和步骤的导入操作")
    @PostMapping("/import")
    public R<SOPImportResult> importSOP(
        @Parameter(description = "Excel文件", required = true)
        @RequestParam("file") MultipartFile file,
        
        @Parameter(description = "是否强制覆盖现有版本")
        @RequestParam(value = "forceOverride", defaultValue = "false") Boolean forceOverride,
        
        @Parameter(description = "版本升级策略：MAJOR/MINOR/PATCH")
        @RequestParam(value = "versionStrategy", defaultValue = "MINOR") String versionStrategy,
        
        @Parameter(description = "导入模式：CREATE_NEW/UPDATE_EXISTING/MERGE")
        @RequestParam(value = "importMode", defaultValue = "MERGE") String importMode,
        
        @Parameter(description = "是否备份现有数据")
        @RequestParam(value = "backupExisting", defaultValue = "true") Boolean backupExisting,
        
        @Parameter(description = "导入说明")
        @RequestParam(value = "importDescription", required = false) String importDescription
        ) {
        SOPImportRequest request = new SOPImportRequest();
        try {
            log.info("开始执行SOP文件导入，文件名: {}, 强制覆盖: {}, 版本策略: {}, 导入模式: {}", 
                file.getOriginalFilename(), forceOverride, versionStrategy, importMode);
            
            // 文件格式校验
            if (!isValidExcelFile(file)) {
                return R.error("文件格式不正确，请上传.xlsx格式的Excel文件");
            }
            
            request.setFile(file);
            request.setFileParsed(true);
            
            // 构建导入请求
            request.setForceOverride(forceOverride);
            request.setVersionStrategy(versionStrategy);
            request.setImportMode(importMode);
            request.setBackupExisting(backupExisting);
            request.setImportDescription(importDescription);
            
            // 设置操作人信息（从当前登录用户获取）
            Long userId = CoolSecurityUtil.getCurrentUserId();
            String username=CoolSecurityUtil.getAdminUsername();
            request.setOperatorId(userId); 
            request.setOperatorName(username); 
            
            SOPImportResult result = sopImportService.importSOP(request);
            
            log.info("SOP导入完成，成功: {}, 成功场景数: {}", 
                result.getSuccess(),
                result.getStatistics() != null ? result.getStatistics().getSuccessScenarios() : 0);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("SOP导入失败: {}", e.getMessage(), e);
            return R.error("导入失败: " + e.getMessage());
        }
    }

    @Operation(summary = "执行SOP数据导入", description = "通过JSON数据执行SOP场景和步骤的导入操作")
    @PostMapping("/import-data")
    public R<SOPImportResult> importSOPData(
        @RequestBody SOPFlatExcelData sopFlatExcelData,
        
        @Parameter(description = "是否强制覆盖现有版本")
        @RequestParam(value = "forceOverride", defaultValue = "false") Boolean forceOverride,
        
        @Parameter(description = "版本升级策略：MAJOR/MINOR/PATCH")
        @RequestParam(value = "versionStrategy", defaultValue = "MINOR") String versionStrategy,
        
        @Parameter(description = "导入模式：CREATE_NEW/UPDATE_EXISTING/MERGE")
        @RequestParam(value = "importMode", defaultValue = "MERGE") String importMode,
        
        @Parameter(description = "是否备份现有数据")
        @RequestParam(value = "backupExisting", defaultValue = "true") Boolean backupExisting,
        
        @Parameter(description = "导入说明")
        @RequestParam(value = "importDescription", required = false) String importDescription
        ) {
        SOPImportRequest request = new SOPImportRequest();
        try {
            log.info("开始执行SOP数据导入，强制覆盖: {}, 版本策略: {}, 导入模式: {}", 
                forceOverride, versionStrategy, importMode);
            
            // 验证数据
            if (sopFlatExcelData == null) {
                return R.error("请提供导入数据");
            }
            
            request.setSopFlatExcelData(sopFlatExcelData);
            request.setFileParsed(true);
            
            // 构建导入请求
            request.setForceOverride(forceOverride);
            request.setVersionStrategy(versionStrategy);
            request.setImportMode(importMode);
            request.setBackupExisting(backupExisting);
            request.setImportDescription(importDescription);
            
            // 设置操作人信息（从当前登录用户获取）
            Long userId = CoolSecurityUtil.getCurrentUserId();
            String username = CoolSecurityUtil.getAdminUsername();
            request.setOperatorId(userId);
            request.setOperatorName(username);
            
            SOPImportResult result = sopImportService.importSOP(request);
            
            log.info("SOP数据导入完成，成功: {}, 成功场景数: {}", 
                result.getSuccess(),
                result.getStatistics() != null ? result.getStatistics().getSuccessScenarios() : 0);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("SOP数据导入失败: {}", e.getMessage(), e);
            return R.error("导入失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查版本冲突", description = "检查导入文件中的场景是否存在版本冲突")
    @PostMapping("/check-conflicts")
    public R<Object> checkVersionConflicts(
        @Parameter(description = "Excel文件", required = true)
        @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("开始检查SOP版本冲突，文件名: {}", file.getOriginalFilename());
            
            if (!isValidExcelFile(file)) {
                return R.error("文件格式不正确，请上传.xlsx格式的Excel文件");
            }
            
            // 检查冲突
            SOPImportResult result = sopImportService.checkVersionConflicts(file);
            
            log.info("版本冲突检查完成");

            return R.ok(result);
            
        } catch (Exception e) {
            log.error("检查版本冲突失败: {}", e.getMessage(), e);
            return R.error("检查失败: " + e.getMessage());
        }
    }

    @Operation(summary = "恢复备份数据", description = "根据备份ID恢复之前备份的SOP数据")
    @PostMapping("/restore/{backupId}")
    public R<String> restoreBackup(
        @Parameter(description = "备份ID", required = true)
        @PathVariable String backupId) {
        
        try {
            log.info("开始恢复SOP备份数据，备份ID: {}", backupId);
            
            SOPImportResult result = sopImportService.restoreBackup(backupId);
            Boolean success = result.getSuccess();
            
            if (success) {
                log.info("SOP备份数据恢复成功，备份ID: {}", backupId);
                return R.ok("数据恢复成功");
            } else {
                log.warn("SOP备份数据恢复失败，备份ID: {}", backupId);
                return R.error("数据恢复失败");
            }
            
        } catch (Exception e) {
            log.error("恢复SOP备份数据失败: {}", e.getMessage(), e);
            return R.error("恢复失败: " + e.getMessage());
        }
    }

    @Operation(summary = "生成新版本号", description = "根据当前版本和升级策略生成新版本号")
    @GetMapping("/generate-version")
    public R<String> generateVersion(
        @Parameter(description = "当前版本号")
        @RequestParam("currentVersion") String currentVersion,
        
        @Parameter(description = "版本策略：MAJOR/MINOR/PATCH")
        @RequestParam(value = "strategy", defaultValue = "MINOR") String strategy) {
        
        try {
            String newVersion = sopImportService.generateVersion(currentVersion, strategy);
            
            return R.ok(newVersion);
            
        } catch (Exception e) {
            log.error("生成版本号失败: {}", e.getMessage(), e);
            return R.error("生成版本号失败: " + e.getMessage());
        }
    }

    /**
     * 校验Excel文件格式
     */
    private boolean isValidExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null) {
            return false;
        }
        
        return filename.toLowerCase().endsWith(".xlsx");
    }
}
