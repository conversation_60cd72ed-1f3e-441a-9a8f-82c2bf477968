package com.cool.modules.sop.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.sop.entity.SOPIndustryEntity;
import com.cool.modules.sop.service.SOPIndustryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import cn.hutool.json.JSONObject;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * SOP行业管理控制器
 */
@Tag(name = "SOP行业管理", description = "SOP行业管理相关接口")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
public class AdminSOPIndustryController extends BaseController<SOPIndustryService, SOPIndustryEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 可以在这里添加查询条件配置
    }

    @Operation(summary = "获取启用的行业列表")
    @GetMapping("/active")
    public R getActiveIndustries() {
        List<SOPIndustryEntity> industries = service.getActiveIndustries();
        return R.ok(industries);
    }

    @Operation(summary = "启用行业")
    @PostMapping("/enable")
    public R enableIndustry(@RequestParam Long industryId) {
        service.enableIndustry(industryId);
        return R.ok("行业已启用");
    }

    @Operation(summary = "禁用行业")
    @PostMapping("/disable")
    public R disableIndustry(@RequestParam Long industryId) {
        service.disableIndustry(industryId);
        return R.ok("行业已禁用");
    }

    @Operation(summary = "根据编码获取行业")
    @GetMapping("/code/{code}")
    public R getByCode(@PathVariable String code) {
        SOPIndustryEntity industry = service.getByCode(code);
        return R.ok(industry);
    }

    @Operation(summary = "搜索行业")
    @GetMapping("/search")
    public R searchByName(@RequestParam String name) {
        List<SOPIndustryEntity> industries = service.searchByName(name);
        return R.ok(industries);
    }

    @Operation(summary = "更新排序")
    @PostMapping("/sort")
    public R updateSort(@RequestParam Long industryId, @RequestParam Integer sort) {
        service.updateSort(industryId, sort);
        return R.ok("排序已更新");
    }
} 