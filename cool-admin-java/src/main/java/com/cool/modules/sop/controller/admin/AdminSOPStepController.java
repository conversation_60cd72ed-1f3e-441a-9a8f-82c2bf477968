package com.cool.modules.sop.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.sop.entity.SOPStepEntity;
import com.cool.modules.sop.service.SOPStepService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import cn.hutool.json.JSONObject;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * SOP步骤管理控制器
 */
@Tag(name = "SOP步骤管理", description = "SOP步骤管理相关接口")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
public class AdminSOPStepController extends BaseController<SOPStepService, SOPStepEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 添加查询条件配置
        // 如果传入了sopId参数，则按场景ID过滤
        Long sopId = requestParams.getLong("sopId");
        
        setPageOption(createOp().queryWrapper(
            com.mybatisflex.core.query.QueryWrapper.create()
                .eq("sop_id", sopId, sopId != null)
                .orderBy("step_order", true)
        ));
        
        setListOption(createOp().queryWrapper(
            com.mybatisflex.core.query.QueryWrapper.create()
                .eq("sop_id", sopId, sopId != null)
                .orderBy("step_order", true)
        ));
    }

    @Operation(summary = "根据模板ID获取步骤列表")
    @GetMapping("/template/{templateId}")
    public R getByTemplateId(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        try {
            List<SOPStepEntity> steps = service.getByTemplateId(templateId);
            return R.ok(steps);
        } catch (Exception e) {
            return R.error("获取步骤列表失败：" + e.getMessage());
        }
    }

    @Operation(summary = "更新步骤顺序")
    @PostMapping("/order")
    public R updateStepOrder(@Parameter(description = "步骤ID") @RequestParam Long stepId,
                            @Parameter(description = "新顺序") @RequestParam Integer newOrder) {
        try {
            service.updateStepOrder(stepId, newOrder);
            return R.ok("步骤顺序已更新");
        } catch (Exception e) {
            return R.error("更新步骤顺序失败：" + e.getMessage());
        }
    }

    @Operation(summary = "批量创建步骤")
    @PostMapping("/batch")
    public R batchCreateSteps(@Parameter(description = "模板ID") @RequestParam Long templateId,
                             @RequestBody List<SOPStepEntity> steps) {
        try {
            service.batchCreateSteps(templateId, steps);
            return R.ok("批量创建步骤成功");
        } catch (Exception e) {
            return R.error("批量创建步骤失败：" + e.getMessage());
        }
    }

    @Operation(summary = "根据模板ID删除所有步骤")
    @DeleteMapping("/template/{templateId}")
    public R deleteByTemplateId(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        try {
            service.deleteByTemplateId(templateId);
            return R.ok("步骤删除成功");
        } catch (Exception e) {
            return R.error("删除步骤失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取步骤统计信息")
    @GetMapping("/stats")
    public R getStepStats(@Parameter(description = "模板ID") @RequestParam(required = false) Long templateId) {
        try {
            // 这里可以添加步骤统计逻辑
            return R.ok("步骤统计信息");
        } catch (Exception e) {
            return R.error("获取统计信息失败：" + e.getMessage());
        }
    }
} 