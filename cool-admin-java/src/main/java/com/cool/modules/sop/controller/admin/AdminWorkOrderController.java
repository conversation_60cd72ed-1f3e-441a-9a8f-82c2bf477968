package com.cool.modules.sop.controller.admin;

import cn.hutool.core.util.ObjUtil;
import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.sop.entity.WorkOrderEntity;
import com.cool.modules.sop.service.WorkOrderService;
import com.cool.modules.sop.service.AILLMService;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;
import cn.hutool.json.JSONObject;

import java.util.Map;
import java.util.List;

/**
 * 工作工单管理控制器
 */
@Tag(name = "工作工单管理", description = "工作工单的增删改查及状态管理")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
public class AdminWorkOrderController extends BaseController<WorkOrderService, WorkOrderEntity> {

    @Autowired
    private AILLMService aiLLMService;

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // Required implementation for BaseController
    }

    protected void init(QueryWrapper queryWrapper, Map<String, Object> params) {
        queryWrapper.like("title", params.get("title"))
                   .eq("status", params.get("status"))
                   .eq("priority", params.get("priority"))
                   .eq("assignee_id", params.get("assigneeId"))
                   .orderBy("id", false);
    }

    @Operation(summary = "开始执行工单")
    @PostMapping("/start")
    public R start(@Parameter(description = "工单ID") @RequestParam Long id) {
        try {
            service.startWorkOrder(id);
            return R.ok("工单已开始执行");
        } catch (Exception e) {
            return R.error("开始执行失败：" + e.getMessage());
        }
    }

    @Operation(summary = "暂停工单")
    @PostMapping("/pause")
    public R pause(@Parameter(description = "工单ID") @RequestParam Long id) {
        try {
            service.pauseWorkOrder(id);
            return R.ok("工单已暂停");
        } catch (Exception e) {
            return R.error("暂停失败：" + e.getMessage());
        }
    }

    @Operation(summary = "恢复工单")
    @PostMapping("/resume")
    public R resume(@Parameter(description = "工单ID") @RequestParam Long id) {
        try {
            service.resumeWorkOrder(id);
            return R.ok("工单已恢复执行");
        } catch (Exception e) {
            return R.error("恢复失败：" + e.getMessage());
        }
    }

    @Operation(summary = "完成工单")
    @PostMapping("/complete")
    public R complete(@Parameter(description = "工单ID") @RequestParam Long id) {
        try {
            service.completeWorkOrder(id);
            return R.ok("工单已完成");
        } catch (Exception e) {
            return R.error("完成失败：" + e.getMessage());
        }
    }

    @Operation(summary = "取消工单")
    @PostMapping("/cancel")
    public R cancel(@Parameter(description = "工单ID") @RequestParam Long id,
                   @Parameter(description = "取消原因") @RequestParam(required = false) String reason) {
        try {
            service.cancelWorkOrder(id, reason);
            return R.ok("工单已取消");
        } catch (Exception e) {
            return R.error("取消失败：" + e.getMessage());
        }
    }

    @Operation(summary = "AI智能调度工单")
    @PostMapping("/ai-schedule")
    public R aiSchedule(@Parameter(description = "工单ID列表") @RequestBody List<Long> workOrderIds) {
        try {
            // 使用AI服务进行智能调度
            var scheduleResult = aiLLMService.scheduleTask(workOrderIds);
            
            // 应用调度结果
            service.applyScheduleResult(scheduleResult);
            
            return R.ok(scheduleResult);
        } catch (Exception e) {
            return R.error("AI调度失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取工单统计信息")
    @GetMapping("/stats")
    public R getStats(@Parameter(description = "负责人ID") @RequestParam(required = false) Long assigneeId,
                     @Parameter(description = "时间范围") @RequestParam(required = false) String timeRange) {
        try {
            var stats = service.getWorkOrderStats(assigneeId, timeRange);
            return R.ok(stats);
        } catch (Exception e) {
            return R.error("获取统计信息失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取工单详情及任务列表")
    @GetMapping("/detail/{id}")
    public R getDetail(@Parameter(description = "工单ID") @PathVariable Long id) {
        try {
            var detail = service.getWorkOrderDetail(id);
            return R.ok(detail);
        } catch (Exception e) {
            return R.error("获取详情失败：" + e.getMessage());
        }
    }

    @Operation(summary = "基于SOP模板创建工单")
    @PostMapping("/create-from-template")
    public R createFromTemplate(@Parameter(description = "SOP模板ID") @RequestParam Long templateId,
                               @RequestBody WorkOrderEntity workOrder) {
        try {
            var createdWorkOrder = service.createFromTemplate(templateId, workOrder);
            return R.ok(createdWorkOrder);
        } catch (Exception e) {
            return R.error("创建工单失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取工单执行进度")
    @GetMapping("/progress/{id}")
    public R getProgress(@Parameter(description = "工单ID") @PathVariable Long id) {
        try {
            var progress = service.getWorkOrderProgress(id);
            return R.ok(progress);
        } catch (Exception e) {
            return R.error("获取进度失败：" + e.getMessage());
        }
    }

    @Operation(summary = "批量分配工单")
    @PostMapping("/batch-assign")
    public R batchAssign(@Parameter(description = "工单ID列表") @RequestBody List<Long> workOrderIds,
                        @Parameter(description = "负责人ID") @RequestParam Long assigneeId) {
        try {
            service.batchAssign(workOrderIds, assigneeId);
            return R.ok("批量分配成功");
        } catch (Exception e) {
            return R.error("批量分配失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取AI优化建议")
    @GetMapping("/ai-optimization/{id}")
    public R getAIOptimization(@Parameter(description = "工单ID") @PathVariable Long id) {
        try {
            // 获取工单执行数据
            var workOrder = service.getById(id);
            if (ObjUtil.isNull(workOrder)) {
                return R.error("工单不存在");
            }

            // 调用AI服务获取优化建议
            var optimization = aiLLMService.optimizeProcess(
                workOrder.getSopTemplateId(),
                service.getExecutionHistory(id)
            );

            return R.ok(optimization);
        } catch (Exception e) {
            return R.error("获取AI优化建议失败：" + e.getMessage());
        }
    }
} 