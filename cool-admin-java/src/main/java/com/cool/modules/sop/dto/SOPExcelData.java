package com.cool.modules.sop.dto;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;
import java.util.List;

/**
 * SOP Excel数据结构
 */
@Data
@Builder
public class SOPExcelData implements Serializable{
    
    /**
     * 场景信息
     */
    private ScenarioData scenario;
    
    /**
     * 步骤列表
     */
    private List<StepData> steps;
    
    @Data
    @Builder
    public static class ScenarioData {
        private String industryName;
        private String stage;
        private String moduleCode;
        private String moduleName;
        private String scenarioCode;
        private String scenarioName;
        private String executionCycle;
        private String executionFrequency;
        private Integer executionCount;
        private String version;
        private String description;
        private Integer difficultyLevel;
        private String qualityStandard;
        private String successCriteria;
        private String riskPoints;
        private String attentionPoints;
        private String applicableArea;
        
        // Excel行号，用于错误定位
        private Integer rowNumber;
    }
    
    @Data
    @Builder
    public static class StepData {
        private String stepCode;
        private String stepName;
        private String stepDescription;
        private Integer stepOrder;
        private String entityTouchpoint;
        private String userActivity;
        private String employeeBehavior;
        private String workHighlight;
        private String employeeRole;
        private String stepType;
        private Boolean isRequired;
        private Integer estimatedTime;
        private String skillRequirements;
        private String toolsRequired;
        private String qualityCheckPoints;
        private String riskWarnings;
        private String successCriteria;
        private String failureHandling;
        private String nextStepCondition;
        private String parallelSteps;
        private String prerequisiteSteps;
        private String version;
        
        // Excel行号，用于错误定位
        private Integer rowNumber;
    }
}
