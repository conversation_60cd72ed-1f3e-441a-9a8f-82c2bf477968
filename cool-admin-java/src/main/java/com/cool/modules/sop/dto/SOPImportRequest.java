package com.cool.modules.sop.dto;

import java.io.Serializable;

import org.springframework.web.multipart.MultipartFile;

import lombok.Data;

/**
 * SOP导入请求DTO
 */
@Data
public class SOPImportRequest implements Serializable {
    
    /**
     * 导入的Excel文件
     */
    private MultipartFile file;
    private SOPFlatExcelData sopFlatExcelData;
    private Boolean fileParsed = false;
    /**
     * 是否强制覆盖现有版本
     */
    private Boolean forceOverride = false;
    
    /**
     * 版本升级策略
     * MAJOR: 主版本升级 (1.0 -> 2.0)
     * MINOR: 次版本升级 (1.0 -> 1.1)
     * PATCH: 补丁版本升级 (1.0.0 -> 1.0.1)
     */
    private String versionStrategy = "MINOR";
    
    /**
     * 导入模式
     * CREATE_NEW: 创建新版本
     * UPDATE_EXISTING: 更新现有版本
     * MERGE: 合并模式
     */
    private String importMode = "MERGE";
    
    /**
     * 是否备份现有数据
     */
    private Boolean backupExisting = true;
    
    /**
     * 导入说明
     */
    private String importDescription;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
}
