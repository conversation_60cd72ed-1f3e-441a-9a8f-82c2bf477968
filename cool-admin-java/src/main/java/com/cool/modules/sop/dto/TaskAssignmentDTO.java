package com.cool.modules.sop.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 任务分配相关DTO
 */
public class TaskAssignmentDTO {

    /**
     * 任务分配请求
     */
    @Data
    @Schema(description = "任务分配请求")
    public static class AssignmentRequest {
        @Schema(description = "任务ID列表")
        private List<Long> taskIds;

        @Schema(description = "分配配置")
        private AssignmentConfig config;

        @Schema(description = "是否强制重新分配")
        private Boolean forceReassign = false;
    }

    /**
     * 分配配置
     */
    @Data
    @Schema(description = "分配配置")
    public static class AssignmentConfig {
        @Schema(description = "角色匹配")
        private Boolean roleMatch = true;

        @Schema(description = "技能匹配")
        private Boolean skillMatch = false;

        @Schema(description = "地理位置匹配")
        private Boolean locationMatch = false;

        @Schema(description = "历史绩效匹配")
        private Boolean performanceMatch = false;

        @Schema(description = "可用性检查")
        private Boolean availabilityCheck = false;
    }

    /**
     * 分配结果
     */
    @Data
    @Schema(description = "分配结果")
    public static class AssignmentResult {
        @Schema(description = "任务ID")
        private Long taskId;

        @Schema(description = "推荐的执行人ID")
        private Long userId;

        @Schema(description = "执行人姓名")
        private String userName;

        @Schema(description = "置信度 0-100")
        private Integer confidence;

        @Schema(description = "推荐理由")
        private List<String> reasons;

        @Schema(description = "备选执行人")
        private List<AlternativeUser> alternatives;

        @Schema(description = "是否成功")
        private Boolean success;

        @Schema(description = "错误信息")
        private String errorMessage;
    }

    /**
     * 备选执行人
     */
    @Data
    @Schema(description = "备选执行人")
    public static class AlternativeUser {
        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "用户姓名")
        private String userName;

        @Schema(description = "置信度")
        private Integer confidence;
    }

    /**
     * 用户档案
     */
    @Data
    @Schema(description = "用户档案")
    public static class UserProfile {
        @Schema(description = "用户ID")
        private Long id;

        @Schema(description = "用户姓名")
        private String name;

        @Schema(description = "角色列表")
        private List<String> roles;

        @Schema(description = "技能列表")
        private List<UserSkill> skills;

        @Schema(description = "部门")
        private String department;

        @Schema(description = "地理位置")
        private String location;

        @Schema(description = "当前工作量 0-100")
        private Integer currentWorkload;

        @Schema(description = "历史绩效 0-100")
        private Integer performance;

        @Schema(description = "是否可用")
        private Boolean available;
    }

    /**
     * 用户技能
     */
    @Data
    @Schema(description = "用户技能")
    public static class UserSkill {
        @Schema(description = "技能名称")
        private String skillName;

        @Schema(description = "技能等级 1-5")
        private Integer skillLevel;

        @Schema(description = "技能描述")
        private String description;
    }

    /**
     * 批量分配结果
     */
    @Data
    @Schema(description = "批量分配结果")
    public static class BatchAssignmentResult {
        @Schema(description = "总任务数")
        private Integer totalTasks;

        @Schema(description = "成功分配数")
        private Integer successCount;

        @Schema(description = "失败分配数")
        private Integer failureCount;

        @Schema(description = "分配结果列表")
        private List<AssignmentResult> results;

        @Schema(description = "处理时间(毫秒)")
        private Long processingTime;
    }
}
