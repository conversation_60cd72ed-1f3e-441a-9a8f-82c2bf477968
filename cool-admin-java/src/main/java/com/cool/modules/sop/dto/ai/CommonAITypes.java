package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * AI通用类型定义
 */
public class CommonAITypes {

    @Data
    @Builder
    public static class ScheduleContext {
        private Object resourceStatus;
        private Object durationPrediction;
        private String businessPriority;
        private Date deadline;
    }

    /**
     * 执行指导
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionGuidance {
        private String guidance;
        private String priority;
        private List<String> actionItems;
        private List<String> risks;
        private Map<String, Object> additionalInfo;
        private LocalDateTime timestamp;
    }

    @Data
    @Builder
    public static class QualityCheckData {
        private Map<String, Object> executionData;
        private List<String> completedSteps;
        private Map<String, String> evidenceFiles;
        private String executorComments;
    }

    @Data
    @Builder
    public static class QualityAssessmentResult {
        private Integer overallScore;
        private Map<String, Integer> dimensionScores;
        private List<QualityIssue> issues;
        private List<String> improvements;
        private Boolean needsRework;
        private String summary;
    }

    @Data
    @Builder
    public static class QualityIssue {
        private String description;
        private String severity;
        private String category;
        private String recommendation;
    }

    @Data
    @Builder
    public static class ProcessOptimizationResult {
        private String summary;
        private List<OptimizationRecommendation> recommendations;
        private Map<String, Object> metrics;
        private String expectedImpact;
    }

    @Data
    @Builder
    public static class OptimizationRecommendation {
        private String title;
        private String description;
        private String priority;
        private String expectedImpact;
        private String difficulty;
        private String category;
    }

    @Data
    @Builder
    public static class ExceptionHandlingAdvice {
        private List<String> immediateActions;
        private String rootCauseAnalysis;
        private List<String> solutionOptions;
        private String riskAssessment;
        private List<String> preventiveMeasures;
        private String severity;
    }

    @Data
    @Builder
    public static class DurationPrediction {
        private Integer estimatedMinutes;
        private Double confidenceLevel;
        private String predictionBasis;
        private Integer minDuration;
        private Integer maxDuration;
    }

    /**
     * AI模型类型
     */
    public enum AIModelType {
        OPENAI_GPT4("openai-gpt4", "OpenAI GPT-4"),
        OPENAI_GPT35("openai-gpt3.5", "OpenAI GPT-3.5"),
        LOCAL_LLM("local-llm", "本地大模型"),
        AZURE_OPENAI("azure-openai", "Azure OpenAI");

        private final String code;
        private final String name;

        AIModelType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * AI处理状态
     */
    public enum AIProcessStatus {
        PENDING("pending", "等待处理"),
        PROCESSING("processing", "处理中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "处理失败"),
        TIMEOUT("timeout", "处理超时");

        private final String code;
        private final String name;

        AIProcessStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 优化建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptimizationSuggestion {
        private String suggestionId;
        private String title;
        private String description;
        private String category;
        private Integer priority;
        private Double expectedImprovement;
        private String implementationGuide;
        private List<String> benefits;
        private List<String> risks;
        private Map<String, Object> metrics;
        private LocalDateTime generatedAt;
    }

    /**
     * 知识图谱
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KnowledgeGraph {
        private String graphId;
        private List<KnowledgeNode> nodes;
        private List<KnowledgeRelation> relations;
        private Map<String, Object> metadata;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    /**
     * 知识节点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KnowledgeNode {
        private String nodeId;
        private String type;
        private String label;
        private Map<String, Object> properties;
        private Double weight;
    }

    /**
     * 知识关系
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KnowledgeRelation {
        private String relationId;
        private String fromNodeId;
        private String toNodeId;
        private String relationType;
        private Map<String, Object> properties;
        private Double strength;
    }

    /**
     * 预测分析结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PredictionResult {
        private String predictionId;
        private String predictionType;
        private Map<String, Object> predictions;
        private Double confidence;
        private List<String> influencingFactors;
        private Map<String, Object> probabilityDistribution;
        private LocalDateTime validUntil;
        private LocalDateTime generatedAt;
    }

    /**
     * AI响应结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AIResponse {
        private String requestId;
        private AIModelType modelType;
        private AIProcessStatus status;
        private Object result;
        private String errorMessage;
        private Long processingTimeMs;
        private Integer tokenUsage;
        private LocalDateTime timestamp;
    }

    /**
     * 提示词模板
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PromptTemplate {
        private String templateId;
        private String name;
        private String description;
        private String template;
        private List<String> parameters;
        private String industry;
        private String category;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
} 