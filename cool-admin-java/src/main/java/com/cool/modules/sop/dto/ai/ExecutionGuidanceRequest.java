package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * AI执行指导请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionGuidanceRequest {
    
    /**
     * 工单ID
     */
    private Long workOrderId;
    
    /**
     * 当前步骤ID
     */
    private Long currentStepId;
    
    /**
     * 执行者技能水平
     */
    private String skillLevel;
    
    /**
     * 当前进度描述
     */
    private String currentProgress;
    
    /**
     * 遇到的问题描述
     */
    private String issueDescription;
    
    /**
     * 历史执行记录
     */
    private String executionHistory;
    
    /**
     * 所需指导类型
     */
    private String guidanceType;
} 