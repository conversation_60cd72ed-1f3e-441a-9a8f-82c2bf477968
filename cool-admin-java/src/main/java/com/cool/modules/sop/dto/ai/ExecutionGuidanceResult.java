package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * AI执行指导结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionGuidanceResult {
    
    /**
     * 指导内容
     */
    private String guidance;
    
    /**
     * 建议的下一步操作
     */
    private List<String> nextSteps;
    
    /**
     * 注意事项
     */
    private List<String> warnings;
    
    /**
     * 相关资源链接
     */
    private List<String> resources;
    
    /**
     * 预计完成时间(分钟)
     */
    private Integer estimatedTimeMinutes;
    
    /**
     * 难度评估
     */
    private String difficultyAssessment;
    
    /**
     * 质量检查建议
     */
    private String qualityCheckSuggestion;
    
    /**
     * 常见问题解答
     */
    private List<FAQItem> faqs;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FAQItem {
        private String question;
        private String answer;
    }
} 