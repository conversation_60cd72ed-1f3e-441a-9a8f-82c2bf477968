package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * AI质量检查请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualityCheckRequest {
    
    /**
     * 工单ID
     */
    private Long workOrderId;
    
    /**
     * 步骤ID
     */
    private Long stepId;
    
    /**
     * 执行结果描述
     */
    private String executionResult;
    
    /**
     * 提交的文件或截图
     */
    private List<String> attachments;
    
    /**
     * 质量标准
     */
    private String qualityStandards;
    
    /**
     * 检查类型
     */
    private String checkType;
    
    /**
     * 预期结果
     */
    private String expectedResult;
    
    /**
     * 执行时间
     */
    private Integer executionTimeMinutes;
} 