package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 质量检查结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualityCheckResult {
    
    /**
     * 质量评分 (0-100)
     */
    private Integer qualityScore;
    
    /**
     * 检查状态：pass-通过, fail-不通过, warning-警告
     */
    private String status;
    
    /**
     * 检查结果描述
     */
    private String description;
    
    /**
     * 具体检查项结果
     */
    private List<CheckItem> checkItems;
    
    /**
     * 改进建议
     */
    private List<String> improvements;
    
    /**
     * 风险点
     */
    private List<String> risks;
    
    /**
     * 额外信息
     */
    private Map<String, Object> additionalInfo;
    
    /**
     * 检查耗时(毫秒)
     */
    private Long checkDurationMs;
    
    /**
     * 检查项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckItem {
        
        /**
         * 检查项名称
         */
        private String name;
        
        /**
         * 检查结果：pass-通过, fail-不通过, skip-跳过
         */
        private String result;
        
        /**
         * 得分 (0-100)
         */
        private Integer score;
        
        /**
         * 检查说明
         */
        private String description;
        
        /**
         * 建议
         */
        private String suggestion;
    }
} 