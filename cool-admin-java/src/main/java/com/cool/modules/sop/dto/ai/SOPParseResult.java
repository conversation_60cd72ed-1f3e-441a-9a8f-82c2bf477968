package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import java.util.List;

/**
 * SOP解析结果
 */
@Data
@Builder
public class SOPParseResult {
    
    /**
     * SOP基本信息
     */
    private SOPInfo sopInfo;
    
    /**
     * 步骤列表
     */
    private List<StepInfo> steps;
    
    /**
     * 质量标准
     */
    private QualityStandards qualityStandards;
    
    /**
     * 风险评估
     */
    private RiskAssessment riskAssessment;
    
    /**
     * AI评估信息
     */
    private AssessmentInfo assessment;

    @Data
    @Builder
    public static class SOPInfo {
        private String name;
        private String description;
        private String industry;
        private Integer estimatedDuration;
        private Integer difficultyLevel;
        private List<String> requiredRoles;
        private String version;
    }

    @Data
    @Builder
    public static class StepInfo {
        private Integer stepOrder;
        private String stepName;
        private String stepType;
        private String instructions;
        private String acceptanceCriteria;
        private Integer estimatedTime;
        private List<String> requiredSkills;
        private List<Integer> dependencies;
        private List<String> qualityCheckPoints;
        private List<String> riskPoints;
        private List<String> resources;
    }

    @Data
    @Builder
    public static class QualityStandards {
        private String overallStandards;
        private List<String> specificCriteria;
    }

    @Data
    @Builder
    public static class RiskAssessment {
        private String overallRiskLevel;
        private List<String> riskFactors;
        private List<String> mitigationMeasures;
    }

    @Data
    @Builder
    public static class AssessmentInfo {
        private Integer completeness;
        private Integer executability;
        private Integer safety;
        private List<String> suggestions;
    }
} 