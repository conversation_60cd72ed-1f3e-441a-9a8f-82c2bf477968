package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Collections;

/**
 * AI任务生成请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskGenerateRequest {

    /**
     * 任务描述（用户自然语言输入）
     */
    @NotBlank(message = "任务描述不能为空")
    @Size(max = 1000, message = "任务描述长度不能超过1000字符")
    private String taskDescription;

    /**
     * 指定行业ID（可选）
     */
    private Long industryId;

    /**
     * 指定场景ID（可选）
     */
    private Long scenarioId;

    /**
     * 优先级（可选，1-5，默认3）
     */
    private Integer priority = 3;

    /**
     * 期望的任务数量（可选，默认由AI决定）
     */
    private Integer expectedTaskCount;

    /**
     * 执行人ID（可选）
     */
    private Long assigneeId;

    /**
     * 开始时间（可选，格式：yyyy-MM-dd HH:mm:ss）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 结束时间（可选，格式：yyyy-MM-dd HH:mm:ss）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    /**
     * 截止时间（可选，格式：yyyy-MM-dd HH:mm:ss）
     * @deprecated 使用startTime和endTime替代
     */
    @Deprecated
    private String deadline;

    /**
     * 额外的上下文信息（可选）
     */
    private String additionalContext;

    /**
     * 是否使用AI增强模式（默认true）
     */
    @Builder.Default
    private Boolean useAIEnhancement = true;

    /**
     * 是否自动分配执行人
     */
    private Boolean autoAssign;
    
    /**
     * 指定部门ID列表（可选，如不指定则使用当前用户的部门）
     */
    private List<Long> departmentIds;

    /**
     * 指定项目ID列表（可选，用于按项目生成任务）
     */
    private List<Long> projectIds;

    /**
     * 生成模式（department: 按部门生成, project: 按项目生成）
     */
    @Builder.Default
    private String generationMode = "department";

    @JsonProperty("departmentId")
    private void setDepartmentId(Long departmentId) {
        // This setter is for backward compatibility with older API clients
        // that still send 'departmentId'.
        // We only use it if the new 'departmentIds' list is not already provided.
        if (departmentId != null && (this.departmentIds == null || this.departmentIds.isEmpty())) {
            this.departmentIds = Collections.singletonList(departmentId);
        }
    }
}