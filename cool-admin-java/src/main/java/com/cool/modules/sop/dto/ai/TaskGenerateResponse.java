package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * AI任务生成响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskGenerateResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 匹配的场景信息
     */
    private ScenarioInfo scenario;

    /**
     * 生成的任务数量
     */
    private Integer tasksGenerated;

    /**
     * 生成的任务列表
     */
    private List<GeneratedTask> tasks;

    /**
     * AI置信度分数（0-1）
     */
    private Double confidenceScore;

    /**
     * 处理时间（毫秒）
     */
    private Long processingTime;

    /**
     * 建议和提示
     */
    private List<String> suggestions;

    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;

    /**
     * 归属的部门ID
     */
    private Long departmentId;

    /**
     * 归属的部门名称
     */
    private String departmentName;

    /**
     * 归属的项目ID
     */
    private Long projectId;

    /**
     * 归属的项目名称
     */
    private String projectName;

    /**
     * 生成模式（department/project）
     */
    private String generationMode;

    /**
     * 场景信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScenarioInfo {
        private Long id;
        private String name;
        private String code;
        private String description;
        private String industry;
        private Double matchScore;
    }

    /**
     * 生成的任务信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GeneratedTask {
        private Long id;
        private String previewId;
        private String name;
        private String taskName;
        private String description;
        private String taskDescription;
        private String taskType;
        private String taskCategory;
        private Integer priority;
        private Integer estimatedDuration; // 预估时长（分钟）
        private Long assigneeId;
        private String status;
        private List<String> requirements;
        private Map<String, Object> metadata;

        // 场景相关字段
        private Long scenarioId;
        private String scenarioCode;
        private String scenarioName;

        // 步骤相关字段
        private Long stepId;
        private String stepCode;
        private String stepName;
        
        // 项目相关字段
        private Long projectId;

        // 任务详细信息
        private String entityTouchpoint;
        private String taskActivity;
        private String employeeBehavior;
        private String workHighlight;
        private String employeeRole;
        private Boolean photoRequired;
        private Boolean attachmentRequired;

        // 时间信息
        private String startTime;
        private String endTime;

        // 推荐执行人信息
        private List<RecommendedAssignee> recommendedAssignees;
        
        // 智能分配结果字段
        private String assigneeName;
        private String assigneeRole;
        private String assignmentReason;
        private Integer assignmentConfidence; // 分配置信度 (0-100)
        private List<Map<String, Object>> executors;
        
        // 候选人列表（用于预览和调整）
        private List<Map<String, Object>> candidates;
        
        // 分配状态标识
        private Boolean isAssigned; // 是否已分配
        private Boolean canReassign; // 是否可以重新分配
        private String assignmentStatus; // 分配状态：SUCCESS, FAILED, PENDING
        private String assignmentType; // 分配类型：MANUAL, AI
    }

    /**
     * 推荐执行人信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecommendedAssignee {
        private Long userId;
        private String userName;
        private String userRole;
        private Integer confidence; // 推荐置信度 0-100
        private String reason; // 推荐理由
        private Integer currentWorkload; // 当前工作负载
        private Boolean isPrimary; // 是否为主要推荐
    }

    /**
     * 创建成功响应
     */
    public static TaskGenerateResponse success(ScenarioInfo scenario, List<GeneratedTask> tasks, String message) {
        return TaskGenerateResponse.builder()
                .success(true)
                .message(message)
                .scenario(scenario)
                .tasks(tasks)
                .tasksGenerated(tasks != null ? tasks.size() : 0)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static TaskGenerateResponse error(String message) {
        return TaskGenerateResponse.builder()
                .success(false)
                .message(message)
                .tasksGenerated(0)
                .build();
    }
}