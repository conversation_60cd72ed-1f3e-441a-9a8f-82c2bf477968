package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import java.util.List;
import java.util.Date;

/**
 * 任务调度结果
 */
@Data
@Builder
public class TaskScheduleResult {
    
    /**
     * 调度成功标识
     */
    private Boolean success;
    
    /**
     * 调度的任务数量
     */
    private Integer scheduledCount;
    
    /**
     * 调度方案
     */
    private List<TaskAssignment> assignments;
    
    /**
     * 预期完成时间
     */
    private Date expectedCompletionTime;
    
    /**
     * 资源利用率
     */
    private Double resourceUtilization;
    
    /**
     * 调度建议
     */
    private String recommendation;

    @Data
    @Builder
    public static class TaskAssignment {
        private Long taskId;
        private String taskName;
        private Long assigneeId;
        private String assigneeName;
        private Date plannedStartTime;
        private Date plannedEndTime;
        private Integer estimatedDuration;
        private Double skillMatchScore;
        private String assignmentReason;
    }
} 