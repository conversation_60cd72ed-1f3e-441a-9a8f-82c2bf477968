package com.cool.modules.sop.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Getter;
import lombok.Setter;

/**
 * SOP行业管理实体
 */
@Getter
@Setter
@Table(value = "sop_industry", comment = "SOP行业管理表")
public class SOPIndustryEntity extends BaseEntity<SOPIndustryEntity> {

    @ColumnDefine(comment = "行业代码", length = 50, notNull = true)
    private String industryCode;

    @ColumnDefine(comment = "行业名称", length = 100, notNull = true)
    private String industryName;

    @ColumnDefine(comment = "行业描述", type = "text")
    private String description;

    @ColumnDefine(comment = "父级行业ID", type = "bigint")
    private Long parentId;

    @ColumnDefine(comment = "排序", defaultValue = "0")
    private Integer sort;

    @ColumnDefine(comment = "状态 0:禁用 1:启用", defaultValue = "1")
    private Integer status;

    @ColumnDefine(comment = "行业图标")
    private String icon;

    @ColumnDefine(comment = "行业特色配置", type = "json")
    private String config;
} 