package com.cool.modules.sop.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;
import org.dromara.autotable.annotation.enums.IndexTypeEnum;

/**
 * 匹配规则配置实体
 */
@Getter
@Setter
@Table(value = "sop_matching_rule", comment = "匹配规则配置表")
@Schema(description = "匹配规则配置")
public class SOPMatchingRuleEntity extends BaseEntity<SOPMatchingRuleEntity> {

    @ColumnDefine(comment = "规则名称", length = 50, notNull = true)
    @Schema(description = "规则名称")
    private String ruleName;

    @Index(type = IndexTypeEnum.UNIQUE)
    @ColumnDefine(comment = "规则键", length = 50, notNull = true)
    @Schema(description = "规则键")
    private String ruleKey;

    @ColumnDefine(comment = "规则描述", length = 200)
    @Schema(description = "规则描述")
    private String ruleDescription;

    @ColumnDefine(comment = "是否启用", defaultValue = "1")
    @Schema(description = "是否启用")
    private Boolean isEnabled;

    @ColumnDefine(comment = "权重", defaultValue = "1")
    @Schema(description = "权重 1-10")
    private Integer weight;
}
