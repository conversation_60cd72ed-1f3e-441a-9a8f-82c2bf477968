package com.cool.modules.sop.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import org.dromara.autotable.annotation.Index;
import lombok.Getter;
import lombok.Setter;

/**
 * SOP步骤表实体
 */
@Getter
@Setter
@Table(value = "sop_step", comment = "SOP步骤表")
public class SOPStepEntity extends BaseEntity<SOPStepEntity> {

    @Index
    @ColumnDefine(comment = "关联场景SOP主表ID", type = "bigint", notNull = true)
    private Long sopId;

    @Index
    @ColumnDefine(comment = "所属行业ID", type = "bigint")
    private Long industryId;

    @ColumnDefine(comment = "所属行业名称", length = 100)
    private String industryName;

    @ColumnDefine(comment = "阶段", length = 50)
    private String stage;

    @ColumnDefine(comment = "模块编码", length = 20, notNull = true)
    private String moduleCode;

    @ColumnDefine(comment = "模块名称", length = 100, notNull = true)
    private String moduleName;

    @ColumnDefine(comment = "场景编码", length = 20, notNull = true)
    private String scenarioCode;

    @ColumnDefine(comment = "场景名称", length = 200, notNull = true)
    private String scenarioName;

    @ColumnDefine(comment = "执行周期", length = 100)
    private String executionCycle;

    @ColumnDefine(comment = "步骤编码", length = 20, notNull = true)
    private String stepCode;

    @ColumnDefine(comment = "步骤名称", length = 200, notNull = true)
    private String stepName;

    @ColumnDefine(comment = "步骤描述", type = "text", notNull = true)
    private String stepDescription;

    @Index
    @ColumnDefine(comment = "步骤顺序", notNull = true)
    private Integer stepOrder;

    @ColumnDefine(comment = "实体触点", type = "text")
    private String entityTouchpoint;

    @ColumnDefine(comment = "用户活动", type = "text")
    private String userActivity;

    @ColumnDefine(comment = "员工行为", type = "text")
    private String employeeBehavior;

    @ColumnDefine(comment = "工作亮点", type = "text")
    private String workHighlight;

    @Index
    @ColumnDefine(comment = "员工角色", length = 100)
    private String employeeRole;

    @ColumnDefine(comment = "相关附件", type = "json")
    private String relatedAttachments;

    @Index
    @ColumnDefine(comment = "步骤类型(normal:普通步骤,key:关键步骤,check:检查步骤)", length = 20, defaultValue = "normal")
    private String stepType;

    @ColumnDefine(comment = "是否必需步骤", defaultValue = "true")
    private Boolean isRequired;

    @ColumnDefine(comment = "预估执行时间(分钟)", defaultValue = "30")
    private Integer estimatedTime;

    @ColumnDefine(comment = "技能要求", type = "json")
    private String skillRequirements;

    @ColumnDefine(comment = "所需工具", type = "json")
    private String toolsRequired;

    @ColumnDefine(comment = "质量检查点", type = "text")
    private String qualityCheckPoints;

    @ColumnDefine(comment = "风险提醒", type = "text")
    private String riskWarnings;

    @ColumnDefine(comment = "完成标准", type = "text")
    private String successCriteria;

    @ColumnDefine(comment = "失败处理方案", type = "text")
    private String failureHandling;

    @ColumnDefine(comment = "下一步条件", type = "text")
    private String nextStepCondition;

    @ColumnDefine(comment = "可并行执行的步骤编码", length = 500)
    private String parallelSteps;

    @ColumnDefine(comment = "前置步骤编码", length = 500)
    private String prerequisiteSteps;

    @Index
    @ColumnDefine(comment = "状态(0:禁用 1:启用 2:维护中)", defaultValue = "1")
    private Integer status;

    @ColumnDefine(comment = "版本号", length = 20, defaultValue = "1.0")
    private String version;
} 