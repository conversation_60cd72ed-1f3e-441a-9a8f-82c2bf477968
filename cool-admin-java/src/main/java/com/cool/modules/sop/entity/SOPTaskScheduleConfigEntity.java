package com.cool.modules.sop.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;
import org.dromara.autotable.annotation.enums.IndexTypeEnum;

/**
 * 任务调度配置实体
 */
@Getter
@Setter
@Table(value = "sop_task_schedule_config", comment = "任务调度配置表")
@Schema(description = "任务调度配置")
public class SOPTaskScheduleConfigEntity extends BaseEntity<SOPTaskScheduleConfigEntity> {

    @ColumnDefine(comment = "配置名称", length = 100, notNull = true)
    @Schema(description = "配置名称")
    private String configName;

    @Index(type = IndexTypeEnum.UNIQUE)
    @ColumnDefine(comment = "配置键", length = 100, notNull = true)
    @Schema(description = "配置键")
    private String configKey;

    @ColumnDefine(comment = "配置值JSON格式", type = "text", notNull = true)
    @Schema(description = "配置值")
    private String configValue;

    @ColumnDefine(comment = "配置描述", length = 500)
    @Schema(description = "配置描述")
    private String description;

    @ColumnDefine(comment = "是否启用 0-禁用 1-启用", defaultValue = "1")
    @Schema(description = "是否启用")
    private Boolean isEnabled;
}
