package com.cool.modules.sop.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import org.dromara.autotable.annotation.Index;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * 任务执行记录实体
 */
@Getter
@Setter
@Table(value = "sop_task_execute_record", comment = "SOP任务执行记录表")
public class TaskExecuteRecordEntity extends BaseEntity<TaskExecuteRecordEntity> {

    @Index
    @ColumnDefine(comment = "工单ID", type = "bigint", notNull = true)
    private Long workOrderId;

    @Index
    @ColumnDefine(comment = "SOP步骤ID", type = "bigint", notNull = true)
    private Long sopStepId;

    @ColumnDefine(comment = "任务名称", length = 200, notNull = true)
    private String taskName;

    @ColumnDefine(comment = "任务描述", type = "text")
    private String taskDescription;

    @Index
    @ColumnDefine(comment = "执行人ID", type = "bigint")
    private Long executorId;

    @ColumnDefine(comment = "执行人姓名", length = 50)
    private String executorName;

    @ColumnDefine(comment = "计划开始时间")
    private Date plannedStartTime;

    @ColumnDefine(comment = "计划结束时间")
    private Date plannedEndTime;

    @ColumnDefine(comment = "实际开始时间")
    private Date actualStartTime;

    @ColumnDefine(comment = "实际结束时间")
    private Date actualEndTime;

    @ColumnDefine(comment = "状态 0:待执行 1:执行中 2:已完成 3:暂停 4:跳过 5:失败 6:质检中 7:返工", defaultValue = "0")
    private Integer status;

    @ColumnDefine(comment = "进度百分比", defaultValue = "0")
    private Integer progress;

    @ColumnDefine(comment = "预估时间(分钟)", defaultValue = "0")
    private Integer estimatedTime;

    @ColumnDefine(comment = "实际耗时(分钟)", defaultValue = "0")
    private Integer actualTime;

    @ColumnDefine(comment = "执行结果", type = "text")
    private String executionResult;

    @ColumnDefine(comment = "执行过程记录", type = "json")
    private String executionProcess;

    @ColumnDefine(comment = "AI执行指导", type = "json")
    private String aiGuidance;

    @ColumnDefine(comment = "AI预测状态", type = "json")
    private String aiPrediction;

    @ColumnDefine(comment = "AI风险提醒", type = "json")
    private String aiRiskAlert;

    @ColumnDefine(comment = "质量检查结果", type = "json")
    private String qualityCheckResult;

    @ColumnDefine(comment = "质量评分(1-100)")
    private Integer qualityScore;

    @ColumnDefine(comment = "异常信息", type = "text")
    private String exceptionInfo;

    @ColumnDefine(comment = "异常处理记录", type = "text")
    private String exceptionHandling;

    @ColumnDefine(comment = "跳过原因", type = "text")
    private String skipReason;

    @ColumnDefine(comment = "返工次数", defaultValue = "0")
    private Integer reworkCount;

    @ColumnDefine(comment = "返工原因", type = "text")
    private String reworkReason;

    @ColumnDefine(comment = "附件信息", type = "json")
    private String attachments;

    @ColumnDefine(comment = "备注", type = "text")
    private String remark;

    @ColumnDefine(comment = "技能匹配度", defaultValue = "0")
    private Integer skillMatch;

    @ColumnDefine(comment = "难度评价(1-5)")
    private Integer difficultyRating;

    @ColumnDefine(comment = "满意度评价(1-5)")
    private Integer satisfactionRating;

    @ColumnDefine(comment = "改进建议", type = "text")
    private String improvementSuggestion;
} 