package com.cool.modules.sop.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import org.dromara.autotable.annotation.Index;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * 工单实体
 */
@Getter
@Setter
@Table(value = "sop_work_order", comment = "SOP工单表")
public class WorkOrderEntity extends BaseEntity<WorkOrderEntity> {

    @Index
    @ColumnDefine(comment = "SOP模板ID", type = "bigint", notNull = true)
    private Long sopTemplateId;

    @ColumnDefine(comment = "工单编号", length = 50, notNull = true)
    private String orderNo;

    @ColumnDefine(comment = "工单标题", length = 200, notNull = true)
    private String title;

    @ColumnDefine(comment = "工单描述", type = "text")
    private String description;

    @ColumnDefine(comment = "优先级(1-5)", defaultValue = "3")
    private Integer priority;

    @ColumnDefine(comment = "紧急程度(1-5)", defaultValue = "3")
    private Integer urgency;

    @ColumnDefine(comment = "业务类型", length = 50)
    private String businessType;

    @ColumnDefine(comment = "业务数据", type = "json")
    private String businessData;

    @Index
    @ColumnDefine(comment = "申请人ID", type = "bigint", notNull = true)
    private Long applicantId;

    @ColumnDefine(comment = "申请人姓名", length = 50)
    private String applicantName;

    @ColumnDefine(comment = "申请部门", length = 100)
    private String applicantDept;

    @ColumnDefine(comment = "申请部门ID", type = "bigint")
    private Long applicantDeptId;

    @Index
    @ColumnDefine(comment = "负责人ID", type = "bigint")
    private Long assigneeId;

    @ColumnDefine(comment = "负责人姓名", length = 50)
    private String assigneeName;

    @ColumnDefine(comment = "计划开始时间")
    private Date plannedStartTime;

    @ColumnDefine(comment = "计划结束时间")
    private Date plannedEndTime;

    @ColumnDefine(comment = "实际开始时间")
    private Date actualStartTime;

    @ColumnDefine(comment = "实际结束时间")
    private Date actualEndTime;

    @ColumnDefine(comment = "状态 0:待调度 1:已分配 2:执行中 3:暂停 4:已完成 5:已取消 6:执行失败", defaultValue = "0")
    private Integer status;

    @ColumnDefine(comment = "进度百分比", defaultValue = "0")
    private Integer progress;

    @ColumnDefine(comment = "预估工时(分钟)", defaultValue = "0")
    private Integer estimatedWorkTime;

    @ColumnDefine(comment = "实际工时(分钟)", defaultValue = "0")
    private Integer actualWorkTime;

    @ColumnDefine(comment = "质量评分(1-100)", defaultValue = "0")
    private Integer qualityScore;

    @ColumnDefine(comment = "AI调度标识", defaultValue = "false")
    private Boolean aiScheduled;

    @ColumnDefine(comment = "AI调度配置", type = "json")
    private String aiScheduleConfig;

    @ColumnDefine(comment = "AI预测完成时间")
    private Date aiPredictedEndTime;

    @ColumnDefine(comment = "AI风险评估", type = "json")
    private String aiRiskAssessment;

    @ColumnDefine(comment = "执行团队", type = "json")
    private String executionTeam;

    @ColumnDefine(comment = "备注", type = "text")
    private String remark;

    @ColumnDefine(comment = "关联业务ID", type = "bigint")
    private Long relatedBusinessId;

    @ColumnDefine(comment = "关联业务类型", length = 50)
    private String relatedBusinessType;

    @ColumnDefine(comment = "执行结果", type = "text")
    private String executionResult;

    @ColumnDefine(comment = "失败原因", type = "text")
    private String failureReason;

    @ColumnDefine(comment = "客户满意度(1-5)")
    private Integer customerSatisfaction;

    @ColumnDefine(comment = "客户反馈", type = "text")
    private String customerFeedback;

    /**
     * 关联项目ID
     */
    @ColumnDefine(comment = "关联项目ID", type = "bigint")
    private Long projectId;

    /**
     * 项目名称 (查询时填充)
     */
    @Column(ignore = true)
    private String projectName;
} 