package com.cool.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 启用状态枚举
 */
@Getter
@AllArgsConstructor
public enum EnableStatusEnum {
    
    DISABLED(0, "禁用"),
    ENABLED(1, "启用");
    
    private final Integer code;
    private final String desc;
    
    public static EnableStatusEnum getByCode(Integer code) {
        for (EnableStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 