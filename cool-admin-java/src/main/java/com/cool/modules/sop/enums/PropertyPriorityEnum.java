package com.cool.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物业服务优先级枚举
 */
@Getter
@AllArgsConstructor
public enum PropertyPriorityEnum {
    
    LOW(1, "低", "一般事务，3天内处理"),
    MEDIUM(2, "中", "常规服务，24小时内处理"),
    HIGH(3, "高", "重要事项，4小时内处理"),
    URGENT(4, "紧急", "紧急事件，1小时内处理");
    
    private final Integer code;
    private final String desc;
    private final String timeLimitDesc;
    
    public static PropertyPriorityEnum getByCode(Integer code) {
        for (PropertyPriorityEnum priority : values()) {
            if (priority.getCode().equals(code)) {
                return priority;
            }
        }
        return null;
    }
} 