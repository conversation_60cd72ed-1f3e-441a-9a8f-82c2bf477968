package com.cool.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物业服务类型枚举
 */
@Getter
@AllArgsConstructor
public enum PropertyServiceTypeEnum {
    
    COMPLAINT_HANDLING("complaint", "投诉处理"),
    REPAIR_SERVICE("repair", "维修服务"),
    CLEANING_SERVICE("cleaning", "清洁保洁"),
    SECURITY_PATROL("security", "安全巡查"),
    FACILITY_MAINTENANCE("facility", "设施维护"),
    GREENING_MAINTENANCE("greening", "绿化养护"),
    PARKING_MANAGEMENT("parking", "停车管理"),
    DECORATION_MANAGEMENT("decoration", "装修管理"),
    MOVING_SERVICE("moving", "搬家服务"),
    EMERGENCY_RESPONSE("emergency", "应急处理"),
    VISITOR_MANAGEMENT("visitor", "访客管理"),
    PACKAGE_SERVICE("package", "快递代收"),
    COMMUNITY_ACTIVITY("activity", "社区活动"),
    PAYMENT_SERVICE("payment", "费用收缴"),
    PROPERTY_INSPECTION("inspection", "房屋查验");
    
    private final String code;
    private final String desc;
    
    public static PropertyServiceTypeEnum getByCode(String code) {
        for (PropertyServiceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 