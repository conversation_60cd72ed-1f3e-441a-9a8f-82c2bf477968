package com.cool.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SOP阶段枚举
 */
@Getter
@AllArgsConstructor
public enum SOPStageEnum {
    
    PRE_STAGE("前期导入", "项目启动前的准备阶段"),
    EXECUTION_STAGE("执行阶段", "项目正式执行阶段"),
    MONITORING_STAGE("监管阶段", "项目执行过程监控阶段"),
    COMPLETION_STAGE("完成阶段", "项目收尾完成阶段"),
    MAINTENANCE_STAGE("维护阶段", "后续维护服务阶段");
    
    private final String stageName;
    private final String description;
    
    public static SOPStageEnum getByName(String stageName) {
        for (SOPStageEnum stage : values()) {
            if (stage.getStageName().equals(stageName)) {
                return stage;
            }
        }
        return null;
    }
} 