package com.cool.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SOP状态枚举
 */
@Getter
@AllArgsConstructor
public enum SOPStatusEnum {
    
    DRAFT(0, "草稿"),
    ACTIVE(1, "启用"),
    INACTIVE(2, "停用"),
    ARCHIVED(3, "归档");
    
    private final Integer code;
    private final String desc;
    
    public static SOPStatusEnum getByCode(Integer code) {
        for (SOPStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 