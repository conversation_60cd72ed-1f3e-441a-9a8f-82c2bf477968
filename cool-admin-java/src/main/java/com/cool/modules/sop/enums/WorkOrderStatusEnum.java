package com.cool.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单状态枚举
 */
@Getter
@AllArgsConstructor
public enum WorkOrderStatusEnum {
    
    PENDING(0, "待调度"),
    ASSIGNED(1, "已分配"),
    IN_PROGRESS(2, "执行中"),
    PAUSED(3, "暂停"),
    COMPLETED(4, "已完成"),
    CANCELLED(5, "已取消"),
    FAILED(6, "执行失败");
    
    private final Integer code;
    private final String desc;
    
    public static WorkOrderStatusEnum getByCode(Integer code) {
        for (WorkOrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 