package com.cool.modules.sop.mapper;

import com.cool.modules.task.entity.TaskExecutionEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务执行人Mapper
 */
@Mapper
public interface TaskExecutionMapper extends BaseMapper<TaskExecutionEntity> {

    /**
     * 根据任务ID查询执行人记录
     */
    List<TaskExecutionEntity> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据用户ID查询执行记录
     */
    List<TaskExecutionEntity> selectByAssigneeId(@Param("assigneeId") Long assigneeId);

    /**
     * 根据任务ID删除执行记录
     */
    int deleteByTaskId(@Param("taskId") Long taskId);


    /**
     * 查询用户当前任务数量
     */
    int countCurrentTasksByUserId(@Param("assigneeId") Long assigneeId);

    /**
     * 查询用户历史绩效评分
     */
    Double selectUserPerformanceScore(@Param("assigneeId") Long assigneeId);

    /**
     * 查询待分配的任务列表
     */
    List<Long> selectUnassignedTaskIds();

    /**
     * 检查任务是否已分配给指定用户
     */
    boolean existsByTaskIdAndAssigneeId(@Param("taskId") Long taskId, @Param("assigneeId") Long assigneeId);
}
