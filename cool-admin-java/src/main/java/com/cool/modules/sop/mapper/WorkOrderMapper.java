package com.cool.modules.sop.mapper;

import com.mybatisflex.core.BaseMapper;
import com.cool.modules.sop.entity.WorkOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 工作工单 Mapper
 */
@Mapper
public interface WorkOrderMapper extends BaseMapper<WorkOrderEntity> {

    /**
     * 根据ID获取工单详情（关联项目信息）
     */
    WorkOrderEntity selectWorkOrderWithDetailsById(@Param("workOrderId") Long workOrderId);

    /**
     * 分页查询工单列表（关联项目信息）
     */
    List<WorkOrderEntity> selectWorkOrderWithDetails(@Param("params") Map<String, Object> params);

    /**
     * 查询工单列表总数（关联项目信息）
     */
    int countWorkOrderWithDetails(@Param("params") Map<String, Object> params);
} 