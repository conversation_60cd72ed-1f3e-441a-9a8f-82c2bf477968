package com.cool.modules.sop.service;

import com.cool.modules.sop.dto.ai.SOPParseResult;
import com.cool.modules.sop.dto.ai.TaskScheduleResult;
import com.cool.modules.sop.dto.ai.TaskGenerateRequest;
import com.cool.modules.sop.dto.ai.TaskGenerateResponse;
import com.cool.modules.sop.dto.ai.MultiDepartmentGenerateResponse;
import com.cool.modules.sop.dto.ai.CommonAITypes;
import com.cool.modules.sop.dto.ai.AIRecognitionResult;

import java.util.List;
import java.util.Map;

/**
 * AI大语言模型服务接口
 * 提供SOP相关的AI能力支持
 */
public interface AILLMService {


    /**
     * 获取AI场景建议
     * 基于用户输入获取推荐的SOP场景
     *
     * @param description 用户描述
     * @return 场景建议列表
     */
    List<Map<String, Object>> suggestScenarios(String description);

    /**
     * 自然语言SOP解析
     * 将用户的自然语言描述解析为结构化的SOP模板
     *
     * @param naturalLanguageDescription 自然语言描述
     * @param industryId 行业ID
     * @param additionalContext 额外上下文信息
     * @return 解析结果
     */
    SOPParseResult parseNaturalLanguageSOP(String naturalLanguageDescription, Long industryId, String additionalContext);

    /**
     * 智能任务调度
     * 基于资源、技能、历史数据等因素进行任务的最优调度
     *
     * @param workOrderIds 工单ID列表
     * @return 调度结果
     */
    TaskScheduleResult scheduleTask(List<Long> workOrderIds);

    /**
     * 实时执行指导
     * 根据当前任务执行情况提供个性化的AI指导建议
     *
     * @param taskId 任务ID
     * @param currentSituation 当前情况描述
     * @return 执行指导建议
     */
    CommonAITypes.ExecutionGuidance getExecutionGuidance(Long taskId, String currentSituation);

    /**
     * 智能质量检查
     * 自动评估任务执行质量并给出改进建议
     *
     * @param taskId 任务ID
     * @param taskDetail 任务详情
     * @return 质量检查结果
     */
    Map<String, Object> qualityCheck(Long taskId, Map<String, Object> taskDetail);

    /**
     * 流程优化建议
     * 基于执行数据分析提供流程优化建议
     *
     * @param sopTemplateId SOP模板ID
     * @param executionHistory 执行历史数据
     * @return 优化建议
     */
    List<Map<String, Object>> optimizeProcess(Long sopTemplateId, List<Map<String, Object>> executionHistory);

    /**
     * 知识图谱构建
     * 基于SOP执行数据构建知识图谱
     *
     * @param industryId 行业ID
     * @param executionData 执行数据
     * @return 知识图谱构建结果
     */
    Map<String, Object> buildKnowledgeGraph(Long industryId, List<Map<String, Object>> executionData);

    /**
     * 预测分析
     * 基于历史数据预测任务执行时间、风险等
     *
     * @param taskData 任务数据
     * @return 预测分析结果
     */
    List<Map<String, Object>> predictiveAnalysis(Map<String, Object> params);

    /**
     * 分析用户输入并匹配SOP场景
     */
    String selectScenario(String taskDescription, String scenariosContext);

    /**
     * AI场景识别能力（仅AI能力，不含业务流）
     */
    AIRecognitionResult performUnifiedAIRecognition(String taskDescription, String scenariosContext);

    /**
     * AI对话能力（仅AI能力，不含业务流）
     */
    String chat(String prompt, String context);
} 