package com.cool.modules.sop.service;

import com.cool.modules.sop.dto.TaskAssignmentDTO;
import com.cool.modules.task.entity.TaskInfoEntity;

import java.util.List;

/**
 * LLM任务调度服务接口
 */
public interface LLMScheduleService {

    /**
     * 使用LLM进行任务分配决策
     *
     * @param taskInfo   任务信息
     * @param candidates 候选人列表
     * @param config     分配配置
     * @return 分配结果
     */
    TaskAssignmentDTO.AssignmentResult llmAssignTask(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config
    );

    /**
     * 构建LLM提示词
     *
     * @param taskInfo   任务信息
     * @param candidates 候选人列表
     * @param config     分配配置
     * @return 提示词
     */
    String buildPrompt(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config
    );

    /**
     * 解析LLM响应
     *
     * @param response   LLM响应
     * @param candidates 候选人列表
     * @return 分配结果
     */
    TaskAssignmentDTO.AssignmentResult parseResponse(
            String response,
            List<TaskAssignmentDTO.UserProfile> candidates
    );

    /**
     * 基于规则的候选人预筛选
     *
     * @param taskInfo      任务信息
     * @param allUsers      所有用户
     * @param config        分配配置
     * @return 候选人列表
     */
    List<TaskAssignmentDTO.UserProfile> preFilterCandidates(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> allUsers,
            TaskAssignmentDTO.AssignmentConfig config
    );

    /**
     * 基于规则的评分
     *
     * @param taskInfo   任务信息
     * @param candidates 候选人列表
     * @param config     分配配置
     * @return 评分后的候选人列表
     */
    List<TaskAssignmentDTO.UserProfile> scoreByRules(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config
    );

    /**
     * 降级到规则引擎分配
     *
     * @param taskInfo   任务信息
     * @param candidates 候选人列表
     * @param config     分配配置
     * @return 分配结果
     */
    TaskAssignmentDTO.AssignmentResult fallbackToRules(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config
    );

    /**
     * 计算技能匹配度
     *
     * @param requiredSkills 需要的技能
     * @param userSkills     用户技能
     * @return 匹配度 0-1
     */
    double calculateSkillMatch(List<String> requiredSkills, List<TaskAssignmentDTO.UserSkill> userSkills);

    /**
     * 计算地理位置评分
     *
     * @param taskLocation 任务地点
     * @param userLocation 用户地点
     * @return 评分 0-1
     */
    double calculateLocationScore(String taskLocation, String userLocation);

    /**
     * 计算工作量评分
     *
     * @param currentWorkload 当前工作量
     * @return 评分 0-1
     */
    double calculateWorkloadScore(Integer currentWorkload);

    /**
     * 计算绩效评分
     *
     * @param performance 历史绩效
     * @return 评分 0-1
     */
    double calculatePerformanceScore(Integer performance);
}
