package com.cool.modules.sop.service;

import com.cool.core.base.BaseService;
import com.cool.modules.sop.entity.SOPIndustryEntity;

import java.util.List;

/**
 * SOP行业服务接口
 */
public interface SOPIndustryService extends BaseService<SOPIndustryEntity> {

    /**
     * 获取启用的行业列表
     * @return 行业列表
     */
    List<SOPIndustryEntity> getActiveIndustries();

    /**
     * 启用行业
     * @param industryId 行业ID
     */
    void enableIndustry(Long industryId);

    /**
     * 禁用行业
     * @param industryId 行业ID
     */
    void disableIndustry(Long industryId);

    /**
     * 根据编码获取行业
     * @param code 行业编码
     * @return 行业信息
     */
    SOPIndustryEntity getByCode(String code);

    /**
     * 根据名称搜索行业
     * @param name 行业名称
     * @return 行业列表
     */
    List<SOPIndustryEntity> searchByName(String name);

    /**
     * 更新排序
     * @param industryId 行业ID
     * @param sort 排序值
     */
    void updateSort(Long industryId, Integer sort);
} 