package com.cool.modules.sop.service;

import com.cool.core.base.BaseService;
import com.cool.modules.sop.entity.SOPStepEntity;

import java.util.List;

/**
 * SOP步骤服务接口
 */
public interface SOPStepService extends BaseService<SOPStepEntity> {

    /**
     * 根据模板ID获取步骤列表
     * @param templateId 模板ID
     * @return 步骤列表
     */
    List<SOPStepEntity> getByTemplateId(Long templateId);

    /**
     * 更新步骤顺序
     * @param stepId 步骤ID
     * @param newOrder 新顺序
     */
    void updateStepOrder(Long stepId, Integer newOrder);

    /**
     * 批量创建步骤
     * @param templateId 模板ID
     * @param steps 步骤列表
     */
    void batchCreateSteps(Long templateId, List<SOPStepEntity> steps);

    /**
     * 根据模板ID删除所有步骤
     * @param templateId 模板ID
     */
    void deleteByTemplateId(Long templateId);
} 