package com.cool.modules.sop.service;

import com.cool.modules.sop.dto.TaskAssignmentDTO;
import com.cool.modules.task.entity.TaskInfoEntity;

import java.util.List;

/**
 * 任务分配服务接口
 */
public interface TaskAssignmentService {

    /**
     * 单个任务智能分配
     *
     * @param taskId 任务ID
     * @param config 分配配置
     * @return 分配结果
     */
    TaskAssignmentDTO.AssignmentResult assignTask(Long taskId, TaskAssignmentDTO.AssignmentConfig config);

    /**
     * 批量任务智能分配
     *
     * @param taskIds 任务ID列表
     * @param config  分配配置
     * @return 批量分配结果
     */
    TaskAssignmentDTO.BatchAssignmentResult batchAssignTasks(List<Long> taskIds, TaskAssignmentDTO.AssignmentConfig config);

    /**
     * 手动调整任务分配
     *
     * @param taskId  任务ID
     * @param userIds 新的执行人ID列表
     * @param reason  调整原因
     * @return 是否成功
     */
    boolean adjustTaskAssignment(Long taskId, List<Long> userIds, String reason);

    /**
     * 获取可分配的用户列表
     *
     * @param requiredRole 需要的角色
     * @param taskInfo     任务信息
     * @return 用户档案列表
     */
    List<TaskAssignmentDTO.UserProfile> getAvailableUsers(String requiredRole, TaskInfoEntity taskInfo);

    /**
     * 获取任务的当前分配情况
     *
     * @param taskId 任务ID
     * @return 分配情况
     */
    List<TaskAssignmentDTO.UserProfile> getCurrentAssignees(Long taskId);

    /**
     * 移除任务分配
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param reason 移除原因
     * @return 是否成功
     */
    boolean removeTaskAssignment(Long taskId, Long userId, String reason);

    /**
     * 获取用户的任务分配历史
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 分配历史
     */
    List<TaskAssignmentDTO.AssignmentResult> getUserAssignmentHistory(Long userId, Integer limit);

    /**
     * 获取任务的分配历史
     *
     * @param taskId 任务ID
     * @return 分配历史
     */
    List<TaskAssignmentDTO.AssignmentResult> getTaskAssignmentHistory(Long taskId);

    /**
     * 检查用户是否有分配权限
     *
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasAssignmentPermission(Long userId);

    /**
     * 检查用户是否有调整权限
     *
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasAdjustmentPermission(Long userId);
}
