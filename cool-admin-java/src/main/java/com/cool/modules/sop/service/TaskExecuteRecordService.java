package com.cool.modules.sop.service;

import com.cool.core.base.BaseService;
import com.cool.modules.sop.entity.TaskExecuteRecordEntity;

import java.util.Map;
import java.util.List;

/**
 * 任务执行记录服务接口
 */
public interface TaskExecuteRecordService extends BaseService<TaskExecuteRecordEntity> {

    /**
     * 开始执行任务
     * @param taskId 任务ID
     */
    void startTask(Long taskId);

    /**
     * 暂停任务
     * @param taskId 任务ID
     */
    void pauseTask(Long taskId);

    /**
     * 恢复任务
     * @param taskId 任务ID
     */
    void resumeTask(Long taskId);

    /**
     * 完成任务
     * @param taskId 任务ID
     */
    void completeTask(Long taskId);

    /**
     * 获取任务统计信息
     * @param assigneeId 负责人ID
     * @return 统计信息
     */
    Map<String, Object> getTaskStats(Long assigneeId);

    /**
     * 根据筛选条件获取任务列表
     * @param filter 筛选条件
     * @param assigneeId 负责人ID
     * @return 任务列表
     */
    List<TaskExecuteRecordEntity> getTasksByFilter(String filter, Long assigneeId);

    /**
     * 记录任务执行日志
     * @param taskId 任务ID
     * @param logContent 日志内容
     * @param logType 日志类型
     */
    void addExecutionLog(Long taskId, String logContent, String logType);

    /**
     * 获取任务执行日志
     * @param taskId 任务ID
     * @return 执行日志列表
     */
    List<Map<String, Object>> getExecutionLogs(Long taskId);

    /**
     * 质量检查
     * @param taskId 任务ID
     * @param checkResult 检查结果
     * @return 检查结果
     */
    Map<String, Object> qualityCheck(Long taskId, Map<String, Object> checkResult);

    /**
     * 获取任务详情（包含执行记录）
     * @param taskId 任务ID
     * @return 任务详情
     */
    Map<String, Object> getTaskDetail(Long taskId);
} 