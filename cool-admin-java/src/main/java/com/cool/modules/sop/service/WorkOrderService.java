package com.cool.modules.sop.service;

import com.cool.core.base.BaseService;
import com.cool.modules.sop.entity.WorkOrderEntity;
import com.cool.modules.sop.dto.ai.TaskScheduleResult;

import java.util.Map;
import java.util.List;

/**
 * 工作工单服务接口
 */
public interface WorkOrderService extends BaseService<WorkOrderEntity> {

    /**
     * 开始执行工单
     * @param workOrderId 工单ID
     */
    void startWorkOrder(Long workOrderId);

    /**
     * 暂停工单
     * @param workOrderId 工单ID
     */
    void pauseWorkOrder(Long workOrderId);

    /**
     * 恢复工单
     * @param workOrderId 工单ID
     */
    void resumeWorkOrder(Long workOrderId);

    /**
     * 完成工单
     * @param workOrderId 工单ID
     */
    void completeWorkOrder(Long workOrderId);

    /**
     * 取消工单
     * @param workOrderId 工单ID
     * @param reason 取消原因
     */
    void cancelWorkOrder(Long workOrderId, String reason);

    /**
     * 应用AI调度结果
     * @param scheduleResult 调度结果
     */
    void applyScheduleResult(TaskScheduleResult scheduleResult);

    /**
     * 获取工单统计信息
     * @param assigneeId 负责人ID
     * @param timeRange 时间范围
     * @return 统计信息
     */
    Map<String, Object> getWorkOrderStats(Long assigneeId, String timeRange);

    /**
     * 获取工单详情
     * @param workOrderId 工单ID
     * @return 工单详情（包含任务列表）
     */
    Map<String, Object> getWorkOrderDetail(Long workOrderId);

    /**
     * 基于SOP模板创建工单
     * @param templateId 模板ID
     * @param workOrder 工单信息
     * @return 创建的工单
     */
    WorkOrderEntity createFromTemplate(Long templateId, WorkOrderEntity workOrder);

    /**
     * 获取工单执行进度
     * @param workOrderId 工单ID
     * @return 进度信息
     */
    Map<String, Object> getWorkOrderProgress(Long workOrderId);

    /**
     * 批量分配工单
     * @param workOrderIds 工单ID列表
     * @param assigneeId 负责人ID
     */
    void batchAssign(List<Long> workOrderIds, Long assigneeId);

    /**
     * 获取执行历史
     * @param workOrderId 工单ID
     * @return 执行历史
     */
    List<Map<String, Object>> getExecutionHistory(Long workOrderId);

    /**
     * 智能调度工单
     * @param workOrderIds 工单ID列表
     * @return 调度结果
     */
    TaskScheduleResult intelligentSchedule(List<Long> workOrderIds);

    /**
     * 获取工单统计信息
     * @param assigneeId 负责人ID
     * @return 统计信息
     */
    Map<String, Object> getWorkOrderStats(Long assigneeId);

    /**
     * 获取高优先级工单
     * @param limit 数量限制
     * @return 工单列表
     */
    List<WorkOrderEntity> getHighPriorityWorkOrders(Integer limit);

    /**
     * 获取超期工单
     * @return 工单列表
     */
    List<WorkOrderEntity> getOverdueWorkOrders();
} 