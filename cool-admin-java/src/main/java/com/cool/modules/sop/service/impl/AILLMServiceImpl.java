package com.cool.modules.sop.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cool.core.ai.OpenAIService;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.core.util.StringUtils;
import com.cool.modules.dify.dto.DifyWorkflowBlockingResponse;
import com.cool.modules.dify.dto.DifyWorkflowExecuteRequest;
import com.cool.modules.dify.emuns.DifyWorkflowEnums;
import com.cool.modules.dify.service.DifyWorkflowService;
import com.cool.modules.sop.dto.ai.AIRecognitionResult;
import com.cool.modules.sop.dto.ai.CommonAITypes;
import com.cool.modules.sop.dto.ai.MultiDepartmentGenerateResponse;
import com.cool.modules.sop.dto.ai.SOPParseResult;
import com.cool.modules.sop.dto.ai.TaskGenerateRequest;
import com.cool.modules.sop.dto.ai.TaskScheduleResult;
import com.cool.modules.sop.entity.AiTaskGenerateRecordEntity;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.entity.SOPStepEntity;
import com.cool.modules.sop.service.AILLMService;
import com.cool.modules.sop.service.AiTaskGenerateRecordService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.json.JSONObject;

/**
 * AI大语言模型服务实现
 */
@Service
public class AILLMServiceImpl implements AILLMService {

    private static final Logger log = LoggerFactory.getLogger(AILLMServiceImpl.class);

    @Autowired
    private OpenAIService openAIService;
    @Autowired
    private SOPScenarioService sopScenarioService;
    @Autowired
    private DifyWorkflowService difyWorkflowService;


    @Override
    public AIRecognitionResult performUnifiedAIRecognition(String taskDescription, String scenariosContext) {
        // log.info("开始执行统一AI场景识别...");
        // log.debug("传入的任务描述: {}", taskDescription);

        // String systemPrompt = "你是一个专业的业务流程分析师。" +
        //     "请根据以下任务描述，从提供的SOP场景列表中选择最匹配的一个。" +
        //     "同时，请识别出任务的优先级（1-5，5为最高）和具体的时间范围（格式：yyyy-MM-dd HH:mm:ss）。" +
        //     "请以JSON格式返回结果，包含 'scenarioCode', 'priority', 'timeInfo': {'startTime': '...', 'endTime': '...'} 字段。" +
        //     "如果无法识别，请在对应字段返回null。";

        // String userMessage = String.format(
        //     "任务描述：\"%s\"\n\nSOP场景列表（JSON格式）:\n%s",
        //     taskDescription, scenariosContext
        // );

        try {
            log.info("向LLM发送场景识别请求...");
            DifyWorkflowExecuteRequest request = new DifyWorkflowExecuteRequest();
            String token = CoolSecurityUtil.getToken();
            request.setWorkflowName(DifyWorkflowEnums.SOP_FRI);
            request.setInputs(Map.of("input", taskDescription, "token", token));
            request.setUser(CoolSecurityUtil.getCurrentUserId().toString());
            DifyWorkflowBlockingResponse rawResponse = difyWorkflowService.executeWorkflowByName(request);
            String output = rawResponse.getData().getString("output");
            log.info("从LLM收到场景识别响应: {}", rawResponse);

            // 清理LLM可能返回的markdown代码块
            String cleanedJson = StringUtils.cleanMarkdown(output);

            ObjectMapper objectMapper = new ObjectMapper();
            AIRecognitionResult result = objectMapper.readValue(cleanedJson, AIRecognitionResult.class);

            // 修复点：兼容只返回scenarioCode的情况
            if (result.getScenario() == null && result.getScenarioCode() != null) {
                // 根据scenarioCode查库
                SOPScenarioEntity scenarioEntity = sopScenarioService.getOne(QueryWrapper.create().eq("scenario_code", result.getScenarioCode()));
                if (scenarioEntity != null) {
                    AIRecognitionResult.ScenarioRecognition scenario = new AIRecognitionResult.ScenarioRecognition();
                    scenario.setScenarioCode(scenarioEntity.getScenarioCode());
                    scenario.setScenarioId(scenarioEntity.getId());
                    scenario.setScenarioName(scenarioEntity.getScenarioName());
                    result.setScenario(scenario);
                } else {
                    log.warn("AI识别的场景编码无效: {}", result.getScenarioCode());
                    return AIRecognitionResult.error("AI识别的场景编码无效: " + result.getScenarioCode());
                }
            }

            if (result.getScenario() == null || result.getScenario().getScenarioCode() == null) {
                log.warn("LLM未能识别出场景。");
                return AIRecognitionResult.error("AI未能识别出明确的业务场景，请尝试更具体的描述。");
            }

            SOPScenarioEntity scenario = sopScenarioService.getOne(QueryWrapper.create().eq("scenario_code", result.getScenario().getScenarioCode()));
            if (scenario == null) {
                 return AIRecognitionResult.error("AI识别的场景编码无效: " + result.getScenario().getScenarioCode());
            }

            result.getScenario().setScenarioId(scenario.getId());
            result.getScenario().setScenarioName(scenario.getScenarioName());

            result.setSuccess(true);
            log.info("AI场景识别成功，匹配场景: {}", result.getScenario().getScenarioCode());
            return result;

        } catch (Exception e) {
            log.error("AI场景识别失败，调用LLM时发生异常", e);
            return AIRecognitionResult.error("调用AI服务进行场景识别时出错: " + e.getMessage());
        }
    }

    @Override
    public String chat(String prompt, String context) {
        try {
            return openAIService.chat(prompt, context);
        } catch (Exception e) {
            log.error("AI对话失败", e);
            return null;
        }
    }

    @Override
    public List<Map<String, Object>> suggestScenarios(String description) {
        return new ArrayList<>();
    }

    @Override
    public String selectScenario(String description, String context) {
        try {
            return openAIService.chat(description, context);
        } catch (Exception e) {
            log.error("AI场景选择失败", e);
            return null;
        }
    }

    @Override
    public CommonAITypes.ExecutionGuidance getExecutionGuidance(Long taskId, String currentSituation) {
        return null;
    }

    @Override
    public Map<String, Object> qualityCheck(Long taskId, Map<String, Object> taskDetail) {
        return new java.util.HashMap<>();
    }

    @Override
    public List<Map<String, Object>> predictiveAnalysis(Map<String, Object> params) {
        return null;
    }

    @Override
    public Map<String, Object> buildKnowledgeGraph(Long industryId, List<Map<String, Object>> executionData) {
        return null;
    }

    @Override
    public SOPParseResult parseNaturalLanguageSOP(String naturalLanguageDescription, Long industryId, String additionalContext) {
        return null;
    }

    @Override
    public List<Map<String, Object>> optimizeProcess(Long sopTemplateId,
            List<Map<String, Object>> executionHistory) {
        return null;
    }

    @Override
    public TaskScheduleResult scheduleTask(List<Long> workOrderIds) {
        return null;
    }

} 