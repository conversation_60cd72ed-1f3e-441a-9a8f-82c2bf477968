package com.cool.modules.sop.service.impl;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.cool.core.base.BaseServiceImpl;
import com.cool.core.exception.CoolException;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysDepartmentService;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.sop.dto.ai.AIRecognitionResult;
import com.cool.modules.sop.dto.ai.MultiDepartmentGenerateResponse;
import com.cool.modules.sop.dto.ai.PreviewResultDTO;
import com.cool.modules.sop.dto.ai.TaskGenerateRequest;
import com.cool.modules.sop.dto.ai.TaskGenerateResponse;
import com.cool.modules.sop.entity.AiTaskGenerateRecordEntity;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.enums.TaskGenerateMode;
import com.cool.modules.sop.mapper.AiTaskGenerateRecordMapper;
import com.cool.modules.sop.service.AILLMService;
import com.cool.modules.sop.service.AiTaskGenerateRecordService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.sop.service.SOPStepService;
import com.cool.modules.sop.service.WorkOrderService;
import com.cool.modules.task.dto.AssignmentRequest;
import com.cool.modules.task.dto.AssignmentResult;
import com.cool.modules.task.dto.TaskPreviewDTO;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.enums.AssignmentTypeEnum;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.service.AutoAssignmentService;
import com.cool.modules.task.service.TaskDepartmentPermissionService;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;

@Service
public class AiTaskGenerateRecordServiceImpl
        extends BaseServiceImpl<AiTaskGenerateRecordMapper, AiTaskGenerateRecordEntity>
        implements AiTaskGenerateRecordService {
    private static final Logger log = LoggerFactory.getLogger(AiTaskGenerateRecordServiceImpl.class);

    @Autowired
    private AiTaskGenerateRecordMapper mapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    @Lazy
    private AiTaskGenerateRecordService self;

    @Autowired
    private AILLMService aillmService;

    @Autowired
    private SOPScenarioService sopScenarioService;

    @Autowired
    private WorkOrderService workOrderService;

    @Autowired
    private SOPStepService sopStepService;

    @Autowired
    private TaskDepartmentPermissionService taskDepartmentPermissionService;

    @Autowired
    private TaskPackageService taskPackageService;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private AutoAssignmentService autoAssignmentService;

    @Autowired
    private BaseSysDepartmentService baseSysDepartmentService;

    @Autowired
    private BaseSysUserService baseSysUserService;

    @Autowired
    private TaskExecutionService taskExecutionService;

    @Autowired
    private com.cool.modules.organization.service.ProjectInfoService projectInfoService;

    private final Map<Long, List<SseEmitter>> emitterMap = new ConcurrentHashMap<>();

    @Override
    public Long submitAsyncTask(AiTaskGenerateRecordEntity record) {
        log.info("[submitAsyncTask] 线程:{} 用户:{} 权限:{}", Thread.currentThread().getName(),
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication(),
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication() != null
                        ? org.springframework.security.core.context.SecurityContextHolder.getContext()
                                .getAuthentication().getAuthorities()
                        : null);
        record.setStatus(0); // 排队中
        record.setProgress(0);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        mapper.insert(record);
        self.asyncGenerateTask(record.getId());
        return record.getId();
    }

    @Override
    @org.springframework.scheduling.annotation.Async("taskExecutor")
    public void asyncGenerateTask(Long taskId) {
        try {
            updateStatusAndProgress(taskId, 1, 5, null, "任务已开始");
            AiTaskGenerateRecordEntity record = mapper.selectOneById(taskId);
            if (record == null) {
                throw new com.cool.core.exception.CoolException("任务记录不存在: " + taskId);
            }
            TaskGenerateRequest params = objectMapper.readValue(record.getParams(), TaskGenerateRequest.class);

            // 第一步：需求确认
            addProgressLog(taskId, "接收到用户需求：" + params.getTaskDescription());
            updateStatusAndProgress(taskId, null, 10, null, null);

            // 第二步：AI综合识别
            addProgressLog(taskId, "AI正在进行综合识别（场景匹配、时间分析、优先级评估）...");
            updateStatusAndProgress(taskId, null, 20, null, null);

            // 这里应该调用AI识别服务，但由于当前方法不完整，暂时保留原有逻辑
            // TODO: 完善AI识别和任务生成逻辑

        } catch (Exception e) {
            log.error("[asyncGenerateTask] 任务ID:{} 发生异常:{}", taskId, e.getMessage(), e);
            String errorMessage = (e instanceof com.cool.core.exception.CoolException) ? e.getMessage()
                    : "任务生成过程中发生未知错误";
            updateStatusAndProgress(taskId, 3, 100, errorMessage, "任务生成失败: " + errorMessage);
        }
    }

    /**
     * 新增：统一的异步任务生成提交入口
     */
    @Override
    public Long submitAsyncTaskGeneration(TaskGenerateRequest request, TaskGenerateMode mode) {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        ensureDepartmentIds(request, userId);

        AiTaskGenerateRecordEntity record = new AiTaskGenerateRecordEntity();
        try {
            record.setStatus(0); // 排队中
            record.setProgress(0);
            record.setMode(mode.getCode());
            record.setParams(objectMapper.writeValueAsString(request));
            record.setTaskDesc(request.getTaskDescription());
            record.setUserId(userId);
            // 补充发起人名称，供前端显示
            record.setUserName(getUserNameById(userId));
        } catch (JsonProcessingException e) {
            log.error("参数序列化失败", e);
            throw new RuntimeException("参数序列化失败", e);
        }
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        save(record);

        Long recordId = record.getId();
        log.info("提交异步任务生成请求，记录ID: {}, 模式: {}", recordId, mode.getDescription());

        // 调用异步处理
        self.processAsyncTaskGeneration(recordId, mode);

        return recordId;
    }

    /**
     * 新增：统一的异步任务生成处理
     */
    @Override
    @Async("taskExecutor")
    public void processAsyncTaskGeneration(Long recordId, TaskGenerateMode mode) {
        log.info("开始异步处理任务生成，记录ID: {}, 模式: {}", recordId, mode.getDescription());

        try {
            AiTaskGenerateRecordEntity record = getById(recordId);
            if (record == null) {
                log.error("无法找到任务记录: {}", recordId);
                return;
            }

            TaskGenerateRequest request = objectMapper.readValue(record.getParams(), TaskGenerateRequest.class);

            // 推送初始状态
            pushSse(recordId, record);
            sleep(300);

            // 第一步：开始任务
            updateProgress(recordId, 1, 5, "开始任务");
            sleep(300);

            // 第二步：需求确认
            updateProgress(recordId, 1, 10, "接收到用户需求：" + request.getTaskDescription());
            sleep(500);

            // 第三步：AI综合识别
            updateProgress(recordId, 1, 20, "AI正在进行综合识别（场景匹配、时间分析、优先级评估）...");
            AIRecognitionResult aiResult = performAIRecognitionWithProgress(recordId, request);

            if (!aiResult.isSuccess()) {
                updateProgress(recordId, 3, 100, "AI识别失败: " + aiResult.getErrorMessage());
                return;
            }

            // 第四步：显示AI识别结果
            displayAIRecognitionResult(recordId, aiResult);
            sleep(800);

            // 第五步：任务构建
            updateProgress(recordId, 1, 60, mode == TaskGenerateMode.PREVIEW ? "任务预览生成中" : "任务生成中");
            List<TaskGenerateResponse> results = buildTasksFromAIResult(request, aiResult);

            // 第六步：根据模式处理结果
            if (mode == TaskGenerateMode.PREVIEW) {
                // 预览模式：存储预览数据，如果选择了智能分配则进行分配
                if (request.getAutoAssign() != null && request.getAutoAssign()) {
                    updateProgress(recordId, 1, 70, "正在进行智能分配...");
                    // 构造AssignmentRequest，调用统一入口
                    AssignmentRequest assignRequest = new AssignmentRequest();
                    assignRequest.setPreviewMode(true);
                    java.util.List<com.cool.modules.task.dto.TaskPreviewDTO> previewList = new java.util.ArrayList<>();
                    for (TaskGenerateResponse result : results) {
                        if (result.getTasks() != null) {
                            for (TaskGenerateResponse.GeneratedTask genTask : result.getTasks()) {
                                TaskPreviewDTO preview = new TaskPreviewDTO();
                                preview.setId(genTask.getPreviewId());
                                preview.setName(genTask.getTaskName());
                                preview.setDescription(genTask.getDescription());
                                preview.setPriority(genTask.getPriority());
                                preview.setEmployeeRole(genTask.getEmployeeRole());
                                preview.setEntityTouchpoint(genTask.getEntityTouchpoint());
                                preview.setEmployeeBehavior(genTask.getEmployeeBehavior());
                                previewList.add(preview);
                            }
                        }
                    }
                    assignRequest.setPreviewTaskList(previewList);
                    AssignmentResult assignResult = autoAssignmentService.executeAssignment(assignRequest);
                    // 将assignResult结果回写到results/前端展示
                    if (assignResult != null && assignResult.getAssignments() != null) {
                        Map<String, AssignmentResult.TaskAssignment> assignmentMap = assignResult.getAssignments()
                                .stream()
                                .collect(java.util.stream.Collectors.toMap(AssignmentResult.TaskAssignment::getTaskId,
                                        a -> a, (a, b) -> a));
                        for (TaskGenerateResponse result : results) {
                            if (result.getTasks() != null) {
                                for (TaskGenerateResponse.GeneratedTask genTask : result.getTasks()) {
                                    AssignmentResult.TaskAssignment assignment = assignmentMap
                                            .get(genTask.getPreviewId());
                                    if (assignment != null && assignment.getAssignees() != null
                                            && !assignment.getAssignees().isEmpty()) {
                                        AssignmentResult.Assignee assignee = assignment.getAssignees().get(0);
                                        genTask.setAssigneeId(assignee.getUserId());
                                        genTask.setAssigneeName(assignee.getUserName());
                                        genTask.setAssigneeRole(assignee.getUserRole());
                                        genTask.setAssignmentReason(assignment.getReason());
                                        genTask.setAssignmentConfidence(assignment.getConfidence());
                                        genTask.setAssignmentType(AssignmentTypeEnum.AI.getCode());
                                        genTask.setIsAssigned(true);
                                    } else {
                                        genTask.setAssigneeId(null);
                                        genTask.setAssigneeName(null);
                                        genTask.setAssigneeRole(null);
                                        genTask.setAssignmentReason("暂无分配建议");
                                        genTask.setAssignmentConfidence(0);
                                        genTask.setIsAssigned(false);
                                    }
                                }
                            }
                        }
                    }
                    updateProgress(recordId, 1, 90, "智能分配完成");
                }
                savePreviewResults(recordId, results, request);
                updateProgress(recordId, 2, 100, "生成预览成功");
            } else if (mode == TaskGenerateMode.GENERATE) {
                // 正式生成模式：持久化任务到数据库
                updateProgress(recordId, 1, 80, "正在创建工单和任务...");
                persistTasksToDatabase(recordId, request, results);
                updateProgress(recordId, 2, 100, "任务生成成功");
            }

            // 推送最终结果
            pushSse(recordId, getById(recordId));
            completeEmitter(recordId);

        } catch (Exception e) {
            log.error("异步任务生成失败，记录ID: {}", recordId, e);
            String errorMessage = (e instanceof CoolException) ? e.getMessage() : "任务生成过程中发生未知错误";
            updateProgress(recordId, 3, 100, "任务生成失败: " + errorMessage);
        }
    }

    /**
     * 新增：接受预览结果并生成正式任务
     */
    @Override
    public Long acceptPreviewAndGenerate(Long previewRecordId) {
        AiTaskGenerateRecordEntity previewRecord = getById(previewRecordId);
        if (previewRecord == null) {
            throw new CoolException("预览记录不存在: " + previewRecordId);
        }

        if (!"preview".equals(previewRecord.getMode())) {
            throw new CoolException("记录不是预览模式，无法接受生成");
        }

        if (previewRecord.getStatus() != 2) {
            throw new CoolException("预览任务未完成，无法接受生成");
        }

        try {
            // 创建新的生成记录
            TaskGenerateRequest request = objectMapper.readValue(previewRecord.getParams(), TaskGenerateRequest.class);

            AiTaskGenerateRecordEntity generateRecord = new AiTaskGenerateRecordEntity();
            generateRecord.setStatus(0);
            generateRecord.setProgress(0);
            generateRecord.setMode(TaskGenerateMode.ACCEPT_PREVIEW.getCode());
            generateRecord.setParentRecordId(previewRecordId);
            generateRecord.setParams(previewRecord.getParams());
            generateRecord.setTaskDesc(previewRecord.getTaskDesc());
            generateRecord.setUserId(previewRecord.getUserId());
            // 继承发起人名称，避免前端为空
            generateRecord.setUserName(getUserNameById(previewRecord.getUserId()));
            generateRecord.setCreateTime(new Date());
            generateRecord.setUpdateTime(new Date());
            save(generateRecord);

            Long recordId = generateRecord.getId();
            log.info("接受预览结果生成任务，预览记录ID: {}, 新记录ID: {}", previewRecordId, recordId);

            // 异步处理任务生成（基于预览结果）
            processAcceptPreviewGeneration(recordId, previewRecordId);

            return recordId;

        } catch (JsonProcessingException e) {
            log.error("反序列化预览记录参数失败", e);
            throw new CoolException("预览记录参数解析失败");
        }
    }

    /**
     * 新增：调整预览任务的执行人分配
     */
    public Map<String, Object> adjustPreviewAssignment(Long recordId, Integer taskIndex, Long newAssigneeId,
            String reason) {
        try {
            AiTaskGenerateRecordEntity record = getById(recordId);
            if (record == null) {
                throw new CoolException("预览记录不存在: " + recordId);
            }

            if (!"preview".equals(record.getMode())) {
                throw new CoolException("记录不是预览模式，无法调整分配");
            }

            if (record.getResult() == null || record.getResult().isEmpty()) {
                throw new CoolException("预览记录没有结果数据");
            }

            // 解析预览结果
            Map<String, Object> previewData = objectMapper.readValue(record.getResult(),
                    new TypeReference<Map<String, Object>>() {
                    });

            // 检查是否为多部门数据
            Boolean isMultiDepartment = getBooleanValue(previewData, "multiDepartment");
            Map<String, Object> targetTask = null;

            if (isMultiDepartment != null && isMultiDepartment) {
                // 处理多部门数据
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> departments = (List<Map<String, Object>>) previewData.get("departments");
                if (departments == null || departments.isEmpty()) {
                    throw new CoolException("多部门预览数据中没有部门信息");
                }

                // 在所有部门中查找指定索引的任务
                int currentIndex = 0;
                boolean found = false;
                for (Map<String, Object> dept : departments) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> tasks = (List<Map<String, Object>>) dept.get("tasks");
                    if (tasks != null) {
                        if (taskIndex >= currentIndex && taskIndex < currentIndex + tasks.size()) {
                            targetTask = tasks.get(taskIndex - currentIndex);
                            found = true;
                            break;
                        }
                        currentIndex += tasks.size();
                    }
                }

                if (!found) {
                    throw new CoolException("在多部门数据中未找到指定索引的任务: " + taskIndex);
                }
            } else {
                // 处理单部门数据（保持向后兼容）
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> tasks = (List<Map<String, Object>>) previewData.get("tasks");
                if (tasks == null || taskIndex >= tasks.size()) {
                    throw new CoolException("任务索引无效");
                }
                targetTask = tasks.get(taskIndex);
            }

            if (targetTask == null) {
                throw new CoolException("未找到目标任务");
            }

            // 调用智能分配服务获取新的分配信息
            if (newAssigneeId != null) {
                // 获取用户信息
                String assigneeName = getUserNameById(newAssigneeId);
                String assigneeRole = getUserRoleById(newAssigneeId);

                targetTask.put("assigneeId", newAssigneeId);
                targetTask.put("assigneeName", assigneeName);
                targetTask.put("assigneeRole", assigneeRole);
                targetTask.put("assignmentReason", reason != null ? reason : "手动调整");
                targetTask.put("assignmentConfidence", 100);
                targetTask.put("isAssigned", true);
                targetTask.put("canReassign", true);
                targetTask.put("assignmentStatus", "SUCCESS");
            } else {
                // 取消分配
                targetTask.put("assigneeId", null);
                targetTask.put("assigneeName", null);
                targetTask.put("assigneeRole", null);
                targetTask.put("assignmentReason", null);
                targetTask.put("assignmentConfidence", null);
                targetTask.put("isAssigned", false);
                targetTask.put("canReassign", true);
                targetTask.put("assignmentStatus", "PENDING");
            }

            // 保存更新后的预览结果
            record.setResult(objectMapper.writeValueAsString(previewData));
            record.setUpdateTime(new Date());
            updateById(record);

            log.info("调整预览任务分配成功，记录ID: {}, 任务索引: {}, 新执行人ID: {}", recordId, taskIndex, newAssigneeId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "分配调整成功");
            result.put("taskIndex", taskIndex);
            result.put("newAssigneeId", newAssigneeId);
            result.put("updatedTask", targetTask);

            return result;

        } catch (Exception e) {
            log.error("调整预览任务分配失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "分配调整失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根据用户ID获取真实用户名称
     */
    private String getUserNameById(Long userId) {
        try {
            BaseSysUserEntity user = baseSysUserService.getById(userId);
            if (user != null) {
                return user.getName() != null ? user.getName() : user.getUsername();
            }
            return "未知用户";
        } catch (Exception e) {
            log.warn("获取用户名称失败，用户ID: {}", userId, e);
            return "未知用户";
        }
    }

    /**
     * 根据用户ID获取真实角色名
     */
    private String getUserRoleById(Long userId) {
        try {
            BaseSysUserEntity user = baseSysUserService.getById(userId);
            if (user != null && user.getRoleName() != null) {
                return user.getRoleName();
            }
            return "员工";
        } catch (Exception e) {
            log.warn("获取用户角色失败，用户ID: {}", userId, e);
            return "员工";
        }
    }

    /**
     * 新增：处理接受预览的任务生成
     */
    @Async("taskExecutor")
    public void processAcceptPreviewGeneration(Long recordId, Long previewRecordId) {
        log.info("开始处理接受预览的任务生成，记录ID: {}, 预览记录ID: {}", recordId, previewRecordId);
        try {
            AiTaskGenerateRecordEntity record = getById(recordId);
            AiTaskGenerateRecordEntity previewRecord = getById(previewRecordId);

            if (record == null || previewRecord == null) {
                log.error("记录不存在，记录ID: {}, 预览记录ID: {}", recordId, previewRecordId);
                return;
            }

            TaskGenerateRequest request = objectMapper.readValue(record.getParams(), TaskGenerateRequest.class);

            // 推送初始状态
            pushSse(recordId, record);
            sleep(300);

            updateProgress(recordId, 1, 10, "开始基于预览结果生成任务");
            sleep(300);

            updateProgress(recordId, 1, 30, "读取预览结果数据");

            // 从预览记录中获取结果数据
            if (previewRecord.getResult() == null || previewRecord.getResult().isEmpty()) {
                updateProgress(recordId, 3, 100, "预览记录没有结果数据");
                return;
            }

            // 解析预览结果
            Map<String, Object> previewData = objectMapper.readValue(previewRecord.getResult(),
                    new TypeReference<Map<String, Object>>() {
                    });
            List<TaskGenerateResponse> results = convertPreviewDataToTaskResponses(previewData, request);

            updateProgress(recordId, 1, 60, "正在创建工单和任务...");

            // 持久化任务到数据库
            persistTasksToDatabase(recordId, request, results);

            updateProgress(recordId, 2, 100, "基于预览结果生成任务成功");

            // 推送最终结果
            pushSse(recordId, getById(recordId));
            completeEmitter(recordId);

        } catch (Exception e) {
            log.error("处理接受预览的任务生成失败，记录ID: {}", recordId, e);
            String errorMessage = (e instanceof CoolException) ? e.getMessage() : "基于预览结果生成任务失败";
            updateProgress(recordId, 3, 100, errorMessage);
        }
    }

    /**
     * 提取的公共方法：执行AI识别并推送进度
     */
    private AIRecognitionResult performAIRecognitionWithProgress(Long recordId, TaskGenerateRequest request) {
        AIRecognitionResult aiResult = aillmService.performUnifiedAIRecognition(
                request.getTaskDescription(),
                sopScenarioService.getScenariosAsJsonContext());
        return aiResult;
    }

    /**
     * 提取的公共方法：显示AI识别结果
     */
    private void displayAIRecognitionResult(Long recordId, AIRecognitionResult aiResult) {
        StringBuilder recognitionResult = new StringBuilder();
        recognitionResult.append("AI识别完成 - ");

        if (aiResult.getScenario() != null) {
            recognitionResult.append("场景：").append(aiResult.getScenario().getScenarioName()).append("；");
        }

        if (aiResult.getPriority() != null) {
            String priorityLabel = getPriorityLabel(aiResult.getPriority());
            recognitionResult.append("优先级：").append(priorityLabel).append("；");
        }

        if (aiResult.getTimeInfo() != null && aiResult.getTimeInfo().getStartTime() != null) {
            recognitionResult.append("时间范围：").append(aiResult.getTimeInfo().getStartTime())
                    .append(" 至 ").append(aiResult.getTimeInfo().getEndTime()).append("；");
        } else {
            recognitionResult.append("时间范围：默认从今天开始一个月；");
        }

        updateProgress(recordId, 1, 40, recognitionResult.toString());
    }

    /**
     * 提取的公共方法：从AI识别结果构建任务
     */
    private List<TaskGenerateResponse> buildTasksFromAIResult(TaskGenerateRequest request,
            AIRecognitionResult aiResult) {
        List<TaskGenerateResponse> results = new ArrayList<>();

        // 根据生成模式处理
        String generationMode = request.getGenerationMode() != null ? request.getGenerationMode() : "department";
        log.info("任务生成模式: {}", generationMode);

        // 根据AI识别的场景编码查找SOP场景
        SOPScenarioEntity scenario = sopScenarioService.getOne(
                com.mybatisflex.core.query.QueryWrapper.create()
                        .eq("scenario_code", aiResult.getScenario().getScenarioCode()));

        if (scenario == null) {
            log.error("未找到场景编码为 {} 的SOP场景", aiResult.getScenario().getScenarioCode());
            return results;
        }

        log.info("找到匹配场景: {} (ID: {})", scenario.getScenarioName(), scenario.getId());

        // 获取场景下的所有步骤
        List<com.cool.modules.sop.entity.SOPStepEntity> steps = sopStepService.getByTemplateId(scenario.getId());
        log.info("场景包含步骤数量: {}", steps.size());

        if (steps.isEmpty()) {
            log.warn("场景 {} 下没有找到任何步骤", scenario.getScenarioName());
        }

        if ("project".equals(generationMode)) {
            // 按项目生成任务
            results = buildTasksForProjects(request, scenario, steps, aiResult);
        } else {
            // 按部门生成任务（默认模式）
            results = buildTasksForDepartments(request, scenario, steps, aiResult);
        }

        log.info("任务构建完成，共生成 {} 个响应", results.size());
        return results;
    }

    /**
     * 按部门生成任务
     */
    private List<TaskGenerateResponse> buildTasksForDepartments(TaskGenerateRequest request, SOPScenarioEntity scenario,
            List<com.cool.modules.sop.entity.SOPStepEntity> steps, AIRecognitionResult aiResult) {
        List<TaskGenerateResponse> results = new ArrayList<>();

        // 确保部门ID列表不为空
        List<Long> departmentIds = request.getDepartmentIds();
        if (departmentIds == null || departmentIds.isEmpty()) {
            // 如果没有指定部门，使用当前用户的部门或创建一个默认处理
            log.warn("未指定部门ID，将生成通用任务");
            departmentIds = new ArrayList<>();
            departmentIds.add(0L); // 使用0作为默认部门标识
        }

        log.info("开始根据AI识别结果构建部门任务，场景编码: {}, 部门数量: {}",
                aiResult.getScenario().getScenarioCode(), departmentIds.size());

        // 获取部门信息映射
        Map<Long, String> departmentNameMap = getDepartmentNameMap(departmentIds);

        // 为每个部门生成任务
        for (Long deptId : departmentIds) {
            List<TaskGenerateResponse.GeneratedTask> tasks = new ArrayList<>();

            for (com.cool.modules.sop.entity.SOPStepEntity step : steps) {
                TaskGenerateResponse.GeneratedTask task = buildTaskFromStep(step, scenario, request);
                tasks.add(task);
                log.debug("生成任务: {} (步骤: {})", task.getTaskName(), step.getStepName());
            }

            TaskGenerateResponse.ScenarioInfo scenarioInfo = new TaskGenerateResponse.ScenarioInfo();
            scenarioInfo.setName(scenario.getScenarioName());
            scenarioInfo.setDescription(scenario.getDescription());
            scenarioInfo.setCode(scenario.getScenarioCode());
            scenarioInfo.setId(scenario.getId());

            TaskGenerateResponse response = TaskGenerateResponse.success(scenarioInfo, tasks, "生成成功");

            // 设置部门信息
            response.setDepartmentId(deptId);
            response.setDepartmentName(departmentNameMap.getOrDefault(deptId, deptId == 0L ? "默认部门" : "未知部门"));
            response.setGenerationMode("department");

            results.add(response);

            log.info("为部门 {} ({}) 生成了 {} 个任务", deptId, response.getDepartmentName(), tasks.size());
        }

        return results;
    }

    /**
     * 按项目生成任务
     */
    private List<TaskGenerateResponse> buildTasksForProjects(TaskGenerateRequest request, SOPScenarioEntity scenario,
            List<com.cool.modules.sop.entity.SOPStepEntity> steps, AIRecognitionResult aiResult) {
        List<TaskGenerateResponse> results = new ArrayList<>();

        // 确保项目ID列表不为空
        List<Long> projectIds = request.getProjectIds();
        if (projectIds == null || projectIds.isEmpty()) {
            log.warn("未指定项目ID，无法生成项目任务");
            return results;
        }

        log.info("开始根据AI识别结果构建项目任务，场景编码: {}, 项目数量: {}",
                aiResult.getScenario().getScenarioCode(), projectIds.size());

        // 获取项目信息映射
        Map<Long, String> projectNameMap = getProjectNameMap(projectIds);

        // 为每个项目生成任务
        for (Long projectId : projectIds) {
            List<TaskGenerateResponse.GeneratedTask> tasks = new ArrayList<>();

            for (com.cool.modules.sop.entity.SOPStepEntity step : steps) {
                TaskGenerateResponse.GeneratedTask task = buildTaskFromStep(step, scenario, request);
                // 为项目任务设置项目相关信息
                task.setProjectId(projectId);
                tasks.add(task);
                log.debug("生成项目任务: {} (步骤: {}, 项目: {})", task.getTaskName(), step.getStepName(), projectId);
            }

            TaskGenerateResponse.ScenarioInfo scenarioInfo = new TaskGenerateResponse.ScenarioInfo();
            scenarioInfo.setName(scenario.getScenarioName());
            scenarioInfo.setDescription(scenario.getDescription());
            scenarioInfo.setCode(scenario.getScenarioCode());
            scenarioInfo.setId(scenario.getId());

            TaskGenerateResponse response = TaskGenerateResponse.success(scenarioInfo, tasks, "生成成功");

            // 设置项目信息
            response.setProjectId(projectId);
            response.setProjectName(projectNameMap.getOrDefault(projectId, "未知项目"));
            response.setGenerationMode("project");

            results.add(response);

            log.info("为项目 {} ({}) 生成了 {} 个任务", projectId, response.getProjectName(), tasks.size());
        }

        return results;
    }

    /**
     * 获取项目名称映射
     */
    private Map<Long, String> getProjectNameMap(List<Long> projectIds) {
        Map<Long, String> projectNameMap = new HashMap<>();

        try {
            // 查询项目信息
            List<com.cool.modules.organization.entity.ProjectInfoEntity> projects = projectInfoService.list(
                    com.mybatisflex.core.query.QueryWrapper.create()
                            .in("id", projectIds));

            for (com.cool.modules.organization.entity.ProjectInfoEntity project : projects) {
                projectNameMap.put(project.getId(), project.getProjectName());
            }

        } catch (Exception e) {
            log.error("获取项目信息失败", e);
            // 使用默认名称
            for (Long projectId : projectIds) {
                projectNameMap.put(projectId, "项目" + projectId);
            }
        }

        return projectNameMap;
    }

    /**
     * 获取部门名称映射
     */
    private Map<Long, String> getDepartmentNameMap(List<Long> departmentIds) {
        Map<Long, String> departmentNameMap = new HashMap<>();

        try {
            // 查询部门信息
            List<com.cool.modules.base.entity.sys.BaseSysDepartmentEntity> departments = baseSysDepartmentService.list(
                    com.mybatisflex.core.query.QueryWrapper.create()
                            .in("id", departmentIds));

            for (com.cool.modules.base.entity.sys.BaseSysDepartmentEntity dept : departments) {
                departmentNameMap.put(dept.getId(), dept.getName());
            }

            // 处理默认部门
            if (departmentIds.contains(0L)) {
                departmentNameMap.put(0L, "默认部门");
            }

        } catch (Exception e) {
            log.error("获取部门信息失败", e);
            // 使用默认名称
            for (Long deptId : departmentIds) {
                departmentNameMap.put(deptId, deptId == 0L ? "默认部门" : "部门" + deptId);
            }
        }

        return departmentNameMap;
    }

    /**
     * 提取的公共方法：从步骤构建任务
     */
    private TaskGenerateResponse.GeneratedTask buildTaskFromStep(
            com.cool.modules.sop.entity.SOPStepEntity step,
            SOPScenarioEntity scenario,
            TaskGenerateRequest request) {

        TaskGenerateResponse.GeneratedTask task = new TaskGenerateResponse.GeneratedTask();
        task.setPreviewId(UUID.randomUUID().toString());
        // 场景相关信息
        task.setScenarioId(scenario.getId());
        task.setScenarioCode(scenario.getScenarioCode());
        task.setScenarioName(scenario.getScenarioName());

        // 步骤相关信息
        task.setStepId(step.getId());
        task.setStepCode(step.getStepCode());
        task.setStepName(step.getStepName());

        // 任务基本信息
        task.setTaskType(step.getStepType() != null ? step.getStepType() : "normal");
        task.setTaskCategory("SOP_STEP");
        task.setName(step.getStepName()); // 设置任务名称
        task.setTaskName(step.getStepName()); // 设置任务名称（兼容字段）
        task.setDescription(step.getStepDescription());
        task.setTaskDescription(step.getStepDescription()); // 设置任务描述（兼容字段）
        task.setStatus("pending"); // 设置默认状态为待处理

        // 角色和行为信息
        task.setEmployeeRole(step.getEmployeeRole() != null ? step.getEmployeeRole() : "未指定");
        task.setTaskActivity(step.getUserActivity() != null ? step.getUserActivity() : "");
        task.setEmployeeBehavior(step.getEmployeeBehavior() != null ? step.getEmployeeBehavior() : "");
        task.setWorkHighlight(step.getWorkHighlight() != null ? step.getWorkHighlight() : "");
        task.setEntityTouchpoint(step.getEntityTouchpoint() != null ? step.getEntityTouchpoint() : "");

        // 附件和拍照要求
        task.setPhotoRequired(step.getStepDescription() != null && step.getStepDescription().contains("拍照"));
        task.setAttachmentRequired(step.getStepDescription() != null && step.getStepDescription().contains("附件"));

        // 优先级和时间信息
        task.setPriority(request.getPriority() != null ? request.getPriority() : 3);
        task.setStartTime(request.getStartTime());
        task.setEndTime(request.getEndTime());

        // 预估时长（从步骤获取或设置默认值）
        task.setEstimatedDuration(step.getEstimatedTime() != null ? step.getEstimatedTime() : 30);

        return task;
    }

    /**
     * 提取的公共方法：保存预览结果（统一多部门结构）
     */
    private void savePreviewResults(Long recordId, List<TaskGenerateResponse> results, TaskGenerateRequest request) {
        try {
            AiTaskGenerateRecordEntity record = getById(recordId);
            if (!results.isEmpty()) {
                PreviewResultDTO previewResult = new PreviewResultDTO();
                previewResult.setMode("preview");

                // 根据generationMode设置不同的数据结构
                String generationMode = request.getGenerationMode() != null ? request.getGenerationMode()
                        : "department";

                if ("project".equals(generationMode)) {
                    // 项目模式：设置multiProject为true，构建projects结构
                    previewResult.setMultiProject(true);
                    previewResult.setMultiDepartment(false);

                    List<PreviewResultDTO.ProjectPreview> projects = new ArrayList<>();
                    for (TaskGenerateResponse resp : results) {
                        PreviewResultDTO.ProjectPreview project = new PreviewResultDTO.ProjectPreview();
                        project.setProjectId(resp.getProjectId());
                        project.setProjectName(resp.getProjectName());

                        // 场景
                        PreviewResultDTO.ScenarioInfo scenario = new PreviewResultDTO.ScenarioInfo();
                        if (resp.getScenario() != null) {
                            scenario.setId(resp.getScenario().getId());
                            scenario.setName(resp.getScenario().getName());
                            scenario.setDescription(resp.getScenario().getDescription());
                            scenario.setCode(resp.getScenario().getCode());
                        }
                        project.setScenario(scenario);
                        project.setTasks(resp.getTasks());
                        projects.add(project);
                    }
                    previewResult.setProjects(projects);

                    // 统计信息
                    Map<String, Object> summary = new HashMap<>();
                    summary.put("totalProjects", projects.size());
                    summary.put("totalTasks",
                            projects.stream().mapToInt(p -> p.getTasks() != null ? p.getTasks().size() : 0).sum());
                    if (!projects.isEmpty()) {
                        summary.put("scenario", projects.get(0).getScenario());
                    }
                    previewResult.setSummary(summary);
                } else {
                    // 部门模式：设置multiDepartment为true，构建departments结构
                    previewResult.setMultiDepartment(true);
                    previewResult.setMultiProject(false);

                    List<PreviewResultDTO.DepartmentPreview> departments = new ArrayList<>();
                    for (TaskGenerateResponse resp : results) {
                        PreviewResultDTO.DepartmentPreview dept = new PreviewResultDTO.DepartmentPreview();
                        dept.setDepartmentId(resp.getDepartmentId());
                        dept.setDepartmentName(resp.getDepartmentName());

                        // 场景
                        PreviewResultDTO.ScenarioInfo scenario = new PreviewResultDTO.ScenarioInfo();
                        if (resp.getScenario() != null) {
                            scenario.setId(resp.getScenario().getId());
                            scenario.setName(resp.getScenario().getName());
                            scenario.setDescription(resp.getScenario().getDescription());
                            scenario.setCode(resp.getScenario().getCode());
                        }
                        dept.setScenario(scenario);
                        dept.setTasks(resp.getTasks());
                        departments.add(dept);
                    }
                    previewResult.setDepartments(departments);

                    // 统计信息
                    Map<String, Object> summary = new HashMap<>();
                    summary.put("totalDepartments", departments.size());
                    summary.put("totalTasks",
                            departments.stream().mapToInt(d -> d.getTasks() != null ? d.getTasks().size() : 0).sum());
                    if (!departments.isEmpty()) {
                        summary.put("scenario", departments.get(0).getScenario());
                    }
                    previewResult.setSummary(summary);
                }

                record.setResult(objectMapper.writeValueAsString(previewResult));
            } else {
                PreviewResultDTO previewResult = new PreviewResultDTO();
                previewResult.setMode("preview");

                String generationMode = request.getGenerationMode() != null ? request.getGenerationMode()
                        : "department";
                if ("project".equals(generationMode)) {
                    previewResult.setMultiProject(true);
                    previewResult.setMultiDepartment(false);
                    previewResult.setProjects(new ArrayList<>());
                    previewResult.setSummary(Map.of("totalProjects", 0, "totalTasks", 0));
                } else {
                    previewResult.setMultiDepartment(true);
                    previewResult.setMultiProject(false);
                    previewResult.setDepartments(new ArrayList<>());
                    previewResult.setSummary(Map.of("totalDepartments", 0, "totalTasks", 0));
                }

                record.setResult(objectMapper.writeValueAsString(previewResult));
                addProgressLogToRecord(record, "未生成任何任务，请调整描述");
            }
            record.setStatus(2);
            record.setProgress(100);
            addProgressLogToRecord(record, "生成预览成功");
            updateById(record);
        } catch (JsonProcessingException e) {
            log.error("序列化预览结果失败，任务ID: {}", recordId, e);
            throw new CoolException("序列化预览结果失败");
        }
    }

    /**
     * 提取的公共方法：持久化任务到数据库
     */
    private void persistTasksToDatabase(Long recordId, TaskGenerateRequest request,
            List<TaskGenerateResponse> results) {
        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            String currentUserName = "系统用户"; // 默认用户名，可以根据需要从用户信息中获取
            int totalTasksCreated = 0;
            int totalPackagesCreated = 0;
            int totalWorkOrdersCreated = 0;
            List<Long> createdPackageIds = new ArrayList<>(); // 记录创建的任务包ID，用于智能分配

            for (TaskGenerateResponse result : results) {
                if (result.getScenario() == null || result.getTasks() == null || result.getTasks().isEmpty()) {
                    continue;
                }

                // 1. 创建工单
                com.cool.modules.sop.entity.WorkOrderEntity workOrder = new com.cool.modules.sop.entity.WorkOrderEntity();
                workOrder.setApplicantId(currentUserId);
                workOrder.setSopTemplateId(result.getScenario().getId());
                workOrder.setTitle("AI生成-" + request.getTaskDescription().substring(0,
                        Math.min(request.getTaskDescription().length(), 20)));
                workOrder.setDescription("由AI根据用户自然语言描述生成的一组任务。匹配场景: " + result.getScenario().getName());
                workOrder.setPriority(request.getPriority());
                workOrder.setCreateTime(new Date());
                workOrder.setUpdateTime(new Date());
                workOrderService.add(workOrder);
                totalWorkOrdersCreated++;

                log.info("成功创建工单，ID: {}, 标题: {}", workOrder.getId(), workOrder.getTitle());

                // 2. 创建任务包
                TaskPackageEntity taskPackage = new TaskPackageEntity();
                taskPackage.setPackageName(result.getScenario().getName() + "-任务包");
                taskPackage.setDescription("AI生成的任务包，基于场景：" + result.getScenario().getName());
                taskPackage.setScenarioId(result.getScenario().getId());
                taskPackage.setScenarioName(result.getScenario().getName());
                taskPackage.setScenarioCode(result.getScenario().getCode());
                taskPackage.setSopScenarioId(result.getScenario().getId());
                taskPackage.setWorkOrderId(workOrder.getId());
                taskPackage.setPackageStatus(0); // 0-待分配
                taskPackage.setPackageType(0); // 0-AI生成
                taskPackage.setTotalTasks(result.getTasks().size());
                taskPackage.setCompletedTasks(0);
                taskPackage.setInProgressTasks(0);
                taskPackage.setPendingTasks(result.getTasks().size());
                taskPackage.setCompletionRate(0);
                taskPackage.setExpectedStartTime(parseStringToLocalDateTime(request.getStartTime()));
                taskPackage.setExpectedEndTime(parseStringToLocalDateTime(request.getEndTime()));
                taskPackage.setCreatorId(currentUserId);
                taskPackage.setCreatorName(currentUserName);
                taskPackage.setPriority(request.getPriority());
                taskPackage.setIsDeleted(0);

                // 设置部门或项目信息
                String generationMode = request.getGenerationMode() != null ? request.getGenerationMode()
                        : "department";
                if ("project".equals(generationMode)) {
                    // 项目模式：设置项目信息
                    if (request.getProjectIds() != null && !request.getProjectIds().isEmpty()) {
                        taskPackage.setProjectId(request.getProjectIds().get(0));
                        // 项目模式下部门信息可以为空或使用默认值
                        taskPackage.setDepartmentId(0L);
                        taskPackage.setCreatorDepartmentId(0L);
                    } else {
                        taskPackage.setProjectId(0L);
                        taskPackage.setDepartmentId(0L);
                        taskPackage.setCreatorDepartmentId(0L);
                    }
                } else {
                    // 部门模式：设置部门信息
                    if (request.getDepartmentIds() != null && !request.getDepartmentIds().isEmpty()) {
                        taskPackage.setDepartmentId(request.getDepartmentIds().get(0));
                        taskPackage.setCreatorDepartmentId(request.getDepartmentIds().get(0));
                    } else {
                        taskPackage.setDepartmentId(0L);
                        taskPackage.setCreatorDepartmentId(0L);
                    }
                    // 部门模式下项目信息为空
                    taskPackage.setProjectId(0L);
                }

                taskPackage.setCreateTime(new Date());
                taskPackage.setUpdateTime(new Date());
                taskPackageService.save(taskPackage);
                totalPackagesCreated++;
                createdPackageIds.add(taskPackage.getId()); // 记录任务包ID

                log.info("成功创建任务包，ID: {}, 名称: {}", taskPackage.getId(), taskPackage.getPackageName());

                // 3. 创建具体任务
                List<TaskInfoEntity> taskEntities = new ArrayList<>();
                for (TaskGenerateResponse.GeneratedTask generatedTask : result.getTasks()) {
                    TaskInfoEntity taskEntity = new TaskInfoEntity();

                    // 基本信息
                    taskEntity.setName(generatedTask.getTaskName() != null ? generatedTask.getTaskName()
                            : generatedTask.getName());
                    taskEntity.setDescription(
                            generatedTask.getTaskDescription() != null ? generatedTask.getTaskDescription()
                                    : generatedTask.getDescription());
                    taskEntity.setPriority(generatedTask.getPriority());
                    taskEntity.setTaskStatus(0); // 0:待分配
                    taskEntity.setTaskCategory(
                            generatedTask.getTaskCategory() != null ? generatedTask.getTaskCategory() : "SOP_STEP");

                    // 关联信息
                    taskEntity.setPackageId(taskPackage.getId());
                    taskEntity.setScenarioId(generatedTask.getScenarioId());
                    taskEntity.setScenarioCode(generatedTask.getScenarioCode());
                    taskEntity.setScenarioName(generatedTask.getScenarioName());
                    taskEntity.setStepId(generatedTask.getStepId());
                    taskEntity.setStepCode(generatedTask.getStepCode());
                    taskEntity.setStepName(generatedTask.getStepName());

                    // 详细信息
                    taskEntity.setEntityTouchpoint(generatedTask.getEntityTouchpoint());
                    taskEntity.setTaskActivity(generatedTask.getTaskActivity());
                    taskEntity.setEmployeeBehavior(generatedTask.getEmployeeBehavior());
                    taskEntity.setWorkHighlight(generatedTask.getWorkHighlight());
                    taskEntity.setEmployeeRole(generatedTask.getEmployeeRole());
                    taskEntity.setPhotoRequired(generatedTask.getPhotoRequired());
                    taskEntity.setAttachmentRequired(generatedTask.getAttachmentRequired());

                    // 时间信息
                    taskEntity.setStartTime(parseStringToDate(request.getStartTime()));
                    taskEntity.setEndTime(parseStringToDate(request.getEndTime()));

                    // 部门或项目信息
                    if ("project".equals(generationMode)) {
                        // 项目模式：设置项目信息
                        if (request.getProjectIds() != null && !request.getProjectIds().isEmpty()) {
                            taskEntity.setProjectId(request.getProjectIds().get(0));
                            // 项目模式下部门信息可以为空或使用默认值
                            taskEntity.setDepartmentId(0L);
                            taskEntity.setCreatorDepartmentId(0L);
                        } else {
                            taskEntity.setProjectId(0L);
                            taskEntity.setDepartmentId(0L);
                            taskEntity.setCreatorDepartmentId(0L);
                        }
                    } else {
                        // 部门模式：设置部门信息
                        if (request.getDepartmentIds() != null && !request.getDepartmentIds().isEmpty()) {
                            taskEntity.setDepartmentId(request.getDepartmentIds().get(0));
                            taskEntity.setCreatorDepartmentId(request.getDepartmentIds().get(0));
                        } else {
                            taskEntity.setDepartmentId(0L);
                            taskEntity.setCreatorDepartmentId(0L);
                        }
                        // 部门模式下项目信息为空
                        taskEntity.setProjectId(0L);
                    }

                    // 调度相关（设置为不调度）
                    taskEntity.setScheduleStatus(0); // 0:停止
                    taskEntity.setType(1); // 1：用户
                    taskEntity.setScheduleType(0); // 0:cron

                    taskEntity.setCreateTime(new Date());
                    taskEntity.setUpdateTime(new Date());

                    taskEntities.add(taskEntity);
                }

                // 批量保存任务
                if (!taskEntities.isEmpty()) {
                    taskInfoService.saveBatch(taskEntities);
                    totalTasksCreated += taskEntities.size();
                    log.info("成功创建 {} 个任务，任务包ID: {}", taskEntities.size(), taskPackage.getId());
                }
            }

            // 4. 处理执行人分配（优先使用预览数据中的分配，其次才智能分配）
            int totalAssignedTasks = 0;
            int totalFailedAssignments = 0;
            int totalPreviewAssignments = 0;

            // 先处理预览数据中已有的分配信息
            updateProgress(recordId, 1, 82, "正在处理执行人分配...");

            for (int resultIndex = 0; resultIndex < results.size(); resultIndex++) {
                TaskGenerateResponse result = results.get(resultIndex);
                if (result.getTasks() == null || result.getTasks().isEmpty()) {
                    continue;
                }

                // 获取对应的任务包ID
                Long packageId = createdPackageIds.get(resultIndex);

                // 获取该任务包下所有任务
                List<TaskInfoEntity> tasks = taskInfoService.list(
                        com.mybatisflex.core.query.QueryWrapper.create()
                                .eq("package_id", packageId)
                                .eq("is_deleted", 0));

                // 创建任务ID到TaskInfoEntity的映射，按创建顺序
                Map<Integer, TaskInfoEntity> taskIndexMap = new HashMap<>();
                for (int i = 0; i < tasks.size(); i++) {
                    taskIndexMap.put(i, tasks.get(i));
                }

                // 处理每个生成的任务的执行人分配
                for (int taskIndex = 0; taskIndex < result.getTasks().size(); taskIndex++) {
                    TaskGenerateResponse.GeneratedTask generatedTask = result.getTasks().get(taskIndex);
                    TaskInfoEntity taskEntity = taskIndexMap.get(taskIndex);

                    if (taskEntity == null) {
                        log.warn("找不到对应的任务实体，跳过分配，taskIndex: {}", taskIndex);
                        continue;
                    }

                    // 检查是否已有预览阶段的分配信息
                    if (generatedTask.getAssigneeId() != null &&
                            generatedTask.getIsAssigned() != null &&
                            generatedTask.getIsAssigned()) {

                        // 使用预览数据中的分配信息创建TaskExecutionEntity
                        TaskExecutionEntity exec = new TaskExecutionEntity();
                        exec.setTaskId(taskEntity.getId());
                        exec.setAssigneeId(generatedTask.getAssigneeId());
                        exec.setAssigneeName(generatedTask.getAssigneeName());
                        exec.setExecutionStatusEnum(TaskExecutionStatusEnum.ASSIGNED);
                        exec.setAssignmentTypeEnum(AssignmentTypeEnum.getByCode(generatedTask.getAssignmentType())); // 使用预览数据中的分配类型

                        // 使用预览数据中的置信度和原因
                        if (generatedTask.getAssignmentConfidence() != null) {
                            exec.setConfidence(generatedTask.getAssignmentConfidence());
                        }
                        if (generatedTask.getAssignmentReason() != null
                                && !generatedTask.getAssignmentReason().isEmpty()) {
                            exec.setAssignmentReasons(generatedTask.getAssignmentReason());
                        } else {
                            exec.setAssignmentReasons("基于预览结果分配");
                        }

                        exec.setCreateTime(new Date());
                        exec.setUpdateTime(new Date());
                        taskExecutionService.save(exec);

                        // 更新任务状态：从待分配(0)变为待执行(1)
                        if (taskEntity.getTaskStatus() == 0) { // PENDING_ASSIGN = 0
                            taskEntity.setTaskStatus(TaskBusinessStatusEnum.PENDING_EXECUTE.getCode()); // PENDING_EXECUTE
                                                                                                        // = 1
                            taskEntity.setUpdateTime(new Date());
                            taskInfoService.updateById(taskEntity);
                            log.info("任务状态已更新：taskId={}, 从待分配(0)变为待执行(1)", taskEntity.getId());
                        }

                        totalPreviewAssignments++;
                        log.debug("使用预览分配：任务 {} 分配给 {} ({})",
                                taskEntity.getId(), generatedTask.getAssigneeName(), generatedTask.getAssigneeId());
                    }
                }
            }

            log.info("执行人分配完成，预览分配: {}, 智能分配: {}, 分配失败: {}",
                    totalPreviewAssignments, totalAssignedTasks, totalFailedAssignments);

            // 更新记录状态
            AiTaskGenerateRecordEntity record = getById(recordId);
            record.setStatus(2);
            record.setProgress(100);

            // 构建完成消息
            StringBuilder completionMessage = new StringBuilder();
            completionMessage.append(String.format("任务生成成功，已创建%d个工单、%d个任务包、%d个任务",
                    totalWorkOrdersCreated, totalPackagesCreated, totalTasksCreated));

            // 分配统计信息
            int totalAssignmentsProcessed = totalPreviewAssignments + totalAssignedTasks;
            if (totalAssignmentsProcessed > 0) {
                completionMessage.append("；执行人分配完成");
                if (totalPreviewAssignments > 0) {
                    completionMessage.append(String.format("，基于预览分配%d个任务", totalPreviewAssignments));
                }
                if (totalAssignedTasks > 0) {
                    completionMessage.append(String.format("，智能分配%d个任务", totalAssignedTasks));
                }
                if (totalFailedAssignments > 0) {
                    completionMessage.append(String.format("，%d个任务分配失败", totalFailedAssignments));
                }
            }

            addProgressLogToRecord(record, completionMessage.toString());

            // 保存生成结果 - 包含完整的任务数据以便前端显示
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workOrdersCreated", totalWorkOrdersCreated);
            resultData.put("taskPackagesCreated", totalPackagesCreated);
            resultData.put("totalTasks", totalTasksCreated);

            // 添加执行人分配结果
            resultData.put("previewAssignments", totalPreviewAssignments);
            resultData.put("aiAssignments", totalAssignedTasks);
            resultData.put("totalAssignments", totalPreviewAssignments + totalAssignedTasks);
            resultData.put("failedAssignments", totalFailedAssignments);

            if (request.getAutoAssign() != null && request.getAutoAssign()) {
                resultData.put("autoAssignEnabled", true);
            }

            // 添加完整的任务数据供前端显示
            if (!results.isEmpty()) {
                TaskGenerateResponse firstResult = results.get(0);
                resultData.put("scenario", firstResult.getScenario());
                resultData.put("tasks", firstResult.getTasks());
                resultData.put("mode", "generate"); // 标识这是正式生成模式
            }

            record.setResult(objectMapper.writeValueAsString(resultData));
            updateById(record);

            // 5. 更新所有创建的任务包统计信息
            for (Long packageId : createdPackageIds) {
                try {
                    taskPackageService.updatePackageStats(packageId);
                    log.debug("已更新任务包统计信息，包ID: {}", packageId);
                } catch (Exception e) {
                    log.warn("更新任务包统计信息失败，包ID: {}, 错误: {}", packageId, e.getMessage());
                }
            }

            log.info("任务生成完成，记录ID: {}, 工单: {}, 任务包: {}, 任务: {}, 执行人分配: 预览分配{}/智能分配{}/失败{}",
                    recordId, totalWorkOrdersCreated, totalPackagesCreated, totalTasksCreated,
                    totalPreviewAssignments, totalAssignedTasks, totalFailedAssignments);

        } catch (Exception e) {
            log.error("持久化任务到数据库失败，记录ID: {}", recordId, e);
            throw new CoolException("创建工单和任务失败: " + e.getMessage());
        }
    }

    /**
     * 提取的公共方法：将预览数据转换为TaskGenerateResponse（支持多部门和多项目结构）
     */
    private List<TaskGenerateResponse> convertPreviewDataToTaskResponses(Map<String, Object> previewData,
            TaskGenerateRequest request) {
        try {
            List<TaskGenerateResponse> results = new ArrayList<>();

            // 检查是否为项目模式
            Boolean multiProject = (Boolean) previewData.get("multiProject");
            if (Boolean.TRUE.equals(multiProject)) {
                // 处理项目模式数据结构
                Object projectsObj = previewData.get("projects");
                if (projectsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> projectsList = (List<Object>) projectsObj;
                    for (Object projObj : projectsList) {
                        if (projObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> projMap = (Map<String, Object>) projObj;
                            TaskGenerateResponse projResponse = new TaskGenerateResponse();
                            // 项目ID/名称
                            projResponse.setProjectId(getLongValue(projMap, "projectId"));
                            projResponse.setProjectName(getStringValue(projMap, "projectName"));
                            // 场景
                            Object scenarioObj = projMap.get("scenario");
                            if (scenarioObj instanceof Map) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> scenarioMap = (Map<String, Object>) scenarioObj;
                                TaskGenerateResponse.ScenarioInfo scenarioInfo = new TaskGenerateResponse.ScenarioInfo();
                                scenarioInfo.setId(getLongValue(scenarioMap, "id"));
                                scenarioInfo.setName(getStringValue(scenarioMap, "name"));
                                scenarioInfo.setDescription(getStringValue(scenarioMap, "description"));
                                scenarioInfo.setCode(getStringValue(scenarioMap, "code"));
                                projResponse.setScenario(scenarioInfo);
                            }
                            // 任务列表
                            Object tasksObj = projMap.get("tasks");
                            List<TaskGenerateResponse.GeneratedTask> tasks = new ArrayList<>();
                            if (tasksObj instanceof List) {
                                @SuppressWarnings("unchecked")
                                List<Object> tasksList = (List<Object>) tasksObj;
                                for (Object taskObj : tasksList) {
                                    if (taskObj instanceof Map) {
                                        @SuppressWarnings("unchecked")
                                        Map<String, Object> taskMap = (Map<String, Object>) taskObj;
                                        TaskGenerateResponse.GeneratedTask task = convertMapToGeneratedTask(taskMap);
                                        tasks.add(task);
                                    } else {
                                        TaskGenerateResponse.GeneratedTask task = objectMapper.convertValue(taskObj,
                                                TaskGenerateResponse.GeneratedTask.class);
                                        tasks.add(task);
                                    }
                                }
                            }
                            projResponse.setTasks(tasks);
                            projResponse.setTasksGenerated(tasks.size());
                            results.add(projResponse);
                        } else {
                            TaskGenerateResponse projResponse = objectMapper.convertValue(projObj,
                                    TaskGenerateResponse.class);
                            results.add(projResponse);
                        }
                    }
                }
            } else {
                // 处理部门模式数据结构
                Object departmentsObj = previewData.get("departments");
                if (departmentsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> departmentsList = (List<Object>) departmentsObj;
                    for (Object deptObj : departmentsList) {
                        if (deptObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> deptMap = (Map<String, Object>) deptObj;
                            TaskGenerateResponse deptResponse = new TaskGenerateResponse();
                            // 部门ID/名称
                            deptResponse.setDepartmentId(getLongValue(deptMap, "departmentId"));
                            deptResponse.setDepartmentName(getStringValue(deptMap, "departmentName"));
                            // 场景
                            Object scenarioObj = deptMap.get("scenario");
                            if (scenarioObj instanceof Map) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> scenarioMap = (Map<String, Object>) scenarioObj;
                                TaskGenerateResponse.ScenarioInfo scenarioInfo = new TaskGenerateResponse.ScenarioInfo();
                                scenarioInfo.setId(getLongValue(scenarioMap, "id"));
                                scenarioInfo.setName(getStringValue(scenarioMap, "name"));
                                scenarioInfo.setDescription(getStringValue(scenarioMap, "description"));
                                scenarioInfo.setCode(getStringValue(scenarioMap, "code"));
                                deptResponse.setScenario(scenarioInfo);
                            }
                            // 任务列表
                            Object tasksObj = deptMap.get("tasks");
                            List<TaskGenerateResponse.GeneratedTask> tasks = new ArrayList<>();
                            if (tasksObj instanceof List) {
                                @SuppressWarnings("unchecked")
                                List<Object> tasksList = (List<Object>) tasksObj;
                                for (Object taskObj : tasksList) {
                                    if (taskObj instanceof Map) {
                                        @SuppressWarnings("unchecked")
                                        Map<String, Object> taskMap = (Map<String, Object>) taskObj;
                                        TaskGenerateResponse.GeneratedTask task = convertMapToGeneratedTask(taskMap);
                                        tasks.add(task);
                                    } else {
                                        TaskGenerateResponse.GeneratedTask task = objectMapper.convertValue(taskObj,
                                                TaskGenerateResponse.GeneratedTask.class);
                                        tasks.add(task);
                                    }
                                }
                            }
                            deptResponse.setTasks(tasks);
                            deptResponse.setTasksGenerated(tasks.size());
                            results.add(deptResponse);
                        } else {
                            TaskGenerateResponse deptResponse = objectMapper.convertValue(deptObj,
                                    TaskGenerateResponse.class);
                            results.add(deptResponse);
                        }
                    }
                }
            }
            return results;
        } catch (Exception e) {
            log.error("转换预览数据失败", e);
            throw new CoolException("转换预览数据失败: " + e.getMessage());
        }
    }

    /**
     * 辅助方法：将Map转换为GeneratedTask
     */
    private TaskGenerateResponse.GeneratedTask convertMapToGeneratedTask(Map<String, Object> taskMap) {
        TaskGenerateResponse.GeneratedTask task = new TaskGenerateResponse.GeneratedTask();

        // 基本信息
        task.setId(getLongValue(taskMap, "id"));
        task.setName(getStringValue(taskMap, "name"));
        task.setTaskName(getStringValue(taskMap, "taskName"));
        task.setDescription(getStringValue(taskMap, "description"));
        task.setTaskDescription(getStringValue(taskMap, "taskDescription"));

        // 场景相关字段
        task.setScenarioId(getLongValue(taskMap, "scenarioId"));
        task.setScenarioCode(getStringValue(taskMap, "scenarioCode"));
        task.setScenarioName(getStringValue(taskMap, "scenarioName"));

        // 步骤相关字段
        task.setStepId(getLongValue(taskMap, "stepId"));
        task.setStepCode(getStringValue(taskMap, "stepCode"));
        task.setStepName(getStringValue(taskMap, "stepName"));

        // 项目相关字段
        task.setProjectId(getLongValue(taskMap, "projectId"));

        // 任务分类
        task.setTaskType(getStringValue(taskMap, "taskType"));
        task.setTaskCategory(getStringValue(taskMap, "taskCategory"));

        // 员工和行为相关
        task.setEmployeeRole(getStringValue(taskMap, "employeeRole"));
        task.setTaskActivity(getStringValue(taskMap, "taskActivity"));
        task.setEmployeeBehavior(getStringValue(taskMap, "employeeBehavior"));
        task.setWorkHighlight(getStringValue(taskMap, "workHighlight"));
        task.setEntityTouchpoint(getStringValue(taskMap, "entityTouchpoint"));

        // 附件要求
        task.setPhotoRequired(getBooleanValue(taskMap, "photoRequired"));
        task.setAttachmentRequired(getBooleanValue(taskMap, "attachmentRequired"));

        // 优先级和时间
        task.setPriority(getIntegerValue(taskMap, "priority"));
        task.setEstimatedDuration(getIntegerValue(taskMap, "estimatedDuration"));
        task.setStartTime(getStringValue(taskMap, "startTime"));
        task.setEndTime(getStringValue(taskMap, "endTime"));

        // 执行人分配信息 - 这是关键部分，确保预览的分配结果被保留
        task.setAssigneeId(getLongValue(taskMap, "assigneeId"));
        task.setAssigneeName(getStringValue(taskMap, "assigneeName"));
        task.setAssigneeRole(getStringValue(taskMap, "assigneeRole"));
        task.setAssignmentReason(getStringValue(taskMap, "assignmentReason"));
        task.setAssignmentConfidence(getIntegerValue(taskMap, "assignmentConfidence"));
        task.setIsAssigned(getBooleanValue(taskMap, "isAssigned"));
        task.setAssignmentType(getStringValue(taskMap, "assignmentType"));
        task.setCanReassign(getBooleanValue(taskMap, "canReassign"));
        task.setAssignmentStatus(getStringValue(taskMap, "assignmentStatus"));

        // 状态
        task.setStatus(getStringValue(taskMap, "status"));

        // 需求列表
        Object requirementsObj = taskMap.get("requirements");
        if (requirementsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> reqList = (List<Object>) requirementsObj;
            List<String> requirements = new ArrayList<>();
            for (Object req : reqList) {
                if (req != null) {
                    requirements.add(req.toString());
                }
            }
            task.setRequirements(requirements);
        }

        // 候选人列表（保持原有的Map格式）
        Object candidatesObj = taskMap.get("candidates");
        if (candidatesObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> candList = (List<Object>) candidatesObj;
            List<Map<String, Object>> candidates = new ArrayList<>();
            for (Object candObj : candList) {
                if (candObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> candMap = (Map<String, Object>) candObj;
                    candidates.add(candMap);
                }
            }
            task.setCandidates(candidates);
        }

        // 执行人列表
        Object executorsObj = taskMap.get("executors");
        if (executorsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> execList = (List<Object>) executorsObj;
            List<Map<String, Object>> executors = new ArrayList<>();
            for (Object execObj : execList) {
                if (execObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> execMap = (Map<String, Object>) execObj;
                    executors.add(execMap);
                }
            }
            task.setExecutors(executors);
        }

        // 元数据
        Object metadataObj = taskMap.get("metadata");
        if (metadataObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> metadata = (Map<String, Object>) metadataObj;
            task.setMetadata(metadata);
        }

        return task;
    }

    /**
     * 辅助方法：安全获取String值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 辅助方法：安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null)
            return null;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 辅助方法：安全获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null)
            return null;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 辅助方法：安全获取Boolean值
     */
    private Boolean getBooleanValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null)
            return null;
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return Boolean.parseBoolean(value.toString());
    }

    // 保持原有方法的向后兼容性
    @Override
    public Long submitPreviewTask(TaskGenerateRequest request) {
        return submitAsyncTaskGeneration(request, TaskGenerateMode.PREVIEW);
    }

    @Override
    @Async("taskExecutor")
    public void processPreviewGeneration(Long recordId) {
        processAsyncTaskGeneration(recordId, TaskGenerateMode.PREVIEW);
    }

    @Override
    @Deprecated
    public MultiDepartmentGenerateResponse generateTasksByAI(TaskGenerateRequest request) {
        // 保持向后兼容，但建议使用新的异步方法
        log.warn("使用了已过时的generateTasksByAI方法，建议使用submitAsyncTaskGeneration");

        try {
            Long recordId = submitAsyncTaskGeneration(request, TaskGenerateMode.GENERATE);

            // 简单的同步等待逻辑（不推荐，仅为兼容性）
            int maxWait = 30; // 最多等待30秒
            int waited = 0;

            while (waited < maxWait) {
                AiTaskGenerateRecordEntity record = getById(recordId);
                if (record != null && (record.getStatus() == 2 || record.getStatus() == 3)) {
                    // 任务完成或失败
                    if (record.getStatus() == 2) {
                        return MultiDepartmentGenerateResponse.builder()
                                .success(true)
                                .message("任务生成成功")
                                .build();
                    } else {
                        return MultiDepartmentGenerateResponse.builder()
                                .success(false)
                                .message(record.getFailReason())
                                .build();
                    }
                }

                try {
                    Thread.sleep(1000);
                    waited++;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            return MultiDepartmentGenerateResponse.builder()
                    .success(false)
                    .message("任务生成超时")
                    .build();

        } catch (Exception e) {
            log.error("generateTasksByAI执行失败", e);
            return MultiDepartmentGenerateResponse.builder()
                    .success(false)
                    .message("任务生成失败: " + e.getMessage())
                    .build();
        }
    }

    private void updateStatusAndProgress(Long taskId, Integer status, Integer progress, String failReason,
            String logMessage) {
        if (logMessage != null && !logMessage.isEmpty()) {
            addProgressLog(taskId, logMessage);
        }

        AiTaskGenerateRecordEntity record = new AiTaskGenerateRecordEntity();
        record.setId(taskId);
        record.setUpdateTime(new Date());
        if (status != null)
            record.setStatus(status);
        if (progress != null)
            record.setProgress(progress);
        if (failReason != null)
            record.setFailReason(failReason);

        if (status != null && (status == 2 || status == 3)) {
            AiTaskGenerateRecordEntity originalRecord = mapper.selectOneById(taskId);
            if (originalRecord != null && originalRecord.getCreateTime() != null) {
                long duration = new Date().getTime() - originalRecord.getCreateTime().getTime();
                record.setCostTime((int) duration);
            }
        }

        mapper.update(record);

        AiTaskGenerateRecordEntity updatedRecord = mapper.selectOneById(taskId);
        if (updatedRecord != null) {
            pushSse(taskId, updatedRecord);
            if (updatedRecord.getStatus() == 2 || updatedRecord.getStatus() == 3) {
                completeEmitter(taskId);
            }
        }
    }

    private synchronized void addProgressLog(Long taskId, String message) {
        AiTaskGenerateRecordEntity record = mapper.selectOneById(taskId);
        if (record == null)
            return;

        try {
            List<Map<String, String>> progressDetails;
            String detailsJson = record.getProgressDetails();

            progressDetails = (detailsJson == null || detailsJson.isEmpty())
                    ? new ArrayList<>()
                    : objectMapper.readValue(detailsJson, new TypeReference<>() {
                    });

            Map<String, String> logEntry = new HashMap<>();
            logEntry.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            logEntry.put("message", message);
            progressDetails.add(logEntry);

            record.setProgressDetails(objectMapper.writeValueAsString(progressDetails));
            record.setUpdateTime(new Date());
            mapper.update(record);

        } catch (JsonProcessingException e) {
            log.error("更新进度详情失败，无法序列化JSON", e);
        }
    }

    @Override
    public SseEmitter subscribe(Long taskId) {
        SseEmitter emitter = new SseEmitter(3600_000L); // 1 hour timeout
        emitterMap.computeIfAbsent(taskId, k -> new CopyOnWriteArrayList<>()).add(emitter);
        emitter.onCompletion(() -> removeEmitter(taskId, emitter));
        emitter.onTimeout(() -> removeEmitter(taskId, emitter));
        emitter.onError(e -> removeEmitter(taskId, emitter));

        AiTaskGenerateRecordEntity currentRecord = mapper.selectOneById(taskId);
        if (currentRecord != null) {
            pushSse(taskId, currentRecord);
        } else {
            pushSseError(taskId, "任务ID不存在: " + taskId);
            completeEmitter(taskId);
        }
        return emitter;
    }

    public void pushSse(Long taskId, Object data) {
        List<SseEmitter> emitters = emitterMap.get(taskId);
        if (emitters == null)
            return;

        String json;
        try {
            json = objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.error("SSE推送失败，无法序列化任务记录: {}", e.getMessage());
            json = "{\"error\": \"无法序列化数据\"}";
        }

        final String finalJson = json;
        Iterator<SseEmitter> iterator = emitters.iterator();
        while (iterator.hasNext()) {
            SseEmitter emitter = iterator.next();
            try {
                emitter.send(SseEmitter.event().name("message").data(finalJson));
            } catch (IOException e) {
                log.debug("推送消息到客户端时出错, emitter将关闭: {}", e.getMessage());
                iterator.remove();
                emitter.complete();
            }
        }
    }

    private void pushSseError(Long taskId, String errorMessage) {
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("error", errorMessage);
        pushSse(taskId, errorData);
        completeEmitter(taskId);
    }

    private void completeEmitter(Long taskId) {
        List<SseEmitter> emitters = emitterMap.get(taskId);
        if (emitters != null) {
            emitters.forEach(emitter -> {
                try {
                    emitter.send(SseEmitter.event().name("close").data("Task finished"));
                } catch (java.io.IOException e) {
                    log.warn("向SSE emitter发送关闭事件失败, taskId {}: {}", taskId, e.getMessage());
                } finally {
                    emitter.complete();
                }
            });
        }
    }

    private void removeEmitter(Long taskId, SseEmitter emitter) {
        List<SseEmitter> emitters = emitterMap.get(taskId);
        if (emitters != null) {
            emitters.remove(emitter);
        }
    }

    @Override
    public AiTaskGenerateRecordEntity getById(Long id) {
        return mapper.selectOneById(id);
    }

    @Override
    public Page<AiTaskGenerateRecordEntity> page(int page, int size, String taskDesc, Integer status, Long userId,
            boolean isAdmin) {
        // 构建分页对象
        Page<AiTaskGenerateRecordEntity> pg = Page.of(page, size);

        // 构建查询条件
        QueryWrapper qw = QueryWrapper.create()
                .orderBy(AiTaskGenerateRecordEntity::getCreateTime, false);

        if (StrUtil.isNotEmpty(taskDesc)) {
            qw.and(AiTaskGenerateRecordEntity::getTaskDesc).like(taskDesc);
        }

        if (status != null) {
            qw.and(AiTaskGenerateRecordEntity::getStatus).eq(status);
        }

        // 普通用户只能查看自己的记录，admin 可查看全部
        if (!isAdmin && userId != null) {
            qw.and(AiTaskGenerateRecordEntity::getUserId).eq(userId);
        }

        return mapper.paginate(pg, qw);
    }

    @Override
    public boolean retry(Long id) {
        AiTaskGenerateRecordEntity record = mapper.selectOneById(id);
        if (record == null)
            return false;
        record.setStatus(0);
        record.setProgress(0);
        record.setFailReason(null);
        record.setResult(null);
        record.setUpdatedTime(new Date());
        mapper.update(record);

        // 根据模式重新异步处理
        String modeStr = record.getMode();
        if (modeStr == null || modeStr.isEmpty()) {
            modeStr = "preview"; // 默认为预览模式
        }
        TaskGenerateMode mode = TaskGenerateMode.valueOf(modeStr.toUpperCase());
        self.processAsyncTaskGeneration(id, mode);
        return true;
    }

    @Override
    public boolean cancel(Long id) {
        AiTaskGenerateRecordEntity record = mapper.selectOneById(id);
        if (record == null)
            return false;
        record.setStatus(4);
        record.setUpdatedTime(new Date());
        mapper.update(record);
        pushSse(id, record);
        return true;
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            mapper.deleteById(id);
        }
        return true;
    }

    private void updateProgress(Long recordId, int status, int progress, String logMsg) {
        AiTaskGenerateRecordEntity record = getById(recordId);
        if (record == null)
            return;

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, String>> progressDetails;
            String detailsJson = record.getProgressDetails();
            progressDetails = (detailsJson == null || detailsJson.isEmpty())
                    ? new ArrayList<>()
                    : objectMapper.readValue(detailsJson,
                            new com.fasterxml.jackson.core.type.TypeReference<List<Map<String, String>>>() {
                            });
            Map<String, String> logEntry = new HashMap<>();
            logEntry.put("timestamp",
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")));
            logEntry.put("message", logMsg);
            progressDetails.add(logEntry);
            record.setProgressDetails(objectMapper.writeValueAsString(progressDetails));
        } catch (Exception ignore) {
        }

        record.setStatus(status);
        record.setProgress(progress);
        record.setUpdateTime(new java.util.Date());
        updateById(record);

        pushSse(recordId, getById(recordId));
    }

    private void ensureDepartmentIds(TaskGenerateRequest request, Long userId) {
        if (request.getDepartmentIds() == null || request.getDepartmentIds().isEmpty()) {
            Long[] deptIds = taskDepartmentPermissionService.getUserDepartmentIds(userId);
            if (deptIds != null && deptIds.length > 0) {
                request.setDepartmentIds(Collections.singletonList(deptIds[0]));
            } else {
                throw new CoolException("未指定部门，且未找到用户可用部门");
            }
        }
    }

    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Thread sleep interrupted", e);
        }
    }

    private void addProgressLogToRecord(AiTaskGenerateRecordEntity record, String message) {
        try {
            List<Map<String, String>> progressDetails;
            String detailsJson = record.getProgressDetails();
            progressDetails = (detailsJson == null || detailsJson.isEmpty()) ? new ArrayList<>()
                    : objectMapper.readValue(detailsJson, new TypeReference<>() {
                    });
            Map<String, String> logEntry = new HashMap<>();
            logEntry.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            logEntry.put("message", message);
            progressDetails.add(logEntry);
            record.setProgressDetails(objectMapper.writeValueAsString(progressDetails));
        } catch (JsonProcessingException e) {
            log.error("更新进度详情失败(in-memory)", e);
        }
    }

    private String getPriorityLabel(Integer priority) {
        if (priority == null)
            return "中";
        switch (priority) {
            case 1:
                return "低";
            case 2:
                return "中低";
            case 3:
                return "中";
            case 4:
                return "高";
            case 5:
                return "紧急";
            default:
                return "中";
        }
    }

    /**
     * 将字符串时间转换为LocalDateTime
     */
    private LocalDateTime parseStringToLocalDateTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(timeStr, formatter);
        } catch (Exception e) {
            log.warn("时间格式解析失败: {}", timeStr, e);
            return null;
        }
    }

    /**
     * 将字符串时间转换为Date
     */
    private Date parseStringToDate(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);
            return Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            log.warn("时间格式解析失败: {}", timeStr, e);
            return null;
        }
    }

    @Override
    public void savePreviewResults(Long recordId, PreviewResultDTO previewData) {
        try {
            AiTaskGenerateRecordEntity record = getById(recordId);
            if (record == null) {
                throw new CoolException("预览记录不存在: " + recordId);
            }
            // 直接序列化DTO对象
            record.setResult(objectMapper.writeValueAsString(previewData));
            record.setUpdateTime(new Date());
            updateById(record);
            addProgressLogToRecord(record, "保存前端最新预览结果成功");
        } catch (Exception e) {
            log.error("保存预览结果失败", e);
            throw new CoolException("保存预览结果失败: " + e.getMessage());
        }
    }
}