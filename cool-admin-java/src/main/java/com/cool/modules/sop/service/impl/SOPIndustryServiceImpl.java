package com.cool.modules.sop.service.impl;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.sop.entity.SOPIndustryEntity;
import com.cool.modules.sop.mapper.SOPIndustryMapper;
import com.cool.modules.sop.service.SOPIndustryService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * SOP行业服务实现类
 */
@Slf4j
@Service
public class SOPIndustryServiceImpl extends BaseServiceImpl<SOPIndustryMapper, SOPIndustryEntity> implements SOPIndustryService {

    @Override
    public List<SOPIndustryEntity> getActiveIndustries() {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq("status", 1)
            .orderBy("sort", true)
            .orderBy("create_time", false);
        return list(wrapper);
    }

    @Override
    @Transactional
    public void enableIndustry(Long industryId) {
        SOPIndustryEntity industry = getById(industryId);
        if (industry == null) {
            throw new RuntimeException("行业不存在");
        }
        
        industry.setStatus(1);
        industry.setUpdateTime(new Date());
        updateById(industry);
        
        log.info("行业已启用，ID: {}", industryId);
    }

    @Override
    @Transactional
    public void disableIndustry(Long industryId) {
        SOPIndustryEntity industry = getById(industryId);
        if (industry == null) {
            throw new RuntimeException("行业不存在");
        }
        
        industry.setStatus(0);
        industry.setUpdateTime(new Date());
        updateById(industry);
        
        log.info("行业已禁用，ID: {}", industryId);
    }

    @Override
    public SOPIndustryEntity getByCode(String code) {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq("code", code)
            .eq("status", 1);
        return getOne(wrapper);
    }

    @Override
    public List<SOPIndustryEntity> searchByName(String name) {
        QueryWrapper wrapper = QueryWrapper.create()
            .like("industry_name", name)
            .eq("status", 1)
            .orderBy("sort", true);
        return list(wrapper);
    }

    @Override
    @Transactional
    public void updateSort(Long industryId, Integer sort) {
        SOPIndustryEntity industry = getById(industryId);
        if (industry == null) {
            throw new RuntimeException("行业不存在");
        }
        
        industry.setSort(sort);
        industry.setUpdateTime(new Date());
        updateById(industry);
        
        log.info("行业排序已更新，ID: {}, 排序: {}", industryId, sort);
    }
} 