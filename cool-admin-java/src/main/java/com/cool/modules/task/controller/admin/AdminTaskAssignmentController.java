package com.cool.modules.task.controller.admin;

import com.cool.core.request.R;
import com.cool.modules.task.dto.AssignmentRequest;
import com.cool.modules.task.dto.AssignmentResult;
import com.cool.modules.task.dto.CandidateProfile;
import com.cool.modules.task.service.AutoAssignmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务分配管理控制器
 */
@Tag(name = "任务分配管理", description = "任务自动分配和手动分配管理")
@RestController()
@RequestMapping("/admin/task/assignment")
@RequiredArgsConstructor
public class AdminTaskAssignmentController {

    private final AutoAssignmentService autoAssignmentService;

    /**
     * 执行自动分配
     */
    @Operation(summary = "执行自动分配")
    @PostMapping("/execute")
    public R<AssignmentResult> executeAssignment(@RequestBody AssignmentRequest request) {
        try {
            AssignmentResult result = autoAssignmentService.executeAssignment(request);
            return R.ok(result);
        } catch (Exception e) {
            return R.error("分配执行失败: " + e.getMessage());
        }
    }

    /**
     * 为单个任务分配执行人
     */
    @Operation(summary = "为单个任务分配执行人")
    @PostMapping("/single/{taskId}")
    public R<AssignmentResult> assignSingleTask(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "true") Boolean autoAssign) {
        try {
            AssignmentResult result = autoAssignmentService.assignSingleTask(taskId, autoAssign);
            return R.ok(result);
        } catch (Exception e) {
            return R.error("单任务分配失败: " + e.getMessage());
        }
    }

    /**
     * 为任务包批量分配执行人
     */
    @Operation(summary = "为任务包批量分配执行人")
    @PostMapping("/package/{packageId}")
    public R<AssignmentResult> assignTaskPackage(
            @PathVariable Long packageId,
            @RequestParam(defaultValue = "true") Boolean autoAssign) {
        try {
            AssignmentResult result = autoAssignmentService.assignTaskPackage(packageId, autoAssign);
            return R.ok(result);
        } catch (Exception e) {
            return R.error("任务包分配失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务的候选人列表
     */
    @Operation(summary = "获取任务的候选人列表")
    @GetMapping("/candidates/{taskId}")
    public R<List<CandidateProfile>> getCandidatesForTask(@PathVariable Long taskId) {
        try {
            List<CandidateProfile> candidates = autoAssignmentService.getCandidatesForTask(taskId);
            return R.ok(candidates);
        } catch (Exception e) {
            return R.error("获取候选人失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有可用的候选人
     */
    @Operation(summary = "获取所有可用的候选人")
    @GetMapping("/candidates")
    public R<List<CandidateProfile>> getAllCandidates() {
        try {
            List<CandidateProfile> candidates = autoAssignmentService.getAllCandidates();
            return R.ok(candidates);
        } catch (Exception e) {
            return R.error("获取候选人失败: " + e.getMessage());
        }
    }

    /**
     * 根据角色筛选候选人
     */
    @Operation(summary = "根据角色筛选候选人")
    @PostMapping("/candidates/by-roles")
    public R<List<CandidateProfile>> getCandidatesByRoles(@RequestBody List<String> requiredRoles) {
        try {
            List<CandidateProfile> candidates = autoAssignmentService.getCandidatesByRoles(requiredRoles);
            return R.ok(candidates);
        } catch (Exception e) {
            return R.error("筛选候选人失败: " + e.getMessage());
        }
    }

    /**
     * 验证分配结果
     */
    @Operation(summary = "验证分配结果")
    @PostMapping("/validate/{taskId}")
    public R<Boolean> validateAssignment(
            @PathVariable Long taskId,
            @RequestBody List<Long> assigneeIds) {
        try {
            Boolean valid = autoAssignmentService.validateAssignment(taskId, assigneeIds);
            return R.ok(valid);
        } catch (Exception e) {
            return R.error("验证分配失败: " + e.getMessage());
        }
    }

    /**
     * 手动分配任务
     */
    @Operation(summary = "手动分配任务")
    @PostMapping("/manual")
    public R<String> manualAssignment(@RequestBody ManualAssignmentRequest request) {
        try {
            // 添加参数验证和日志
            if (request.getTaskId() == null) {
                return R.error("任务ID不能为空");
            }
            if (request.getAssigneeIds() == null || request.getAssigneeIds().isEmpty()) {
                return R.error("执行人ID列表不能为空");
            }
            
            // 记录调用信息
            System.out.println("手动分配任务请求: taskId=" + request.getTaskId() + 
                             ", assigneeIds=" + request.getAssigneeIds() + 
                             ", reason=" + request.getReason());
            
            // 直接调用手动分配方法
            boolean success = autoAssignmentService.manualAssignTask(
                request.getTaskId(), 
                request.getAssigneeIds(), 
                request.getReason()
            );
            
            if (success) {
                return R.ok("手动分配任务成功");
            } else {
                return R.error("手动分配任务失败");
            }
        } catch (Exception e) {
            System.err.println("手动分配任务异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("手动分配失败: " + e.getMessage());
        }
    }

    /**
     * 手动分配请求DTO
     */
    public static class ManualAssignmentRequest {
        private Long taskId;
        private List<Long> assigneeIds;
        private Boolean collaborationAllowed;
        private String reason;

        // Getters and Setters
        public Long getTaskId() { return taskId; }
        public void setTaskId(Long taskId) { this.taskId = taskId; }

        public List<Long> getAssigneeIds() { return assigneeIds; }
        public void setAssigneeIds(List<Long> assigneeIds) { this.assigneeIds = assigneeIds; }

        public Boolean getCollaborationAllowed() { return collaborationAllowed; }
        public void setCollaborationAllowed(Boolean collaborationAllowed) { this.collaborationAllowed = collaborationAllowed; }

        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
