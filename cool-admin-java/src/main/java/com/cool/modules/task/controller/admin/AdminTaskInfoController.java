package com.cool.modules.task.controller.admin;

import static com.cool.modules.task.entity.table.TaskInfoEntityTableDef.TASK_INFO_ENTITY;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.service.TaskInfoService;
import com.mybatisflex.core.paginate.Page;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.Valid;

/**
 * 任务
 */
@Tag(name = "任务管理", description = "统一管理任务")
@CoolRestController(api = { "add", "delete", "update", "info", "page" })
public class AdminTaskInfoController extends BaseController<TaskInfoService, TaskInfoEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 设置查询条件：支持按字段精确匹配和关键字模糊搜索
        setPageOption(createOp()
            .fieldEq(
                TASK_INFO_ENTITY.ID,               // 任务ID（支持批量查询）
                TASK_INFO_ENTITY.TASK_STATUS,      // 任务状态
                TASK_INFO_ENTITY.TASK_CATEGORY,    // 任务类别
                TASK_INFO_ENTITY.SCENARIO_ID,      // 场景ID
                TASK_INFO_ENTITY.STEP_ID,          // 步骤ID
                TASK_INFO_ENTITY.SCHEDULE_STATUS,  // 调度状态
                TASK_INFO_ENTITY.TYPE,             // 任务类型
                TASK_INFO_ENTITY.PACKAGE_ID        // 任务包ID
            )
            .keyWordLikeFields(
                TASK_INFO_ENTITY.NAME,             // 任务名称
                TASK_INFO_ENTITY.SCENARIO_NAME,    // 场景名称
                TASK_INFO_ENTITY.STEP_NAME,        // 步骤名称
                TASK_INFO_ENTITY.DESCRIPTION       // 任务描述
            )
        );

        // 设置列表查询条件（与分页查询保持一致）
        setListOption(createOp()
            .fieldEq(
                TASK_INFO_ENTITY.ID,               // 任务ID（支持批量查询）
                TASK_INFO_ENTITY.TASK_STATUS,
                TASK_INFO_ENTITY.TASK_CATEGORY,
                TASK_INFO_ENTITY.SCENARIO_ID,
                TASK_INFO_ENTITY.STEP_ID,
                TASK_INFO_ENTITY.SCHEDULE_STATUS,
                TASK_INFO_ENTITY.TYPE,
                TASK_INFO_ENTITY.PACKAGE_ID        // 任务包ID
            )
            .keyWordLikeFields(
                TASK_INFO_ENTITY.NAME,
                TASK_INFO_ENTITY.SCENARIO_NAME,
                TASK_INFO_ENTITY.STEP_NAME,
                TASK_INFO_ENTITY.DESCRIPTION
            )
        );
    }

    @Operation(summary = "执行一次")
    @PostMapping("/once")
    public R once(@RequestAttribute JSONObject requestParams) {
        service.once(requestParams.getLong("id"));
        return R.ok();
    }

    @Operation(summary = "开始任务")
    @PostMapping("/start")
    public R start(@RequestAttribute JSONObject requestParams) {
        service.start(requestParams.getLong("id"), requestParams.getInt("type"));
        return R.ok();
    }

    @Operation(summary = "停止任务")
    @PostMapping("/stop")
    public R stop(@RequestAttribute JSONObject requestParams) {
        service.stop(requestParams.getLong("id"));
        return R.ok();
    }

    @Operation(summary = "任务日志")
    @GetMapping("/log")
    public R log(@RequestAttribute JSONObject requestParams) {
        Integer page = requestParams.getInt("page", 0);
        Integer size = requestParams.getInt("size", 20);
        return R.ok(pageResult((Page) service.log(new Page<>(page, size), requestParams.getLong("id"),
                requestParams.getInt("status"))));
    }

    @Operation(summary = "个人工作台任务列表", description = "获取指定用户的个人任务，包含执行时间和执行人信息")
    @GetMapping("/personal-tasks")
    public R getPersonalTasks(@RequestAttribute JSONObject requestParams) {
        try {
            Long assigneeId = requestParams.getLong("assigneeId");
            Integer page = requestParams.getInt("page", 1);
            Integer size = requestParams.getInt("size", 20);
            String businessStatus = requestParams.getStr("businessStatus"); // 业务状态筛选
            String startDate = requestParams.getStr("startDate"); // 新增：开始日期
            String endDate = requestParams.getStr("endDate"); // 新增：结束日期

            if (assigneeId == null) {
                return R.error("执行人ID不能为空");
            }

            // 使用MyBatis-Flex关联查询获取个人任务
            Page<TaskInfoEntity> personalTasks = service.getPersonalTasksWithExecution(assigneeId, page, size, businessStatus, startDate, endDate);

            return R.ok(pageResult(personalTasks));
        } catch (Exception e) {
            return R.error("获取个人任务失败: " + e.getMessage());
        }
    }

    @Operation(summary = "开始任务", description = "将待执行任务转换为执行中状态")
    @PostMapping("/start-task")
    public R startTask(@RequestAttribute JSONObject requestParams) {
        try {
            Long taskId = requestParams.getLong("taskId");
            Long assigneeId = requestParams.getLong("assigneeId");

            if (taskId == null || assigneeId == null) {
                return R.error("任务ID和执行人ID不能为空");
            }

            // 调用服务开始任务
            boolean success = service.startTask(taskId, assigneeId);

            if (success) {
                return R.ok("任务已开始");
            } else {
                return R.error("开始任务失败");
            }
        } catch (Exception e) {
            return R.error("开始任务失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取任务执行详情", description = "获取任务的执行时间和执行人信息")
    @GetMapping("/execution-details/{taskId}")
    public R getTaskExecutionDetails(@PathVariable Long taskId) {
        try {
            // 获取任务基本信息
            TaskInfoEntity task = service.getById(taskId);
            if (task == null) {
                return R.error("任务不存在");
            }

            // 获取执行记录
            List<TaskExecutionEntity> executions = service.getTaskExecutions(taskId);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("task", task);
            result.put("executions", executions);

            // 计算执行统计信息
            Map<String, Object> stats = calculateExecutionStats(executions);
            result.put("stats", stats);

            return R.ok(result);
        } catch (Exception e) {
            return R.error("获取任务执行详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量更新任务时间", description = "批量更新多个任务的计划开始和结束时间")
    @PostMapping("/batch-update")
    public R batchUpdateTaskTime(@RequestBody Map<String, Object> params) {
        try {
            // 安全地处理ID类型转换
            Object idsObj = params.get("ids");
            List<Long> taskIds = new ArrayList<>();
            
            if (idsObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> idsList = (List<Object>) idsObj;
                for (Object id : idsList) {
                    if (id instanceof Integer) {
                        taskIds.add(((Integer) id).longValue());
                    } else if (id instanceof Long) {
                        taskIds.add((Long) id);
                    } else if (id instanceof String) {
                        taskIds.add(Long.parseLong((String) id));
                    }
                }
            }
            
            String startTime = (String) params.get("startTime");
            String endTime = (String) params.get("endTime");

            if (taskIds.isEmpty()) {
                return R.error("任务ID列表不能为空");
            }

            // 调用服务层批量更新时间
            boolean success = service.batchUpdateTaskTime(taskIds, startTime, endTime);

            if (success) {
                return R.ok("批量更新任务时间成功");
            } else {
                return R.error("批量更新任务时间失败");
            }
        } catch (Exception e) {
            return R.error("批量更新任务时间失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量获取任务执行详情", description = "批量获取多个任务的执行时间和执行人信息")
    @PostMapping("/batch-execution-details")
    public R getBatchTaskExecutionDetails(@RequestBody Map<String, Object> params) {
        try {
            // 安全地处理ID类型转换
            Object idsObj = params.get("taskIds");
            List<Long> taskIds = new ArrayList<>();
            
            if (idsObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> idsList = (List<Object>) idsObj;
                for (Object id : idsList) {
                    if (id instanceof Integer) {
                        taskIds.add(((Integer) id).longValue());
                    } else if (id instanceof Long) {
                        taskIds.add((Long) id);
                    } else if (id instanceof String) {
                        taskIds.add(Long.parseLong((String) id));
                    }
                }
            }

            if (taskIds.isEmpty()) {
                return R.error("任务ID列表不能为空");
            }

            List<Map<String, Object>> results = new ArrayList<>();

            for (Long taskId : taskIds) {
                // 获取任务基本信息
                TaskInfoEntity task = service.getById(taskId);
                if (task != null) {
                    // 获取执行记录
                    List<TaskExecutionEntity> executions = service.getTaskExecutions(taskId);

                    // 构建任务数据
                    Map<String, Object> taskData = new HashMap<>();
                    taskData.put("task", task);
                    taskData.put("executions", executions);

                    // 计算执行统计信息
                    Map<String, Object> stats = calculateExecutionStats(executions);
                    taskData.put("stats", stats);

                    results.add(taskData);
                }
            }

            return R.ok(results);
        } catch (Exception e) {
            return R.error("批量获取任务执行详情失败: " + e.getMessage());
        }
    }

    /**
     * 批量通过ID查询任务信息
     */
    @Operation(summary = "批量通过ID查询任务信息")
    @PostMapping("/listByIds")
    public R listByIds(@RequestBody @Valid IdsRequest request) {
        return R.ok(service.listByIds(request.getIds()));
    }

    @Data
    public static class IdsRequest {
        @NotEmpty(message = "ids不能为空")
        private List<Long> ids;
    }

    /**
     * 计算执行统计信息
     */
    private Map<String, Object> calculateExecutionStats(List<TaskExecutionEntity> executions) {
        Map<String, Object> stats = new HashMap<>();

        if (executions.isEmpty()) {
            stats.put("totalExecutors", 0);
            stats.put("completedExecutors", 0);
            stats.put("inProgressExecutors", 0);
            stats.put("completionRate", 0);
            return stats;
        }

        int totalExecutors = executions.size();
        long completedExecutors = executions.stream()
            .filter(e -> TaskExecutionStatusEnum.COMPLETED.getCode().equals(e.getExecutionStatus()))
            .count();
        long inProgressExecutors = executions.stream()
            .filter(e -> TaskExecutionStatusEnum.IN_PROGRESS.getCode().equals(e.getExecutionStatus()))
            .count();

        double completionRate = totalExecutors > 0 ? (double) completedExecutors / totalExecutors * 100 : 0;

        stats.put("totalExecutors", totalExecutors);
        stats.put("completedExecutors", completedExecutors);
        stats.put("inProgressExecutors", inProgressExecutors);
        stats.put("completionRate", Math.round(completionRate * 100.0) / 100.0);

        // 获取最早开始时间和最晚完成时间
        Optional<Date> earliestAcceptTime = executions.stream()
            .map(TaskExecutionEntity::getAcceptTime)
            .filter(Objects::nonNull)
            .min(Date::compareTo);

        Optional<Date> latestCompletionTime = executions.stream()
            .map(TaskExecutionEntity::getCompletionTime)
            .filter(Objects::nonNull)
            .max(Date::compareTo);

        earliestAcceptTime.ifPresent(time -> stats.put("actualStartTime", time));
        latestCompletionTime.ifPresent(time -> stats.put("actualEndTime", time));

        return stats;
    }
}
