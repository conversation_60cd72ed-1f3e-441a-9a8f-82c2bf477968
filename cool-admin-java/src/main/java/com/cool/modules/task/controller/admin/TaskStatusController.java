package com.cool.modules.task.controller.admin;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.task.dto.StatusChangeRequest;
import com.cool.modules.task.dto.StatusChangeResult;
import com.cool.modules.task.entity.TaskHistoryEntity;
import com.cool.modules.task.service.TaskHistoryService;
import com.cool.modules.task.service.TaskStatusService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 任务状态流转控制器
 */
@Tag(name = "任务状态流转", description = "任务状态流转相关接口")
@RestController
@RequestMapping("/admin/task/status")
@RequiredArgsConstructor
public class TaskStatusController {

    private final TaskStatusService taskStatusService;
    private final TaskHistoryService taskHistoryService;

    @Operation(summary = "统一状态变更接口")
    @PostMapping("/change")
    public R<StatusChangeResult> changeStatus(@Valid @RequestBody StatusChangeRequest request, HttpServletRequest httpRequest) {
        try {
            // 设置操作人信息
            if (request.getOperatorId() == null) {
                request.setOperatorId(CoolSecurityUtil.getCurrentUserId());
            }
            if (request.getOperatorName() == null || request.getOperatorName().trim().isEmpty()) {
                request.setOperatorName(CoolSecurityUtil.getAdminUsername());
            }
            
            // 设置客户端信息
            request.setClientIp(getClientIp(httpRequest));
            request.setUserAgent(httpRequest.getHeader("User-Agent"));

            StatusChangeResult result = taskStatusService.changeStatus(request);
            
            if (result.getSuccess()) {
                return R.ok(result);
            } else {
                return R.error(result.getErrorMessage());
            }
        } catch (Exception e) {
            return R.error("状态变更失败：" + e.getMessage());
        }
    }

    @Operation(summary = "检查状态转换是否有效")
    @GetMapping("/check-transition")
    public R<Boolean> checkTransition(
            @Parameter(description = "源状态") @RequestParam Integer sourceStatus,
            @Parameter(description = "目标状态") @RequestParam Integer targetStatus) {
        try {
            Boolean isValid = taskStatusService.isValidTransition(sourceStatus, targetStatus);
            return R.ok(isValid);
        } catch (Exception e) {
            return R.error("检查状态转换失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取任务可执行的操作列表")
    @GetMapping("/available-actions/{taskId}")
    public R<List<String>> getAvailableActions(
            @Parameter(description = "任务ID") @PathVariable Long taskId,
            @Parameter(description = "操作人ID") @RequestParam(required = false) Long operatorId) {
        try {
            if (operatorId == null) {
                operatorId = CoolSecurityUtil.getCurrentUserId();
            }
            
            List<String> actions = taskStatusService.getAvailableActions(taskId, operatorId);
            return R.ok(actions);
        } catch (Exception e) {
            return R.error("获取可执行操作失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取任务状态变更历史")
    @GetMapping("/history/{taskId}")
    public R<List<TaskHistoryEntity>> getStatusChangeHistory(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {
        try {
            List<TaskHistoryEntity> history = taskHistoryService.getStatusChangeHistory(taskId);
            return R.ok(history);
        } catch (Exception e) {
            return R.error("获取状态变更历史失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取任务完整操作历史")
    @GetMapping("/history/{taskId}/full")
    public R<List<TaskHistoryEntity>> getTaskHistory(
            @Parameter(description = "任务ID") @PathVariable Long taskId,
            @Parameter(description = "限制数量") @RequestParam(required = false) Integer limit) {
        try {
            List<TaskHistoryEntity> history = taskHistoryService.getTaskHistory(taskId, limit);
            return R.ok(history);
        } catch (Exception e) {
            return R.error("获取任务历史失败：" + e.getMessage());
        }
    }

    @Operation(summary = "批量状态变更")
    @PostMapping("/batch-change")
    public R<Map<String, Object>> batchChangeStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> taskIds = (List<Long>) request.get("taskIds");
            Integer targetStatus = (Integer) request.get("targetStatus");
            String reason = (String) request.get("reason");
            
            if (taskIds == null || taskIds.isEmpty()) {
                return R.error("任务ID列表不能为空");
            }
            if (targetStatus == null) {
                return R.error("目标状态不能为空");
            }

            Long operatorId = CoolSecurityUtil.getCurrentUserId();
            String operatorName = CoolSecurityUtil.getAdminUsername();
            
            int successCount = 0;
            int failCount = 0;
            
            for (Long taskId : taskIds) {
                try {
                    StatusChangeRequest changeRequest = StatusChangeRequest.builder()
                            .taskId(taskId)
                            .targetStatus(targetStatus)
                            .reason(reason)
                            .operatorId(operatorId)
                            .operatorName(operatorName)
                            .build();
                    
                    StatusChangeResult result = taskStatusService.changeStatus(changeRequest);
                    if (result.getSuccess()) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    failCount++;
                }
            }
            
            Map<String, Object> result = Map.of(
                "total", taskIds.size(),
                "success", successCount,
                "failed", failCount
            );
            
            // 在结果中添加消息
            result.put("message", String.format("批量操作完成，成功%d个，失败%d个", successCount, failCount));
            return R.ok(result);
        } catch (Exception e) {
            return R.error("批量状态变更失败：" + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


}
