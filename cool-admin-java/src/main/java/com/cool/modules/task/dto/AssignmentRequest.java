package com.cool.modules.task.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务分配请求DTO
 */
@Data
public class AssignmentRequest {

    /**
     * 分配类型：SINGLE-单个任务，BATCH-批量任务
     */
    private String assignmentType;

    /**
     * 任务ID列表
     */
    private List<Long> taskIds;

    /**
     * 是否为预览模式
     */
    private Boolean previewMode;

    /**
     * 任务预览数据列表
     */
    private List<TaskPreviewDTO> previewTaskList;

    /**
     * 是否自动分配
     */
    private Boolean autoAssign=true;

    /**
     * 分配约束条件
     */
    private AssignmentConstraints constraints;

    /**
     * 分配偏好设置
     */
    private AssignmentPreferences preferences;

    /**
     * 分配约束条件
     */
    @Data
    public static class AssignmentConstraints {
        /**
         * 必需角色列表
         */
        private List<String> requiredRoles;

        /**
         * 最大工作负载百分比
         */
        private Integer maxWorkload;

        /**
         * 截止时间
         */
        private LocalDateTime deadline;

        /**
         * 是否允许协作
         */
        private Boolean collaborationAllowed;

        /**
         * 地理位置要求
         */
        private String locationRequirement;

        /**
         * 技能要求
         */
        private List<String> requiredSkills;

        /**
         * 必需执行人ID列表
         */
        private List<Long> requiredAssigneeIds;

        /**
         * 最小工作负载
         */
        private Integer minLoad;

        /**
         * 最大工作负载
         */
        private Integer maxLoad;

        /**
         * 获取必需执行人ID列表
         */
        public List<Long> getRequiredAssigneeIds() {
            return requiredAssigneeIds;
        }

        /**
         * 设置必需执行人ID列表
         */
        public void setRequiredAssigneeIds(List<Long> requiredAssigneeIds) {
            this.requiredAssigneeIds = requiredAssigneeIds;
        }

        /**
         * 获取最大工作负载
         */
        public Integer getMaxLoad() {
            return maxLoad;
        }

        /**
         * 设置最大工作负载
         */
        public void setMaxLoad(Integer maxLoad) {
            this.maxLoad = maxLoad;
        }

        /**
         * 获取协作允许状态
         */
        public Boolean getCollaborationAllowed() {
            return collaborationAllowed;
        }

        /**
         * 设置协作允许状态
         */
        public void setCollaborationAllowed(Boolean collaborationAllowed) {
            this.collaborationAllowed = collaborationAllowed;
        }
    }

    /**
     * 分配偏好设置
     */
    @Data
    public static class AssignmentPreferences {
        /**
         * 优先考虑经验
         */
        private Boolean prioritizeExperience;

        /**
         * 平衡工作负载
         */
        private Boolean balanceWorkload;

        /**
         * 优先本地人员
         */
        private Boolean prioritizeLocal;

        /**
         * 角色匹配权重 (0-100)
         */
        private Integer roleMatchWeight;

        /**
         * 工作负载权重 (0-100)
         */
        private Integer workloadWeight;

        /**
         * 绩效权重 (0-100)
         */
        private Integer performanceWeight;
    }
}
