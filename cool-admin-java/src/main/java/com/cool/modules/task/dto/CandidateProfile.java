package com.cool.modules.task.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 候选人档案DTO
 */
@Data
public class CandidateProfile {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户角色列表
     */
    private List<String> roles;

    /**
     * 技能列表
     */
    private List<UserSkill> skills;

    /**
     * 当前工作负载百分比 (0-100)
     */
    private Integer currentWorkload;

    /**
     * 历史绩效评分 (0-100)
     */
    private Integer performanceScore;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 是否可用
     */
    private Boolean isAvailable;

    /**
     * 地理位置
     */
    private String location;

    /**
     * 工作时间偏好
     */
    private WorkTimePreference workTimePreference;

    /**
     * 最近完成的类似任务数量
     */
    private Integer recentSimilarTasks;

    /**
     * 平均任务完成时间（小时）
     */
    private Double averageCompletionTime;

    /**
     * 任务完成质量评分 (0-100)
     */
    private Integer qualityScore;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 用户技能
     */
    @Data
    public static class UserSkill {
        /**
         * 技能名称
         */
        private String skillName;

        /**
         * 技能等级 (1-5)
         */
        private Integer skillLevel;

        /**
         * 技能经验年限
         */
        private Double experienceYears;

        /**
         * 技能认证状态
         */
        private Boolean certified;
    }

    /**
     * 工作时间偏好
     */
    @Data
    public static class WorkTimePreference {
        /**
         * 工作开始时间
         */
        private String startTime;

        /**
         * 工作结束时间
         */
        private String endTime;

        /**
         * 工作日偏好 (1-7, 1=周一)
         */
        private List<Integer> workDays;

        /**
         * 时区
         */
        private String timezone;
    }

    /**
     * 计算与任务的匹配度
     */
    public Integer calculateMatchScore(List<String> requiredRoles, List<String> requiredSkills) {
        int score = 0;
        
        // 角色匹配 (40分)
        if (requiredRoles != null && roles != null) {
            long matchingRoles = requiredRoles.stream()
                    .filter(roles::contains)
                    .count();
            score += (int) (40.0 * matchingRoles / requiredRoles.size());
        }
        
        // 技能匹配 (30分)
        if (requiredSkills != null && skills != null) {
            List<String> userSkillNames = skills.stream()
                    .map(UserSkill::getSkillName)
                    .toList();
            long matchingSkills = requiredSkills.stream()
                    .filter(userSkillNames::contains)
                    .count();
            score += (int) (30.0 * matchingSkills / requiredSkills.size());
        }
        
        // 工作负载 (20分)
        if (currentWorkload != null) {
            score += Math.max(0, 20 - (currentWorkload / 5));
        }
        
        // 绩效评分 (10分)
        if (performanceScore != null) {
            score += (int) (10.0 * performanceScore / 100);
        }
        
        return Math.min(100, score);
    }
}
