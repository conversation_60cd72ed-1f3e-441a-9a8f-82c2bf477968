package com.cool.modules.task.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * 状态变更上下文
 * 用于在状态流转过程中传递相关信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatusChangeContext {

    /**
     * 变更原因/说明
     */
    private String reason;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 附件列表(JSON格式)
     */
    private String attachments;

    /**
     * 照片列表(JSON格式)
     */
    private String photos;

    /**
     * 扩展属性
     */
    private Map<String, Object> extraData;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 是否强制操作（跳过某些校验）
     */
    private Boolean forceOperation;

    /**
     * 创建简单的状态变更上下文
     */
    public static StatusChangeContext simple(String reason) {
        return StatusChangeContext.builder()
                .reason(reason)
                .operationTime(new Date())
                .forceOperation(false)
                .build();
    }

    /**
     * 创建带操作人信息的状态变更上下文
     */
    public static StatusChangeContext withOperator(String reason, Long operatorId, String operatorName) {
        return StatusChangeContext.builder()
                .reason(reason)
                .operatorId(operatorId)
                .operatorName(operatorName)
                .operationTime(new Date())
                .forceOperation(false)
                .build();
    }

    /**
     * 创建完整的状态变更上下文
     */
    public static StatusChangeContext full(String reason, Long operatorId, String operatorName,
            String attachments, String photos, String clientIp, String userAgent) {
        return StatusChangeContext.builder()
                .reason(reason)
                .operatorId(operatorId)
                .operatorName(operatorName)
                .operationTime(new Date())
                .attachments(attachments)
                .photos(photos)
                .clientIp(clientIp)
                .userAgent(userAgent)
                .forceOperation(false)
                .build();
    }
}
