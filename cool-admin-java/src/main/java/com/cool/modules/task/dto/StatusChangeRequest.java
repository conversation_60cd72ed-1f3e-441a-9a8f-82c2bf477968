package com.cool.modules.task.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import jakarta.validation.constraints.NotNull;

/**
 * 状态变更请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatusChangeRequest {
    
    /**
     * 任务ID
     */
    @jakarta.validation.constraints.NotNull(message = "任务ID不能为空")
    private Long taskId;
    
    /**
     * 目标状态
     */
    @NotNull(message = "目标状态不能为空")
    private Integer targetStatus;
    
    /**
     * 变更原因/说明
     */
    private String reason;
    
    /**
     * 操作人ID（通常从当前登录用户获取）
     */
    private Long operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 执行人ID列表（用于分配任务）
     */
    private List<Long> assigneeIds;
    
    /**
     * 附件列表
     */
    private List<String> attachments;
    
    /**
     * 照片列表
     */
    private List<String> photos;
    
    /**
     * 完成说明（完成任务时使用）
     */
    private String completionNote;
    
    /**
     * 关闭原因（关闭任务时使用）
     */
    private String closeReason;
    
    /**
     * 重新打开原因（重新打开任务时使用）
     */
    private String reopenReason;
    
    /**
     * 是否强制操作
     */
    private Boolean forceOperation;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extraData;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 获取实际的变更原因
     * 根据不同的操作类型返回相应的原因字段
     */
    public String getActualReason() {
        if (completionNote != null && !completionNote.trim().isEmpty()) {
            return completionNote;
        }
        if (closeReason != null && !closeReason.trim().isEmpty()) {
            return closeReason;
        }
        if (reopenReason != null && !reopenReason.trim().isEmpty()) {
            return reopenReason;
        }
        return reason;
    }
    
    /**
     * 转换为状态变更上下文
     */
    public StatusChangeContext toContext() {
        return StatusChangeContext.builder()
                .reason(getActualReason())
                .operatorId(operatorId)
                .operatorName(operatorName)
                .operationTime(new java.util.Date())
                .attachments(attachments != null ? String.join(",", attachments) : null)
                .photos(photos != null ? String.join(",", photos) : null)
                .clientIp(clientIp)
                .userAgent(userAgent)
                .forceOperation(forceOperation != null ? forceOperation : false)
                .extraData(extraData)
                .build();
    }
}
