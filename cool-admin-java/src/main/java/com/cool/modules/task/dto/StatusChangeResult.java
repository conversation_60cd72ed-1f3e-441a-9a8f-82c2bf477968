package com.cool.modules.task.dto;

import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 状态变更结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatusChangeResult {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 原状态
     */
    private Integer oldStatus;
    
    /**
     * 新状态
     */
    private Integer newStatus;
    
    /**
     * 变更时间
     */
    private Date changeTime;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 变更原因
     */
    private String reason;
    
    /**
     * 错误消息（失败时）
     */
    private String errorMessage;
    
    /**
     * 错误代码（失败时）
     */
    private String errorCode;
    
    /**
     * 警告消息列表
     */
    private List<String> warnings;
    
    /**
     * 后续可执行的操作
     */
    private List<String> nextActions;
    
    /**
     * 创建成功结果
     */
    public static StatusChangeResult success(Long taskId, Integer oldStatus, Integer newStatus, 
                                           Long operatorId, String operatorName, String reason) {
        return StatusChangeResult.builder()
                .success(true)
                .taskId(taskId)
                .oldStatus(oldStatus)
                .newStatus(newStatus)
                .changeTime(new Date())
                .operatorId(operatorId)
                .operatorName(operatorName)
                .reason(reason)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static StatusChangeResult failure(Long taskId, String errorMessage, String errorCode) {
        return StatusChangeResult.builder()
                .success(false)
                .taskId(taskId)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .changeTime(new Date())
                .build();
    }
    
    /**
     * 获取状态名称
     */
    public String getOldStatusName() {
        return TaskBusinessStatusEnum.getNameByCode(oldStatus);
    }
    
    /**
     * 获取状态名称
     */
    public String getNewStatusName() {
        return TaskBusinessStatusEnum.getNameByCode(newStatus);
    }
    
    /**
     * 添加警告消息
     */
    public StatusChangeResult addWarning(String warning) {
        if (warnings == null) {
            warnings = new java.util.ArrayList<>();
        }
        warnings.add(warning);
        return this;
    }
    
    /**
     * 添加后续操作
     */
    public StatusChangeResult addNextAction(String action) {
        if (nextActions == null) {
            nextActions = new java.util.ArrayList<>();
        }
        nextActions.add(action);
        return this;
    }
}
