package com.cool.modules.task.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.RelationOneToMany;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Table(value = "task_info", comment = "任务信息")
public class TaskInfoEntity extends BaseEntity<TaskInfoEntity> {
    /**
     * 任务调度参数key
     */
    @Column(ignore = true)
    public static final String JOB_PARAM_KEY = "JOB_PARAM_KEY";

    @ColumnDefine(comment = "名称", notNull = true)
    private String name;

    @ColumnDefine(comment = "任务描述", type = "text")
    private String description;

    @ColumnDefine(comment = "优先级 1-5，5最高", type = "tinyint")
    private Integer priority;

    @ColumnDefine(comment = "任务业务状态 0:待分配 1:待执行 2:执行中 3:已完成 4:已关闭", type = "tinyint")
    private Integer taskStatus;

    @ColumnDefine(comment = "任务类别", length = 50) // ROUTINE, PERIODIC, AD_HOC
    private String taskCategory;

    @ColumnDefine(comment = "任务整体完成时间")
    private Date completionTime;

    @ColumnDefine(comment = "关闭原因", length = 500)
    private String closeReason;

    @ColumnDefine(comment = "关闭人", length = 100)
    private String closedBy;

    @ColumnDefine(comment = "关闭时间")
    private Date closeTime;

    @ColumnDefine(comment = "关联场景ID")
    private Long scenarioId;

    @ColumnDefine(comment = "任务包ID")
    private Long packageId;

    @ColumnDefine(comment = "场景编号", length = 100)
    private String scenarioCode;

    @ColumnDefine(comment = "场景名称", length = 255)
    private String scenarioName;

    @ColumnDefine(comment = "关联步骤ID")
    private Long stepId;

    @ColumnDefine(comment = "步骤编号", length = 100)
    private String stepCode;

    @ColumnDefine(comment = "步骤名称", length = 255)
    private String stepName;

    @ColumnDefine(comment = "实体触点", type = "text")
    private String entityTouchpoint;

    @ColumnDefine(comment = "任务活动", type = "text")
    private String taskActivity;

    @ColumnDefine(comment = "员工行为", type = "text")
    private String employeeBehavior;

    @ColumnDefine(comment = "工作亮点", type = "text")
    private String workHighlight;

    @ColumnDefine(comment = "员工角色", length = 100)
    private String employeeRole;

    @ColumnDefine(comment = "是否需要拍照", type = "tinyint", defaultValue = "0")
    private Boolean photoRequired;

    @ColumnDefine(comment = "是否需要附件", type = "tinyint", defaultValue = "0")
    private Boolean attachmentRequired;

    @ColumnDefine(comment = "任务备注", type = "text")
    private String remark;

    // 以下为任务调度器使用字段

    @ColumnDefine(comment = "任务ID")
    private String jobId;

    @ColumnDefine(comment = "最大执行次数 不传为无限次")
    private Integer repeatCount;

    @ColumnDefine(comment = "每间隔多少毫秒执行一次 如果cron设置了 这项设置就无效")
    private Integer every;

    @ColumnDefine(comment = "调度状态 0:停止 1：运行", defaultValue = "1", notNull = true)
    private Integer scheduleStatus;

    @ColumnDefine(comment = "服务实例名称")
    private String service;

    @ColumnDefine(comment = "调度类型 0:cron 1：时间间隔", defaultValue = "0")
    private Integer scheduleType;

    @ColumnDefine(comment = "来源 0:系统 1：用户", defaultValue = "0")
    private Integer type;

    @ColumnDefine(comment = "任务参数数据")
    private String data;

    @ColumnDefine(comment = "cron表达式")
    private String cron;

    @ColumnDefine(comment = "下一次执行时间")
    private Date nextRunTime;

    @ColumnDefine(comment = "任务开始时间")
    private Date startTime;

    @ColumnDefine(comment = "任务结束时间")
    private Date endTime;

    // 临时字段，不映射到数据库，用于传递执行人信息
    @Column(ignore = true)
    private String assigneeName;

    @Column(ignore = true)
    private Long assigneeId;

    // 临时字段，存储执行人手机号（多个用逗号分隔）
    @Column(ignore = true)
    private String assigneePhone;

    // 临时字段，存储所有执行记录
    @Column(ignore = true)
    private java.util.List<TaskExecutionEntity> executions;

    @RelationOneToMany(selfField = "id", targetField = "taskId")
    @ColumnDefine(comment = "任务执行人信息")
    private java.util.List<TaskExecutionEntity> executionEntitys;

    /**
     * 所属部门ID
     */
    @ColumnDefine(comment = "所属部门ID", type = "bigint")
    private Long departmentId;

    /**
     * 所属部门名称 (查询时填充)
     */
    @Column(ignore = true)
    private String departmentName;

    /**
     * 创建者部门ID
     */
    @ColumnDefine(comment = "创建者部门ID", type = "bigint")
    private Long creatorDepartmentId;

    /**
     * 创建者部门名称 (查询时填充)
     */
    @Column(ignore = true)
    private String creatorDepartmentName;

    /**
     * 关联项目ID
     */
    @ColumnDefine(comment = "关联项目ID", type = "bigint")
    private Long projectId;

    /**
     * 项目名称 (查询时填充)
     */
    @Column(ignore = true)
    private String projectName;

    /**
     * 项目角色名称 (查询时填充)
     */
    @Column(ignore = true)
    private String projectRoleName;
}
