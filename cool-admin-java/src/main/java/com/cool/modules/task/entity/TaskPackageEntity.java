package com.cool.modules.task.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Column;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 场景任务包实体
 */
@Getter
@Setter
@Table("task_package")
public class TaskPackageEntity extends BaseEntity<TaskPackageEntity> {

    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 任务包名称
     */
    private String packageName;

    /**
     * 任务包描述
     */
    private String description;

    /**
     * 关联场景ID
     */
    private Long scenarioId;

    /**
     * 场景名称（冗余字段，便于查询）
     */
    private String scenarioName;

    /**
     * 场景编码（冗余字段，便于查询）
     */
    private String scenarioCode;

    /**
     * 关联的SOP场景ID
     */
    private Long sopScenarioId;

    /**
     * 关联的工单ID
     */
    @Column("work_order_id")
    private Long workOrderId;

    /**
     * 任务包状态：0-待分配，1-执行中，2-已完成，3-已关闭
     */
    private Integer packageStatus;

    /**
     * 任务包类型：0-AI生成，1-手动创建，2-模板生成
     */
    private Integer packageType;

    /**
     * 总任务数
     */
    private Integer totalTasks;

    /**
     * 已完成任务数
     */
    private Integer completedTasks;

    /**
     * 进行中任务数
     */
    private Integer inProgressTasks;

    /**
     * 待分配任务数
     */
    private Integer pendingTasks;

    /**
     * 完成率（百分比）
     */
    private Integer completionRate;

    /**
     * 预计开始时间
     */
    private LocalDateTime expectedStartTime;

    /**
     * 预计结束时间
     */
    private LocalDateTime expectedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 负责人ID
     */
    private Long ownerId;

    /**
     * 负责人姓名
     */
    private String ownerName;

    /**
     * 优先级：1-低，2-中，3-高，4-紧急
     */
    private Integer priority;

    /**
     * 标签（JSON格式）
     */
    private String tags;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 所属部门ID
     */
    @ColumnDefine(comment = "所属部门ID", type = "bigint")
    private Long departmentId;

    /**
     * 所属部门名称 (查询时填充)
     */
    @Column(ignore = true)
    private String departmentName;

    /**
     * 创建者部门ID
     */
    @ColumnDefine(comment = "创建者部门ID", type = "bigint")
    private Long creatorDepartmentId;

    /**
     * 创建者部门名称 (查询时填充)
     */
    @Column(ignore = true)
    private String creatorDepartmentName;

    /**
     * 关联项目ID
     */
    @ColumnDefine(comment = "关联项目ID", type = "bigint")
    private Long projectId;

    /**
     * 项目名称 (查询时填充)
     */
    @Column(ignore = true)
    private String projectName;

    /**
     * 是否跨部门任务 (查询时计算)
     */
    @Column(ignore = true)
    private Boolean isCrossDepartment;
}
