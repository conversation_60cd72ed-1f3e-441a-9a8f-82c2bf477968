package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务业务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskBusinessStatusEnum {
    
    PENDING_ASSIGN(0, "待分配"),
    PENDING_EXECUTE(1, "待执行"),
    EXECUTING(2, "执行中"),
    COMPLETED(3, "已完成"),
    CLOSED(4, "已关闭");

    private final Integer code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static TaskBusinessStatusEnum getByCode(Integer code) {
        if (code == null) return null;
        for (TaskBusinessStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(Integer code) {
        TaskBusinessStatusEnum status = getByCode(code);
        return status != null ? status.getName() : "未知";
    }
}
