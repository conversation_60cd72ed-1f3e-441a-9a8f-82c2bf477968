package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型枚举
 */
@Getter
@AllArgsConstructor
public enum TaskCategoryEnum {
    
    SOP_STEP("SOP_STEP", "场景步骤"),
    RC("RC", "日常"),
    ZQ("ZQ", "周期"),
    LS("LS", "临时");

    private final String code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static TaskCategoryEnum getByCode(String code) {
        for (TaskCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(String code) {
        TaskCategoryEnum category = getByCode(code);
        return category != null ? category.getName() : code;
    }
}
