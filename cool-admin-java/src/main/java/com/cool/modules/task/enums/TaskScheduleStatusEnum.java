package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务调度状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskScheduleStatusEnum {
    
    STOPPED(0, "停止"),
    RUNNING(1, "运行");

    private final Integer code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static TaskScheduleStatusEnum getByCode(Integer code) {
        if (code == null) return null;
        for (TaskScheduleStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(Integer code) {
        TaskScheduleStatusEnum status = getByCode(code);
        return status != null ? status.getName() : "未知";
    }
}
