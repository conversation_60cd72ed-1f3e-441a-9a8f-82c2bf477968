package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务来源枚举
 */
@Getter
@AllArgsConstructor
public enum TaskSourceEnum {
    
    SYSTEM(0, "系统"),
    USER(1, "用户");

    private final Integer code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static TaskSourceEnum getByCode(Integer code) {
        if (code == null) return null;
        for (TaskSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(Integer code) {
        TaskSourceEnum source = getByCode(code);
        return source != null ? source.getName() : "未知";
    }
}
