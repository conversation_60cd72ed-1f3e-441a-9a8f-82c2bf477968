package com.cool.modules.task.enums;

/**
 * 任务状态权限枚举
 * 基于系统菜单权限进行任务状态权限控制
 */
public enum TaskStatusPermissionEnum {

    /**
     * 分配任务执行人
     */
    ASSIGN("task:info:assign", "分配执行人"),

    /**
     * 完成任务
     */
    COMPLETE("task:info:complete", "完成任务"),

    /**
     * 关闭任务
     */
    CLOSE("task:info:close", "关闭任务"),

    /**
     * 接受任务
     */
    ACCEPT("task:info:accept", "接受任务"),

    /**
     * 重新开启任务
     */
    REOPEN("task:info:reopen", "重新开启任务"),

    /**
     * 强制完成任务
     */
    FORCE_COMPLETE("task:info:force-complete", "强制完成任务"),

    /**
     * 编辑任务
     */
    EDIT("task:info:edit", "编辑任务"),

    /**
     * 删除任务
     */
    DELETE("task:info:delete", "删除任务"),

    /**
     * 重新激活任务
     */
    REACTIVATE("task:info:reactivate", "重新激活任务");

    private final String permission;
    private final String description;

    TaskStatusPermissionEnum(String permission, String description) {
        this.permission = permission;
        this.description = description;
    }

    public String getPermission() {
        return permission;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据权限字符串获取枚举
     */
    public static TaskStatusPermissionEnum fromPermission(String permission) {
        for (TaskStatusPermissionEnum value : values()) {
            if (value.getPermission().equals(permission)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断权限字符串是否有效
     */
    public static boolean isValidPermission(String permission) {
        return fromPermission(permission) != null;
    }
}