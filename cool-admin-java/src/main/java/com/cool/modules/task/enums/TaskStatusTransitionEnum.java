package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态转换枚举
 * 定义所有允许的状态转换路径和对应的事件
 */
@Getter
@AllArgsConstructor
public enum TaskStatusTransitionEnum {
    
    // 从待分配状态的转换
    ASSIGN_TASK(TaskBusinessStatusEnum.PENDING_ASSIGN, TaskBusinessStatusEnum.PENDING_EXECUTE, "ASSIGN", "分配任务"),
    CLOSE_FROM_PENDING_ASSIGN(TaskBusinessStatusEnum.PENDING_ASSIGN, TaskBusinessStatusEnum.CLOSED, "CLOSE", "关闭任务"),
    
    // 从待执行状态的转换
    START_TASK(TaskBusinessStatusEnum.PENDING_EXECUTE, TaskBusinessStatusEnum.EXECUTING, "START", "开始执行"),
    CLOSE_FROM_PENDING_EXECUTE(TaskBusinessStatusEnum.PENDING_EXECUTE, TaskBusinessStatusEnum.CLOSED, "CLOSE", "关闭任务"),
    
    // 从执行中状态的转换
    COMPLETE_TASK(TaskBusinessStatusEnum.EXECUTING, TaskBusinessStatusEnum.COMPLETED, "COMPLETE", "完成任务"),
    CLOSE_FROM_EXECUTING(TaskBusinessStatusEnum.EXECUTING, TaskBusinessStatusEnum.CLOSED, "CLOSE", "关闭任务"),
    
    // 从已完成状态的转换
    REACTIVATE_TASK(TaskBusinessStatusEnum.COMPLETED, TaskBusinessStatusEnum.EXECUTING, "REACTIVATE", "重新激活"),
    
    // 从已关闭状态的转换
    REOPEN_TASK(TaskBusinessStatusEnum.CLOSED, TaskBusinessStatusEnum.PENDING_EXECUTE, "REOPEN", "重新打开");
    
    /**
     * 源状态
     */
    private final TaskBusinessStatusEnum sourceStatus;
    
    /**
     * 目标状态
     */
    private final TaskBusinessStatusEnum targetStatus;
    
    /**
     * 触发事件
     */
    private final String event;
    
    /**
     * 转换描述
     */
    private final String description;
    
    /**
     * 根据源状态和目标状态查找转换枚举
     */
    public static TaskStatusTransitionEnum findTransition(Integer sourceStatus, Integer targetStatus) {
        if (sourceStatus == null || targetStatus == null) {
            return null;
        }
        
        TaskBusinessStatusEnum source = TaskBusinessStatusEnum.getByCode(sourceStatus);
        TaskBusinessStatusEnum target = TaskBusinessStatusEnum.getByCode(targetStatus);
        
        if (source == null || target == null) {
            return null;
        }
        
        for (TaskStatusTransitionEnum transition : values()) {
            if (transition.getSourceStatus() == source && transition.getTargetStatus() == target) {
                return transition;
            }
        }
        return null;
    }
    
    /**
     * 检查状态转换是否有效
     */
    public static boolean isValidTransition(Integer sourceStatus, Integer targetStatus) {
        return findTransition(sourceStatus, targetStatus) != null;
    }
    
    /**
     * 获取转换事件
     */
    public static String getTransitionEvent(Integer sourceStatus, Integer targetStatus) {
        TaskStatusTransitionEnum transition = findTransition(sourceStatus, targetStatus);
        return transition != null ? transition.getEvent() : null;
    }
    
    /**
     * 获取转换描述
     */
    public static String getTransitionDescription(Integer sourceStatus, Integer targetStatus) {
        TaskStatusTransitionEnum transition = findTransition(sourceStatus, targetStatus);
        return transition != null ? transition.getDescription() : "未知转换";
    }
}
