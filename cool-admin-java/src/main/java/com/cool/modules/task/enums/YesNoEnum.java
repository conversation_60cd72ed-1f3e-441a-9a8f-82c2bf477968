package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否枚举
 */
@Getter
@AllArgsConstructor
public enum YesNoEnum {
    
    NO(0, false, "否"),
    YES(1, true, "是");

    private final Integer intValue;
    private final Boolean boolValue;
    private final String name;

    /**
     * 根据整数值获取枚举
     */
    public static YesNoEnum getByIntValue(Integer value) {
        if (value == null) return null;
        for (YesNoEnum yesNo : values()) {
            if (yesNo.getIntValue().equals(value)) {
                return yesNo;
            }
        }
        return null;
    }

    /**
     * 根据布尔值获取枚举
     */
    public static YesNoEnum getByBoolValue(Boolean value) {
        if (value == null) return null;
        for (YesNoEnum yesNo : values()) {
            if (yesNo.getBoolValue().equals(value)) {
                return yesNo;
            }
        }
        return null;
    }

    /**
     * 根据整数值获取名称
     */
    public static String getNameByIntValue(Integer value) {
        YesNoEnum yesNo = getByIntValue(value);
        return yesNo != null ? yesNo.getName() : "未知";
    }

    /**
     * 根据布尔值获取名称
     */
    public static String getNameByBoolValue(Boolean value) {
        YesNoEnum yesNo = getByBoolValue(value);
        return yesNo != null ? yesNo.getName() : "未知";
    }

    /**
     * 布尔值转整数值
     */
    public static Integer boolToInt(Boolean value) {
        YesNoEnum yesNo = getByBoolValue(value);
        return yesNo != null ? yesNo.getIntValue() : null;
    }

    /**
     * 整数值转布尔值
     */
    public static Boolean intToBool(Integer value) {
        YesNoEnum yesNo = getByIntValue(value);
        return yesNo != null ? yesNo.getBoolValue() : null;
    }
}
