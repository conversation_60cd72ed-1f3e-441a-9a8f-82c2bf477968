package com.cool.modules.task.mapper;

import com.mybatisflex.core.BaseMapper;
import com.cool.modules.task.entity.TaskInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 任务信息Mapper
 */
@Mapper
public interface TaskInfoMapper extends BaseMapper<TaskInfoEntity> {

    /**
     * 分页查询任务信息列表（关联部门信息）
     */
    List<TaskInfoEntity> selectTaskInfoWithDetails(@Param("params") Map<String, Object> params);

    /**
     * 查询任务信息列表总数（关联部门和项目信息）
     */
    int countTaskInfoWithDetails(@Param("params") Map<String, Object> params);

    /**
     * 根据ID获取任务信息详情（关联部门信息）
     */
    TaskInfoEntity selectTaskInfoWithDepartmentById(@Param("taskId") Long taskId);

    /**
     * 根据任务包ID查询任务列表（关联部门信息）
     */
    List<TaskInfoEntity> selectTaskInfoByPackageId(@Param("packageId") Long packageId);

    /**
     * 查询用户有权限的任务列表（关联部门和执行信息）
     */
    List<Map<String, Object>> selectUserTasksWithDepartment(@Param("params") Map<String, Object> params);

    /**
     * 查询用户任务总数
     */
    int countUserTasksWithDepartment(@Param("params") Map<String, Object> params);

    /**
     * 分页查询任务信息列表（关联执行人信息）
     */
    List<TaskInfoEntity> selectTaskInfoWithExecutions(@Param("params") Map<String, Object> params);
}
