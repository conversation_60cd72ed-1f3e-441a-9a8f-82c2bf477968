package com.cool.modules.task.service;

import com.cool.modules.task.dto.AssignmentRequest;
import com.cool.modules.task.dto.AssignmentResult;
import com.cool.modules.task.dto.CandidateProfile;
import com.cool.modules.task.entity.TaskInfoEntity;

import java.util.List;

/**
 * 自动分配服务接口
 */
public interface AutoAssignmentService {

    /**
     * 执行自动分配
     * 
     * @param request 分配请求
     * @return 分配结果
     */
    AssignmentResult executeAssignment(AssignmentRequest request);

    /**
     * 为单个任务分配执行人
     * 
     * @param taskId 任务ID
     * @param autoAssign 是否自动分配
     * @return 分配结果
     */
    AssignmentResult assignSingleTask(Long taskId, Boolean autoAssign);

    /**
     * 为任务包批量分配执行人
     * 
     * @param packageId 任务包ID
     * @param autoAssign 是否自动分配
     * @return 分配结果
     */
    AssignmentResult assignTaskPackage(Long packageId, Boolean autoAssign);

    /**
     * 获取任务的候选人列表
     * 
     * @param taskId 任务ID
     * @return 候选人列表
     */
    List<CandidateProfile> getCandidatesForTask(Long taskId);

    /**
     * 获取任务的候选人列表（支持直接传递任务对象）
     * @param task 任务对象
     * @return 候选人列表
     */
    List<CandidateProfile> getCandidatesForTask(TaskInfoEntity task);

    /**
     * 获取所有可用的候选人
     * 
     * @return 候选人列表
     */
    List<CandidateProfile> getAllCandidates();

    /**
     * 根据角色筛选候选人
     * 
     * @param requiredRoles 必需角色
     * @return 候选人列表
     */
    List<CandidateProfile> getCandidatesByRoles(List<String> requiredRoles);

    /**
     * 验证分配结果
     * 
     * @param taskId 任务ID
     * @param assigneeIds 执行人ID列表
     * @return 验证是否通过
     */
    Boolean validateAssignment(Long taskId, List<Long> assigneeIds);

    /**
     * 手动分配任务（替换现有分配）
     * 
     * @param taskId 任务ID
     * @param assigneeIds 执行人ID列表
     * @param reason 分配原因
     * @return 是否成功
     */
    Boolean manualAssignTask(Long taskId, List<Long> assigneeIds, String reason);

    /**
     * 生成分配理由
     * 
     * @param task 任务信息
     * @param assignee 分配的执行人
     * @return 分配理由
     */
    String generateAssignmentReason(TaskInfoEntity task, CandidateProfile assignee);

    /**
     * 计算分配置信度
     * 
     * @param task 任务信息
     * @param assignee 分配的执行人
     * @return 置信度 (0-100)
     */
    Integer calculateConfidence(TaskInfoEntity task, CandidateProfile assignee);

    /**
     * 处理分配失败的情况
     * 
     * @param taskId 任务ID
     * @param failureReason 失败原因
     * @return 处理建议
     */
    List<String> handleAssignmentFailure(Long taskId, String failureReason);
}
