package com.cool.modules.task.service;

import com.cool.core.base.BaseService;
import com.cool.modules.task.entity.TaskExecutionEntity;

import java.util.List;
import java.util.Map;

/**
 * 任务执行服务接口
 */
public interface TaskExecutionService extends BaseService<TaskExecutionEntity> {

    /**
     * 根据任务ID获取执行记录
     *
     * @param taskId 任务ID
     * @return 执行记录列表
     */
    List<TaskExecutionEntity> getByTaskId(Long taskId);

    /**
     * 开始任务
     *
     * @param taskId 任务ID
     * @param assigneeId 执行人ID
     * @return 是否成功
     */
    boolean startTask(Long taskId, Long assigneeId);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param assigneeId 执行人ID
     * @param remark 备注
     * @return 是否成功
     */
    boolean completeTask(Long taskId, Long assigneeId, String remark);

    /**
     * 根据执行人ID获取执行记录
     * 
     * @param assigneeId 执行人ID
     * @return 执行记录列表
     */
    List<TaskExecutionEntity> getByAssigneeId(Long assigneeId);

    /**
     * 检查任务是否已分配
     * 
     * @param taskId 任务ID
     * @return 是否已分配
     */
    Boolean isTaskAssigned(Long taskId);

    /**
     * 取消任务分配
     * 
     * @param taskId 任务ID
     * @param assigneeId 执行人ID
     * @return 是否成功
     */
    Boolean cancelAssignment(Long taskId, Long assigneeId);

    /**
     * 完成任务执行
     * 
     * @param taskId 任务ID
     * @param assigneeId 执行人ID
     * @return 是否成功
     */
    Boolean completeExecution(Long taskId, Long assigneeId);

    /**
     * 获取用户当前工作负载
     *
     * @param userId 用户ID
     * @return 工作负载百分比
     */
    Integer getUserWorkload(Long userId);

    /**
     * 获取个人工作台任务列表
     *
     * @param assigneeId 执行人ID
     * @return 个人任务列表
     */
    List<Map<String, Object>> getPersonalTasks(Long assigneeId);

}
