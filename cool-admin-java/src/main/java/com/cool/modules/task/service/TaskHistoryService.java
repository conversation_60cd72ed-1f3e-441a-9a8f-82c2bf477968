package com.cool.modules.task.service;

import com.cool.core.base.BaseService;
import com.cool.modules.task.dto.StatusChangeContext;
import com.cool.modules.task.entity.TaskHistoryEntity;

import java.util.List;

/**
 * 任务历史记录服务接口
 */
public interface TaskHistoryService extends BaseService<TaskHistoryEntity> {

    /**
     * 记录任务状态变更历史
     * @param taskId 任务ID
     * @param actionType 操作类型
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param actionBy 操作人ID
     * @param actionByName 操作人姓名
     * @param note 操作说明
     */
    void recordTaskHistory(Long taskId, String actionType, Integer oldStatus, Integer newStatus, 
                          Long actionBy, String actionByName, String note);

    /**
     * 记录任务操作历史（不涉及状态变更）
     * @param taskId 任务ID
     * @param actionType 操作类型
     * @param actionBy 操作人ID
     * @param actionByName 操作人姓名
     * @param note 操作说明
     */
    void recordTaskAction(Long taskId, String actionType, Long actionBy, String actionByName, String note);

    /**
     * 记录状态流转历史（增强版）
     * @param taskId 任务ID
     * @param sourceStatus 源状态
     * @param targetStatus 目标状态
     * @param context 状态变更上下文
     */
    void recordStatusChange(Long taskId, Integer sourceStatus, Integer targetStatus, StatusChangeContext context);

    /**
     * 获取任务的状态变更历史
     * @param taskId 任务ID
     * @return 状态变更历史列表
     */
    List<TaskHistoryEntity> getStatusChangeHistory(Long taskId);

    /**
     * 获取任务的完整操作历史
     * @param taskId 任务ID
     * @param limit 限制数量，null表示不限制
     * @return 操作历史列表
     */
    List<TaskHistoryEntity> getTaskHistory(Long taskId, Integer limit);

    /**
     * 获取用户的操作历史统计
     * @param operatorId 操作人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作统计
     */
    List<Object> getOperatorStats(Long operatorId, java.util.Date startTime, java.util.Date endTime);
}
