package com.cool.modules.task.service;

import com.cool.core.base.BaseService;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.mybatisflex.core.paginate.Page;

import java.util.List;
import java.util.Map;

/**
 * 场景任务包Service
 */
public interface TaskPackageService extends BaseService<TaskPackageEntity> {

    /**
     * 创建任务包
     */
    TaskPackageEntity createTaskPackage(TaskPackageEntity taskPackage);

    /**
     * 更新任务包统计信息
     */
    void updatePackageStats(Long packageId);

    /**
     * 获取场景任务包列表（分页）
     */
    Page<TaskPackageEntity> getScenarioPackages(Page<TaskPackageEntity> page, Map<String, Object> params);

    /**
     * 获取任务包详情（包含统计信息）
     */
    TaskPackageEntity getPackageDetailWithStats(Long packageId);

    /**
     * 根据场景ID获取任务包列表
     */
    List<TaskPackageEntity> getPackagesByScenarioId(Long scenarioId);

    /**
     * 删除任务包（软删除）
     */
    boolean deletePackage(Long packageId);

    /**
     * 完成任务包
     */
    boolean completePackage(Long packageId);

    /**
     * 关闭任务包
     */
    boolean closePackage(Long packageId);
}
