package com.cool.modules.task.service.impl;

import com.cool.core.util.CoolSecurityUtil;
import com.cool.core.util.StringUtils;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.task.dto.AssignmentRequest;
import com.cool.modules.task.dto.AssignmentResult;
import com.cool.modules.task.dto.CandidateProfile;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.enums.AssignmentTypeEnum;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.AssignmentStrategyEnum;
import com.cool.modules.task.enums.AssignmentFailureCodeEnum;
import com.cool.modules.task.enums.TaskRoleEnum;
import com.cool.modules.task.service.AutoAssignmentService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.dify.dto.DifyWorkflowBlockingResponse;
import com.cool.modules.dify.dto.DifyWorkflowExecuteRequest;
import com.cool.modules.dify.dto.DifyWorkflowResponseModeEnum;
import com.cool.modules.dify.emuns.DifyWorkflowEnums;
import com.cool.modules.dify.service.DifyWorkflowService;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
// import com.cool.modules.sop.service.LLMScheduleService; // 暂时注释，该服务不存在
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cool.modules.task.service.TaskPackageService;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动分配服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AutoAssignmentServiceImpl implements AutoAssignmentService {

    private final TaskInfoService taskInfoService;
    private final TaskExecutionService taskExecutionService;
    private final BaseSysUserService baseSysUserService;
    private final TaskPackageService taskPackageService;
    // private final LLMScheduleService llmScheduleService; // 暂时注释，该服务不存在
    private final DifyWorkflowService difyWorkflowService;

    @Override
    @Transactional
    public AssignmentResult executeAssignment(AssignmentRequest request) {
        log.info("开始执行自动分配，请求: {}", request);
        try {
            DifyWorkflowExecuteRequest difyReq = new DifyWorkflowExecuteRequest();
            difyReq.setWorkflowName(DifyWorkflowEnums.TASK_ASSIGN);
            difyReq.setUser(CoolSecurityUtil.getCurrentUserId().toString());
            Map<String, Object> inputs = new HashMap<>();
            inputs.put("token", CoolSecurityUtil.getToken());
            inputs.put("request", JSONUtil.toJsonStr(request));
            difyReq.setInputs(inputs);
            difyReq.setResponseMode(DifyWorkflowResponseModeEnum.BLOCKING.getValue());

            // 调用 Dify 工作流
            DifyWorkflowBlockingResponse difyResp = difyWorkflowService.executeWorkflowByName(difyReq);
            String output=difyResp.getData().getString("output");
            output=StringUtils.cleanMarkdown(output);
            AssignmentResult result=JSONUtil.toBean(output,AssignmentResult.class);
            log.info("自动分配完成，结果: {}", result.getSummary());
            return result;
        } catch (Exception e) {
            log.error("自动分配执行失败", e);
            AssignmentResult result = new AssignmentResult();
            result.setSuccess(false);
            result.setSuggestions(Collections.singletonList("系统异常，请稍后重试或联系管理员: " + e.getMessage()));
            return result;
        }
    }

    @Override
    public AssignmentResult assignSingleTask(Long taskId, Boolean autoAssign) {
        AssignmentRequest request = new AssignmentRequest();
        request.setAssignmentType(AssignmentTypeEnum.MANUAL.getCode());
        request.setTaskIds(Collections.singletonList(taskId));
        request.setAutoAssign(autoAssign);
        
        // 设置默认约束条件
        AssignmentRequest.AssignmentConstraints constraints = new AssignmentRequest.AssignmentConstraints();
        constraints.setMaxWorkload(90);
        constraints.setCollaborationAllowed(true);
        request.setConstraints(constraints);

        return executeAssignment(request);
    }

    @Override
    public AssignmentResult assignTaskPackage(Long packageId, Boolean autoAssign) {
        try {
            // 获取任务包下的所有任务
            List<TaskInfoEntity> tasks = taskInfoService.list(
                com.mybatisflex.core.query.QueryWrapper.create()
                    .eq("package_id", packageId)
                    .eq("is_deleted", 0)
            );

            if (tasks.isEmpty()) {
                AssignmentResult result = new AssignmentResult();
                result.setSuccess(false);
                result.setSuggestions(Collections.singletonList("任务包中没有找到任务"));
                return result;
            }

            List<Long> taskIds = tasks.stream()
                    .map(TaskInfoEntity::getId)
                    .collect(Collectors.toList());

            AssignmentRequest request = new AssignmentRequest();
            request.setAssignmentType(AssignmentTypeEnum.BATCH.getCode());
            request.setTaskIds(taskIds);
            request.setAutoAssign(autoAssign);

            // 设置默认约束条件
            AssignmentRequest.AssignmentConstraints constraints = new AssignmentRequest.AssignmentConstraints();
            constraints.setMaxWorkload(90);
            constraints.setCollaborationAllowed(true);
            request.setConstraints(constraints);

            return executeAssignment(request);

        } catch (Exception e) {
            log.error("任务包分配失败，packageId: {}", packageId, e);
            AssignmentResult result = new AssignmentResult();
            result.setSuccess(false);
            result.setSuggestions(Collections.singletonList("任务包分配失败: " + e.getMessage()));
            return result;
        }
    }

    @Override
    public List<CandidateProfile> getCandidatesForTask(Long taskId) {
        try {
            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return Collections.emptyList();
            }

            // 获取任务所需角色
            List<String> requiredRoles = new ArrayList<>();
            if (task.getEmployeeRole() != null) {
                requiredRoles.add(task.getEmployeeRole());
            }

            return getCandidatesByRoles(requiredRoles);

        } catch (Exception e) {
            log.error("获取任务候选人失败，taskId: {}", taskId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<CandidateProfile> getCandidatesForTask(TaskInfoEntity task) {
        if (task == null) {
            return Collections.emptyList();
        }
        // 获取任务所需角色
        List<String> requiredRoles = new ArrayList<>();
        if (task.getEmployeeRole() != null) {
            requiredRoles.add(task.getEmployeeRole());
        }
        return getCandidatesByRoles(requiredRoles);
    }

    @Override
    public List<CandidateProfile> getAllCandidates() {
        try {
            // 获取所有启用的用户
            List<BaseSysUserEntity> users = baseSysUserService.list(
                com.mybatisflex.core.query.QueryWrapper.create()
                    .eq("status", 1) // 启用状态
                    .eq("is_deleted", 0) // 未删除
            );
            
            List<CandidateProfile> candidates = new ArrayList<>();
            
            for (BaseSysUserEntity user : users) {
                CandidateProfile candidate = new CandidateProfile();
                candidate.setUserId(user.getId());
                candidate.setUserName(getUserRealName(user.getId()));
                
                // 设置角色信息 - 这里可以根据实际业务需求从用户角色表获取
                candidate.setRoles(Arrays.asList("员工")); // 默认角色
                
                // 设置默认工作负载和绩效评分 - 这里可以从其他业务表获取真实数据
                candidate.setCurrentWorkload(50); // 默认工作负载50%
                candidate.setPerformanceScore(80); // 默认绩效评分80分
                candidate.setIsOnline(true); // 默认在线
                candidate.setIsAvailable(true); // 默认可用
                
                candidates.add(candidate);
            }
            
            log.info("获取到 {} 个候选人", candidates.size());
            return candidates;
            
        } catch (Exception e) {
            log.error("获取所有候选人失败", e);
            
            // 返回默认的模拟数据作为备选方案
            List<CandidateProfile> candidates = new ArrayList<>();
            
            CandidateProfile candidate1 = new CandidateProfile();
            candidate1.setUserId(1L);
            candidate1.setUserName("张三");
            candidate1.setRoles(Arrays.asList("产品经理", "项目经理"));
            candidate1.setCurrentWorkload(60);
            candidate1.setPerformanceScore(85);
            candidate1.setIsOnline(true);
            candidate1.setIsAvailable(true);
            candidates.add(candidate1);

            CandidateProfile candidate2 = new CandidateProfile();
            candidate2.setUserId(2L);
            candidate2.setUserName("李四");
            candidate2.setRoles(Arrays.asList("开发工程师", "技术专家"));
            candidate2.setCurrentWorkload(40);
            candidate2.setPerformanceScore(90);
            candidate2.setIsOnline(true);
            candidate2.setIsAvailable(true);
            candidates.add(candidate2);

            return candidates;
        }
    }

    @Override
    public List<CandidateProfile> getCandidatesByRoles(List<String> requiredRoles) {
        List<CandidateProfile> allCandidates = getAllCandidates();
        
        if (requiredRoles == null || requiredRoles.isEmpty()) {
            return allCandidates;
        }

        return allCandidates.stream()
                .filter(candidate -> candidate.getRoles() != null && 
                        candidate.getRoles().stream().anyMatch(requiredRoles::contains))
                .collect(Collectors.toList());
    }

    @Override
    public Boolean validateAssignment(Long taskId, List<Long> assigneeIds) {
        // 基础验证
        if (taskId == null || assigneeIds == null || assigneeIds.isEmpty()) {
            return false;
        }

        // 检查任务是否存在
        TaskInfoEntity task = taskInfoService.getById(taskId);
        if (task == null) {
            return false;
        }

        // 检查执行人是否有效
        List<CandidateProfile> candidates = getAllCandidates();
        List<Long> validUserIds = candidates.stream()
                .map(CandidateProfile::getUserId)
                .collect(Collectors.toList());

        return assigneeIds.stream().allMatch(validUserIds::contains);
    }

    @Override
    @Transactional
    public Boolean manualAssignTask(Long taskId, List<Long> assigneeIds, String reason) {
        try {
            // 1. 验证任务是否存在
            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                log.error("任务不存在: {}", taskId);
                return false;
            }

            // 2. 验证执行人是否有效
            if (assigneeIds == null || assigneeIds.isEmpty()) {
                log.error("执行人列表为空");
                return false;
            }

            // 3. 获取现有分配记录
            List<TaskExecutionEntity> existingExecutions = taskExecutionService.getByTaskId(taskId);
            
            // 4. 智能差异化处理
            Set<Long> existingAssigneeIds = existingExecutions.stream()
                    .map(TaskExecutionEntity::getAssigneeId)
                    .collect(Collectors.toSet());
            
            Set<Long> newAssigneeIds = new HashSet<>(assigneeIds);
            
            // 5. 找出需要移除的执行人（在旧列表中但不在新列表中）
            Set<Long> toRemove = new HashSet<>(existingAssigneeIds);
            toRemove.removeAll(newAssigneeIds);
            
            // 6. 找出需要添加的执行人（在新列表中但不在旧列表中）
            Set<Long> toAdd = new HashSet<>(newAssigneeIds);
            toAdd.removeAll(existingAssigneeIds);
            
            // 7. 移除不再需要的执行人（仅移除未开始执行的）
            for (TaskExecutionEntity execution : existingExecutions) {
                if (toRemove.contains(execution.getAssigneeId())) {
                    // 只有在"已分配"状态时才允许移除，保护已经开始的任务
                    if (TaskExecutionStatusEnum.ASSIGNED.getCode().equals(execution.getExecutionStatus())) {
                        taskExecutionService.removeById(execution.getId());
                        log.info("移除执行人: taskId={}, assigneeId={}", taskId, execution.getAssigneeId());
                    } else {
                        // 对于已经开始执行的任务，只标记为已取消，保留历史记录
                        execution.setExecutionStatusEnum(TaskExecutionStatusEnum.CANCELLED);
                        execution.setUpdateTime(new Date());
                        execution.setRemark("手动调整执行人 - 取消原因");
                        taskExecutionService.updateById(execution);
                        log.info("取消执行人任务（保留历史）: taskId={}, assigneeId={}, 原状态={}", 
                               taskId, execution.getAssigneeId(), execution.getExecutionStatus());
                    }
                }
            }
            
            // 8. 添加新的执行人
            for (Long assigneeId : toAdd) {
                TaskExecutionEntity execution = new TaskExecutionEntity();
                execution.setTaskId(taskId);
                execution.setAssigneeId(assigneeId);
                execution.setAssigneeName(getUserRealName(assigneeId)); // 获取真实用户姓名
                execution.setExecutionStatusEnum(TaskExecutionStatusEnum.ASSIGNED);
                execution.setAcceptTime(new Date());
                execution.setAssignmentTypeEnum(AssignmentTypeEnum.MANUAL); // 标记为手动分配
                execution.setConfidence(100); // 手动分配置信度为100%
                execution.setAssignmentReasons(reason != null ? reason : "手动分配执行人");
                execution.setCreateTime(new Date());
                execution.setUpdateTime(new Date());

                taskExecutionService.save(execution);
                log.info("新增执行人: taskId={}, assigneeId={}", taskId, assigneeId);
            }
            
            // 9. 如果有新增执行人且任务状态为待分配，更新任务状态为待执行
            if (!toAdd.isEmpty()) {
                TaskInfoEntity taskEntity = taskInfoService.getById(taskId);
                if (taskEntity != null && taskEntity.getTaskStatus() == TaskBusinessStatusEnum.PENDING_ASSIGN.getCode()) { // PENDING_ASSIGN = 0
                    taskEntity.setTaskStatus(TaskBusinessStatusEnum.PENDING_EXECUTE.getCode()); // PENDING_EXECUTE = 1
                    taskEntity.setUpdateTime(new Date());
                    taskInfoService.updateById(taskEntity);
                    log.info("任务状态已更新：taskId={}, 从待分配(0)变为待执行(1)", taskId);
                }
            }
            
            // 9. 对于保持不变的执行人，更新分配原因（可选）
            if (reason != null && !reason.isEmpty()) {
                for (TaskExecutionEntity execution : existingExecutions) {
                    if (newAssigneeIds.contains(execution.getAssigneeId()) && 
                        !toRemove.contains(execution.getAssigneeId())) {
                        execution.setAssignmentReasons(reason);
                        execution.setUpdateTime(new Date());
                        taskExecutionService.updateById(execution);
                    }
                }
            }

            log.info("智能手动分配任务完成: taskId={}, 新增={}, 移除={}, 保持={}", 
                   taskId, toAdd.size(), toRemove.size(), 
                   newAssigneeIds.size() - toAdd.size());
            
            // 10. 更新任务包统计信息
            if (task.getPackageId() != null) {
                taskPackageService.updatePackageStats(task.getPackageId());
                log.info("已更新任务包统计信息: packageId={}", task.getPackageId());
            }
            
            return true;

        } catch (Exception e) {
            log.error("手动分配任务失败: taskId={}, assigneeIds={}", taskId, assigneeIds, e);
            return false;
        }
    }

    @Override
    public String generateAssignmentReason(TaskInfoEntity task, CandidateProfile assignee) {
        StringBuilder reason = new StringBuilder();
        
        // 角色匹配
        if (task.getEmployeeRole() != null && assignee.getRoles() != null && 
            assignee.getRoles().contains(task.getEmployeeRole())) {
            reason.append("• 角色完全匹配任务要求(").append(task.getEmployeeRole()).append(")\n");
        }
        
        // 工作负载
        if (assignee.getCurrentWorkload() != null) {
            reason.append("• 当前工作负载为").append(assignee.getCurrentWorkload())
                  .append("%，有充足时间处理新任务\n");
        }
        
        // 绩效评分
        if (assignee.getPerformanceScore() != null && assignee.getPerformanceScore() > 80) {
            reason.append("• 历史绩效评分").append(assignee.getPerformanceScore())
                  .append("分，表现优秀\n");
        }
        
        // 可用性
        if (Boolean.TRUE.equals(assignee.getIsAvailable())) {
            reason.append("• 当前状态可用，可以立即开始工作\n");
        }

        return reason.length() > 0 ? reason.toString() : "系统推荐的合适人选";
    }

    @Override
    public Integer calculateConfidence(TaskInfoEntity task, CandidateProfile assignee) {
        int confidence = 0;
        
        // 角色匹配 (40%)
        if (task.getEmployeeRole() != null && assignee.getRoles() != null && 
            assignee.getRoles().contains(task.getEmployeeRole())) {
            confidence += 40;
        }
        
        // 工作负载 (25%)
        if (assignee.getCurrentWorkload() != null) {
            confidence += Math.max(0, 25 - (assignee.getCurrentWorkload() / 4));
        }
        
        // 绩效评分 (20%)
        if (assignee.getPerformanceScore() != null) {
            confidence += (int) (20.0 * assignee.getPerformanceScore() / 100);
        }
        
        // 可用性 (15%)
        if (Boolean.TRUE.equals(assignee.getIsAvailable())) {
            confidence += 15;
        }
        
        return Math.min(100, confidence);
    }

    @Override
    public List<String> handleAssignmentFailure(Long taskId, String failureReason) {
        List<String> suggestions = new ArrayList<>();
        
        if (failureReason.contains("没有合适的执行人")) {
            suggestions.add("调整任务的角色要求");
            suggestions.add("增加任务的截止时间");
            suggestions.add("手动指定执行人");
        } else if (failureReason.contains("工作负载过高")) {
            suggestions.add("等待其他任务完成后重新分配");
            suggestions.add("将任务分解为更小的子任务");
            suggestions.add("调整任务优先级");
        } else {
            suggestions.add("检查任务信息是否完整");
            suggestions.add("联系管理员处理");
        }
        
        return suggestions;
    }

    // 私有方法
    private AssignmentResult assignSingleTaskInternal(Long taskId, AssignmentRequest request) {
        AssignmentResult result = new AssignmentResult();
        result.setAssignments(new ArrayList<>());
        result.setFailedTasks(new ArrayList<>());

        try {
            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                addFailedTask(result, taskId, "任务不存在", AssignmentFailureCodeEnum.TASK_NOT_FOUND.getCode());
                return result;
            }

            List<CandidateProfile> candidates = getCandidatesForTask(task);
            if (candidates.isEmpty()) {
                addFailedTask(result, taskId, "没有找到合适的候选人", AssignmentFailureCodeEnum.NO_CANDIDATES.getCode());
                return result;
            }

            // 选择最佳候选人
            CandidateProfile bestCandidate = selectBestCandidate(task, candidates);
            if (bestCandidate == null) {
                addFailedTask(result, taskId, "没有合适的执行人", AssignmentFailureCodeEnum.NO_SUITABLE_ASSIGNEE.getCode());
                return result;
            }

            // 创建分配记录
            if (createAssignmentRecord(task, bestCandidate)) {
                AssignmentResult.TaskAssignment assignment = new AssignmentResult.TaskAssignment();
                assignment.setTaskId(String.valueOf(taskId));
                assignment.setTaskName(task.getName());
                assignment.setReason(generateAssignmentReason(task, bestCandidate));
                assignment.setAiGenerated(request.getAutoAssign());
                assignment.setAssignmentTime(LocalDateTime.now());
                assignment.setConfidence(calculateConfidence(task, bestCandidate));

                AssignmentResult.Assignee assignee = new AssignmentResult.Assignee();
                assignee.setUserId(bestCandidate.getUserId());
                assignee.setUserName(bestCandidate.getUserName());
                assignee.setTaskRole(TaskRoleEnum.PRIMARY.getCode());
                assignee.setConfidence(assignment.getConfidence());
                assignee.setCurrentWorkload(bestCandidate.getCurrentWorkload());

                assignment.setAssignees(Collections.singletonList(assignee));
                result.getAssignments().add(assignment);
            } else {
                addFailedTask(result, taskId, "创建分配记录失败", AssignmentFailureCodeEnum.ASSIGNMENT_CREATION_FAILED.getCode());
            }

        } catch (Exception e) {
            log.error("单任务分配失败", e);
            addFailedTask(result, taskId, "分配过程中发生异常: " + e.getMessage(), AssignmentFailureCodeEnum.SYSTEM_ERROR.getCode());
        }

        result.setSuccess(Boolean.TRUE.equals(result.getSuccess()));
        return result;
    }

    private AssignmentResult assignBatchTasks(AssignmentRequest request) {
        AssignmentResult result = new AssignmentResult();
        result.setAssignments(new ArrayList<>());
        result.setFailedTasks(new ArrayList<>());

        for (Long taskId : request.getTaskIds()) {
            AssignmentResult singleResult = assignSingleTaskInternal(taskId, request);
            result.getAssignments().addAll(singleResult.getAssignments());
            result.getFailedTasks().addAll(singleResult.getFailedTasks());
        }

        result.setSuccess(Boolean.TRUE.equals(result.getSuccess()));
        return result;
    }

    private CandidateProfile selectBestCandidate(TaskInfoEntity task, List<CandidateProfile> candidates) {
        return candidates.stream()
                .filter(candidate -> Boolean.TRUE.equals(candidate.getIsAvailable()))
                .filter(candidate -> candidate.getCurrentWorkload() == null || candidate.getCurrentWorkload() < 90)
                .max(Comparator.comparingInt(candidate -> calculateConfidence(task, candidate)))
                .orElse(null);
    }

    private boolean createAssignmentRecord(TaskInfoEntity task, CandidateProfile assignee) {
        try {
            TaskExecutionEntity execution = new TaskExecutionEntity();
            execution.setTaskId(task.getId());
            execution.setAssigneeId(assignee.getUserId());
            execution.setAssigneeName(getUserRealName(assignee.getUserId()));
            execution.setExecutionStatusEnum(TaskExecutionStatusEnum.ASSIGNED);
            execution.setAcceptTime(new Date());
            execution.setAssignmentTypeEnum(AssignmentTypeEnum.AUTO);
            execution.setConfidence(calculateConfidence(task, assignee));
            execution.setAssignmentReasons(generateAssignmentReason(task, assignee));
            execution.setCreateTime(new Date());
            execution.setUpdateTime(new Date());

            boolean result = taskExecutionService.save(execution);
            
            // 如果创建分配记录成功，且任务状态为待分配，则更新为待执行
            if (result && task.getTaskStatus() == 0) { // PENDING_ASSIGN = 0
                task.setTaskStatus(1); // PENDING_EXECUTE = 1
                task.setUpdateTime(new Date());
                taskInfoService.updateById(task);
                log.info("自动分配后任务状态已更新：taskId={}, 从待分配(0)变为待执行(1)", task.getId());
                
                // 更新任务包统计信息
                if (task.getPackageId() != null) {
                    taskPackageService.updatePackageStats(task.getPackageId());
                    log.info("已更新任务包统计信息: packageId={}", task.getPackageId());
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("创建分配记录失败", e);
            return false;
        }
    }

    private void addFailedTask(AssignmentResult result, Long taskId, String reason, String code) {
        AssignmentResult.FailedTask failedTask = new AssignmentResult.FailedTask();
        failedTask.setTaskId(taskId);
        failedTask.setFailureReason(reason);
        failedTask.setFailureCode(code);
        failedTask.setSuggestedActions(handleAssignmentFailure(taskId, reason));
        result.getFailedTasks().add(failedTask);
    }

    /**
     * 获取用户真实姓名
     * @param userId 用户ID
     * @return 用户真实姓名，如果查询失败则返回默认值
     */
    private String getUserRealName(Long userId) {
        try {
            if (userId == null) {
                return "未知用户";
            }
            
            BaseSysUserEntity user = baseSysUserService.getById(userId);
            if (user != null) {
                // 优先使用真实姓名，如果没有则使用用户名，最后使用昵称
                if (user.getName() != null && !user.getName().trim().isEmpty()) {
                    return user.getName();
                } else if (user.getUsername() != null && !user.getUsername().trim().isEmpty()) {
                    return user.getUsername();
                } else if (user.getNickName() != null && !user.getNickName().trim().isEmpty()) {
                    return user.getNickName();
                }
            }
            
            log.warn("无法获取用户真实姓名，userId: {}", userId);
            return "用户" + userId;
            
        } catch (Exception e) {
            log.error("获取用户真实姓名失败，userId: {}", userId, e);
            return "用户" + userId;
        }
    }

    /**
     * 构建Dify智能分配输入
     */
    private List<Map<String, Object>> buildDifyTasksFromRequest(AssignmentRequest request) {
        List<Map<String, Object>> tasks = new ArrayList<>();
        if (request.getTaskIds() != null) {
            for (Long taskId : request.getTaskIds()) {
                Map<String, Object> task = new HashMap<>();
                task.put("taskId", taskId);
                // 可补充更多字段，如角色、描述、时间等
                tasks.add(task);
            }
        }
        return tasks;
    }

}
