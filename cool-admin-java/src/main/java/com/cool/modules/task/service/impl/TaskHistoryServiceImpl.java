package com.cool.modules.task.service.impl;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.task.dto.StatusChangeContext;
import com.cool.modules.task.entity.TaskHistoryEntity;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.TaskStatusTransitionEnum;
import com.cool.modules.task.mapper.TaskHistoryMapper;
import com.cool.modules.task.service.TaskHistoryService;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 任务历史记录服务实现
 */
@Service
public class TaskHistoryServiceImpl extends BaseServiceImpl<TaskHistoryMapper, TaskHistoryEntity>
        implements TaskHistoryService {

    @Override
    public void recordTaskHistory(Long taskId, String actionType, Integer oldStatus, Integer newStatus,
                                 Long actionBy, String actionByName, String note) {
        try {
            TaskHistoryEntity history = new TaskHistoryEntity();

            // 使用反射设置字段值，避免Lombok问题
            setFieldValue(history, "taskId", taskId);
            setFieldValue(history, "actionType", actionType);
            setFieldValue(history, "oldStatus", oldStatus);
            setFieldValue(history, "newStatus", newStatus);
            setFieldValue(history, "actionBy", actionBy);
            setFieldValue(history, "actionByName", actionByName);
            setFieldValue(history, "actionTime", new Date());
            setFieldValue(history, "note", note);

            save(history);
            System.out.println("记录任务历史成功，taskId: " + taskId + ", actionType: " + actionType);
        } catch (Exception e) {
            System.err.println("记录任务历史失败，taskId: " + taskId + ", actionType: " + actionType + ", error: " + e.getMessage());
        }
    }

    @Override
    public void recordTaskAction(Long taskId, String actionType, Long actionBy, String actionByName, String note) {
        recordTaskHistory(taskId, actionType, null, null, actionBy, actionByName, note);
    }

    @Override
    public void recordStatusChange(Long taskId, Integer sourceStatus, Integer targetStatus, StatusChangeContext context) {
        try {
            TaskHistoryEntity history = new TaskHistoryEntity();

            // 获取转换事件和描述
            String event = TaskStatusTransitionEnum.getTransitionEvent(sourceStatus, targetStatus);
            String description = TaskStatusTransitionEnum.getTransitionDescription(sourceStatus, targetStatus);

            // 构建详细的操作说明
            StringBuilder noteBuilder = new StringBuilder();
            noteBuilder.append(description);
            if (context.getReason() != null && !context.getReason().trim().isEmpty()) {
                noteBuilder.append("：").append(context.getReason());
            }

            // 添加额外信息
            if (context.getAttachments() != null && !context.getAttachments().trim().isEmpty()) {
                noteBuilder.append("，附件：").append(context.getAttachments());
            }
            if (context.getPhotos() != null && !context.getPhotos().trim().isEmpty()) {
                noteBuilder.append("，照片：").append(context.getPhotos());
            }

            // 设置字段值
            setFieldValue(history, "taskId", taskId);
            setFieldValue(history, "actionType", event);
            setFieldValue(history, "oldStatus", sourceStatus);
            setFieldValue(history, "newStatus", targetStatus);
            setFieldValue(history, "actionBy", context.getOperatorId());
            setFieldValue(history, "actionByName", context.getOperatorName());
            setFieldValue(history, "actionTime", context.getOperationTime() != null ? context.getOperationTime() : new Date());
            setFieldValue(history, "note", noteBuilder.toString());

            save(history);
            System.out.println("记录状态变更历史成功，taskId: " + taskId + ", " +
                TaskBusinessStatusEnum.getNameByCode(sourceStatus) + " -> " +
                TaskBusinessStatusEnum.getNameByCode(targetStatus));
        } catch (Exception e) {
            System.err.println("记录状态变更历史失败，taskId: " + taskId + ", error: " + e.getMessage());
        }
    }

    @Override
    public List<TaskHistoryEntity> getStatusChangeHistory(Long taskId) {
        return list(QueryWrapper.create()
                .eq("task_id", taskId)
                .isNotNull("old_status")
                .isNotNull("new_status")
                .orderBy("action_time", false));
    }

    @Override
    public List<TaskHistoryEntity> getTaskHistory(Long taskId, Integer limit) {
        QueryWrapper wrapper = QueryWrapper.create()
                .eq("task_id", taskId)
                .orderBy("action_time", false);

        if (limit != null && limit > 0) {
            wrapper.limit(limit);
        }

        return list(wrapper);
    }

    @Override
    public List<Object> getOperatorStats(Long operatorId, Date startTime, Date endTime) {
        // 这里可以根据需要实现具体的统计逻辑
        // 暂时返回空列表，后续可以扩展
        return new java.util.ArrayList<>();
    }

    private void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception e) {
            System.err.println("设置字段值失败，fieldName: " + fieldName + ", error: " + e.getMessage());
        }
    }
}
