package com.cool.modules.task.service.impl;

// 移除静态导入，使用字符串字段名

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cool.core.base.BaseServiceImpl;
import com.cool.core.request.PageResult;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.organization.service.ProjectAccessService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskLogEntity;
import static com.cool.modules.task.entity.table.TaskInfoEntityTableDef.TASK_INFO_ENTITY;
import static com.cool.modules.task.entity.table.TaskLogEntityTableDef.TASK_LOG_ENTITY;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.mapper.TaskInfoMapper;
import com.cool.modules.task.service.TaskDepartmentPermissionService;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.cool.modules.task.utils.ScheduleUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper; // 新增导入

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskInfoServiceImpl extends BaseServiceImpl<TaskInfoMapper, TaskInfoEntity> implements
        TaskInfoService {

    final private Scheduler scheduler;

    @Lazy
    @Autowired
    private SOPScenarioService sopScenarioService;

    @Lazy
    @Autowired
    private TaskExecutionService taskExecutionService;

    @Lazy
    @Autowired
    private TaskInfoService taskInfoService;

    @Lazy
    @Autowired
    private TaskDepartmentPermissionService departmentPermissionService;

    @Lazy
    @Autowired
    private TaskPackageService taskPackageService;

    @Autowired
    private BaseSysUserService baseSysUserService;

    @Lazy
    @Autowired
    private ProjectAccessService projectAccessService; // 新增依赖

    @Override
    public PageResult<TaskInfoEntity> kanbanPage(JSONObject requestParams) {
        try {
            // 复用page方法的权限检查和参数构建逻辑
            Page<TaskInfoEntity> page = new Page<>(requestParams.getInt("page", 1), requestParams.getInt("size", 10));
            Page<TaskInfoEntity> resultPage = (Page<TaskInfoEntity>) this.page(requestParams, page, null);
            return PageResult.of(resultPage);
        } catch (Exception e) {
            log.error("看板分页查询失败", e);
            // 失败时返回空分页结果
            Page<TaskInfoEntity> emptyPage = new Page<>();
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotalRow(0);
            emptyPage.setPageNumber(1);
            emptyPage.setPageSize(10);
            return PageResult.of(emptyPage);
        }
    }

    @Override
    public void init() {
        // try {
        // List<TaskInfoEntity> list = list();
        // list.forEach(scheduleJob -> {
        // CronTrigger cronTrigger = ScheduleUtils.getCronTrigger(scheduler,
        // scheduleJob.getJobId());
        // if (cronTrigger == null) {
        // ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
        // } else {
        // ScheduleUtils.updateScheduleJob(scheduler, scheduleJob);
        // }
        // updateById(scheduleJob);
        // });
        // } catch (Exception ignored) {
        // }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void once(Long taskId) {
        ScheduleUtils.run(scheduler, getById(taskId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stop(Long taskId) {
        ScheduleUtils.pauseJob(scheduler, taskId + "");
        TaskInfoEntity taskInfoEntity = getById(taskId);
        taskInfoEntity.setScheduleStatus(0);
        updateById(taskInfoEntity);

        // 更新任务包统计信息
        if (taskInfoEntity.getPackageId() != null) {
            taskPackageService.updatePackageStats(taskInfoEntity.getPackageId());
            log.info("任务开始执行后已更新任务包统计信息: packageId={}", taskInfoEntity.getPackageId());
        }

        modifyAfter(JSONUtil.parseObj(taskInfoEntity), taskInfoEntity);
    }

    @Override
    public Object log(Page page, Long taskId, Integer status) {

        QueryWrapper queryWrapper = QueryWrapper.create().select(TASK_LOG_ENTITY.DETAIL,
                TASK_LOG_ENTITY.STATUS, TASK_LOG_ENTITY.CREATE_TIME,
                TASK_INFO_ENTITY.NAME).from(TASK_LOG_ENTITY)
                .leftJoin(TASK_INFO_ENTITY).on(TASK_LOG_ENTITY.TASK_ID.eq(TASK_INFO_ENTITY.ID))
                .eq(TaskLogEntity::getTaskId, taskId, taskId != null)
                .eq(TaskLogEntity::getStatus, status, status != null)
                .orderBy(TaskLogEntity::getCreateTime, false);
        return mapper.paginateAs(page, queryWrapper, TaskLogEntity.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void start(Long taskId, Integer type) {
        TaskInfoEntity taskInfoEntity = getById(taskId);
        taskInfoEntity.setTaskStatus(TaskBusinessStatusEnum.EXECUTING.getCode());
        // taskInfoEntity.setScheduleStatus(1);
        // if (type != null) {
        // taskInfoEntity.setScheduleType(type);
        // }
        // boolean isExists = false;
        // try {
        // isExists = scheduler.checkExists(ScheduleUtils.getJobKey(taskId + ""));
        // } catch (SchedulerException e) {
        // log.error("err", e);
        // }
        // if (isExists) {
        // ScheduleUtils.updateScheduleJob(scheduler, taskInfoEntity);
        // ScheduleUtils.resumeJob(scheduler, taskId + "");
        // } else {
        // ScheduleUtils.createScheduleJob(scheduler, taskInfoEntity);
        // }
        updateById(taskInfoEntity);
        modifyAfter(JSONUtil.parseObj(taskInfoEntity), taskInfoEntity);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(JSONObject requestParams, TaskInfoEntity scheduleJob) {
        scheduleJob.setScheduleStatus(1);
        super.add(scheduleJob);
        scheduleJob.setJobId(scheduleJob.getId() + "");

        ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
        updateById(scheduleJob);
        super.modifyAfter(requestParams, scheduleJob);
        return scheduleJob.getId();
    }

    @Override
    public List<TaskInfoEntity> list(QueryWrapper queryWrapper) {
        List<TaskInfoEntity> tasks = super.list(queryWrapper);
        return enrichTasksWithExecutions(tasks);
    }

    /**
     * 重写page方法，全量使用MyBatis关联查询实现原有的数据输出能力
     */
    @Override
    public Object page(JSONObject requestParams, Page<TaskInfoEntity> page, QueryWrapper queryWrapper) {
        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            boolean isAdmin = baseSysUserService.isSuperAdmin(currentUserId);

            // 转换参数格式
            Map<String, Object> params = new HashMap<>();
            if (requestParams != null) {
                params.putAll(requestParams);
            }

            // 项目权限检查和高级搜索参数处理
            if (Objects.nonNull(requestParams)) {
                Long projectId = requestParams.getLong("projectId");
                if (projectId != null) {
                    // 检查用户是否有项目访问权限
                    if (!isAdmin && !projectAccessService.hasProjectAccess(currentUserId, projectId)) {
                        log.warn("用户 {} 无权访问项目 {}", currentUserId, projectId);
                        // 如果没有权限，返回空结果
                        return new Page<TaskInfoEntity>(page.getPageNumber(), page.getPageSize(), 0);
                    }
                    params.put("projectId", projectId);
                }

                // 处理高级搜索参数
                // 1. 任务执行时间过滤
                String executionStartDate = requestParams.getStr("executionStartDate");
                String executionEndDate = requestParams.getStr("executionEndDate");
                if (executionStartDate != null && !executionStartDate.isEmpty()) {
                    params.put("executionStartDate", executionStartDate);
                }
                if (executionEndDate != null && !executionEndDate.isEmpty()) {
                    params.put("executionEndDate", executionEndDate);
                }

                // 2. 执行人模糊搜索
                String assigneeName = requestParams.getStr("assigneeName");
                if (assigneeName != null && !assigneeName.isEmpty()) {
                    params.put("assigneeName", assigneeName);
                    log.info("添加执行人姓名搜索条件: {}", assigneeName);
                }

                // 3. 执行人手机号模糊搜索
                String assigneePhone = requestParams.getStr("assigneePhone");
                if (assigneePhone != null && !assigneePhone.isEmpty()) {
                    params.put("assigneePhone", assigneePhone);
                }

                // 4. 任务状态搜索
                Integer taskStatus = requestParams.getInt("taskStatus");
                if (taskStatus != null) {
                    params.put("taskStatus", taskStatus);
                }

                // 5. 任务类型过滤
                String taskCategory = requestParams.getStr("taskCategory");
                if (taskCategory != null && !taskCategory.isEmpty()) {
                    params.put("taskCategory", taskCategory);
                }

                // 6. 任务名称模糊搜索（keyWord参数）
                String keyWord = requestParams.getStr("keyWord");
                if (keyWord != null && !keyWord.isEmpty()) {
                    params.put("keyWord", keyWord);
                }

                // 7. 部门ID筛选
                Object departmentIdsParam = requestParams.get("departmentIds");
                if (departmentIdsParam != null) {
                    List<Long> departmentIds = new ArrayList<>();
                    if (departmentIdsParam instanceof List) {
                        List<?> list = (List<?>) departmentIdsParam;
                        for (Object item : list) {
                            if (item instanceof Long) {
                                departmentIds.add((Long) item);
                            } else if (item instanceof Integer) {
                                departmentIds.add(((Integer) item).longValue());
                            } else if (item instanceof String) {
                                try {
                                    departmentIds.add(Long.parseLong((String) item));
                                } catch (NumberFormatException e) {
                                    log.warn("无效的部门ID格式: {}", item);
                                }
                            }
                        }
                    } else if (departmentIdsParam instanceof String) {
                        String[] ids = ((String) departmentIdsParam).split(",");
                        for (String id : ids) {
                            try {
                                departmentIds.add(Long.parseLong(id.trim()));
                            } catch (NumberFormatException e) {
                                log.warn("无效的部门ID格式: {}", id);
                            }
                        }
                    }
                    if (!departmentIds.isEmpty()) {
                        params.put("departmentIds", departmentIds);
                    }
                }

                // 8. 项目ID筛选
                Object projectIdsParam = requestParams.get("projectIds");
                if (projectIdsParam != null) {
                    List<Long> projectIds = new ArrayList<>();
                    if (projectIdsParam instanceof List) {
                        List<?> list = (List<?>) projectIdsParam;
                        for (Object item : list) {
                            if (item instanceof Long) {
                                projectIds.add((Long) item);
                            } else if (item instanceof Integer) {
                                projectIds.add(((Integer) item).longValue());
                            } else if (item instanceof String) {
                                try {
                                    projectIds.add(Long.parseLong((String) item));
                                } catch (NumberFormatException e) {
                                    log.warn("无效的项目ID格式: {}", item);
                                }
                            }
                        }
                    } else if (projectIdsParam instanceof String) {
                        String[] ids = ((String) projectIdsParam).split(",");
                        for (String id : ids) {
                            try {
                                projectIds.add(Long.parseLong(id.trim()));
                            } catch (NumberFormatException e) {
                                log.warn("无效的项目ID格式: {}", id);
                            }
                        }
                    }
                    if (!projectIds.isEmpty()) {
                        params.put("projectIds", projectIds);
                    }
                }

                log.info("处理高级搜索参数完成，参数数量: {}, 参数详情: {}", params.size(), params);
            }

            // 应用部门权限过滤（如果需要）
            if (!isAdmin && departmentPermissionService != null && currentUserId != null) {
                try {
                    // 获取用户有权限的部门ID列表
                    Long[] userDepartmentIds = departmentPermissionService.getUserDepartmentIds(currentUserId);
                    if (userDepartmentIds != null && userDepartmentIds.length > 0) {
                        params.put("departmentIds", Arrays.asList(userDepartmentIds));
                    }
                } catch (Exception e) {
                    log.warn("应用部门权限过滤失败，跳过权限过滤", e);
                }
            }

            // 计算分页参数
            int pageNum = (int) page.getPageNumber();
            int pageSize = (int) page.getPageSize();
            int offset = (pageNum - 1) * pageSize;
            params.put("offset", offset);
            params.put("limit", pageSize);

            // 使用关联查询获取包含执行人信息的完整数据
            List<TaskInfoEntity> records = mapper.selectTaskInfoWithExecutions(params);
            int total = mapper.countTaskInfoWithDetails(params);

            // 进一步丰富执行人信息（处理多个执行人的情况）
            List<TaskInfoEntity> enrichedTasks = enrichTasksWithFullExecutions(records);

            // 构造分页结果
            Page<TaskInfoEntity> resultPage = new Page<>();
            resultPage.setRecords(enrichedTasks);
            resultPage.setTotalRow(total);
            resultPage.setPageNumber(pageNum);
            resultPage.setPageSize(pageSize);

            log.info("查询任务信息列表成功 - 总记录数: {}, 当前页: {}, 页大小: {}, 已使用MyBatis关联查询", total, pageNum, pageSize);
            return resultPage;

        } catch (Exception e) {
            log.error("MyBatis关联查询失败，回退到原始方法", e);
            // 出错时回退到原始方法
            Page<TaskInfoEntity> result = (Page<TaskInfoEntity>) super.page(requestParams, page, queryWrapper);

            // 为分页结果中的每个任务关联执行人信息
            List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(result.getRecords());
            result.setRecords(enrichedTasks);

            return result;
        }
    }

    /**
     * 为任务列表关联执行人信息（用于非MyBatis关联查询的情况）
     */
    private List<TaskInfoEntity> enrichTasksWithExecutions(List<TaskInfoEntity> tasks) {
        // 为每个任务关联执行人信息
        for (TaskInfoEntity task : tasks) {
            List<TaskExecutionEntity> executions = taskExecutionService.getByTaskId(task.getId());

            // 设置执行记录列表 - 同时设置两个字段确保兼容性
            task.setExecutions(executions);
            task.setExecutionEntitys(executions);

            if (!executions.isEmpty()) {
                // 构建所有执行人的显示名称
                List<String> assigneeNames = executions.stream()
                        .map(TaskExecutionEntity::getAssigneeName)
                        .collect(Collectors.toList());

                // 设置主要执行人信息（第一个）
                TaskExecutionEntity primaryExecution = executions.get(0);
                task.setAssigneeId(primaryExecution.getAssigneeId());

                // 如果有多个执行人，显示为 "主要执行人 + N人"
                if (assigneeNames.size() == 1) {
                    task.setAssigneeName(assigneeNames.get(0));
                } else {
                    task.setAssigneeName(assigneeNames.get(0) + " +" + (assigneeNames.size() - 1) + "人");
                }
            }
        }

        return tasks;
    }

    /**
     * 为MyBatis关联查询返回的任务列表进一步丰富执行人信息
     */
    private List<TaskInfoEntity> enrichTasksWithFullExecutions(List<TaskInfoEntity> tasks) {
        // 为每个任务进一步丰富执行人信息
        for (TaskInfoEntity task : tasks) {
            // 获取完整的执行记录列表
            List<TaskExecutionEntity> executions = taskExecutionService.getByTaskId(task.getId());

            // 设置执行记录列表 - 同时设置两个字段确保兼容性
            task.setExecutions(executions);
            task.setExecutionEntitys(executions);

            // 如果MyBatis查询已经设置了assigneeName但没有设置assigneeId，或者需要重新处理多执行人显示
            if (!executions.isEmpty()) {
                // 如果MyBatis查询没有正确设置assigneeId，设置主要执行人ID
                if (task.getAssigneeId() == null) {
                    TaskExecutionEntity primaryExecution = executions.get(0);
                    task.setAssigneeId(primaryExecution.getAssigneeId());
                }

                // 处理执行人名称显示（MyBatis查询可能已经用GROUP_CONCAT处理了，但我们需要确保格式正确）
                if (executions.size() > 1) {
                    // 如果有多个执行人，确保显示格式为 "主要执行人 + N人"
                    String primaryName = executions.get(0).getAssigneeName();
                    if (primaryName != null && !primaryName.isEmpty()) {
                        task.setAssigneeName(primaryName + " +" + (executions.size() - 1) + "人");
                    }
                } else if (executions.size() == 1) {
                    // 单个执行人，直接设置名称
                    String assigneeName = executions.get(0).getAssigneeName();
                    if (assigneeName != null && !assigneeName.isEmpty()) {
                        task.setAssigneeName(assigneeName);
                    }
                }
            }
        }

        return tasks;
    }

    @Override
    public boolean update(JSONObject requestParams, TaskInfoEntity entity) {
        updateById(entity);
        ScheduleUtils.deleteScheduleJob(scheduler, entity.getId().toString());
        if (entity.getScheduleStatus() == 1) {
            start(entity.getId(), entity.getScheduleType());
        } else {
            stop(entity.getId());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(JSONObject requestParams, Long... ids) {
        // 删除前收集需要更新统计信息的任务包ID
        Set<Long> packageIds = new HashSet<>();
        for (Long id : ids) {
            TaskInfoEntity task = getById(id);
            if (task != null && task.getPackageId() != null) {
                packageIds.add(task.getPackageId());
            }
        }

        // 删除调度任务
        Convert.toList(String.class, ids).forEach(jobId -> {
            ScheduleUtils.deleteScheduleJob(scheduler, jobId);
        });

        // 执行删除操作
        boolean result = super.delete(requestParams, ids);

        // 删除成功后更新相关任务包的统计信息
        if (result && !packageIds.isEmpty()) {
            for (Long packageId : packageIds) {
                try {
                    taskPackageService.updatePackageStats(packageId);
                    log.info("删除任务后更新任务包统计信息，packageId: {}", packageId);
                } catch (Exception e) {
                    log.error("删除任务后更新任务包统计信息失败，packageId: {}", packageId, e);
                }
            }
        }

        return result;
    }

    @Override
    public List<TaskExecutionEntity> getTaskExecutions(Long taskId) {
        return taskExecutionService.getByTaskId(taskId);
    }

    @Override
    public Page<TaskInfoEntity> getPersonalTasksWithExecution(Long assigneeId, int page, int size,
            String businessStatus, String startDate, String endDate) {
        try {
            // 第一步：获取当前用户的所有执行记录
            QueryWrapper executionQuery = QueryWrapper.create()
                    .eq("assignee_id", assigneeId);

            // 根据业务状态筛选
            if (businessStatus != null && !businessStatus.isEmpty()) {
                if ("pending".equals(businessStatus)) {
                    executionQuery.in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(),
                            TaskExecutionStatusEnum.ACCEPTED.getCode());
                } else if ("inProgress".equals(businessStatus)) {
                    executionQuery.eq("execution_status", TaskExecutionStatusEnum.IN_PROGRESS.getCode());
                } else if ("completed".equals(businessStatus)) {
                    executionQuery.eq("execution_status", TaskExecutionStatusEnum.COMPLETED.getCode());
                }
            }

            // 根据时间范围筛选
            if (startDate != null && !startDate.isEmpty()) {
                executionQuery.ge("create_time", startDate);
            }
            if (endDate != null && !endDate.isEmpty()) {
                executionQuery.le("create_time", endDate);
            }

            List<TaskExecutionEntity> userExecutions = taskExecutionService.list(executionQuery);

            if (userExecutions.isEmpty()) {
                return new com.mybatisflex.core.paginate.Page<>(page, size, 0);
            }

            // 提取任务ID
            List<Long> taskIds = userExecutions.stream()
                    .map(TaskExecutionEntity::getTaskId)
                    .distinct()
                    .collect(Collectors.toList());

            if (taskIds.isEmpty()) {
                return new com.mybatisflex.core.paginate.Page<>(page, size, 0);
            }

            // 第二步：查询这些任务的详细信息
            com.mybatisflex.core.query.QueryWrapper taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                    .in("id", taskIds);

            taskQuery.orderBy("create_time", false);

            // 执行分页查询
            com.mybatisflex.core.paginate.Page<TaskInfoEntity> result = mapper.paginate(
                    new com.mybatisflex.core.paginate.Page<>(page, size), taskQuery);

            // 第三步：为每个任务填充执行记录信息
            if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(result.getRecords());
                result.setRecords(enrichedTasks);
            }

            return result;
        } catch (Exception e) {
            System.err.println("获取个人任务失败: " + e.getMessage());
            e.printStackTrace();
            return new com.mybatisflex.core.paginate.Page<>(page, size, 0);
        }
    }

    @Override
    public List<TaskInfoEntity> listByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        QueryWrapper queryWrapper = QueryWrapper.create().in("id", ids);
        return super.list(queryWrapper);
    }

    @Transactional
    @Override
    public boolean startTask(Long taskId, Long assigneeId) {
        try {
            // 更新任务执行记录状态为IN_PROGRESS
            // 更新任状态也为IN_PROGRESS
            taskInfoService.start(taskId, null);
            return taskExecutionService.startTask(taskId, assigneeId);
        } catch (Exception e) {
            System.err.println("开始任务失败: " + e.getMessage());
            return false;
        }
    }

    @Transactional
    @Override
    public boolean batchUpdateTaskTime(List<Long> taskIds, String startTime, String endTime) {
        try {
            if (taskIds == null || taskIds.isEmpty()) {
                return false;
            }

            // 批量更新任务时间
            for (Long taskId : taskIds) {
                TaskInfoEntity task = getById(taskId);
                if (task != null) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    if (startTime != null && !startTime.isEmpty()) {
                        try {
                            task.setStartTime(dateFormat.parse(startTime));
                        } catch (Exception e) {
                            log.warn("解析开始时间失败: {}", startTime);
                        }
                    }
                    if (endTime != null && !endTime.isEmpty()) {
                        try {
                            task.setEndTime(dateFormat.parse(endTime));
                        } catch (Exception e) {
                            log.warn("解析结束时间失败: {}", endTime);
                        }
                    }
                    task.setUpdateTime(new java.util.Date());
                    updateById(task);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("批量更新任务时间失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
