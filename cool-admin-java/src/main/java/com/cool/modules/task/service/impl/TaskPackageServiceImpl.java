package com.cool.modules.task.service.impl;

import cn.hutool.json.JSONObject;
import com.cool.core.base.BaseServiceImpl;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.mapper.TaskPackageMapper;
import com.cool.modules.task.mapper.TaskInfoMapper;
import com.cool.modules.task.service.TaskPackageService;
import com.cool.modules.task.service.TaskDepartmentPermissionService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 场景任务包Service实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskPackageServiceImpl extends BaseServiceImpl<TaskPackageMapper, TaskPackageEntity> implements TaskPackageService {

    private final TaskInfoMapper taskInfoMapper;
    private final TaskDepartmentPermissionService departmentPermissionService;
    private final BaseSysUserService baseSysUserService;

    /**
     * 重写page方法，使用MyBatis关联查询返回包含部门名称的数据
     */
    @Override
    public Object page(JSONObject requestParams, Page<TaskPackageEntity> page, QueryWrapper queryWrapper) {
        try {
            JSONObject adminUserInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
            Long currentUserId = adminUserInfo.getLong("userId");
            boolean isAdmin = "admin".equals(adminUserInfo.getStr("username"));

            // 转换参数格式
            Map<String, Object> params = new HashMap<>();
            if (requestParams != null) {
                params.putAll(requestParams);
            }
            
            // 应用部门权限过滤（如果需要）
            if (!isAdmin && departmentPermissionService != null && currentUserId != null) {
                try {
                    // 获取用户有权限的部门ID列表
                    Long[] userDepartmentIds = departmentPermissionService.getUserDepartmentIds(currentUserId);
                    if (userDepartmentIds != null && userDepartmentIds.length > 0) {
                        params.put("departmentIds", Arrays.asList(userDepartmentIds));
                    }
                } catch (Exception e) {
                    log.warn("应用部门权限过滤失败，跳过权限过滤", e);
                }
            }
            
            // 处理部门和项目筛选参数
            if (requestParams.containsKey("departmentIds")) {
                Object deptIds = requestParams.get("departmentIds");
                if (deptIds instanceof List) {
                    params.put("departmentIds", deptIds);
                } else if (deptIds instanceof String) {
                    String[] deptIdArray = ((String) deptIds).split(",");
                    params.put("departmentIds", Arrays.stream(deptIdArray)
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(Collectors.toList()));
                }
            }
            
            if (requestParams.containsKey("projectIds")) {
                Object projIds = requestParams.get("projectIds");
                if (projIds instanceof List) {
                    params.put("projectIds", projIds);
                } else if (projIds instanceof String) {
                    String[] projIdArray = ((String) projIds).split(",");
                    params.put("projectIds", Arrays.stream(projIdArray)
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(Collectors.toList()));
                }
            }
            
            // 计算分页参数
            int pageNum = (int) page.getPageNumber();
            int pageSize = (int) page.getPageSize();
            int offset = (pageNum - 1) * pageSize;
            params.put("offset", offset);
            params.put("limit", pageSize);
            
            // 使用自定义SQL查询
            List<TaskPackageEntity> records = mapper.selectPackagesWithDetails(params);
            int total = mapper.countPackagesWithDetails(params);
            
            // 构造分页结果
            Page<TaskPackageEntity> resultPage = new Page<>();
            resultPage.setRecords(records);
            resultPage.setTotalRow(total);
            resultPage.setPageNumber(pageNum);
            resultPage.setPageSize(pageSize);
            
            log.info("查询任务包列表成功 - 总记录数: {}, 当前页: {}, 页大小: {}", total, pageNum, pageSize);
            return resultPage;
            
        } catch (Exception e) {
            log.error("查询任务包列表失败", e);
            // 出错时回退到基类方法
            return super.page(requestParams, page, queryWrapper);
        }
    }

    @Override
    @Transactional
    public TaskPackageEntity createTaskPackage(TaskPackageEntity taskPackage) {
        // 设置部门信息
        setupDepartmentInfo(taskPackage);
        // 保存任务包
        save(taskPackage);
        return taskPackage;
    }

    @Override
    @Transactional
    public void updatePackageStats(Long packageId) {
        try {
            log.info("开始更新任务包统计信息，包ID: {}", packageId);

            // 查询任务包下的所有任务
            QueryWrapper taskQuery = QueryWrapper.create()
                    .eq("package_id", packageId)
                    .eq("is_deleted", 0);

            List<TaskInfoEntity> tasks = taskInfoMapper.selectListByQuery(taskQuery);

            // 计算统计信息
            int totalTasks = tasks.size();
            int completedTasks = 0;
            int inProgressTasks = 0;
            int pendingTasks = 0;

            for (TaskInfoEntity task : tasks) {
                Integer status = task.getTaskStatus();
                if (status == null) {
                    pendingTasks++; // 默认为待分配
                } else {
                    switch (status) {
                        case 0: // 待分配
                            pendingTasks++;
                            break;
                        case 1: // 待执行（已分配执行人）
                            inProgressTasks++; // 已分配执行人的任务应该算作进行中
                            break;
                        case 2: // 执行中
                            inProgressTasks++;
                            break;
                        case 3: // 已完成
                            completedTasks++;
                            break;
                        case 4: // 已关闭
                            completedTasks++; // 关闭的任务也算完成
                            break;
                        default:
                            pendingTasks++;
                            break;
                    }
                }
            }

            // 计算完成率
            int completionRate = totalTasks > 0 ? (int) Math.round((double) completedTasks / totalTasks * 100) : 0;

            // 更新任务包统计信息
            getMapper().updatePackageStats(packageId, totalTasks, completedTasks, inProgressTasks, pendingTasks, completionRate);

            log.info("任务包统计信息更新完成 - 包ID: {}, 总任务: {}, 已完成: {}, 执行中: {}, 待分配: {}, 完成率: {}%",
                    packageId, totalTasks, completedTasks, inProgressTasks, pendingTasks, completionRate);

        } catch (Exception e) {
            log.error("更新任务包统计信息失败，包ID: {}", packageId, e);
            throw new RuntimeException("更新任务包统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Page<TaskPackageEntity> getScenarioPackages(Page<TaskPackageEntity> page, Map<String, Object> params) {
        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            
            // 应用部门权限过滤
            Map<String, Object> queryParams = new HashMap<>(params);
            // TODO: 实现部门权限过滤逻辑，暂时跳过
            // departmentPermissionService.applyTaskPackageDepartmentFilter(queryParams, currentUserId);
            
            // 处理部门和项目筛选参数
            if (params.containsKey("departmentIds")) {
                Object deptIds = params.get("departmentIds");
                if (deptIds instanceof List) {
                    queryParams.put("departmentIds", deptIds);
                } else if (deptIds instanceof String) {
                    String[] deptIdArray = ((String) deptIds).split(",");
                    queryParams.put("departmentIds", Arrays.stream(deptIdArray)
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(Collectors.toList()));
                }
            }
            
            if (params.containsKey("projectIds")) {
                Object projIds = params.get("projectIds");
                if (projIds instanceof List) {
                    queryParams.put("projectIds", projIds);
                } else if (projIds instanceof String) {
                    String[] projIdArray = ((String) projIds).split(",");
                    queryParams.put("projectIds", Arrays.stream(projIdArray)
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(Collectors.toList()));
                }
            }
            
            // 计算分页参数
            int pageNum = (int) page.getPageNumber();
            int pageSize = (int) page.getPageSize();
            int offset = (pageNum - 1) * pageSize;
            queryParams.put("offset", offset);
            queryParams.put("limit", pageSize);
            
            // 使用自定义SQL查询
            List<TaskPackageEntity> records = mapper.selectPackagesWithDetails(queryParams);
            int total = mapper.countPackagesWithDetails(queryParams);
            
            Page<TaskPackageEntity> resultPage = new Page<>();
            resultPage.setRecords(records);
            resultPage.setTotalRow(total);
            resultPage.setPageNumber(pageNum);
            resultPage.setPageSize(pageSize);
            
            return resultPage;
            
        } catch (Exception e) {
            log.error("查询场景任务包失败", e);
            return new Page<>();
        }
    }

    @Override
    public TaskPackageEntity getPackageDetailWithStats(Long packageId) {
        try {
            // 使用自定义SQL查询，直接获取关联的部门信息
            TaskPackageEntity result = mapper.selectPackageWithDepartmentById(packageId);
            if (result == null) {
                return new TaskPackageEntity();
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取任务包详情失败，包ID: {}", packageId, e);
            return new TaskPackageEntity();
        }
    }

    @Override
    public List<TaskPackageEntity> getPackagesByScenarioId(Long scenarioId) {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("scenario_id", scenarioId)
                .eq("is_deleted", 0)
                .orderBy("create_time", false);
                
            return mapper.selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据场景ID获取任务包列表失败，场景ID: {}", scenarioId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean deletePackage(Long packageId) {
        try {
            // 软删除
            return updateById(new TaskPackageEntity() {{
                setId(packageId);
                setIsDeleted(1);
            }});
        } catch (Exception e) {
            log.error("删除任务包失败，包ID: {}", packageId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean completePackage(Long packageId) {
        try {
            boolean result = updateById(new TaskPackageEntity() {{
                setId(packageId);
                setPackageStatus(2); // 已完成
                setActualEndTime(LocalDateTime.now());
            }});
            
            // 完成任务包后更新统计信息
            if (result) {
                try {
                    updatePackageStats(packageId);
                    log.info("任务包完成后统计信息更新成功，包ID: {}", packageId);
                } catch (Exception e) {
                    log.error("任务包完成后更新统计信息失败，包ID: {}", packageId, e);
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("完成任务包失败，包ID: {}", packageId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean closePackage(Long packageId) {
        try {
            boolean result = updateById(new TaskPackageEntity() {{
                setId(packageId);
                setPackageStatus(3); // 已关闭
                setActualEndTime(LocalDateTime.now());
            }});
            
            // 关闭任务包后更新统计信息
            if (result) {
                try {
                    updatePackageStats(packageId);
                    log.info("任务包关闭后统计信息更新成功，包ID: {}", packageId);
                } catch (Exception e) {
                    log.error("任务包关闭后更新统计信息失败，包ID: {}", packageId, e);
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("关闭任务包失败，包ID: {}", packageId, e);
            return false;
        }
    }



    /**
     * 设置任务包的部门信息
     */
    private void setupDepartmentInfo(TaskPackageEntity taskPackage) {
        try {
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            if (currentUserId == null) {
                log.warn("当前用户ID为空，无法设置部门信息");
                return;
            }
            
            BaseSysUserEntity currentUser = baseSysUserService.getById(currentUserId);
            if (currentUser == null) {
                log.warn("无法获取当前用户信息: userId={}", currentUserId);
                return;
            }
            
            // 如果未指定部门，使用创建者的部门
            if (taskPackage.getDepartmentId() == null) {
                taskPackage.setDepartmentId(currentUser.getDepartmentId());
            }
            
            // 设置创建者部门
            taskPackage.setCreatorDepartmentId(currentUser.getDepartmentId());
            
            // 验证用户是否有权限在指定部门创建任务
            if (!departmentPermissionService.canCreateTaskInDepartment(currentUserId, taskPackage.getDepartmentId())) {
                log.warn("用户无权限在指定部门创建任务: userId={}, departmentId={}", 
                        currentUserId, taskPackage.getDepartmentId());
                // 强制使用用户自己的部门
                taskPackage.setDepartmentId(currentUser.getDepartmentId());
            }
            
        } catch (Exception e) {
            log.error("设置任务包部门信息失败", e);
        }
    }
}
