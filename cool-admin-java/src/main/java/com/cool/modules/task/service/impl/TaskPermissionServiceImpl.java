package com.cool.modules.task.service.impl;

import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.enums.TaskStatusPermissionEnum;
import com.cool.modules.task.service.TaskPermissionService;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.base.service.sys.BaseSysPermsService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 任务权限服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskPermissionServiceImpl implements TaskPermissionService {

    private final TaskExecutionService taskExecutionService;
    private final TaskInfoService taskInfoService;
    private final BaseSysPermsService baseSysPermsService;

    @Override
    public Boolean canCompleteTaskExecution(Long taskId, Long assigneeId, Long currentUserId) {
        // 1. 检查是否有完成任务的权限
        if (!hasStatusChangePermission(currentUserId, TaskStatusPermissionEnum.COMPLETE)||!isTaskOwner(taskId,currentUserId)) {
            log.warn("用户{}无权限完成任务，taskId: {}", currentUserId, taskId);
            return false;
        }

        // 2. 检查是否是本人的任务或具有分配权限
        if (!assigneeId.equals(currentUserId)) {
            if (!hasStatusChangePermission(currentUserId, TaskStatusPermissionEnum.ASSIGN)) {
                log.warn("用户{}无权限完成其他人的任务，taskId: {}, assigneeId: {}", 
                    currentUserId, taskId, assigneeId);
                return false;
            }
        }

        // 3. 检查执行记录是否存在且状态正确
        TaskExecutionEntity execution = taskExecutionService.getOne(QueryWrapper.create()
                .eq("task_id", taskId)
                .eq("assignee_id", assigneeId)
                .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(), TaskExecutionStatusEnum.IN_PROGRESS.getCode()));

        if (execution == null) {
            log.warn("未找到可完成的执行记录，taskId: {}, assigneeId: {}", taskId, assigneeId);
            return false;
        }

        // 4. 检查任务状态
        TaskInfoEntity task = taskInfoService.getById(taskId);
        if (task == null || task.getTaskStatus().equals(TaskBusinessStatusEnum.CLOSED.getCode())) {
            log.warn("任务不存在或已关闭，无法完成，taskId: {}", taskId);
            return false;
        }

        return true;
    }

    @Override
    public Boolean canForceCompleteTask(Long taskId, Long operatorId) {
        // 检查是否有强制完成任务的权限
        return hasStatusChangePermission(operatorId, TaskStatusPermissionEnum.FORCE_COMPLETE);
    }

    @Override
    public Boolean canCloseTask(Long taskId, Long operatorId) {
        // 检查任务状态
        TaskInfoEntity task = taskInfoService.getById(taskId);
        if (task == null) {
            return false;
        }

        // 只有已完成的任务才能关闭
        if (!TaskBusinessStatusEnum.COMPLETED.getCode().equals(task.getTaskStatus())) {
            log.warn("任务状态不是已完成，无法关闭，taskId: {}, status: {}", taskId, task.getTaskStatus());
            return false;
        }

        // 检查是否有关闭任务的权限
        return hasStatusChangePermission(operatorId, TaskStatusPermissionEnum.CLOSE);
    }

    @Override
    public Boolean canReopenTask(Long taskId, Long operatorId) {
        // 检查任务状态
        TaskInfoEntity task = taskInfoService.getById(taskId);
        if (task == null) {
            return false;
        }

        // 只有已关闭的任务才能重新开启
        if (!TaskBusinessStatusEnum.CLOSED.getCode().equals(task.getTaskStatus())) {
            log.warn("任务状态不是已关闭，无法重新开启，taskId: {}, status: {}", taskId, task.getTaskStatus());
            return false;
        }

        // 检查是否有重新开启任务的权限
        return hasStatusChangePermission(operatorId, TaskStatusPermissionEnum.REOPEN);
    }

    @Override
    public Boolean isAdmin(Long userId) {
        try {
            // 检查是否是超级管理员（admin用户）
            String currentUsername = CoolSecurityUtil.getAdminUsername();
            if ("admin".equals(currentUsername)) {
                return true;
            }

            // 这里应该根据实际的用户角色系统来判断
            // 暂时简单实现：用户ID为1的是管理员
            return userId != null && userId.equals(1L);
        } catch (Exception e) {
            // 如果获取用户名失败，回退到用户ID判断
            return userId != null && userId.equals(1L);
        }
    }

    @Override
    public Boolean isTaskOwner(Long taskId, Long userId) {
        // 检查用户是否是任务的主要负责人
        TaskExecutionEntity execution = taskExecutionService.getOne(QueryWrapper.create()
                .eq("task_id", taskId)
                .eq("assignee_id", userId)
                .orderBy("id", true) // 第一个分配的是主要负责人
                .limit(1));

        return execution != null;
    }

    @Override
    public Boolean hasTaskStatusPermission(Long userId, String permission) {
        if (userId == null || permission == null) {
            return false;
        }
        
        // 管理员拥有所有权限
        if (isAdmin(userId)) {
            return true;
        }
        
        TaskStatusPermissionEnum permissionEnum = TaskStatusPermissionEnum.fromPermission(permission);
        if (permissionEnum == null) {
            return false;
        }
        
        // 使用BaseSysPermsService封装的方法检查权限
        return baseSysPermsService.hasPermission(userId, permission);
    }

    /**
     * 检查用户是否具有指定的状态变更权限
     * @param userId 用户ID
     * @param permission 权限枚举
     * @return 是否有权限
     */
    private Boolean hasStatusChangePermission(Long userId, TaskStatusPermissionEnum permission) {
        if (userId == null || permission == null) {
            return false;
        }
        
        // 管理员拥有所有权限
        if (isAdmin(userId)) {
            return true;
        }
        
        // 使用BaseSysPermsService封装的方法检查权限
        return baseSysPermsService.hasPermission(userId, permission.getPermission());
    }
}