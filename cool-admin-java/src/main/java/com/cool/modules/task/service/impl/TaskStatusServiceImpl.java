package com.cool.modules.task.service.impl;

import com.cool.core.exception.CoolException;
import com.cool.modules.task.dto.StatusChangeContext;
import com.cool.modules.task.dto.StatusChangeRequest;
import com.cool.modules.task.dto.StatusChangeResult;
import com.cool.modules.task.dto.TaskCompletionRequest;
import com.cool.modules.task.dto.TaskCloseRequest;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.enums.TaskStatusPermissionEnum;
import com.cool.modules.task.enums.TaskStatusTransitionEnum;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskStatusService;
import com.cool.modules.task.service.TaskHistoryService;
import com.cool.modules.task.service.TaskPermissionService;
import com.cool.modules.task.service.TaskPackageService;
import com.cool.modules.task.utils.ScheduleUtils;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 任务状态管理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskStatusServiceImpl implements TaskStatusService {

    private final TaskExecutionService taskExecutionService;
    private final TaskInfoService taskInfoService;
    private final TaskHistoryService taskHistoryService;
    private final TaskPermissionService taskPermissionService;
    private final TaskPackageService taskPackageService;
    private final Scheduler scheduler;

    // 状态转换规则定义
    private static final Map<Integer, Set<Integer>> ALLOWED_TRANSITIONS = new HashMap<>();

    static {
        ALLOWED_TRANSITIONS.put(0, Set.of(1, 4)); // 待分配 -> 待执行, 已关闭
        ALLOWED_TRANSITIONS.put(1, Set.of(2, 4)); // 待执行 -> 执行中, 已关闭
        ALLOWED_TRANSITIONS.put(2, Set.of(3, 4)); // 执行中 -> 已完成, 已关闭
        ALLOWED_TRANSITIONS.put(3, Set.of(2));    // 已完成 -> 执行中
        ALLOWED_TRANSITIONS.put(4, Set.of(1));    // 已关闭 -> 待执行
    }

    @Override
    @Transactional
    public Boolean completeTaskExecution(TaskCompletionRequest request) {
        try {
            // 1. 检查权限（这里需要传入当前用户ID，暂时使用assigneeId）
            if (!taskPermissionService.canCompleteTaskExecution(request.getTaskId(), request.getAssigneeId(), request.getAssigneeId())) {
                log.warn("用户无权限完成任务，taskId: {}, assigneeId: {}",
                    request.getTaskId(), request.getAssigneeId());
                return false;
            }

            // 2. 更新执行记录
            TaskExecutionEntity execution = taskExecutionService.getOne(QueryWrapper.create()
                    .eq("task_id", request.getTaskId())
                    .eq("assignee_id", request.getAssigneeId())
                    .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(), TaskExecutionStatusEnum.IN_PROGRESS.getCode()));

            if (execution == null) {
                log.warn("未找到可完成的执行记录，taskId: {}, assigneeId: {}", 
                    request.getTaskId(), request.getAssigneeId());
                return false;
            }

            execution.setExecutionStatusEnum(TaskExecutionStatusEnum.COMPLETED);
            execution.setCompletionTime(new Date());
            execution.setCompletionNote(request.getCompletionNote());
            
            // 处理附件和照片
            if (!CollectionUtils.isEmpty(request.getAttachments())) {
                execution.setAttachments(String.join(",", request.getAttachments()));
            }
            if (!CollectionUtils.isEmpty(request.getPhotos())) {
                execution.setPhotos(String.join(",", request.getPhotos()));
            }

            taskExecutionService.updateById(execution);

            // 3. 检查是否所有执行人都完成，如果是则更新任务状态
            if (areAllExecutionsCompleted(request.getTaskId())) {
                TaskInfoEntity task = taskInfoService.getById(request.getTaskId());
                Integer oldStatus = task.getTaskStatus();
                updateTaskStatus(request.getTaskId(), TaskBusinessStatusEnum.COMPLETED.getCode());

                // 记录任务状态变更历史
                taskHistoryService.recordTaskHistory(
                    request.getTaskId(),
                    "COMPLETE",
                    oldStatus,
                    TaskBusinessStatusEnum.COMPLETED.getCode(),
                    request.getAssigneeId(),
                    execution.getAssigneeName(),
                    "所有执行人完成任务，任务状态自动更新为已完成"
                );
                
                // 更新任务包统计信息
                if (task.getPackageId() != null) {
                    taskPackageService.updatePackageStats(task.getPackageId());
                }
            }

            // 记录执行人完成操作
            taskHistoryService.recordTaskAction(
                request.getTaskId(),
                "EXECUTION_COMPLETE",
                request.getAssigneeId(),
                execution.getAssigneeName(),
                "执行人完成任务：" + (request.getCompletionNote() != null ? request.getCompletionNote() : "")
            );

            log.info("任务执行完成成功，taskId: {}, assigneeId: {}", 
                request.getTaskId(), request.getAssigneeId());
            return true;

        } catch (Exception e) {
            log.error("完成任务执行失败，taskId: {}, assigneeId: {}", 
                request.getTaskId(), request.getAssigneeId(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean forceCompleteTask(Long taskId, String reason, Long operatorId) {
        try {
            // 检查权限
            if (!taskPermissionService.canForceCompleteTask(taskId, operatorId)) {
                log.warn("用户无权限强制完成任务，taskId: {}, operatorId: {}", taskId, operatorId);
                return false;
            }

            // 更新任务状态为已完成
            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return false;
            }

            task.setTaskStatus(TaskBusinessStatusEnum.COMPLETED.getCode());
            task.setCompletionTime(new Date());
            taskInfoService.updateById(task);

            // 将所有未完成的执行记录标记为完成
            List<TaskExecutionEntity> executions = taskExecutionService.list(QueryWrapper.create()
                    .eq("task_id", taskId)
                    .notIn("execution_status", TaskExecutionStatusEnum.COMPLETED.getCode(), TaskExecutionStatusEnum.CANCELLED.getCode()));

            for (TaskExecutionEntity execution : executions) {
                execution.setExecutionStatusEnum(TaskExecutionStatusEnum.COMPLETED);
                execution.setCompletionTime(new Date());
                execution.setCompletionNote("管理员强制完成：" + reason);
                taskExecutionService.updateById(execution);
            }

            // 记录强制完成历史
            taskHistoryService.recordTaskHistory(
                taskId,
                "FORCE_COMPLETE",
                task.getTaskStatus(),
                TaskBusinessStatusEnum.COMPLETED.getCode(),
                operatorId,
                "管理员",
                "强制完成任务：" + reason
            );
            
            // 更新任务包统计信息
            if (task.getPackageId() != null) {
                taskPackageService.updatePackageStats(task.getPackageId());
            }

            log.info("强制完成任务成功，taskId: {}, operatorId: {}, reason: {}",
                taskId, operatorId, reason);
            return true;

        } catch (Exception e) {
            log.error("强制完成任务失败，taskId: {}, operatorId: {}", taskId, operatorId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean closeTask(TaskCloseRequest request) {
        try {
            // 检查权限
            if (!taskPermissionService.canCloseTask(request.getTaskId(), request.getOperatorId())) {
                log.warn("用户无权限关闭任务，taskId: {}, operatorId: {}",
                    request.getTaskId(), request.getOperatorId());
                return false;
            }

            TaskInfoEntity task = taskInfoService.getById(request.getTaskId());
            if (task == null) {
                return false;
            }

            // 更新任务状态为已关闭
            task.setTaskStatus(TaskBusinessStatusEnum.CLOSED.getCode());
            task.setCloseReason(request.getCloseReason());
            task.setClosedBy(request.getOperatorName());
            task.setCloseTime(new Date());

            // 停止任务调度
            task.setScheduleStatus(0);

            // 停止Quartz调度器中的任务
            try {
                ScheduleUtils.pauseJob(scheduler, request.getTaskId() + "");
                log.info("已停止任务调度，taskId: {}", request.getTaskId());
            } catch (Exception e) {
                log.warn("停止任务调度失败，taskId: {}, error: {}", request.getTaskId(), e.getMessage());
            }

            taskInfoService.updateById(task);

            // 记录关闭任务历史
            taskHistoryService.recordTaskHistory(
                request.getTaskId(),
                "CLOSE",
                TaskBusinessStatusEnum.COMPLETED.getCode(),
                TaskBusinessStatusEnum.CLOSED.getCode(),
                request.getOperatorId(),
                request.getOperatorName(),
                "关闭任务：" + request.getCloseReason()
            );
            
            // 更新任务包统计信息
            if (task.getPackageId() != null) {
                taskPackageService.updatePackageStats(task.getPackageId());
            }

            log.info("关闭任务成功，taskId: {}, operatorId: {}, reason: {}",
                request.getTaskId(), request.getOperatorId(), request.getCloseReason());
            return true;

        } catch (Exception e) {
            log.error("关闭任务失败，taskId: {}, operatorId: {}", 
                request.getTaskId(), request.getOperatorId(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean reopenTask(Long taskId, String reason, Long operatorId) {
        try {
            // 检查权限
            if (!taskPermissionService.canReopenTask(taskId, operatorId)) {
                log.warn("用户无权限重新开启任务，taskId: {}, operatorId: {}", taskId, operatorId);
                return false;
            }

            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return false;
            }

            // 重新开启任务，状态设为执行中
            task.setTaskStatus(TaskBusinessStatusEnum.EXECUTING.getCode());
            task.setCloseReason(null);
            task.setClosedBy(null);
            task.setCloseTime(null);
            task.setCompletionTime(null);

            taskInfoService.updateById(task);

            // 记录重新开启任务历史
            taskHistoryService.recordTaskHistory(
                taskId,
                "REOPEN",
                TaskBusinessStatusEnum.CLOSED.getCode(),
                TaskBusinessStatusEnum.EXECUTING.getCode(),
                operatorId,
                "管理员",
                "重新开启任务：" + reason
            );
            
            // 更新任务包统计信息
            if (task.getPackageId() != null) {
                taskPackageService.updatePackageStats(task.getPackageId());
            }

            log.info("重新开启任务成功，taskId: {}, operatorId: {}, reason: {}",
                taskId, operatorId, reason);
            return true;

        } catch (Exception e) {
            log.error("重新开启任务失败，taskId: {}, operatorId: {}", taskId, operatorId, e);
            return false;
        }
    }

    @Override
    public Boolean canCompleteTask(Long taskId, Long assigneeId) {
        // 使用权限服务检查（这里假设当前用户就是assigneeId）
        return taskPermissionService.canCompleteTaskExecution(taskId, assigneeId, assigneeId);
    }

    @Override
    public Boolean canCloseTask(Long taskId, Long operatorId) {
        // 使用权限服务检查
        return taskPermissionService.canCloseTask(taskId, operatorId);
    }

    @Override
    public Boolean areAllExecutionsCompleted(Long taskId) {
        // 检查是否所有执行记录都已完成
        List<TaskExecutionEntity> executions = taskExecutionService.list(QueryWrapper.create()
                .eq("task_id", taskId)
                .notIn("execution_status", TaskExecutionStatusEnum.CANCELLED.getCode()));

        if (executions.isEmpty()) {
            return false;
        }

        return executions.stream()
                .allMatch(execution -> TaskExecutionStatusEnum.COMPLETED.getCode().equals(execution.getExecutionStatus()));
    }

    @Override
    @Transactional
    public Integer batchForceCompleteTask(List<Long> taskIds, String reason, Long operatorId) {
        int successCount = 0;
        for (Long taskId : taskIds) {
            try {
                if (forceCompleteTask(taskId, reason, operatorId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量强制完成任务失败，taskId: {}", taskId, e);
            }
        }
        return successCount;
    }

    @Override
    @Transactional
    public Integer batchCloseTask(List<Long> taskIds, String reason, Long operatorId) {
        int successCount = 0;
        for (Long taskId : taskIds) {
            try {
                TaskCloseRequest request = new TaskCloseRequest();
                request.setTaskId(taskId);
                request.setCloseReason(reason);
                request.setOperatorId(operatorId);
                request.setOperatorName("批量操作");

                if (closeTask(request)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量关闭任务失败，taskId: {}", taskId, e);
            }
        }
        return successCount;
    }

    @Override
    @Transactional
    public Integer batchReopenTask(List<Long> taskIds, String reason, Long operatorId) {
        int successCount = 0;
        for (Long taskId : taskIds) {
            try {
                if (reopenTask(taskId, reason, operatorId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量重新开启任务失败，taskId: {}", taskId, e);
            }
        }
        return successCount;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, Integer status) {
        TaskInfoEntity task = taskInfoService.getById(taskId);
        if (task != null) {
            task.setTaskStatus(status);
            if (TaskBusinessStatusEnum.COMPLETED.getCode().equals(status)) {
                task.setCompletionTime(new Date());
            }
            taskInfoService.updateById(task);

            // 更新任务包统计信息
            if (task.getPackageId() != null) {
                taskPackageService.updatePackageStats(task.getPackageId());
            }
        }
    }

    // ========== 核心状态机方法 ==========

    /**
     * 统一的状态变更处理方法
     * 这是状态机的核心方法，负责状态转换的校验和执行
     */
    @Transactional
    public StatusChangeResult changeStatus(StatusChangeRequest request) {
        try {
            // 1. 基础校验
            if (request.getTaskId() == null || request.getTargetStatus() == null) {
                return StatusChangeResult.failure(request.getTaskId(), "任务ID和目标状态不能为空", "INVALID_PARAMS");
            }

            TaskInfoEntity task = taskInfoService.getById(request.getTaskId());
            if (task == null) {
                return StatusChangeResult.failure(request.getTaskId(), "任务不存在", "TASK_NOT_FOUND");
            }

            Integer sourceStatus = task.getTaskStatus();
            Integer targetStatus = request.getTargetStatus();

            // 2. 状态转换有效性校验
            if (!ALLOWED_TRANSITIONS.getOrDefault(sourceStatus, Set.of()).contains(targetStatus)) {
                String errorMsg = String.format("无效的状态转换：从 %s 到 %s",
                    TaskBusinessStatusEnum.getNameByCode(sourceStatus),
                    TaskBusinessStatusEnum.getNameByCode(targetStatus));
                return StatusChangeResult.failure(request.getTaskId(), errorMsg, "INVALID_TRANSITION");
            }

            // 3. 权限和业务规则校验
            StatusChangeContext context = request.toContext();
            validateTransition(task, targetStatus, request.getOperatorId(), context);

            // 4. 执行状态变更
            executeStatusChange(task, targetStatus, context);

            // 5. 记录状态变更历史
            recordStatusChangeHistory(task.getId(), sourceStatus, targetStatus, context);

            // 6. 返回成功结果
            StatusChangeResult result = StatusChangeResult.success(
                task.getId(), sourceStatus, targetStatus,
                request.getOperatorId(), request.getOperatorName(), request.getActualReason());

            log.info("任务状态变更成功: taskId={}, {}({}) -> {}({}), operator={}",
                task.getId(),
                TaskBusinessStatusEnum.getNameByCode(sourceStatus), sourceStatus,
                TaskBusinessStatusEnum.getNameByCode(targetStatus), targetStatus,
                request.getOperatorName());

            return result;

        } catch (CoolException e) {
            log.warn("任务状态变更失败: taskId={}, error={}", request.getTaskId(), e.getMessage());
            return StatusChangeResult.failure(request.getTaskId(), e.getMessage(), "BUSINESS_ERROR");
        } catch (Exception e) {
            log.error("任务状态变更异常: taskId={}", request.getTaskId(), e);
            return StatusChangeResult.failure(request.getTaskId(), "系统异常：" + e.getMessage(), "SYSTEM_ERROR");
        }
    }

    /**
     * 统一的转换校验方法
     */
    private void validateTransition(TaskInfoEntity task, Integer targetStatus, Long operatorId, StatusChangeContext context) {
        Integer sourceStatus = task.getTaskStatus();

        // 超级管理员拥有最高权限，可以进行任意状态转换
        if (taskPermissionService.isAdmin(operatorId)) {
            log.info("超级管理员执行状态转换，跳过权限检查: taskId={}, {}({}) -> {}({}), operatorId={}",
                task.getId(),
                TaskBusinessStatusEnum.getNameByCode(sourceStatus), sourceStatus,
                TaskBusinessStatusEnum.getNameByCode(targetStatus), targetStatus,
                operatorId);
            return;
        }

        // 根据具体的转换路径执行权限和业务校验
        TaskStatusTransitionEnum transition = TaskStatusTransitionEnum.findTransition(sourceStatus, targetStatus);
        if (transition == null) {
            // 对于普通用户，不支持的状态转换直接抛出异常
            // 对于超级管理员，这个检查已经在上面跳过了
            throw new CoolException("不支持的状态转换");
        }

        switch (transition) {
            case ASSIGN_TASK:
                validateAssignTask(task, operatorId, context);
                break;
            case START_TASK:
                validateStartTask(task, operatorId, context);
                break;
            case COMPLETE_TASK:
                validateCompleteTask(task, operatorId, context);
                break;
            case CLOSE_FROM_PENDING_ASSIGN:
            case CLOSE_FROM_PENDING_EXECUTE:
            case CLOSE_FROM_EXECUTING:
                validateCloseTask(task, operatorId, context);
                break;
            case REACTIVATE_TASK:
                validateReactivateTask(task, operatorId, context);
                break;
            case REOPEN_TASK:
                validateReopenTask(task, operatorId, context);
                break;
            default:
                throw new CoolException("未实现的状态转换校验：" + transition.getDescription());
        }
    }

    /**
     * 执行状态变更
     */
    private TaskInfoEntity executeStatusChange(TaskInfoEntity task, Integer targetStatus, StatusChangeContext context) {
        Integer oldStatus = task.getTaskStatus();

        // 更新任务状态
        task.setTaskStatus(targetStatus);

        // 根据目标状态设置相关字段
        Date now = new Date();
        switch (targetStatus) {
            case 3: // 已完成
                task.setCompletionTime(now);
                break;
            case 4: // 已关闭
                task.setCloseReason(context.getReason());
                task.setClosedBy(context.getOperatorName());
                task.setCloseTime(now);
                task.setScheduleStatus(0); // 停止调度

                // 停止Quartz调度器中的任务
                try {
                    ScheduleUtils.pauseJob(scheduler, task.getId() + "");
                    log.info("已停止任务调度，taskId: {}", task.getId());
                } catch (Exception e) {
                    log.warn("停止任务调度失败，taskId: {}, error: {}", task.getId(), e.getMessage());
                }
                break;
            case 1: // 重新打开到待执行
                if (oldStatus == 4) { // 从已关闭重新打开
                    task.setCloseReason(null);
                    task.setClosedBy(null);
                    task.setCloseTime(null);
                    task.setCompletionTime(null);
                }
                break;
            case 2: // 重新激活到执行中
                if (oldStatus == 3) { // 从已完成重新激活
                    task.setCompletionTime(null);
                }
                break;
        }

        // 保存任务
        taskInfoService.updateById(task);

        // 更新任务包统计信息
        if (task.getPackageId() != null) {
            taskPackageService.updatePackageStats(task.getPackageId());
        }

        return task;
    }

    /**
     * 记录状态变更历史
     */
    private void recordStatusChangeHistory(Long taskId, Integer oldStatus, Integer newStatus, StatusChangeContext context) {
        String event = TaskStatusTransitionEnum.getTransitionEvent(oldStatus, newStatus);
        String description = TaskStatusTransitionEnum.getTransitionDescription(oldStatus, newStatus);

        taskHistoryService.recordTaskHistory(
            taskId,
            event,
            oldStatus,
            newStatus,
            context.getOperatorId(),
            context.getOperatorName(),
            description + "：" + (context.getReason() != null ? context.getReason() : "")
        );
    }

    // ========== 各种状态转换校验方法 ==========

    private void validateAssignTask(TaskInfoEntity task, Long operatorId, StatusChangeContext context) {
        // 检查是否有分配任务的权限
        if (!taskPermissionService.hasTaskStatusPermission(operatorId, TaskStatusPermissionEnum.ASSIGN.getPermission())) {
            throw new CoolException("没有分配任务的权限");
        }

        // 检查是否提供了执行人信息
        if (context.getExtraData() == null || !context.getExtraData().containsKey("assigneeIds")) {
            throw new CoolException("分配任务时必须指定执行人");
        }
    }

    private void validateStartTask(TaskInfoEntity task, Long operatorId, StatusChangeContext context) {
        // 检查操作者是否是任务的执行人
        boolean isAssignee = taskExecutionService.list(QueryWrapper.create()
                .eq("task_id", task.getId())
                .eq("assignee_id", operatorId)
                .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(), TaskExecutionStatusEnum.ACCEPTED.getCode()))
                .size() > 0;

        if (!isAssignee) {
            throw new CoolException("只有任务执行人才能开始任务");
        }
    }

    private void validateCompleteTask(TaskInfoEntity task, Long operatorId, StatusChangeContext context) {
        // 检查操作者是否是任务的执行人
        boolean isAssignee = taskExecutionService.list(QueryWrapper.create()
                .eq("task_id", task.getId())
                .eq("assignee_id", operatorId)
                .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(),
                    TaskExecutionStatusEnum.ACCEPTED.getCode(), TaskExecutionStatusEnum.IN_PROGRESS.getCode()))
                .size() > 0;

        if (!isAssignee) {
            throw new CoolException("只有任务执行人才能完成任务");
        }

        // 检查是否需要附件或照片
        if (task.getAttachmentRequired() != null && task.getAttachmentRequired() &&
            (context.getAttachments() == null || context.getAttachments().trim().isEmpty())) {
            throw new CoolException("该任务要求上传附件");
        }

        if (task.getPhotoRequired() != null && task.getPhotoRequired() &&
            (context.getPhotos() == null || context.getPhotos().trim().isEmpty())) {
            throw new CoolException("该任务要求上传照片");
        }
    }

    private void validateCloseTask(TaskInfoEntity task, Long operatorId, StatusChangeContext context) {
        // 检查是否有关闭任务的权限
        boolean hasClosePermission = taskPermissionService.hasTaskStatusPermission(operatorId, TaskStatusPermissionEnum.CLOSE.getPermission());
        
        // 如果没有关闭权限，检查是否是任务执行人
        if (!hasClosePermission) {
            boolean isAssignee = taskExecutionService.list(QueryWrapper.create()
                    .eq("task_id", task.getId())
                    .eq("assignee_id", operatorId))
                    .size() > 0;

            if (!isAssignee) {
                throw new CoolException("没有关闭任务的权限");
            }
        }

        // 检查是否提供了关闭原因
        if (context.getReason() == null || context.getReason().trim().isEmpty()) {
            throw new CoolException("关闭任务时必须提供关闭原因");
        }
    }

    private void validateReactivateTask(TaskInfoEntity task, Long operatorId, StatusChangeContext context) {
        // 检查是否有重新激活任务的权限
        if (!taskPermissionService.hasTaskStatusPermission(operatorId, TaskStatusPermissionEnum.REACTIVATE.getPermission())) {
            throw new CoolException("没有重新激活任务的权限");
        }

        // 检查是否提供了重新激活原因
        if (context.getReason() == null || context.getReason().trim().isEmpty()) {
            throw new CoolException("重新激活任务时必须提供原因");
        }
    }

    private void validateReopenTask(TaskInfoEntity task, Long operatorId, StatusChangeContext context) {
        // 检查是否有重新打开任务的权限
        if (!taskPermissionService.hasTaskStatusPermission(operatorId, TaskStatusPermissionEnum.REOPEN.getPermission())) {
            throw new CoolException("没有重新打开任务的权限");
        }

        // 检查任务是否有执行人
        List<TaskExecutionEntity> executions = taskExecutionService.list(QueryWrapper.create()
                .eq("task_id", task.getId())
                .notIn("execution_status", TaskExecutionStatusEnum.CANCELLED.getCode()));

        if (executions.isEmpty()) {
            throw new CoolException("重新打开任务前必须先分配执行人");
        }

        // 检查是否提供了重新打开原因
        if (context.getReason() == null || context.getReason().trim().isEmpty()) {
            throw new CoolException("重新打开任务时必须提供原因");
        }
    }

    // ========== 新增的接口方法实现 ==========

    @Override
    public Boolean isValidTransition(Integer sourceStatus, Integer targetStatus) {
        return TaskStatusTransitionEnum.isValidTransition(sourceStatus, targetStatus);
    }

    @Override
    public List<String> getAvailableActions(Long taskId, Long operatorId) {
        List<String> actions = new ArrayList<>();

        TaskInfoEntity task = taskInfoService.getById(taskId);
        if (task == null) {
            return actions;
        }

        Integer currentStatus = task.getTaskStatus();

        // 超级管理员拥有所有操作权限
        if (taskPermissionService.isAdmin(operatorId)) {
            log.info("超级管理员获取可用操作，返回所有可能的操作: taskId={}, currentStatus={}, operatorId={}",
                taskId, currentStatus, operatorId);

            // 为超级管理员返回所有可能的状态转换操作
            Set<Integer> allTargetStatuses = Set.of(0, 1, 2, 3, 4); // 所有状态
            for (Integer targetStatus : allTargetStatuses) {
                if (!targetStatus.equals(currentStatus)) { // 排除当前状态
                    TaskStatusTransitionEnum transition = TaskStatusTransitionEnum.findTransition(currentStatus, targetStatus);
                    if (transition != null) {
                        actions.add(transition.getEvent());
                    } else {
                        // 为超级管理员创建虚拟操作
                        String virtualEvent = getVirtualEventForSuperAdmin(currentStatus, targetStatus);
                        if (virtualEvent != null) {
                            actions.add(virtualEvent);
                        }
                    }
                }
            }
            return actions;
        }

        // 普通用户的权限检查
        Set<Integer> allowedTargetStatuses = ALLOWED_TRANSITIONS.getOrDefault(currentStatus, Set.of());

        for (Integer targetStatus : allowedTargetStatuses) {
            TaskStatusTransitionEnum transition = TaskStatusTransitionEnum.findTransition(currentStatus, targetStatus);
            if (transition != null) {
                try {
                    // 尝试校验权限，如果通过则添加到可用操作列表
                    StatusChangeContext context = StatusChangeContext.withOperator("权限检查", operatorId, "");
                    validateTransition(task, targetStatus, operatorId, context);
                    actions.add(transition.getEvent());
                } catch (Exception e) {
                    // 权限校验失败，不添加到可用操作列表
                    log.debug("用户{}对任务{}无{}权限: {}", operatorId, taskId, transition.getDescription(), e.getMessage());
                }
            }
        }

        return actions;
    }

    /**
     * 为超级管理员获取虚拟事件名称
     */
    private String getVirtualEventForSuperAdmin(Integer sourceStatus, Integer targetStatus) {
        // 为超级管理员的特殊状态转换创建虚拟事件
        if (sourceStatus == 0 && targetStatus == 2) return "DIRECT_START"; // 待分配 -> 执行中
        if (sourceStatus == 0 && targetStatus == 3) return "DIRECT_COMPLETE"; // 待分配 -> 已完成
        if (sourceStatus == 1 && targetStatus == 0) return "RESET_TO_ASSIGN"; // 待执行 -> 待分配
        if (sourceStatus == 1 && targetStatus == 3) return "DIRECT_COMPLETE"; // 待执行 -> 已完成
        if (sourceStatus == 2 && targetStatus == 0) return "RESET_TO_ASSIGN"; // 执行中 -> 待分配
        if (sourceStatus == 2 && targetStatus == 1) return "RESET_TO_PENDING"; // 执行中 -> 待执行
        if (sourceStatus == 3 && targetStatus == 0) return "RESET_TO_ASSIGN"; // 已完成 -> 待分配
        if (sourceStatus == 3 && targetStatus == 1) return "RESET_TO_PENDING"; // 已完成 -> 待执行
        if (sourceStatus == 3 && targetStatus == 4) return "CLOSE"; // 已完成 -> 已关闭
        if (sourceStatus == 4 && targetStatus == 0) return "RESET_TO_ASSIGN"; // 已关闭 -> 待分配
        if (sourceStatus == 4 && targetStatus == 2) return "DIRECT_RESTART"; // 已关闭 -> 执行中
        if (sourceStatus == 4 && targetStatus == 3) return "DIRECT_COMPLETE"; // 已关闭 -> 已完成
        return "ADMIN_OVERRIDE"; // 默认的超级管理员操作
    }
}
