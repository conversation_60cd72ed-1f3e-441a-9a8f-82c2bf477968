{"base_sys_role": [{"id": 100, "userId": "1", "name": "项目负责人", "label": "PROJECT_OWNER", "remark": "项目负责人，拥有项目的完全控制权限，可以删除项目、管理所有成员", "relevance": 1, "menuIdList": null, "departmentIdList": null}, {"id": 101, "userId": "1", "name": "项目管理员", "label": "PROJECT_ADMIN", "remark": "项目管理员，拥有项目管理权限，可以编辑项目、管理成员（除负责人外）", "relevance": 1, "menuIdList": null, "departmentIdList": null}, {"id": 102, "userId": "1", "name": "项目成员", "label": "PROJECT_MEMBER", "remark": "项目成员，拥有项目参与权限，可以查看项目信息、创建和编辑任务", "relevance": 1, "menuIdList": null, "departmentIdList": null}, {"id": 103, "userId": "1", "name": "项目观察者", "label": "PROJECT_VIEWER", "remark": "项目观察者，拥有项目只读权限，只能查看项目信息和报表", "relevance": 1, "menuIdList": null, "departmentIdList": null}]}