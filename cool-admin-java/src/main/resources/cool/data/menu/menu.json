[{"name": "系统管理", "router": "/sys", "perms": null, "type": 0, "icon": "icon-set", "orderNum": 2, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": [{"name": "权限管理", "router": null, "perms": null, "type": 0, "icon": "icon-auth", "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": [{"name": "菜单列表", "router": "/sys/menu", "perms": null, "type": 1, "icon": "icon-menu", "orderNum": 2, "viewPath": "modules/base/views/menu/index.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "新增", "router": null, "perms": "base:sys:menu:add", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "删除", "router": null, "perms": "base:sys:menu:delete", "type": 2, "icon": null, "orderNum": 2, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "查询", "router": null, "perms": "base:sys:menu:page,base:sys:menu:list,base:sys:menu:info", "type": 2, "icon": null, "orderNum": 4, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "参数", "router": "/test/aa", "perms": null, "type": 1, "icon": "icon-goods", "orderNum": 0, "viewPath": "modules/base/views/info.vue", "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "编辑", "router": null, "perms": "base:sys:menu:info,base:sys:menu:update", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}, {"name": "角色列表", "router": "/sys/role", "perms": null, "type": 1, "icon": "icon-dept", "orderNum": 3, "viewPath": "cool/modules/base/views/role.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "新增", "router": null, "perms": "base:sys:role:add", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "删除", "router": null, "perms": "base:sys:role:delete", "type": 2, "icon": null, "orderNum": 2, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "修改", "router": null, "perms": "base:sys:role:update", "type": 2, "icon": null, "orderNum": 3, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "查询", "router": null, "perms": "base:sys:role:page,base:sys:role:list,base:sys:role:info", "type": 2, "icon": null, "orderNum": 4, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}]}, {"name": "用户列表", "router": "/sys/user", "perms": null, "type": 1, "icon": "icon-user", "orderNum": 0, "viewPath": "modules/base/views/user/index.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "部门列表", "router": null, "perms": "base:sys:department:list", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "新增部门", "router": null, "perms": "base:sys:department:add", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "更新部门", "router": null, "perms": "base:sys:department:update", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "删除部门", "router": null, "perms": "base:sys:department:delete", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "部门排序", "router": null, "perms": "base:sys:department:order", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "用户转移", "router": null, "perms": "base:sys:user:move", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "新增", "router": null, "perms": "base:sys:user:add", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "删除", "router": null, "perms": "base:sys:user:delete", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "修改", "router": null, "perms": "base:sys:user:delete,base:sys:user:update", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "查询", "router": null, "perms": "base:sys:user:page,base:sys:user:list,base:sys:user:info", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}, {"name": "项目工作台", "router": "/project", "perms": null, "type": 0, "icon": "icon-project", "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": [{"name": "项目概览", "router": "/project/dashboard", "perms": null, "type": 1, "icon": "icon-dashboard", "orderNum": 1, "viewPath": "modules/organization/views/project/dashboard.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "查看", "router": null, "perms": "project:dashboard:view", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}]}, {"name": "项目管理", "router": "/project/list", "perms": null, "type": 1, "icon": "icon-list", "orderNum": 2, "viewPath": "modules/organization/views/project/list.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "新增", "router": null, "perms": "project:list:add", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "编辑", "router": null, "perms": "project:list:edit,project:list:info", "type": 2, "icon": null, "orderNum": 2, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "删除", "router": null, "perms": "project:list:delete", "type": 2, "icon": null, "orderNum": 3, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "查看", "router": null, "perms": "project:list:view,project:list:page,project:list:list,project:list:info", "type": 2, "icon": null, "orderNum": 4, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "导出", "router": null, "perms": "project:list:export", "type": 2, "icon": null, "orderNum": 5, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}]}, {"name": "成员管理", "router": "/project/member", "perms": null, "type": 1, "icon": "icon-user", "orderNum": 3, "viewPath": "modules/organization/views/project/member.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "添加成员", "router": null, "perms": "project:member:add", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "编辑成员", "router": null, "perms": "project:member:edit", "type": 2, "icon": null, "orderNum": 2, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "移除成员", "router": null, "perms": "project:member:delete", "type": 2, "icon": null, "orderNum": 3, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "查看成员", "router": null, "perms": "project:member:view,project:member:list", "type": 2, "icon": null, "orderNum": 4, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}]}, {"name": "任务管理", "router": "/project/task", "perms": null, "type": 1, "icon": "icon-task", "orderNum": 4, "viewPath": "modules/organization/views/project/task.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "新增任务", "router": null, "perms": "project:task:add", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "编辑任务", "router": null, "perms": "project:task:edit,project:task:info", "type": 2, "icon": null, "orderNum": 2, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "删除任务", "router": null, "perms": "project:task:delete", "type": 2, "icon": null, "orderNum": 3, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "查看任务", "router": null, "perms": "project:task:view,project:task:page,project:task:list,project:task:info", "type": 2, "icon": null, "orderNum": 4, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}]}, {"name": "项目报表", "router": "/project/report", "perms": null, "type": 1, "icon": "icon-chart", "orderNum": 5, "viewPath": "modules/organization/views/project/report.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "查看报表", "router": null, "perms": "project:report:view", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}, {"name": "导出报表", "router": null, "perms": "project:report:export", "type": 2, "icon": null, "orderNum": 2, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}]}]}]}, {"name": "参数配置", "router": null, "perms": null, "type": 0, "icon": "icon-params", "orderNum": 3, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": [{"name": "参数列表", "router": "/sys/param", "perms": null, "type": 1, "icon": "icon-menu", "orderNum": 0, "viewPath": "cool/modules/base/views/param.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "新增", "router": null, "perms": "base:sys:param:add", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "修改", "router": null, "perms": "base:sys:param:info,base:sys:param:update", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "删除", "router": null, "perms": "base:sys:param:delete", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "查看", "router": null, "perms": "base:sys:param:page,base:sys:param:list,base:sys:param:info", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}]}, {"name": "监控管理", "router": null, "perms": null, "type": 0, "icon": "icon-monitor", "orderNum": 9, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": [{"name": "请求日志", "router": "/sys/log", "perms": null, "type": 1, "icon": "icon-log", "orderNum": 1, "viewPath": "cool/modules/base/views/log.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "权限", "router": null, "perms": "base:sys:log:page,base:sys:log:clear,base:sys:log:getKeep,base:sys:log:setKeep", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": false, "isShow": true, "childMenus": []}]}]}, {"name": "任务管理", "router": null, "perms": null, "type": 0, "icon": "icon-activity", "orderNum": 9, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": [{"name": "任务列表", "router": "/task/list", "perms": null, "type": 1, "icon": "icon-menu", "orderNum": 0, "viewPath": "modules/task/views/list.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "权限", "router": null, "perms": "task:info:page,task:info:list,task:info:info,task:info:add,task:info:delete,task:info:update,task:info:stop,task:info:start,task:info:once,task:info:log", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}]}]}, {"name": "框架教程", "router": "/tutorial", "perms": null, "type": 0, "icon": "icon-task", "orderNum": 98, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": [{"name": "文档官网", "router": "/tutorial/doc", "perms": null, "type": 1, "icon": "icon-log", "orderNum": 0, "viewPath": "https://admin.cool-js.com", "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "crud 示例", "router": "/demo/crud", "perms": null, "type": 1, "icon": "icon-favor", "orderNum": 1, "viewPath": "modules/demo/views/crud/index.vue", "keepAlive": true, "isShow": true, "childMenus": []}]}, {"name": "通用", "router": null, "perms": null, "type": 0, "icon": "icon-radioboxfill", "orderNum": 99, "viewPath": null, "keepAlive": true, "isShow": false, "childMenus": [{"name": "图片上传", "router": null, "perms": "space:info:page,space:info:list,space:info:info,space:info:add,space:info:delete,space:info:update,space:type:page,space:type:list,space:type:info,space:type:add,space:type:delete,space:type:update", "type": 2, "icon": null, "orderNum": 1, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}, {"name": "首页", "router": "/", "perms": null, "type": 1, "icon": null, "orderNum": 0, "viewPath": "modules/demo/views/home/<USER>", "keepAlive": true, "isShow": false, "childMenus": []}, {"name": "数据管理", "router": null, "perms": null, "type": 0, "icon": "icon-data", "orderNum": 7, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": [{"name": "字典管理", "router": "/dict/list", "perms": null, "type": 1, "icon": "icon-dict", "orderNum": 3, "viewPath": "modules/dict/views/list.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "删除", "router": null, "perms": "dict:info:delete", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "修改", "router": null, "perms": "dict:info:update,dict:info:info", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "获得字典数据", "router": null, "perms": "dict:info:data", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "单个信息", "router": null, "perms": "dict:info:info", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "列表查询", "router": null, "perms": "dict:info:list", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "分页查询", "router": null, "perms": "dict:info:page", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "新增", "router": null, "perms": "dict:info:add", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "组权限", "router": null, "perms": "dict:type:list,dict:type:update,dict:type:delete,dict:type:add", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}, {"name": "数据回收站", "router": "/recycle/data", "perms": null, "type": 1, "icon": "icon-delete", "orderNum": 6, "viewPath": "modules/recycle/views/data.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "恢复数据", "router": null, "perms": "recycle:data:restore", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "单个信息", "router": null, "perms": "recycle:data:info", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "分页查询", "router": null, "perms": "recycle:data:page", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}, {"name": "文件管理", "router": "/upload/list", "perms": null, "type": 1, "icon": "icon-log", "orderNum": 5, "viewPath": "modules/space/views/list.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "权限", "router": null, "perms": "space:type:delete,space:type:update,space:type:info,space:type:list,space:type:page,space:type:add,space:info:getConfig,space:info:delete,space:info:update,space:info:info,space:info:list,space:info:page,space:info:add", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}]}, {"name": "用户管理", "router": null, "perms": null, "type": 0, "icon": "icon-user", "orderNum": 11, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": [{"name": "用户列表", "router": "/user/list", "perms": null, "type": 1, "icon": "icon-menu", "orderNum": 1, "viewPath": "modules/user/views/list.vue", "keepAlive": true, "isShow": true, "childMenus": [{"name": "删除", "router": null, "perms": "user:info:delete", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "修改", "router": null, "perms": "user:info:update,user:info:info", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "单个信息", "router": null, "perms": "user:info:info", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "列表查询", "router": null, "perms": "user:info:list", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "分页查询", "router": null, "perms": "user:info:page", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}, {"name": "新增", "router": null, "perms": "user:info:add", "type": 2, "icon": null, "orderNum": 0, "viewPath": null, "keepAlive": true, "isShow": true, "childMenus": []}]}]}]