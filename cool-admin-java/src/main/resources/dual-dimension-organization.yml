# 双维度组织架构配置
cool:
  organization:
    # 审计日志配置
    audit:
      enabled: true
      retention-days: 90
      cleanup:
        enabled: true
        cron: "0 0 2 * * ?"
    
    # 安全监控配置
    security:
      monitor:
        enabled: true
        cron: "0 0 * * * ?"
        abnormal-access-threshold: 5
        frequent-switch-threshold: 10
      
      risk-user:
        cron: "0 0 3 * * ?"
        failure-threshold: 10
      
      user-lock:
        default-duration-minutes: 60
        critical-duration-minutes: 60
        high-duration-minutes: 30
    
    # 缓存配置
    cache:
      permission:
        expire-seconds: 300
        key-prefix: "dual_dimension:permission:"
      
      mode:
        expire-seconds: 600
        key-prefix: "dual_dimension:mode:"

# 日志配置
logging:
  level:
    com.cool.modules.organization: INFO
    com.cool.modules.organization.service.impl.SecurityMonitorServiceImpl: WARN
    com.cool.modules.organization.task: INFO