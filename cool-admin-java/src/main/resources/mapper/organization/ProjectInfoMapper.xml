<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.organization.mapper.ProjectInfoMapper">

    <!-- 通用的项目信息字段映射 -->
    <sql id="projectInfoColumns"> pi.id, pi.project_name, pi.project_code, pi.description,
        pi.status, pi.priority, pi.owner_id, pi.creator_id, pi.planned_start_time,
        pi.planned_end_time, pi.actual_start_time, pi.actual_end_time, pi.budget, pi.tags,
        pi.attachments, pi.enabled, pi.order_num, pi.remark, pi.create_time, pi.update_time,
        owner.name AS owner_name, creator.name AS creator_name </sql>

    <!-- 通用的表关联 -->
    <sql id="projectInfoJoins"> FROM org_project_info pi LEFT JOIN base_sys_user owner ON
        pi.owner_id = owner.id LEFT JOIN base_sys_user creator ON pi.creator_id = creator.id </sql>

    <!-- 根据项目编码获取项目信息 -->
    <select id="getByProjectCode"
        resultType="com.cool.modules.organization.entity.ProjectInfoEntity"> SELECT <include
            refid="projectInfoColumns" />
        <include refid="projectInfoJoins" /> WHERE pi.project_code
        = #{projectCode} AND pi.enabled = 1 </select>

    <!-- 检查项目编码是否存在 -->
    <select id="existsByProjectCode" resultType="boolean"> SELECT COUNT(*) > 0 FROM org_project_info
        WHERE project_code = #{projectCode} <if test="excludeId != null"> AND id != #{excludeId} </if>
        AND enabled = 1 </select>

    <!-- 根据负责人ID获取项目列表 -->
    <select id="getByOwnerId" resultType="com.cool.modules.organization.entity.ProjectInfoEntity">
        SELECT <include refid="projectInfoColumns" />
        <include refid="projectInfoJoins" /> WHERE
        pi.owner_id = #{ownerId} AND pi.enabled = 1 ORDER BY pi.create_time DESC </select>

    <!-- 根据创建人ID获取项目列表 -->
    <select id="getByCreatorId" resultType="com.cool.modules.organization.entity.ProjectInfoEntity">
        SELECT <include refid="projectInfoColumns" />
        <include refid="projectInfoJoins" /> WHERE
        pi.creator_id = #{creatorId} AND pi.enabled = 1 ORDER BY pi.create_time DESC </select>

    <!-- 获取用户参与的项目列表（包括负责人、创建人、项目成员） -->
    <select id="getUserProjects" resultType="com.cool.modules.organization.entity.ProjectInfoEntity">
        SELECT DISTINCT <include refid="projectInfoColumns" />, CASE WHEN pi.owner_id = #{userId}
        THEN 'OWNER' WHEN pi.creator_id = #{userId} THEN 'CREATOR' WHEN uo.role_code IS NOT NULL
        THEN uo.role_code ELSE 'MEMBER' END AS user_role FROM org_project_info pi LEFT JOIN
        base_sys_user owner ON pi.owner_id = owner.id LEFT JOIN base_sys_user creator ON
        pi.creator_id = creator.id LEFT JOIN org_user_organization uo ON uo.organization_id = pi.id
        AND uo.organization_type = 'PROJECT' AND uo.user_id = #{userId} AND uo.status = 1 WHERE
        pi.enabled = 1 AND ( pi.owner_id = #{userId} OR pi.creator_id = #{userId} OR uo.user_id =
        #{userId} ) ORDER BY pi.create_time DESC </select>

    <!-- 更新项目状态 -->
    <update id="updateStatus"> UPDATE org_project_info SET status = #{status}, update_time = NOW()
        WHERE id = #{projectId} </update>

    <!-- 批量更新项目状态 -->
    <update id="batchUpdateStatus"> UPDATE org_project_info SET status = #{status}, update_time =
        NOW() WHERE id IN <foreach collection="projectIds" item="projectId" open="(" close=")"
            separator=","> #{projectId} </foreach>
    </update>

</mapper>