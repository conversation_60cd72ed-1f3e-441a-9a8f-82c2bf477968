<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.organization.mapper.UserOrganizationMapper">

    <!-- 通用的用户组织关系字段映射 -->
    <sql id="userOrganizationColumns">
        uo.id,
        uo.user_id,
        uo.organization_type,
        uo.organization_id,
        uo.role_code,
        uo.status,
        uo.join_time,
        uo.expire_time,
        uo.assigner_id,
        uo.assign_time,
        uo.permission_scope,
        uo.remark,
        uo.is_primary,
        uo.order_num,
        uo.create_time,
        uo.update_time,
        u.name AS user_name,
        assigner.name AS assigner_name
    </sql>

    <!-- 通用的表关联 -->
    <sql id="userOrganizationJoins">
        FROM org_user_organization uo
        LEFT JOIN base_sys_user u ON uo.user_id = u.id
        LEFT JOIN base_sys_user assigner ON uo.assigner_id = assigner.id
    </sql>

    <!-- 根据用户ID和组织类型获取组织关系 -->
    <select id="getByUserIdAndType" resultType="com.cool.modules.organization.entity.UserOrganizationEntity">
        SELECT 
        <include refid="userOrganizationColumns"/>
        <include refid="userOrganizationJoins"/>
        WHERE uo.user_id = #{userId} 
        AND uo.organization_type = #{organizationType}
        AND uo.status = 1
        ORDER BY uo.is_primary DESC, uo.order_num ASC, uo.create_time DESC
    </select>

    <!-- 根据组织ID和组织类型获取用户关系 -->
    <select id="getByOrganizationIdAndType" resultType="com.cool.modules.organization.entity.UserOrganizationEntity">
        SELECT 
        <include refid="userOrganizationColumns"/>
        <include refid="userOrganizationJoins"/>
        WHERE uo.organization_id = #{organizationId} 
        AND uo.organization_type = #{organizationType}
        AND uo.status = 1
        ORDER BY uo.is_primary DESC, uo.order_num ASC, uo.create_time DESC
    </select>

    <!-- 检查用户是否在指定组织中 -->
    <select id="existsByUserAndOrganization" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM org_user_organization
        WHERE user_id = #{userId}
        AND organization_id = #{organizationId}
        AND organization_type = #{organizationType}
        AND status = 1
        AND (expire_time IS NULL OR expire_time > NOW())
    </select>

    <!-- 获取用户在指定组织中的角色 -->
    <select id="getUserRoleInOrganization" resultType="string">
        SELECT role_code
        FROM org_user_organization
        WHERE user_id = #{userId}
        AND organization_id = #{organizationId}
        AND organization_type = #{organizationType}
        AND status = 1
        AND (expire_time IS NULL OR expire_time > NOW())
        ORDER BY is_primary DESC, order_num ASC
        LIMIT 1
    </select>

    <!-- 批量删除用户组织关系 -->
    <delete id="batchDeleteByUserAndOrganizations">
        DELETE FROM org_user_organization
        WHERE user_id = #{userId}
        AND organization_type = #{organizationType}
        AND organization_id IN
        <foreach collection="organizationIds" item="organizationId" open="(" close=")" separator=",">
            #{organizationId}
        </foreach>
    </delete>

    <!-- 获取用户的最高项目角色 -->
    <select id="getHighestProjectRole" resultType="string">
        SELECT role_code
        FROM org_user_organization
        WHERE user_id = #{userId}
        AND organization_type = 'PROJECT'
        AND status = 1
        AND (expire_time IS NULL OR expire_time > NOW())
        ORDER BY 
            CASE role_code
                WHEN 'PROJECT_OWNER' THEN 1
                WHEN 'PROJECT_MANAGER' THEN 2
                WHEN 'PROJECT_MEMBER' THEN 3
                ELSE 4
            END ASC,
            is_primary DESC,
            order_num ASC
        LIMIT 1
    </select>


</mapper>