<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.sop.mapper.WorkOrderMapper">

    <!-- 通用的工单字段映射 -->
    <sql id="workOrderColumns">
        wo.id,
        wo.sop_template_id,
        wo.order_no,
        wo.title,
        wo.description,
        wo.priority,
        wo.urgency,
        wo.business_type,
        wo.business_data,
        wo.applicant_id,
        wo.applicant_name,
        wo.applicant_dept,
        wo.applicant_dept_id,
        wo.assignee_id,
        wo.assignee_name,
        wo.planned_start_time,
        wo.planned_end_time,
        wo.actual_start_time,
        wo.actual_end_time,
        wo.status,
        wo.progress,
        wo.estimated_work_time,
        wo.actual_work_time,
        wo.quality_score,
        wo.ai_scheduled,
        wo.ai_schedule_config,
        wo.ai_predicted_end_time,
        wo.ai_risk_assessment,
        wo.execution_team,
        wo.remark,
        wo.related_business_id,
        wo.related_business_type,
        wo.execution_result,
        wo.failure_reason,
        wo.customer_satisfaction,
        wo.customer_feedback,
        wo.project_id,
        wo.create_time,
        wo.update_time,
        pi.project_name AS project_name
    </sql>

    <!-- 通用的表关联 -->
    <sql id="workOrderJoins">
        FROM sop_work_order wo
        LEFT JOIN org_project_info pi ON wo.project_id = pi.id
    </sql>

    <!-- 通用的查询条件 -->
    <sql id="workOrderConditions">
        <where>
            wo.is_deleted = 0
            <if test="params.status != null">
                AND wo.status = #{params.status}
            </if>
            <if test="params.assigneeId != null">
                AND wo.assignee_id = #{params.assigneeId}
            </if>
            <if test="params.applicantId != null">
                AND wo.applicant_id = #{params.applicantId}
            </if>
            <if test="params.projectId != null">
                AND wo.project_id = #{params.projectId}
            </if>
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    wo.title LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR wo.description LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR wo.order_no LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR pi.project_name LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
            <if test="params.businessType != null and params.businessType != ''">
                AND wo.business_type = #{params.businessType}
            </if>
            <if test="params.priority != null">
                AND wo.priority = #{params.priority}
            </if>
            <if test="params.startTime != null and params.endTime != null">
                AND wo.create_time BETWEEN #{params.startTime} AND #{params.endTime}
            </if>
        </where>
    </sql>

    <!-- 分页查询工单列表（关联项目信息） -->
    <select id="selectWorkOrderWithDetails" resultType="com.cool.modules.sop.entity.WorkOrderEntity">
        SELECT 
        <include refid="workOrderColumns"/>
        <include refid="workOrderJoins"/>
        <include refid="workOrderConditions"/>
        ORDER BY wo.create_time DESC
        <if test="params.offset != null and params.limit != null">
            LIMIT #{params.offset}, #{params.limit}
        </if>
    </select>

    <!-- 查询工单列表总数（关联项目信息） -->
    <select id="countWorkOrderWithDetails" resultType="int">
        SELECT COUNT(*)
        <include refid="workOrderJoins"/>
        <include refid="workOrderConditions"/>
    </select>

    <!-- 根据ID获取工单详情（关联项目信息） -->
    <select id="selectWorkOrderWithDetailsById" resultType="com.cool.modules.sop.entity.WorkOrderEntity">
        SELECT 
        <include refid="workOrderColumns"/>
        <include refid="workOrderJoins"/>
        WHERE wo.id = #{workOrderId} AND wo.is_deleted = 0
    </select>

</mapper>