<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.task.mapper.TaskInfoMapper">

    <!-- 通用的任务信息字段映射 -->
    <sql id="taskInfoColumns"> ti.id, ti.name, ti.description, ti.task_status, ti.task_category,
        ti.completion_time, ti.close_reason, ti.closed_by, ti.close_time, ti.scenario_id,
        ti.package_id, ti.scenario_code, ti.scenario_name, ti.step_id, ti.step_code, ti.step_name,
        ti.entity_touchpoint, ti.task_activity, ti.employee_behavior, ti.work_highlight,
        ti.employee_role, ti.photo_required, ti.attachment_required, ti.remark, ti.job_id,
        ti.repeat_count, ti.every, ti.schedule_status, ti.service, ti.schedule_type, ti.type,
        ti.data, ti.cron, ti.next_run_time, ti.start_time, ti.end_time, ti.department_id,
        ti.creator_department_id, ti.create_time, ti.update_time, dept1.name AS department_name,
        dept2.name AS creator_department_name, pi.id AS project_id, pi.project_name AS project_name, tp.package_name,
        tp.scenario_name as package_scenario_name </sql>

    <!-- 通用的表关联 -->
    <sql id="taskInfoJoins"> FROM task_info ti LEFT JOIN base_sys_department dept1 ON
        ti.department_id = dept1.id LEFT JOIN base_sys_department dept2 ON ti.creator_department_id
        = dept2.id LEFT JOIN task_package tp ON ti.package_id = tp.id LEFT JOIN org_project_info pi
        ON tp.project_id = pi.id </sql>

    <!-- 通用的查询条件 -->
    <sql id="taskInfoConditions">
        <where> ti.is_deleted = 0 <if test="params.projectId != null"> AND EXISTS ( SELECT 1 FROM
        task_package tp_inner WHERE tp_inner.id = ti.package_id AND tp_inner.project_id =
        #{params.projectId} ) </if>
            <if test="params.packageId != null"> AND ti.package_id =
        #{params.packageId} </if>
            <if test="params.taskStatus != null"> AND ti.task_status =
        #{params.taskStatus} </if>
            <if
                test="params.departmentIds != null and params.departmentIds.size() > 0"> AND
        ti.department_id IN <foreach collection="params.departmentIds" item="deptId" open="("
                    close=")" separator=","> #{deptId} </foreach>
            </if>
            <if test="params.projectIds != null and params.projectIds.size() > 0"> AND
        tp.project_id IN <foreach collection="params.projectIds" item="projectId" open="("
                    close=")" separator=","> #{projectId} </foreach>
            </if>
            <if
                test="params.keyWord != null and params.keyWord != ''"> AND ( ti.name LIKE
        CONCAT('%', #{params.keyWord}, '%') OR ti.description LIKE CONCAT('%', #{params.keyWord},
        '%') OR ti.scenario_name LIKE CONCAT('%', #{params.keyWord}, '%') OR ti.step_name LIKE
        CONCAT('%', #{params.keyWord}, '%') OR dept1.name LIKE CONCAT('%', #{params.keyWord}, '%')
        OR pi.project_name LIKE CONCAT('%', #{params.keyWord}, '%') ) </if>
            <!-- 高级搜索条件 -->
            <!-- 1. 任务执行时间过滤 -->
            <if
                test="params.executionStartDate != null and params.executionStartDate != ''"> AND
        ti.start_time >= #{params.executionStartDate} </if>
            <if
                test="params.executionEndDate != null and params.executionEndDate != ''"> AND
        ti.end_time &lt;= CONCAT(#{params.executionEndDate}, ' 23:59:59') </if>
            <!-- 2. 执行人模糊搜索 - 需要关联task_execution表 -->
            <if
                test="params.assigneeName != null and params.assigneeName != ''"> AND EXISTS (
        SELECT 1 FROM task_execution te_search WHERE te_search.task_id = ti.id AND
        te_search.assignee_name LIKE CONCAT('%', #{params.assigneeName}, '%') ) </if>
            <!-- 3. 执行人手机号模糊搜索 - 需要关联task_execution表和base_sys_user表 -->
            <if
                test="params.assigneePhone != null and params.assigneePhone != ''"> AND EXISTS (
        SELECT 1 FROM task_execution te_phone LEFT JOIN base_sys_user u ON te_phone.assignee_id =
        u.id WHERE te_phone.task_id = ti.id AND u.phone LIKE CONCAT('%', #{params.assigneePhone},
        '%') ) </if>
            <!-- 4. 任务类型过滤 -->
            <if test="params.taskCategory != null and params.taskCategory != ''"> AND
        ti.task_category = #{params.taskCategory} </if>
            <if test="params.scenarioId != null"> AND
        ti.scenario_id = #{params.scenarioId} </if>
            <if test="params.stepId != null"> AND ti.step_id
        = #{params.stepId} </if>
            <if test="params.scheduleStatus != null"> AND ti.schedule_status =
        #{params.scheduleStatus} </if>
            <if test="params.startTime != null and params.endTime != null">
        AND ti.create_time BETWEEN #{params.startTime} AND #{params.endTime} </if>
        </where>
    </sql>

    <!-- 分页查询任务信息列表（关联部门和项目信息） -->
    <select id="selectTaskInfoWithDetails" resultType="com.cool.modules.task.entity.TaskInfoEntity">
        SELECT <include refid="taskInfoColumns" />
        <include refid="taskInfoJoins" />
        <include
            refid="taskInfoConditions" /> ORDER BY ti.create_time DESC <if
            test="params.offset != null and params.limit != null"> LIMIT #{params.offset},
        #{params.limit} </if>
    </select>

    <!-- 分页查询任务信息列表（关联部门信息） -->
    <select id="selectTaskInfoWithDepartment"
        resultType="com.cool.modules.task.entity.TaskInfoEntity"> SELECT <include
            refid="taskInfoColumns" />
        <include refid="taskInfoJoins" />
        <include
            refid="taskInfoConditions" /> ORDER BY ti.create_time DESC <if
            test="params.offset != null and params.limit != null"> LIMIT #{params.offset},
        #{params.limit} </if>
    </select>

    <!-- 查询任务信息列表总数（关联部门和项目信息） -->
    <select id="countTaskInfoWithDetails" resultType="int"> SELECT COUNT(*) <include
            refid="taskInfoJoins" />
        <include refid="taskInfoConditions" />
    </select>

    <!-- 根据ID获取任务信息详情（关联部门信息） -->
    <select id="selectTaskInfoWithDepartmentById"
        resultType="com.cool.modules.task.entity.TaskInfoEntity"> SELECT <include
            refid="taskInfoColumns" />
        <include refid="taskInfoJoins" /> WHERE ti.id = #{taskId} AND
        ti.is_deleted = 0 </select>

    <!-- 根据任务包ID查询任务列表（关联部门信息） -->
    <select id="selectTaskInfoByPackageId" resultType="com.cool.modules.task.entity.TaskInfoEntity">
        SELECT <include refid="taskInfoColumns" />
        <include refid="taskInfoJoins" /> WHERE
        ti.package_id = #{packageId} AND ti.is_deleted = 0 ORDER BY ti.create_time DESC </select>

    <!-- 查询用户有权限的任务列表（关联部门和执行信息） -->
    <select id="selectUserTasksWithDepartment" resultType="map"> SELECT <include
            refid="taskInfoColumns" />, te.assignee_id, te.assignee_name, te.execution_status,
        te.accept_time, te.start_time as execution_start_time, te.completion_time as
        execution_completion_time, te.actual_duration, te.quality_score, te.performance_rating FROM
        task_info ti LEFT JOIN base_sys_department dept1 ON ti.department_id = dept1.id LEFT JOIN
        base_sys_department dept2 ON ti.creator_department_id = dept2.id LEFT JOIN task_package tp
        ON ti.package_id = tp.id LEFT JOIN org_project_info pi ON tp.project_id = pi.id LEFT JOIN
        task_execution te ON ti.id = te.task_id <where> ti.is_deleted = 0 <if
                test="params.assigneeId != null"> AND te.assignee_id = #{params.assigneeId} </if>
            <if
                test="params.executionStatus != null and params.executionStatus != ''"> AND
        te.execution_status = #{params.executionStatus} </if>
            <if
                test="params.departmentIds != null and params.departmentIds.size() > 0"> AND
        ti.department_id IN <foreach collection="params.departmentIds" item="deptId" open="("
                    close=")" separator=","> #{deptId} </foreach>
            </if>
            <if
                test="params.projectIds != null and params.projectIds.size() > 0"> AND
        tp.project_id IN <foreach collection="params.projectIds" item="projectId" open="("
                    close=")" separator=","> #{projectId} </foreach>
            </if>
            <if
                test="params.keyWord != null and params.keyWord != ''"> AND ( ti.name LIKE
        CONCAT('%', #{params.keyWord}, '%') OR ti.description LIKE CONCAT('%', #{params.keyWord},
        '%') OR dept1.name LIKE CONCAT('%', #{params.keyWord}, '%') ) </if>
        </where> ORDER BY
        ti.create_time DESC <if test="params.offset != null and params.limit != null"> LIMIT
        #{params.offset}, #{params.limit} </if>
    </select>

    <!-- 查询用户任务总数 -->
    <select id="countUserTasksWithDepartment" resultType="int"> SELECT COUNT(*) FROM task_info ti
        LEFT JOIN task_execution te ON ti.id = te.task_id <where> ti.is_deleted = 0 <if
                test="params.assigneeId != null"> AND te.assignee_id = #{params.assigneeId} </if>
            <if
                test="params.executionStatus != null and params.executionStatus != ''"> AND
        te.execution_status = #{params.executionStatus} </if>
            <if
                test="params.departmentIds != null and params.departmentIds.size() > 0"> AND
        ti.department_id IN <foreach collection="params.departmentIds" item="deptId" open="("
                    close=")" separator=","> #{deptId} </foreach>
            </if>
        </where>
    </select>

    <!-- 分页查询任务信息列表（关联执行人信息） -->
    <select id="selectTaskInfoWithExecutions"
        resultType="com.cool.modules.task.entity.TaskInfoEntity"> SELECT <include
            refid="taskInfoColumns" />, GROUP_CONCAT(DISTINCT te.assignee_name ORDER BY
        te.create_time SEPARATOR ', ') as assignee_name, GROUP_CONCAT(DISTINCT u.phone ORDER BY
        te.create_time SEPARATOR ', ') as assignee_phone, GROUP_CONCAT(DISTINCT pm.role_code ORDER
        BY te.create_time SEPARATOR ', ') as project_role_name, (SELECT te_first.assignee_id FROM
        task_execution te_first WHERE te_first.task_id = ti.id ORDER BY te_first.create_time LIMIT
        1) as assignee_id <include refid="taskInfoJoins" /> LEFT JOIN task_execution te ON ti.id =
        te.task_id LEFT JOIN base_sys_user u ON te.assignee_id = u.id LEFT JOIN
        org_user_organization pm ON te.assignee_id = pm.user_id AND tp.project_id =
        pm.organization_id AND pm.organization_type = 'PROJECT' <include refid="taskInfoConditions" />
        GROUP BY ti.id, ti.name, ti.description, ti.task_status, ti.task_category,
        ti.completion_time, ti.close_reason, ti.closed_by, ti.close_time, ti.scenario_id,
        ti.package_id, ti.scenario_code, ti.scenario_name, ti.step_id, ti.step_code, ti.step_name,
        ti.entity_touchpoint, ti.task_activity, ti.employee_behavior, ti.work_highlight,
        ti.employee_role, ti.photo_required, ti.attachment_required, ti.remark, ti.job_id,
        ti.repeat_count, ti.every, ti.schedule_status, ti.service, ti.schedule_type, ti.type,
        ti.data, ti.cron, ti.next_run_time, ti.start_time, ti.end_time, ti.department_id,
        ti.creator_department_id, ti.create_time, ti.update_time, dept1.name, dept2.name,
        pi.id, pi.project_name, tp.package_name, tp.scenario_name ORDER BY ti.create_time DESC <if
            test="params.offset != null and params.limit != null"> LIMIT #{params.offset},
        #{params.limit} </if>
    </select>

</mapper>