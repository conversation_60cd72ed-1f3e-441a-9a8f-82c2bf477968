<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.task.mapper.TaskPackageMapper">

    <!-- 通用的任务包字段映射 -->
    <sql id="packageColumns">
        tp.id,
        tp.package_name,
        tp.description,
        tp.scenario_id,
        tp.scenario_name,
        tp.scenario_code,
        tp.package_status,
        tp.package_type,
        tp.total_tasks,
        tp.completed_tasks,
        tp.in_progress_tasks,
        tp.pending_tasks,
        tp.completion_rate,
        tp.expected_start_time,
        tp.expected_end_time,
        tp.actual_start_time,
        tp.actual_end_time,
        tp.creator_id,
        tp.creator_name,
        tp.owner_id,
        tp.owner_name,
        tp.priority,
        tp.tags,
        tp.remarks,
        tp.department_id,
        tp.creator_department_id,
        tp.project_id,
        tp.create_time,
        tp.update_time,
        dept1.name AS department_name,
        dept2.name AS creator_department_name,
        pi.project_name AS project_name,
        CASE 
            WHEN tp.department_id IS NOT NULL AND tp.creator_department_id IS NOT NULL 
                 AND tp.department_id != tp.creator_department_id 
            THEN 1 ELSE 0 
        END AS is_cross_department
    </sql>

    <!-- 通用的表关联 -->
    <sql id="packageJoins">
        FROM task_package tp
        LEFT JOIN base_sys_department dept1 ON tp.department_id = dept1.id
        LEFT JOIN base_sys_department dept2 ON tp.creator_department_id = dept2.id
        LEFT JOIN org_project_info pi ON tp.project_id = pi.id
    </sql>

    <!-- 通用的查询条件 -->
    <sql id="packageConditions">
        <where>
            tp.is_deleted = 0
            <if test="params.scenarioId != null">
                AND tp.scenario_id = #{params.scenarioId}
            </if>
            <if test="params.packageStatus != null">
                AND tp.package_status = #{params.packageStatus}
            </if>
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND tp.department_id IN
                <foreach collection="params.departmentIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="params.projectIds != null and params.projectIds.size() > 0">
                AND tp.project_id IN
                <foreach collection="params.projectIds" item="projId" open="(" close=")" separator=",">
                    #{projId}
                </foreach>
            </if>
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    tp.package_name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR tp.description LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR tp.scenario_name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR dept1.name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR pi.project_name LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
            <if test="params.creatorId != null">
                AND tp.creator_id = #{params.creatorId}
            </if>
            <if test="params.priority != null">
                AND tp.priority = #{params.priority}
            </if>
            <if test="params.startTime != null and params.endTime != null">
                AND tp.create_time BETWEEN #{params.startTime} AND #{params.endTime}
            </if>
        </where>
    </sql>

    <!-- 更新任务包统计信息 -->
    <update id="updatePackageStats">
        UPDATE task_package SET 
            total_tasks = #{totalTasks}, 
            completed_tasks = #{completedTasks}, 
            in_progress_tasks = #{inProgressTasks}, 
            pending_tasks = #{pendingTasks}, 
            completion_rate = #{completionRate}, 
            update_time = NOW() 
        WHERE id = #{packageId}
    </update>

    <!-- 获取任务包统计信息 -->
    <select id="getPackageStatsWithTasks" resultType="map">
        SELECT 
            tp.*, 
            COUNT(ti.id) as actual_total_tasks, 
            SUM(CASE WHEN ti.task_status = 3 THEN 1 ELSE 0 END) as actual_completed_tasks, 
            SUM(CASE WHEN ti.task_status = 2 THEN 1 ELSE 0 END) as actual_in_progress_tasks, 
            SUM(CASE WHEN ti.task_status = 0 THEN 1 ELSE 0 END) as actual_pending_tasks 
        FROM task_package tp 
        LEFT JOIN task_info ti ON tp.id = ti.package_id AND ti.is_deleted = 0 
        WHERE tp.is_deleted = 0 
        GROUP BY tp.id
    </select>

    <!-- 根据场景ID获取任务包列表 -->
    <select id="getPackagesByScenarioId" resultType="com.cool.modules.task.entity.TaskPackageEntity">
        SELECT * FROM task_package 
        WHERE scenario_id = #{scenarioId} AND is_deleted = 0 
        ORDER BY create_time DESC
    </select>

    <!-- 获取任务包详情（包含统计信息） -->
    <select id="getPackageDetailWithStats" resultType="map">
        SELECT 
            tp.*, 
            COUNT(ti.id) as actual_total_tasks, 
            SUM(CASE WHEN ti.task_status = 3 THEN 1 ELSE 0 END) as actual_completed_tasks, 
            SUM(CASE WHEN ti.task_status = 2 THEN 1 ELSE 0 END) as actual_in_progress_tasks, 
            SUM(CASE WHEN ti.task_status = 0 THEN 1 ELSE 0 END) as actual_pending_tasks 
        FROM task_package tp 
        LEFT JOIN task_info ti ON tp.id = ti.package_id AND ti.is_deleted = 0 
        WHERE tp.id = #{packageId} AND tp.is_deleted = 0 
        GROUP BY tp.id
    </select>

    <!-- 分页查询任务包列表（关联部门和项目信息） -->
    <select id="selectPackagesWithDetails" resultType="com.cool.modules.task.entity.TaskPackageEntity">
        SELECT 
        <include refid="packageColumns"/>
        <include refid="packageJoins"/>
        <include refid="packageConditions"/>
        ORDER BY tp.create_time DESC
        <if test="params.offset != null and params.limit != null">
            LIMIT #{params.offset}, #{params.limit}
        </if>
    </select>

    <!-- 分页查询任务包列表（关联部门信息） -->
    <select id="selectPackagesWithDepartment" resultType="com.cool.modules.task.entity.TaskPackageEntity">
        SELECT 
        <include refid="packageColumns"/>
        <include refid="packageJoins"/>
        <include refid="packageConditions"/>
        ORDER BY tp.create_time DESC
        <if test="params.offset != null and params.limit != null">
            LIMIT #{params.offset}, #{params.limit}
        </if>
    </select>

    <!-- 查询任务包列表总数（关联部门和项目信息） -->
    <select id="countPackagesWithDetails" resultType="int">
        SELECT COUNT(*)
        <include refid="packageJoins"/>
        <include refid="packageConditions"/>
    </select>

    <!-- 根据ID获取任务包详情（关联部门信息） -->
    <select id="selectPackageWithDepartmentById" resultType="com.cool.modules.task.entity.TaskPackageEntity">
        SELECT 
        <include refid="packageColumns"/>
        <include refid="packageJoins"/>
        WHERE tp.id = #{packageId} AND tp.is_deleted = 0
    </select>

</mapper>