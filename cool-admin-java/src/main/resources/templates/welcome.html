<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>COOL-ADMIN 一个很酷的后台管理系统开发框架</title>
    <meta name="keywords" content="cool-admin，后台管理系统，vue3，element-ui、typescript、Springboot"/>
    <meta name="description" content="Ai编码、扩展插件、流程编排、前后端分离、权限管理、快速开发， COOL-AMIND 通用后台管理系统"/>
    <link rel="stylesheet" href="css/welcome.css">

<body>
<div class="reveal">HELLO COOL-ADMIN V8.x</div>
<!-- 添加底部说明 -->
<div class="footer-bar">
    <span>提示：系统为前后端分离模式，前端仓库地址</span>
    <a class ="link" target="_blank" href="https://github.com/cool-team-official/cool-admin-vue">
        https://github.com/cool-team-official/cool-admin-vue
    </a>
</div>
<script src="js/welcome.js"></script>
</body>

</html>
