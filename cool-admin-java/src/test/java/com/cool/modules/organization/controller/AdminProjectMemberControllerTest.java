package com.cool.modules.organization.controller;

import com.cool.modules.organization.controller.admin.AdminProjectMemberController;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.service.UserOrganizationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AdminProjectMemberController 测试类
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@ExtendWith(MockitoExtension.class)
class AdminProjectMemberControllerTest {

    @Mock
    private UserOrganizationService userOrganizationService;

    @InjectMocks
    private AdminProjectMemberController controller;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    void testControllerInitialization() {
        // 测试控制器是否正确初始化
        assert controller != null;
        assert userOrganizationService != null;
    }

    @Test
    void testGetGlobalProjectRoles() throws Exception {
        // 测试获取全局项目角色列表接口
        mockMvc.perform(get("/admin/project-member/global-project-roles"))
                .andExpect(status().isOk());
    }

    @Test
    void testGetUserProjects() throws Exception {
        // 测试获取用户项目列表接口
        mockMvc.perform(get("/admin/project-member/user/projects"))
                .andExpect(status().isOk());
    }

    @Test
    void testHasProjectManagePermission() throws Exception {
        // 测试检查项目管理权限接口
        mockMvc.perform(get("/admin/project-member/project/1/has-manage-permission"))
                .andExpect(status().isOk());
    }

    @Test
    void testGetProjectMembers() throws Exception {
        // 测试获取项目成员列表接口
        mockMvc.perform(get("/admin/project-member/project/1/members"))
                .andExpect(status().isOk());
    }
} 