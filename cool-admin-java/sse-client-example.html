<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cool Admin MCP SSE 客户端</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .tool-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: #f9f9f9;
        }
        .tool-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .tool-card button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-top: 10px;
        }
        .tool-card button:hover {
            background: #0056b3;
        }
        .tool-card input, .tool-card select {
            width: 100%;
            padding: 6px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-left: 3px solid #007bff;
            background: white;
        }
        .message.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .message.success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
    </style>
</head>
<body>
    <h1>🚀 Cool Admin MCP SSE 客户端</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
        <button onclick="connect()">连接</button>
        <button onclick="disconnect()">断开连接</button>
        <button onclick="clearMessages()">清空消息</button>
    </div>

    <div class="container">
        <h2>🔧 任务工具测试</h2>
        <div class="tool-grid">
            <!-- 任务列表 -->
            <div class="tool-card">
                <h4>📋 获取任务列表</h4>
                <input type="number" id="listPage" placeholder="页码 (默认1)" value="1">
                <input type="number" id="listSize" placeholder="每页大小 (默认10)" value="10">
                <input type="text" id="listKeyword" placeholder="搜索关键词">
                <select id="listStatus">
                    <option value="">所有状态</option>
                    <option value="0">待分配</option>
                    <option value="1">待执行</option>
                    <option value="2">执行中</option>
                    <option value="3">已完成</option>
                </select>
                <button onclick="callTool('task_list')">获取列表</button>
            </div>

            <!-- 创建任务 -->
            <div class="tool-card">
                <h4>➕ 创建任务</h4>
                <input type="text" id="createName" placeholder="任务名称" required>
                <input type="text" id="createDesc" placeholder="任务描述">
                <select id="createPriority">
                    <option value="1">低优先级</option>
                    <option value="2" selected>中优先级</option>
                    <option value="3">高优先级</option>
                </select>
                <input type="text" id="createCategory" placeholder="任务类别" value="TEST">
                <button onclick="callTool('task_create')">创建任务</button>
            </div>

            <!-- 获取任务详情 -->
            <div class="tool-card">
                <h4>🔍 获取任务详情</h4>
                <input type="number" id="getId" placeholder="任务ID" required>
                <button onclick="callTool('task_get')">获取详情</button>
            </div>

            <!-- 分配任务 -->
            <div class="tool-card">
                <h4>👤 分配任务</h4>
                <input type="number" id="assignTaskId" placeholder="任务ID" required>
                <input type="number" id="assignUserId" placeholder="用户ID" required>
                <input type="text" id="assignReason" placeholder="分配原因">
                <button onclick="callTool('task_assign')">分配任务</button>
            </div>

            <!-- 开始任务 -->
            <div class="tool-card">
                <h4>▶️ 开始任务</h4>
                <input type="number" id="startTaskId" placeholder="任务ID" required>
                <input type="text" id="startNote" placeholder="开始备注">
                <button onclick="callTool('task_start')">开始任务</button>
            </div>

            <!-- 完成任务 -->
            <div class="tool-card">
                <h4>✅ 完成任务</h4>
                <input type="number" id="completeTaskId" placeholder="任务ID" required>
                <input type="text" id="completeNote" placeholder="完成备注">
                <button onclick="callTool('task_complete')">完成任务</button>
            </div>

            <!-- 任务统计 -->
            <div class="tool-card">
                <h4>📊 任务统计</h4>
                <button onclick="callTool('task_stats')">获取统计</button>
            </div>

            <!-- 任务包列表 -->
            <div class="tool-card">
                <h4>📦 任务包列表</h4>
                <input type="number" id="packagePage" placeholder="页码 (默认1)" value="1">
                <input type="number" id="packageSize" placeholder="每页大小 (默认10)" value="10">
                <button onclick="callTool('task_package_list')">获取任务包</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📨 消息日志</h2>
        <div id="messages" class="messages"></div>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;

        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        function addMessage(message, type = 'info') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `
                <strong>[${new Date().toLocaleTimeString()}]</strong><br>
                <pre>${typeof message === 'object' ? JSON.stringify(message, null, 2) : message}</pre>
            `;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function connect() {
            if (isConnected) {
                addMessage('已经连接，无需重复连接', 'error');
                return;
            }

            updateStatus('正在连接...', 'connecting');
            
            // 连接到MCP SSE端点
            eventSource = new EventSource('/mcp/messages');
            
            eventSource.onopen = function(event) {
                isConnected = true;
                updateStatus('已连接', 'connected');
                addMessage('SSE连接已建立', 'success');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addMessage(`收到消息: ${data}`, 'info');
                } catch (e) {
                    addMessage(`收到消息: ${event.data}`, 'info');
                }
            };

            eventSource.onerror = function(event) {
                isConnected = false;
                updateStatus('连接错误', 'disconnected');
                addMessage('SSE连接错误', 'error');
            };
        }

        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            isConnected = false;
            updateStatus('已断开连接', 'disconnected');
            addMessage('SSE连接已断开', 'info');
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function callTool(toolName) {
            if (!isConnected) {
                addMessage('请先连接SSE', 'error');
                return;
            }

            let params = {};
            
            switch (toolName) {
                case 'task_list':
                    params = {
                        page: parseInt(document.getElementById('listPage').value) || 1,
                        size: parseInt(document.getElementById('listSize').value) || 10,
                        keyword: document.getElementById('listKeyword').value || null,
                        taskStatus: document.getElementById('listStatus').value || null
                    };
                    break;
                case 'task_create':
                    const name = document.getElementById('createName').value;
                    if (!name) {
                        addMessage('请输入任务名称', 'error');
                        return;
                    }
                    params = {
                        name: name,
                        description: document.getElementById('createDesc').value,
                        priority: parseInt(document.getElementById('createPriority').value),
                        taskCategory: document.getElementById('createCategory').value
                    };
                    break;
                case 'task_get':
                    const id = document.getElementById('getId').value;
                    if (!id) {
                        addMessage('请输入任务ID', 'error');
                        return;
                    }
                    params = { id: parseInt(id) };
                    break;
                case 'task_assign':
                    const taskId = document.getElementById('assignTaskId').value;
                    const userId = document.getElementById('assignUserId').value;
                    if (!taskId || !userId) {
                        addMessage('请输入任务ID和用户ID', 'error');
                        return;
                    }
                    params = {
                        taskId: parseInt(taskId),
                        assigneeId: parseInt(userId),
                        assignReason: document.getElementById('assignReason').value
                    };
                    break;
                case 'task_start':
                    const startTaskId = document.getElementById('startTaskId').value;
                    if (!startTaskId) {
                        addMessage('请输入任务ID', 'error');
                        return;
                    }
                    params = {
                        taskId: parseInt(startTaskId),
                        startNote: document.getElementById('startNote').value
                    };
                    break;
                case 'task_complete':
                    const completeTaskId = document.getElementById('completeTaskId').value;
                    if (!completeTaskId) {
                        addMessage('请输入任务ID', 'error');
                        return;
                    }
                    params = {
                        taskId: parseInt(completeTaskId),
                        completeNote: document.getElementById('completeNote').value
                    };
                    break;
                case 'task_package_list':
                    params = {
                        page: parseInt(document.getElementById('packagePage').value) || 1,
                        size: parseInt(document.getElementById('packageSize').value) || 10
                    };
                    break;
            }

            // 发送工具调用请求
            const request = {
                tool: toolName,
                params: params,
                timestamp: Date.now()
            };

            addMessage(`调用工具: ${toolName}`, 'info');
            addMessage(`参数: ${JSON.stringify(params, null, 2)}`, 'info');

            // 这里需要通过POST请求发送到MCP服务器
            fetch('/mcp/call', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request)
            })
            .then(response => response.json())
            .then(data => {
                addMessage(`工具调用结果:`, 'success');
                addMessage(data, 'success');
            })
            .catch(error => {
                addMessage(`工具调用失败: ${error.message}`, 'error');
            });
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            addMessage('页面加载完成，点击"连接"按钮开始', 'info');
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            disconnect();
        };
    </script>
</body>
</html>
