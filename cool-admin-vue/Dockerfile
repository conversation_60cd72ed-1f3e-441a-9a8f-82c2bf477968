# 构建阶段
FROM node:lts-alpine AS builder
WORKDIR /build

# 设置npm国内镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装pnpm
RUN npm install -g pnpm@latest

# 设置pnpm镜像源
RUN pnpm config set registry https://registry.npmmirror.com
RUN pnpm config set store-dir ~/.pnpm-store
RUN pnpm config set shamefully-hoist true

# 复制依赖文件
COPY package.json pnpm-lock.yaml* /build/

# 安装依赖
RUN pnpm install --frozen-lockfile || pnpm install

# 复制源代码
COPY ./ /build

# 构建项目
RUN pnpm run build

# 生产阶段
FROM nginx:alpine
RUN mkdir /app
COPY --from=builder /build/dist /app
COPY --from=builder /build/nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
