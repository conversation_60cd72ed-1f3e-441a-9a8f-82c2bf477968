declare const _default: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    items: {
        type: ArrayConstructor;
        default: () => any[];
    };
    props: ObjectConstructor;
    sync: BooleanConstructor;
    op: ObjectConstructor;
    dialog: ObjectConstructor;
    onOpen: FunctionConstructor;
    onOpened: FunctionConstructor;
    onClose: FunctionConstructor;
    onClosed: FunctionConstructor;
    onInfo: FunctionConstructor;
    onSubmit: FunctionConstructor;
}>, () => any, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, ("opened" | "closed")[], "opened" | "closed", import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    items: {
        type: ArrayConstructor;
        default: () => any[];
    };
    props: ObjectConstructor;
    sync: BooleanConstructor;
    op: ObjectConstructor;
    dialog: ObjectConstructor;
    onOpen: FunctionConstructor;
    onOpened: FunctionConstructor;
    onClose: FunctionConstructor;
    onClosed: FunctionConstructor;
    onInfo: FunctionConstructor;
    onSubmit: FunctionConstructor;
}>> & Readonly<{
    onOpened?: (...args: any[]) => any;
    onClosed?: (...args: any[]) => any;
}>, {
    sync: boolean;
    items: unknown[];
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
export default _default;
