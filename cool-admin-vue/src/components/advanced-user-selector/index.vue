<template>
  <div class="advanced-user-selector">
    <!-- 触发按钮 -->
    <el-button 
      type="primary" 
      :icon="Plus" 
      @click="openDialog"
      v-bind="$attrs"
    >
      {{ buttonText }}
    </el-button>

    <!-- 用户选择对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="1000px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="user-selector-content">
        <!-- 搜索和过滤区域 -->
        <div class="filter-section">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索姓名、用户名、手机号、邮箱"
                :prefix-icon="Search"
                clearable
                @input="handleSearch"
              />
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="searchForm.departmentId"
                placeholder="选择部门"
                clearable
                filterable
                @change="handleDepartmentChange"
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="searchForm.roleId"
                placeholder="选择角色"
                clearable
                filterable
                @change="handleRoleChange"
              >
                <el-option
                  v-for="role in roleOptions"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 已选用户展示区域 -->
        <div v-if="multiple && selectedUsers.length > 0" class="selected-section">
          <div class="selected-header">
            <span>已选用户 ({{ selectedUsers.length }})</span>
            <el-button 
              type="text" 
              size="small" 
              @click="clearSelection"
            >
              清空
            </el-button>
          </div>
          <div class="selected-users">
            <el-tag
              v-for="user in selectedUsers"
              :key="user.id"
              closable
              @close="removeUser(user)"
              class="selected-user-tag"
            >
              <el-avatar :size="20" :src="user.headImg" class="tag-avatar" />
              {{ user.name }} ({{ user.phone }})
            </el-tag>
          </div>
        </div>

        <!-- 用户列表区域 -->
        <div class="user-list-section">
          <cl-crud ref="Crud">
            <cl-row>
              <cl-flex1 />
              <span class="list-info">共 {{ total }} 个用户</span>
            </cl-row>
            <cl-row>
              <cl-table ref="Table" @selection-change="handleSelectionChange" />
            </cl-row>
            <cl-row>
              <cl-flex1 />
              <cl-pagination />
            </cl-row>
          </cl-crud>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirm"
            :disabled="!canConfirm"
          >
            确定{{ multiple && selectedUsers.length > 0 ? `(${selectedUsers.length})` : '' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Search } from '@element-plus/icons-vue';
import { useCrud, useTable } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { debounce } from 'lodash-es';

// 组件属性定义
interface Props {
  modelValue?: any;
  multiple?: boolean;
  title?: string;
  buttonText?: string;
  excludeUserIds?: number[];
  filterByProject?: boolean;
  projectId?: number;
  roleOptions?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  title: '选择用户',
  buttonText: '添加成员',
  excludeUserIds: () => [],
  filterByProject: false,
  roleOptions: () => []
});

// 事件定义
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'confirm': [users: any[]];
}>();

// 响应式数据
const { service } = useCool();
const dialogVisible = ref(false);
const selectedUsers = ref<any[]>([]);
const total = ref(0);
const hasSelection = ref(false);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  departmentId: undefined as number | undefined,
  roleId: undefined as number | undefined
});

// 选项数据
const departmentOptions = ref<any[]>([]);
const roleOptions = ref<any[]>([]);

// 计算属性
const canConfirm = computed(() => {
  return hasSelection.value;
});

// CRUD配置 - 使用正确的用户服务接口
const Crud = useCrud(
  {
    service: service.base.sys.user
  },
  (app) => {
    app.refresh();
  }
);

// 表格配置
const Table = useTable({
  autoHeight: false,
  height: 400,
  columns: [
    {
      type: 'selection',
      width: 60,
      selectable: (row: any) => {
        // 排除已存在的用户
        return !props.excludeUserIds?.includes(row.id);
      }
    },
    {
      label: '姓名',
      prop: 'name',
      minWidth: 120
    },
    {
      label: '用户名',
      prop: 'username',
      minWidth: 120
    },
    {
      label: '手机号',
      prop: 'phone',
      minWidth: 130
    },
    {
      label: '邮箱',
      prop: 'email',
      minWidth: 180
    },
    {
      label: '部门',
      prop: 'departmentName',
      minWidth: 150
    },
    {
      label: '角色',
      prop: 'roleName',
      minWidth: 120
    },
    {
      label: '状态',
      prop: 'status',
      width: 80,
      dict: [
        { label: '正常', value: 1, type: 'success' },
        { label: '禁用', value: 0, type: 'danger' }
      ]
    }
  ]
});

// 防抖搜索
const handleSearch = debounce(() => {
  const params: any = {
    status: 1,
    page: 1,
    size: 20
  };
  
  console.log('=== 搜索参数构建开始 ===');
  console.log('当前表单状态:', JSON.stringify(searchForm));
  
  // 关键词搜索 - 后端支持模糊搜索姓名、用户名、手机号
  if (searchForm.keyword?.trim()) {
    params.keyWord = searchForm.keyword.trim();
    console.log('添加关键词参数:', params.keyWord);
  }
  
  // 部门筛选 - 强制检查清空状态
  console.log('部门ID检查:', searchForm.departmentId, '类型:', typeof searchForm.departmentId);
  if (searchForm.departmentId !== undefined && searchForm.departmentId !== null && String(searchForm.departmentId) !== '' && searchForm.departmentId !== 0) {
    params.departmentIds = [searchForm.departmentId];
    console.log('添加部门参数:', params.departmentIds);
  } else {
    console.log('部门参数被过滤，不添加到请求中');
  }
  
  // 角色筛选 - 强制检查清空状态
  console.log('角色ID检查:', searchForm.roleId, '类型:', typeof searchForm.roleId);
  if (searchForm.roleId !== undefined && searchForm.roleId !== null && String(searchForm.roleId) !== '' && searchForm.roleId !== 0) {
    params.roleIds = [searchForm.roleId];
    console.log('添加角色参数:', params.roleIds);
  } else {
    console.log('角色参数被过滤，不添加到请求中');
  }
  
  // 项目筛选（如果需要）
  if (props.filterByProject && props.projectId) {
    params.projectId = props.projectId;
  }
  
  console.log('最终搜索参数:', JSON.stringify(params));
  console.log('=== 搜索参数构建结束 ===');
  
  // 强制清空CRUD组件的内部状态，防止参数缓存
  if (Crud.value) {
    // 方法1：先重置查询条件，再设置新参数
    Crud.value.params = {};
    // 方法2：直接设置新参数
    Object.assign(Crud.value.params, params);
    // 方法3：强制刷新
    Crud.value.refresh();
  }
}, 300);

// 部门变化处理
const handleDepartmentChange = (value: any) => {
  console.log('部门变化:', value, '类型:', typeof value);
  console.log('当前searchForm.departmentId:', searchForm.departmentId);
  handleSearch();
};

// 角色变化处理
const handleRoleChange = (value: any) => {
  console.log('角色变化:', value, '类型:', typeof value);
  console.log('当前searchForm.roleId:', searchForm.roleId);
  handleSearch();
};

// 重置搜索表单
const resetSearchForm = () => {
  searchForm.keyword = '';
  searchForm.departmentId = undefined;
  searchForm.roleId = undefined;
};

// 打开对话框
const openDialog = () => {
  dialogVisible.value = true;
  // 重置搜索表单状态
  resetSearchForm();
  loadDepartments();
  loadRoles();
  // 使用CRUD的refresh方法，带上默认参数
  setTimeout(() => {
    Crud.value?.refresh({ status: 1 });
  }, 100);
};

// 加载部门列表
const loadDepartments = async () => {
  try {
    const result = await service.base.sys.department.list();
    departmentOptions.value = result || [];
  } catch (error) {
    console.warn('加载部门列表失败:', error);
    departmentOptions.value = [];
  }
};

// 加载角色列表
const loadRoles = async () => {
  if (props.roleOptions && props.roleOptions.length > 0) {
    roleOptions.value = props.roleOptions;
    return;
  }
  try {
    const result = await service.base.sys.role.list();
    roleOptions.value = result || [];
  } catch (error) {
    console.warn('加载角色列表失败:', error);
    roleOptions.value = [];
  }
};

// 移除用户
const removeUser = (user: any) => {
  const index = selectedUsers.value.findIndex(u => u.id === user.id);
  if (index > -1) {
    selectedUsers.value.splice(index, 1);
    // 同时取消表格中的选择
    Table.value?.toggleRowSelection(user, false);
  }
};

// 清除选择
const clearSelection = () => {
  selectedUsers.value = [];
  hasSelection.value = false;
  // 清除表格选择
  Table.value?.clearSelection();
};

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false;
  clearSelection();
  // 重置搜索表单
  Object.assign(searchForm, {
    keyword: '',
    departmentId: null,
    roleId: null
  });
};

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection;
  hasSelection.value = selection.length > 0;
};

// 确认选择
const handleConfirm = () => {
  // 从表格组件获取选中的用户
  const selection = Table.value?.getSelectionRows() || [];
  
  if (selection.length === 0) {
    ElMessage.warning('请选择用户');
    return;
  }
  
  selectedUsers.value = selection;
  emit('confirm', selection);
  dialogVisible.value = false;
  clearSelection();
};

// 监听对话框关闭
watch(dialogVisible, (visible) => {
  if (!visible) {
    handleCancel();
  }
});

// 暴露方法
defineExpose({
  openDialog,
  clearSelection
});
</script>

<style lang="scss" scoped>
.advanced-user-selector {
  display: inline-block;
}

.user-selector-content {
  .filter-section {
    margin-bottom: 16px;
    padding: 16px;
    background-color: var(--el-bg-color-page);
    border-radius: 6px;
  }

  .selected-section {
    margin-bottom: 16px;
    padding: 12px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    background-color: var(--el-fill-color-lighter);

    .selected-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .selected-users {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .selected-user-tag {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;

        .tag-avatar {
          flex-shrink: 0;
        }
      }
    }
  }

  .user-list-section {
    .list-info {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
