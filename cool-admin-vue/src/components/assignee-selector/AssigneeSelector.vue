<template>
  <el-dialog
    v-model="visible"
    :title="`手动分配 - ${taskName}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="assignment-content">
      <div class="task-info">
        <h4>{{ taskName }}</h4>
        <div class="task-tags" v-if="taskTags && taskTags.length > 0">
          <el-tag v-for="tag in taskTags" :key="tag" size="small" type="info">{{ tag }}</el-tag>
        </div>
      </div>

      <div class="assignment-section">
        <h5>选择执行人</h5>

        <!-- 筛选条件 -->
        <div class="search-filters">
          <div class="filter-row">
            <!-- 动态筛选条件 -->
            <div class="filter-item" v-if="filterMode === 'project'">
              <label>项目筛选</label>
              <el-select v-model="filter.project" placeholder="选择项目" clearable @change="handleFilterChange">
                <el-option 
                  v-for="project in projectOptions" 
                  :key="project.value" 
                  :label="project.label" 
                  :value="project.value" 
                />
              </el-select>
            </div>
            <div class="filter-item" v-else-if="filterMode === 'department'">
              <label>部门筛选</label>
              <el-select v-model="filter.department" placeholder="选择部门" clearable @change="handleFilterChange">
                <el-option v-for="dept in departmentOptions" :key="dept.id" :label="dept.name" :value="dept.id" />
              </el-select>
            </div>
            <div class="filter-item">
              <label>角色筛选</label>
              <el-select v-model="filter.role" placeholder="选择角色" clearable @change="handleFilterChange">
                <el-option v-for="role in roleOptions" :key="role.id" :label="role.name" :value="role.id" />
              </el-select>
            </div>
            <div class="filter-item">
              <label>搜索</label>
              <el-input v-model="filter.search" placeholder="姓名或手机号" clearable @input="handleFilterChange">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </div>

        <!-- 执行人列表 -->
        <div class="assignee-list">
          <div class="list-header">
            <span>可选执行人 ({{ availableAssignees.length }}人)</span>
            <el-button size="small" @click="refreshAssignees" :loading="loading">
              <el-icon>
                <Refresh />
              </el-icon>
              刷新
            </el-button>
          </div>

          <div class="assignee-grid">
            <div v-for="assignee in availableAssignees" :key="assignee.id" class="assignee-card"
              :class="{ 'selected': isAssigneeSelected(assignee.id) }"
              @click="selectAssignee(assignee)">
              <div class="assignee-avatar">
                <el-avatar :size="40" :src="assignee.avatar">
                  {{ assignee.name?.charAt(0) }}
                </el-avatar>
              </div>
              <div class="assignee-info">
                <div class="assignee-name">{{ assignee.name || assignee.userName || assignee.nickName }}</div>
                <el-tag v-if="assignee.roleName" size="small" type="info"
                  class="assignee-role-tag" style="margin-right: 4px;">
                  {{ assignee.roleName }}
                </el-tag>
                <el-tag
                  v-if="assignee.departmentName"
                  size="small" type="success" class="assignee-dept-tag">
                  {{ assignee.departmentName }}
                </el-tag>
                <div class="assignee-phone">{{ assignee.phone }}</div>
              </div>
              <div class="assignee-stats">
                <div class="stat-item">
                  <span class="stat-label">负载</span>
                  <el-progress :percentage="assignee.workload || 0" :stroke-width="4" :show-text="false"
                    :color="getWorkloadColor(assignee.workload)" />
                  <span class="stat-value">{{ assignee.workload || 0 }}%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">评分</span>
                  <el-rate v-model="assignee.performanceScore" :max="5" :allow-half="true" disabled size="small" />
                  <span class="stat-value">{{ assignee.performanceScore || 0 }}</span>
                </div>
              </div>
              <div class="assignee-actions" @click.stop>
                <el-checkbox 
                  :model-value="isAssigneeSelected(assignee.id)"
                  @change="(val) => handleAssigneeSelect(val, assignee)" 
                />
              </div>
            </div>
          </div>

          <div v-if="availableAssignees.length === 0" class="no-assignees">
            <el-empty description="没有找到符合条件的执行人" />
          </div>
        </div>
      </div>

      <div class="assignment-reason">
        <h5>分配说明</h5>
        <el-input v-model="reason" type="textarea" :rows="3" placeholder="请输入分配说明（可选）" />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" 
        :disabled="props.multiple ? selectedAssignees.length === 0 : !selectedAssignee" 
        :loading="confirmLoading">
        确认分配{{ props.multiple && selectedAssignees.length > 0 ? `(${selectedAssignees.length}人)` : '' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';

const { service } = useCool();

defineOptions({
  name: 'AssigneeSelector'
});

// Props定义
interface Props {
  modelValue: boolean;
  taskName?: string;
  taskTags?: string[];
  filterMode?: 'department' | 'project'; // 筛选模式
  contextId?: string | number; // 部门ID或项目ID
  contextName?: string; // 部门名称或项目名称
  multiple?: boolean; // 是否支持多选，默认单选
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskName: '未知任务',
  taskTags: () => [],
  filterMode: 'department',
  contextId: '',
  contextName: '',
  multiple: false
});

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'confirm': [data: { assigneeId?: string | number; assigneeIds?: (string | number)[]; assigneeName?: string; assigneeNames?: string[]; reason: string }];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

const loading = ref(false);
const confirmLoading = ref(false);
const selectedAssignee = ref<string | number>(''); // 单选时使用
const selectedAssignees = ref<(string | number)[]>([]); // 多选时使用
const reason = ref('');

// 筛选条件
const filter = reactive({
  department: '',
  project: '',
  role: '',
  search: ''
});

// 选项数据
const departmentOptions = ref<any[]>([]);
const projectOptions = ref<any[]>([]);
const roleOptions = ref<any[]>([]);
const availableAssignees = ref<any[]>([]);

// 检查执行人是否被选中
const isAssigneeSelected = (assigneeId: string | number) => {
  if (props.multiple) {
    return selectedAssignees.value.includes(assigneeId);
  } else {
    return selectedAssignee.value === assigneeId;
  }
};

// 获取可用分配人员
const fetchAvailableAssignees = async () => {
  loading.value = true;
  try {
    let res;
    
    // 根据筛选模式调用不同的API
    if (props.filterMode === 'project') {
      // 项目模式：调用项目成员API
      const projectId = filter.project || props.contextId;
      if (!projectId) {
        availableAssignees.value = [];
        return;
      }
      
      // 构建查询参数 - 注意参数格式
      const params = new URLSearchParams();
      params.append('projectId', projectId.toString());
      if (filter.role) {
        params.append('roleIds', filter.role.toString());
      }
      if (filter.search) {
        params.append('keyword', filter.search);
      }
      
      // 使用GET请求，参数放在URL中
      res = await service.request({
        url: `/admin/organization/project/member/available-assignees?${params.toString()}`,
        method: 'GET'
      });
    } else {
      // 部门模式：使用原有的API
      const params: any = {};
      params.departmentIds = filter.department ? [filter.department] : (props.contextId ? [props.contextId] : undefined);
      if (filter.role) params.roleIds = [filter.role];
      if (filter.search) params.keyword = filter.search;
      
      res = await service.request({
        url: '/admin/base/user-query/available-assignees',
        method: 'GET',
        params: params,
        paramsSerializer: {
          indexes: null // 使用 roleIds=1&roleIds=2 格式而不是 roleIds[]=1&roleIds[]=2
        }
      });
    }

    // 处理返回的数据，确保数据格式正确
    const assignees = Array.isArray(res) ? res : (res?.data || res?.list || []);
    
    // 根据实际返回的数据结构进行映射
    availableAssignees.value = assignees.map((member: any) => {
      // 检查数据结构，适配不同的API返回格式
      return {
        // 用户ID可能是userId或id
        id: member.userId || member.id,
        
        // 用户姓名可能是userName、name或nickName
        name: member.userName || member.name || member.nickName,
        userName: member.userName || member.name || member.username,
        
        // 手机号可能是userPhone或phone
        phone: member.userPhone || member.phone || member.mobile || member.phoneNumber,
        
        // 邮箱可能是userEmail或email
        email: member.userEmail || member.email,
        
        // 头像可能是userAvatar、avatar或headImg
        avatar: member.userAvatar || member.avatar || member.headImg,
        
        // 角色名称可能是roleName或role
        roleName: member.roleName || member.role,
        
        // 角色代码可能是roleCode、roleLabels或roleIds
        roleCode: member.roleCode || member.roleLabels || member.roleIds,
        
        // 部门名称
        departmentName: member.departmentName || member.deptName || member.department_name || member.department,
        
        // 项目名称
        projectName: member.projectName,
        
        // 工作负载，如果没有则默认为0
        workload: member.workload || 0,
        
        // 绩效评分，如果没有则默认为0
        performanceScore: member.performanceScore || 0
      };
    });
  } catch (error) {
    ElMessage.error(`获取可用分配人员失败: ${(error as any)?.message || '未知错误'}`);
    availableAssignees.value = [];
  } finally {
    loading.value = false;
  }
};

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const res = await service.base.sys.department.list();
    departmentOptions.value = res || [];
  } catch (error) {
    departmentOptions.value = [];
  }
};

// 获取项目列表
const fetchProjects = async () => {
  try {
    const res = await service.organization.project.info.options();
    
    // 后端返回格式: [{label: "项目名", value: 项目ID}]
    projectOptions.value = res || [];
  } catch (error) {
    ElMessage.error('获取项目列表失败，请检查权限');
    projectOptions.value = [];
  }
};

// 获取角色列表
const fetchRoles = async () => {
  try {
    const res = await service.base.sys.role.list();
    roleOptions.value = res || [];
  } catch (error) {
    roleOptions.value = [];
  }
};

// 筛选条件变化处理
const handleFilterChange = () => {
  fetchAvailableAssignees();
};

// 刷新执行人列表
const refreshAssignees = () => {
  fetchAvailableAssignees();
};

// 选择执行人
const selectAssignee = (assignee: any) => {
  if (props.multiple) {
    // 多选模式：切换选择状态
    const index = selectedAssignees.value.indexOf(assignee.id);
    if (index > -1) {
      selectedAssignees.value.splice(index, 1);
    } else {
      selectedAssignees.value.push(assignee.id);
    }
  } else {
    // 单选模式：切换选择状态
    if (selectedAssignee.value === assignee.id) {
      // 如果已经选中，则取消选择
      selectedAssignee.value = '';
    } else {
      // 如果未选中，则选择
      selectedAssignee.value = assignee.id;
    }
  }
};

// 处理执行人选择/取消选择
const handleAssigneeSelect = (val: boolean, assignee: any) => {
  if (props.multiple) {
    // 多选模式
    const index = selectedAssignees.value.indexOf(assignee.id);
    if (val) {
      // 选中：添加到数组（如果不存在）
      if (index === -1) {
        selectedAssignees.value.push(assignee.id);
      }
    } else {
      // 取消选中：从数组中移除
      if (index > -1) {
        selectedAssignees.value.splice(index, 1);
      }
    }
  } else {
    // 单选模式：直接根据checkbox的状态设置
    if (val) {
      // 选中：设置为当前执行人
      selectedAssignee.value = assignee.id;
    } else {
      // 取消选中：清空选择
      selectedAssignee.value = '';
    }
  }
};

// 获取工作负载颜色
const getWorkloadColor = (workload: number) => {
  if (workload >= 80) return '#f56c6c';
  if (workload >= 60) return '#e6a23c';
  if (workload >= 40) return '#409eff';
  return '#67c23a';
};

// 确认分配
const handleConfirm = async () => {
  if (props.multiple) {
    // 多选模式
    if (selectedAssignees.value.length === 0) {
      ElMessage.warning('请选择至少一个执行人');
      return;
    }

    const assignees = availableAssignees.value.filter(a => selectedAssignees.value.includes(a.id));
    if (assignees.length === 0) {
      ElMessage.error('选择的执行人不存在');
      return;
    }

    confirmLoading.value = true;
    try {
      emit('confirm', {
        assigneeIds: assignees.map(a => a.id),
        assigneeNames: assignees.map(a => a.name || a.userName || a.nickName),
        reason: reason.value
      });
      handleClose();
    } finally {
      confirmLoading.value = false;
    }
  } else {
    // 单选模式
    if (!selectedAssignee.value) {
      ElMessage.warning('请选择执行人');
      return;
    }

    const assignee = availableAssignees.value.find(a => a.id === selectedAssignee.value);
    if (!assignee) {
      ElMessage.error('选择的执行人不存在');
      return;
    }

    confirmLoading.value = true;
    try {
      emit('confirm', {
        assigneeId: assignee.id,
        assigneeName: assignee.name || assignee.userName || assignee.nickName,
        reason: reason.value
      });
      handleClose();
    } finally {
      confirmLoading.value = false;
    }
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  // 重置数据
  selectedAssignee.value = '';
  selectedAssignees.value = [];
  reason.value = '';
  filter.role = '';
  filter.search = '';
};

// 监听弹窗显示状态
watch(visible, async (val) => {
  if (val) {
    // 重置筛选条件
    filter.role = '';
    filter.search = '';
    filter.department = '';
    filter.project = '';
    
    // 先获取选项数据
    await Promise.all([
      fetchDepartments(),
      fetchProjects(),
      fetchRoles()
    ]);
    
    // 等待选项数据加载完成后，再设置初始筛选条件
    if (props.filterMode === 'department') {
      filter.department = props.contextId as string;
    } else if (props.filterMode === 'project') {
      // 确保项目ID是数字类型
      const projectId = typeof props.contextId === 'string' ? parseInt(props.contextId) : props.contextId;
      
      // 验证项目选项中是否有对应的项目
      const matchedProject = projectOptions.value.find(p => p.value == projectId);
      
      if (matchedProject) {
        filter.project = projectId as any;
      } else {
        ElMessage.error(`未找到项目ID为 ${projectId} 的项目`);
        return;
      }
    }
    
    // 获取可用分配人员
    await fetchAvailableAssignees();
  }
});
</script>

<style scoped>
.assignment-content {
  max-height: 600px;
  overflow-y: auto;
}

.task-info {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
}

.task-info h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.task-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.assignment-section {
  margin-bottom: 20px;
}

.assignment-section h5 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 600;
}

.search-filters {
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  flex: 1;
  min-width: 180px;
}

.filter-item label {
  display: block;
  margin-bottom: 4px;
  color: var(--el-text-color-regular);
  font-size: 12px;
  font-weight: 500;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-light);
}

.assignee-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.assignee-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--el-bg-color);
}

.assignee-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px var(--el-box-shadow-light);
}

.assignee-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.assignee-avatar {
  margin-right: 12px;
}

.assignee-info {
  flex: 1;
  min-width: 0;
}

.assignee-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.assignee-role-tag,
.assignee-dept-tag {
  margin-bottom: 4px;
}

.assignee-phone {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.assignee-stats {
  margin: 0 12px;
  min-width: 80px;
}

.stat-item {
  margin-bottom: 8px;
}

.stat-label {
  font-size: 11px;
  color: var(--el-text-color-placeholder);
  display: block;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 11px;
  color: var(--el-text-color-regular);
  margin-left: 4px;
}

.assignee-actions {
  margin-left: 8px;
}

.no-assignees {
  text-align: center;
  padding: 40px 20px;
  color: var(--el-text-color-placeholder);
}

.assignment-reason {
  margin-top: 20px;
}

.assignment-reason h5 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 600;
}
</style>