<template>
  <SseSidebar
    :visible="sseSidebar.isVisible.value"
    :title="sseSidebar.state.config.title"
    :subtitle="sseSidebar.state.config.subtitle"
    :empty-text="sseSidebar.state.config.emptyText"
    :show-footer="sseSidebar.state.config.showFooter"
    :auto-scroll="sseSidebar.state.config.autoScroll"
    :max-messages="sseSidebar.state.config.maxMessages"
    :close-on-overlay="sseSidebar.state.config.closeOnOverlay"
    @update:visible="handleVisibleChange"
    @close="handleClose"
    @reconnect="handleReconnect"
    @clear="handleClear"
    ref="sidebarRef"
  >
    <template #footer>
      <div class="global-footer-actions">
        <el-button size="small" @click="handleClear">
          <el-icon><Delete /></el-icon>
          清空消息
        </el-button>
        <el-button 
          size="small" 
          type="primary" 
          @click="handleReconnect" 
          :loading="sseSidebar.isConnecting.value"
          :disabled="!currentSseConfig"
        >
          <el-icon><Refresh /></el-icon>
          重新连接
        </el-button>
        <el-button 
          v-if="!sseSidebar.isConnected.value && currentSseConfig"
          size="small" 
          type="success" 
          @click="handleConnect"
          :loading="sseSidebar.isConnecting.value"
        >
          <el-icon><Connection /></el-icon>
          连接
        </el-button>
        <el-button 
          v-if="sseSidebar.isConnected.value"
          size="small" 
          type="danger" 
          @click="handleDisconnect"
        >
          <el-icon><Close /></el-icon>
          断开连接
        </el-button>
      </div>
    </template>
  </SseSidebar>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Delete, 
  Refresh, 
  Connection, 
  Close 
} from '@element-plus/icons-vue'
import SseSidebar from './index.vue'
import useSseSidebar, { type SseConfig } from './useSseSidebar'

// 定义组件名称
defineOptions({
  name: 'GlobalSseSidebar'
})

// 使用全局SSE侧边栏管理器
const sseSidebar = useSseSidebar()
const sidebarRef = ref<InstanceType<typeof SseSidebar>>()

// 当前SSE配置
const currentSseConfig = ref<SseConfig | null>(null)

// 事件处理
const handleVisibleChange = (visible: boolean) => {
  if (visible) {
    sseSidebar.show()
  } else {
    sseSidebar.hide()
  }
}

const handleClose = () => {
  sseSidebar.hide()
}

const handleReconnect = () => {
  if (currentSseConfig.value) {
    sseSidebar.reconnect()
  } else {
    ElMessage.warning('没有可用的连接配置')
  }
}

const handleConnect = () => {
  if (currentSseConfig.value) {
    sseSidebar.connect(currentSseConfig.value)
  } else {
    ElMessage.warning('没有可用的连接配置')
  }
}

const handleDisconnect = () => {
  sseSidebar.disconnect()
}

const handleClear = () => {
  sseSidebar.clearMessages()
}

// 监听SSE状态变化，同步到侧边栏组件
watch(
  () => sseSidebar.state.messages,
  (newMessages) => {
    if (sidebarRef.value) {
      // 清空侧边栏消息
      sidebarRef.value.clearMessages()
      // 添加新消息
      newMessages.forEach(message => {
        sidebarRef.value?.addMessage(message)
      })
    }
  },
  { deep: true }
)

watch(
  () => sseSidebar.state.currentStatus,
  (newStatus) => {
    if (sidebarRef.value) {
      sidebarRef.value.updateCurrentStatus(newStatus)
    }
  },
  { deep: true }
)

// 设置SSE配置
const setSseConfig = (config: SseConfig) => {
  currentSseConfig.value = config
}

// 快捷方法
const showWithConnection = (config: SseConfig, sidebarConfig?: Partial<typeof sseSidebar.state.config>) => {
  setSseConfig(config)
  sseSidebar.show(sidebarConfig)
  sseSidebar.connect(config)
}

// 监听连接状态变化
const unsubscribeConnectionChange = sseSidebar.onConnectionChange((status) => {
  if (status === 'connected') {
    ElMessage.success('SSE连接已建立')
  } else if (status === 'disconnected') {
    ElMessage.error('SSE连接已断开')
  }
})

// 监听错误
const unsubscribeError = sseSidebar.onError((error) => {
  console.error('SSE连接错误:', error)
  ElMessage.error('SSE连接发生错误')
})

// 组件卸载时清理
onUnmounted(() => {
  unsubscribeConnectionChange()
  unsubscribeError()
  sseSidebar.disconnect()
})

// 暴露方法给父组件
defineExpose({
  sseSidebar,
  setSseConfig,
  showWithConnection,
  sidebarRef
})
</script>

<style lang="scss" scoped>
.global-footer-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.global-footer-actions .el-button {
  flex: 1;
  min-width: 80px;
}

@media (max-width: 480px) {
  .global-footer-actions {
    flex-direction: column;
  }
  
  .global-footer-actions .el-button {
    width: 100%;
  }
}
</style>