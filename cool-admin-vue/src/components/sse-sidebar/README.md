# 全局SSE实时交互侧边栏组件

基于 AITaskGenerator.vue 的 SSE 实时交互功能开发的全局侧边栏组件，提供统一的 SSE 连接管理和消息展示功能。

## 功能特性

- 🚀 **全局状态管理** - 统一管理 SSE 连接状态和消息
- 🎨 **美观的UI设计** - 现代化的侧边栏界面，支持深色主题
- 🔄 **自动重连机制** - 连接断开时自动重连，支持指数退避
- 📱 **响应式设计** - 适配移动端和桌面端
- 🎭 **丰富的动画效果** - 消息动画、进度指示器、加载动画等
- 🔧 **高度可配置** - 支持自定义标题、样式、行为等
- 📝 **TypeScript支持** - 完整的类型定义

## 组件结构

```
src/components/sse-sidebar/
├── index.vue              # 基础SSE侧边栏组件
├── GlobalSseSidebar.vue   # 全局SSE侧边栏组件
├── useSseSidebar.ts       # SSE侧边栏管理器
├── index.ts               # 导出文件
└── README.md              # 使用文档
```

## 安装和配置

### 1. 全局注册（推荐）

在 `main.ts` 中注册：

```typescript
import { createApp } from 'vue'
import App from './App.vue'
import { installSseSidebar } from '@/components/sse-sidebar'

const app = createApp(App)

// 安装SSE侧边栏插件
installSseSidebar(app)

app.mount('#app')
```

### 2. 在根组件中添加全局侧边栏

在 `App.vue` 或布局组件中添加：

```vue
<template>
  <div id="app">
    <!-- 你的应用内容 -->
    <router-view />
    
    <!-- 全局SSE侧边栏 -->
    <GlobalSseSidebar ref="globalSseSidebarRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { GlobalSseSidebar } from '@/components/sse-sidebar'

const globalSseSidebarRef = ref()
</script>
```

## 基本使用

### 1. 在组件中使用

```vue
<template>
  <div>
    <el-button @click="startSseConnection">开始SSE连接</el-button>
    <el-button @click="showSidebar">显示侧边栏</el-button>
    <el-button @click="addTestMessage">添加测试消息</el-button>
  </div>
</template>

<script setup>
import { useSseSidebar } from '@/components/sse-sidebar'

const sseSidebar = useSseSidebar()

// 开始SSE连接
const startSseConnection = () => {
  sseSidebar.connect({
    url: '/api/sse/task-progress',
    autoReconnect: true,
    maxReconnectAttempts: 5,
    withCredentials: true
  })
  
  // 显示侧边栏
  sseSidebar.show({
    title: '任务进度',
    subtitle: '实时跟踪任务执行状态'
  })
}

// 显示侧边栏
const showSidebar = () => {
  sseSidebar.show()
}

// 添加测试消息
const addTestMessage = () => {
  sseSidebar.addMessage({
    content: '这是一条测试消息',
    type: 'info'
  })
}
</script>
```

### 2. 使用全局实例

```vue
<script setup>
import { getCurrentInstance } from 'vue'

const instance = getCurrentInstance()
const sseSidebar = instance?.appContext.config.globalProperties.$sseSidebar

// 或者使用 inject
// const sseSidebar = inject('sseSidebar')

const handleTaskStart = () => {
  sseSidebar.showWithConnection({
    url: '/api/sse/ai-task',
    autoReconnect: true
  }, {
    title: 'AI任务执行',
    subtitle: '正在生成任务...'
  })
}
</script>
```

## 高级用法

### 1. 监听SSE事件

```typescript
const sseSidebar = useSseSidebar()

// 监听消息
const unsubscribeMessage = sseSidebar.onMessage((message) => {
  console.log('收到新消息:', message)
})

// 监听连接状态变化
const unsubscribeConnection = sseSidebar.onConnectionChange((status) => {
  console.log('连接状态变化:', status)
})

// 监听状态更新
const unsubscribeStatus = sseSidebar.onStatusChange((status) => {
  console.log('当前状态:', status)
})

// 组件卸载时取消监听
onUnmounted(() => {
  unsubscribeMessage()
  unsubscribeConnection()
  unsubscribeStatus()
})
```

### 2. 自定义消息处理

```typescript
// 添加不同类型的消息
sseSidebar.addMessage({
  content: '任务开始执行',
  type: 'info'
})

sseSidebar.addMessage({
  content: '处理中...',
  type: 'processing',
  progress: 50
})

sseSidebar.addMessage({
  content: '任务完成',
  type: 'success'
})

sseSidebar.addMessage({
  content: '发生错误',
  type: 'error'
})

// 更新当前状态
sseSidebar.updateCurrentStatus({
  content: '正在分析数据...',
  progress: 75,
  type: 'processing'
})
```

### 3. 配置自定义SSE消息格式

如果你的后端SSE消息格式与默认格式不同，可以通过监听原始消息来自定义处理：

```typescript
// 连接SSE但不使用默认消息处理
const eventSource = new EventSource('/api/sse/custom')

eventSource.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data)
    
    // 自定义消息处理逻辑
    if (data.event === 'task_progress') {
      sseSidebar.updateCurrentStatus({
        content: data.message,
        progress: data.percentage,
        type: 'processing'
      })
    } else if (data.event === 'task_complete') {
      sseSidebar.addMessage({
        content: data.result,
        type: 'success'
      })
      sseSidebar.updateCurrentStatus(null)
    }
  } catch (error) {
    console.error('解析SSE消息失败:', error)
  }
}
```

## API 参考

### useSseSidebar()

返回全局SSE侧边栏管理器实例。

#### 方法

| 方法 | 参数 | 返回值 | 描述 |
|------|------|--------|------|
| `show(config?)` | `Partial<SidebarConfig>` | `void` | 显示侧边栏 |
| `hide()` | - | `void` | 隐藏侧边栏 |
| `toggle(config?)` | `Partial<SidebarConfig>` | `void` | 切换侧边栏显示状态 |
| `connect(config)` | `SseConfig` | `void` | 连接SSE |
| `disconnect()` | - | `void` | 断开SSE连接 |
| `reconnect()` | - | `void` | 重新连接SSE |
| `addMessage(message)` | `Omit<SseMessage, 'timestamp'>` | `void` | 添加消息 |
| `updateCurrentStatus(status)` | `CurrentStatus \| null` | `void` | 更新当前状态 |
| `clearMessages()` | - | `void` | 清空所有消息 |

#### 事件监听

| 方法 | 参数 | 返回值 | 描述 |
|------|------|--------|------|
| `onMessage(callback)` | `(message: SseMessage) => void` | `() => void` | 监听新消息 |
| `onStatusChange(callback)` | `(status: CurrentStatus \| null) => void` | `() => void` | 监听状态变化 |
| `onConnectionChange(callback)` | `(status: string) => void` | `() => void` | 监听连接状态变化 |
| `onOpen(callback)` | `() => void` | `() => void` | 监听连接打开 |
| `onClose(callback)` | `() => void` | `() => void` | 监听连接关闭 |
| `onError(callback)` | `(error: Event) => void` | `() => void` | 监听连接错误 |

#### 计算属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `isVisible` | `ComputedRef<boolean>` | 侧边栏是否可见 |
| `isConnected` | `ComputedRef<boolean>` | 是否已连接 |
| `isConnecting` | `ComputedRef<boolean>` | 是否正在连接 |
| `messageCount` | `ComputedRef<number>` | 消息数量 |
| `hasMessages` | `ComputedRef<boolean>` | 是否有消息 |

### 类型定义

```typescript
interface SseMessage {
  id?: string
  content: string
  timestamp: Date
  type?: 'info' | 'success' | 'warning' | 'error' | 'processing'
  progress?: number
  status?: 'pending' | 'processing' | 'completed' | 'error'
}

interface CurrentStatus {
  content: string
  progress?: number
  type?: 'info' | 'success' | 'warning' | 'error' | 'processing'
}

interface SseConfig {
  url: string
  withCredentials?: boolean
  headers?: Record<string, string>
  autoReconnect?: boolean
  maxReconnectAttempts?: number
  reconnectInterval?: number
}

interface SidebarConfig {
  title?: string
  subtitle?: string
  emptyText?: string
  showFooter?: boolean
  autoScroll?: boolean
  maxMessages?: number
  closeOnOverlay?: boolean
}
```

## 样式自定义

组件使用 CSS 变量，可以通过覆盖这些变量来自定义样式：

```css
:root {
  --sse-sidebar-width: 420px;
  --sse-sidebar-bg: var(--el-bg-color);
  --sse-sidebar-border: var(--el-border-color-light);
  --sse-sidebar-shadow: -8px 0 32px rgba(0, 0, 0, 0.12);
}

/* 深色主题 */
[data-theme="dark"] {
  --sse-sidebar-bg: var(--el-bg-color);
  --sse-sidebar-border: var(--el-border-color);
  --sse-sidebar-shadow: -8px 0 32px rgba(0, 0, 0, 0.3);
}
```

## 注意事项

1. **内存管理**: 记得在组件卸载时取消事件监听，避免内存泄漏
2. **连接管理**: SSE连接会自动重连，但建议在页面卸载时手动断开连接
3. **消息限制**: 默认最多保存100条消息，可通过 `maxMessages` 配置调整
4. **浏览器兼容性**: SSE需要现代浏览器支持，IE不支持
5. **网络状态**: 组件会自动处理网络断开重连，但建议配合网络状态检测使用

## 故障排除

### 连接失败
- 检查SSE端点URL是否正确
- 确认服务器支持SSE协议
- 检查跨域配置

### 消息不显示
- 确认消息格式是否正确
- 检查控制台是否有错误信息
- 验证SSE数据格式

### 样式问题
- 确认Element Plus样式已正确加载
- 检查CSS变量是否被覆盖
- 验证深色主题配置