<template>
  <div class="sse-sidebar-example">
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>SSE实时交互侧边栏示例</span>
          <el-tag :type="connectionStatusType">{{ connectionStatusText }}</el-tag>
        </div>
      </template>

      <div class="example-content">
        <!-- 基本操作 -->
        <el-row :gutter="16" class="action-row">
          <el-col :span="8">
            <el-button 
              type="primary" 
              @click="showSidebar"
              :icon="View"
              block
            >
              显示侧边栏
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button 
              type="success" 
              @click="connectSse"
              :icon="Connection"
              :loading="sseSidebar.isConnecting.value"
              :disabled="sseSidebar.isConnected.value"
              block
            >
              连接SSE
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button 
              type="danger" 
              @click="disconnectSse"
              :icon="Close"
              :disabled="!sseSidebar.isConnected.value"
              block
            >
              断开连接
            </el-button>
          </el-col>
        </el-row>

        <!-- 消息操作 -->
        <el-divider>消息操作</el-divider>
        <el-row :gutter="16" class="action-row">
          <el-col :span="6">
            <el-button 
              @click="addInfoMessage"
              :icon="InfoFilled"
              block
            >
              信息消息
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button 
              @click="addSuccessMessage"
              :icon="SuccessFilled"
              type="success"
              block
            >
              成功消息
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button 
              @click="addWarningMessage"
              :icon="WarningFilled"
              type="warning"
              block
            >
              警告消息
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button 
              @click="addErrorMessage"
              :icon="CircleCloseFilled"
              type="danger"
              block
            >
              错误消息
            </el-button>
          </el-col>
        </el-row>

        <!-- 进度操作 -->
        <el-divider>进度操作</el-divider>
        <el-row :gutter="16" class="action-row">
          <el-col :span="8">
            <el-button 
              @click="startProgressTask"
              :icon="Loading"
              type="warning"
              :disabled="isProgressRunning"
              block
            >
              开始进度任务
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button 
              @click="simulateAiTask"
              :icon="MagicStick"
              type="primary"
              :disabled="isAiTaskRunning"
              block
            >
              模拟AI任务
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button 
              @click="clearMessages"
              :icon="Delete"
              block
            >
              清空消息
            </el-button>
          </el-col>
        </el-row>

        <!-- 配置选项 -->
        <el-divider>配置选项</el-divider>
        <el-form :model="config" label-width="120px" size="small">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="侧边栏标题">
                <el-input v-model="config.title" placeholder="请输入标题" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="副标题">
                <el-input v-model="config.subtitle" placeholder="请输入副标题" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="空状态文本">
                <el-input v-model="config.emptyText" placeholder="请输入空状态文本" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大消息数">
                <el-input-number 
                  v-model="config.maxMessages" 
                  :min="10" 
                  :max="500" 
                  :step="10"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="显示底部">
                <el-switch v-model="config.showFooter" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="自动滚动">
                <el-switch v-model="config.autoScroll" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="点击遮罩关闭">
                <el-switch v-model="config.closeOnOverlay" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button @click="applyConfig" type="primary">应用配置</el-button>
            <el-button @click="resetConfig">重置配置</el-button>
          </el-form-item>
        </el-form>

        <!-- 统计信息 -->
        <el-divider>统计信息</el-divider>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="连接状态">
            <el-tag :type="connectionStatusType">{{ connectionStatusText }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="消息数量">
            <el-tag type="info">{{ sseSidebar.messageCount.value }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="侧边栏状态">
            <el-tag :type="sseSidebar.isVisible.value ? 'success' : 'info'">
              {{ sseSidebar.isVisible.value ? '显示' : '隐藏' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 事件日志 -->
    <el-card class="log-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>事件日志</span>
          <el-button size="small" @click="clearLogs" :icon="Delete">清空日志</el-button>
        </div>
      </template>
      <div class="log-content">
        <div 
          v-for="(log, index) in eventLogs" 
          :key="index"
          class="log-item"
          :class="`log-${log.type}`"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="eventLogs.length === 0" class="empty-logs">
          暂无事件日志
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  View,
  Connection,
  Close,
  InfoFilled,
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  Loading,
  MagicStick,
  Delete
} from '@element-plus/icons-vue'
import { useSseSidebar } from '../useSseSidebar'

// 定义组件名称
defineOptions({
  name: 'SseSidebarExample'
})

// 使用SSE侧边栏
const sseSidebar = useSseSidebar()

// 配置
const config = reactive({
  title: 'AI实时交互',
  subtitle: '实时任务进度跟踪',
  emptyText: 'AI正在准备中...',
  showFooter: true,
  autoScroll: true,
  maxMessages: 100,
  closeOnOverlay: true
})

// 状态
const isProgressRunning = ref(false)
const isAiTaskRunning = ref(false)
const eventLogs = ref<Array<{
  timestamp: Date
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}>>([])

// 计算属性
const connectionStatusType = computed(() => {
  if (sseSidebar.isConnected.value) return 'success'
  if (sseSidebar.isConnecting.value) return 'warning'
  return 'danger'
})

const connectionStatusText = computed(() => {
  if (sseSidebar.isConnected.value) return '已连接'
  if (sseSidebar.isConnecting.value) return '连接中'
  return '未连接'
})

// 方法
const addLog = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  eventLogs.value.push({
    timestamp: new Date(),
    message,
    type
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value.shift()
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const showSidebar = () => {
  sseSidebar.show(config)
  addLog('显示侧边栏', 'info')
}

const connectSse = () => {
  // 模拟SSE连接
  sseSidebar.connect({
    url: '/api/sse/demo', // 这是一个示例URL
    autoReconnect: true,
    maxReconnectAttempts: 3,
    withCredentials: true
  })
  addLog('尝试连接SSE', 'info')
}

const disconnectSse = () => {
  sseSidebar.disconnect()
  addLog('断开SSE连接', 'warning')
}

const addInfoMessage = () => {
  sseSidebar.addMessage({
    content: `这是一条信息消息 - ${new Date().toLocaleTimeString()}`,
    type: 'info'
  })
  addLog('添加信息消息', 'info')
}

const addSuccessMessage = () => {
  sseSidebar.addMessage({
    content: `操作成功完成 - ${new Date().toLocaleTimeString()}`,
    type: 'success'
  })
  addLog('添加成功消息', 'success')
}

const addWarningMessage = () => {
  sseSidebar.addMessage({
    content: `警告：请注意检查配置 - ${new Date().toLocaleTimeString()}`,
    type: 'warning'
  })
  addLog('添加警告消息', 'warning')
}

const addErrorMessage = () => {
  sseSidebar.addMessage({
    content: `错误：操作失败，请重试 - ${new Date().toLocaleTimeString()}`,
    type: 'error'
  })
  addLog('添加错误消息', 'error')
}

const startProgressTask = async () => {
  if (isProgressRunning.value) return
  
  isProgressRunning.value = true
  addLog('开始进度任务', 'info')
  
  try {
    // 显示侧边栏
    sseSidebar.show(config)
    
    // 模拟进度任务
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      sseSidebar.updateCurrentStatus({
        content: `正在处理任务... (${i}%)`,
        progress: i,
        type: 'processing'
      })
      
      if (i === 50) {
        sseSidebar.addMessage({
          content: '任务进度已过半',
          type: 'info',
          progress: i
        })
      }
    }
    
    // 完成任务
    sseSidebar.addMessage({
      content: '进度任务完成！',
      type: 'success'
    })
    
    sseSidebar.updateCurrentStatus(null)
    addLog('进度任务完成', 'success')
    
  } catch (error) {
    sseSidebar.addMessage({
      content: '任务执行失败',
      type: 'error'
    })
    addLog('进度任务失败', 'error')
  } finally {
    isProgressRunning.value = false
  }
}

const simulateAiTask = async () => {
  if (isAiTaskRunning.value) return
  
  isAiTaskRunning.value = true
  addLog('开始模拟AI任务', 'info')
  
  try {
    // 显示侧边栏
    sseSidebar.show({
      ...config,
      title: 'AI任务执行',
      subtitle: '正在生成智能内容...'
    })
    
    const tasks = [
      { message: '初始化AI模型...', progress: 10 },
      { message: '分析输入数据...', progress: 25 },
      { message: '生成候选方案...', progress: 45 },
      { message: '优化结果质量...', progress: 70 },
      { message: '格式化输出内容...', progress: 90 },
      { message: '任务完成！', progress: 100 }
    ]
    
    for (const task of tasks) {
      await new Promise(resolve => setTimeout(resolve, 800))
      
      if (task.progress < 100) {
        sseSidebar.updateCurrentStatus({
          content: task.message,
          progress: task.progress,
          type: 'processing'
        })
        
        sseSidebar.addMessage({
          content: task.message,
          type: 'info',
          progress: task.progress
        })
      } else {
        sseSidebar.addMessage({
          content: task.message,
          type: 'success'
        })
        sseSidebar.updateCurrentStatus(null)
      }
    }
    
    addLog('AI任务完成', 'success')
    
  } catch (error) {
    sseSidebar.addMessage({
      content: 'AI任务执行失败',
      type: 'error'
    })
    addLog('AI任务失败', 'error')
  } finally {
    isAiTaskRunning.value = false
  }
}

const clearMessages = () => {
  sseSidebar.clearMessages()
  addLog('清空所有消息', 'info')
}

const applyConfig = () => {
  // 配置会自动应用，因为使用的是响应式数据
  ElMessage.success('配置已应用')
  addLog('应用新配置', 'success')
}

const resetConfig = () => {
  Object.assign(config, {
    title: 'AI实时交互',
    subtitle: '实时任务进度跟踪',
    emptyText: 'AI正在准备中...',
    showFooter: true,
    autoScroll: true,
    maxMessages: 100,
    closeOnOverlay: true
  })
  ElMessage.info('配置已重置')
  addLog('重置配置', 'info')
}

const clearLogs = () => {
  eventLogs.value = []
}

// 监听SSE事件
const unsubscribeMessage = sseSidebar.onMessage((message) => {
  addLog(`收到消息: ${message.content}`, 'info')
})

const unsubscribeConnection = sseSidebar.onConnectionChange((status) => {
  addLog(`连接状态变化: ${status}`, status === 'connected' ? 'success' : 'warning')
})

const unsubscribeError = sseSidebar.onError((error) => {
  addLog('SSE连接错误', 'error')
})

// 组件挂载时添加欢迎日志
onMounted(() => {
  addLog('SSE侧边栏示例组件已加载', 'success')
})

// 组件卸载时清理
onUnmounted(() => {
  unsubscribeMessage()
  unsubscribeConnection()
  unsubscribeError()
  sseSidebar.disconnect()
  addLog('组件已卸载，清理资源', 'info')
})
</script>

<style lang="scss" scoped>
.sse-sidebar-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }
}

.example-content {
  .action-row {
    margin-bottom: 16px;
  }
}

.log-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 12px;
  background: var(--el-fill-color-extra-light);
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-family: 'Courier New', monospace;
  font-size: 13px;
  
  &:last-child {
    border-bottom: none;
  }
  
  .log-time {
    color: var(--el-text-color-secondary);
    min-width: 80px;
    font-weight: 500;
  }
  
  .log-message {
    flex: 1;
  }
  
  &.log-info {
    .log-message {
      color: var(--el-color-info);
    }
  }
  
  &.log-success {
    .log-message {
      color: var(--el-color-success);
    }
  }
  
  &.log-warning {
    .log-message {
      color: var(--el-color-warning);
    }
  }
  
  &.log-error {
    .log-message {
      color: var(--el-color-danger);
    }
  }
}

.empty-logs {
  text-align: center;
  color: var(--el-text-color-secondary);
  padding: 20px;
  font-style: italic;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .sse-sidebar-example {
    padding: 10px;
  }
  
  .action-row {
    .el-col {
      margin-bottom: 8px;
    }
  }
}
</style>