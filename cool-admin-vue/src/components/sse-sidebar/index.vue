<template>
  <div class="sse-sidebar-overlay" v-show="visible" @click="handleOverlayClick">
    <div class="sse-sidebar" :class="{ 'sidebar-visible': visible }" @click.stop>
      <!-- 侧边栏头部 -->
      <div class="sse-header">
        <div class="header-left">
          <div class="header-icon">🤖</div>
          <div class="header-title">
            <h4>{{ title || 'AI实时交互' }}</h4>
            <span class="header-subtitle">{{ subtitle || '实时任务进度跟踪' }}</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button 
            v-if="connectionStatus === 'connected'"
            size="small" 
            type="success" 
            text
            :icon="Connection"
          >
            已连接
          </el-button>
          <el-button 
            v-else-if="connectionStatus === 'connecting'"
            size="small" 
            type="warning" 
            text
            :icon="Loading"
            loading
          >
            连接中
          </el-button>
          <el-button 
            v-else
            size="small" 
            type="danger" 
            text
            :icon="Close"
          >
            已断开
          </el-button>
          <el-button 
            class="close-btn" 
            size="small" 
            :icon="Close" 
            @click="handleClose"
          />
        </div>
      </div>

      <!-- 侧边栏内容 -->
      <div class="sse-content">
        <el-scrollbar ref="scrollbarRef">
          <div class="messages-container">
            <!-- 消息列表 -->
            <div 
              v-for="(message, index) in messages" 
              :key="`msg-${index}-${message.timestamp}`"
              class="message-item"
              :class="getMessageClass(message, index)"
              :style="{ 'animation-delay': `${index * 0.1}s` }"
            >
              <div class="message-indicator">
                <div class="indicator-dot" :class="getIndicatorClass(message, index)">
                  <div class="dot-inner"></div>
                  <div class="dot-ripple"></div>
                  <!-- 处理中的加载动画 -->
                  <div v-if="getIndicatorClass(message, index) === 'processing'" class="processing-loader">
                    <div class="loader-dot" v-for="i in 3" :key="i" :style="{ animationDelay: `${i * 0.2}s` }"></div>
                  </div>
                </div>
                <div class="indicator-line" v-if="index < messages.length - 1"></div>
              </div>
              <div class="message-content-wrapper">
                <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                <div class="message-content">
                  <div class="message-text">{{ message.content }}</div>
                  <!-- 处理中的等待动画 -->
                  <div v-if="getMessageClass(message, index) === 'processing'" class="processing-animation">
                    <div class="waiting-dots">
                      <div class="waiting-dot" v-for="i in 3" :key="i" :style="{ animationDelay: `${i * 0.3}s` }"></div>
                    </div>
                    <div class="progress-wave"></div>
                  </div>
                  <div class="message-decoration"></div>
                </div>
                <!-- 进度条 -->
                <div v-if="message.progress !== undefined" class="message-progress">
                  <el-progress 
                    :percentage="message.progress" 
                    :stroke-width="6" 
                    :show-text="false"
                    :color="getProgressColor(message)"
                  />
                  <span class="progress-text">{{ message.progress }}%</span>
                </div>
              </div>
            </div>

            <!-- 当前状态显示 -->
            <div v-if="currentStatus && messages.length === 0" class="current-status enhanced-status">
              <div class="status-background-animation">
                <div class="particle" v-for="i in 15" :key="i" :style="{
                  animationDelay: `${i * 0.2}s`,
                  left: `${Math.random() * 100}%`,
                  animationDuration: `${3 + Math.random() * 2}s`
                }"></div>
              </div>
              <div class="status-message">
                <div class="message-time">{{ formatTime(new Date()) }}</div>
                <div class="message-content enhanced-content">
                  <div class="content-text">{{ currentStatus.content }}</div>
                  <div class="ai-thinking-indicator">
                    <div class="brain-icon">🧠</div>
                    <div class="thinking-waves">
                      <div class="wave"></div>
                      <div class="wave"></div>
                      <div class="wave"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="currentStatus.progress !== undefined" class="status-progress enhanced-progress">
                <div class="progress-glow"></div>
                <el-progress 
                  :percentage="currentStatus.progress" 
                  :stroke-width="6" 
                  :show-text="false"
                  :color="getProgressColor(currentStatus)"
                />
                <div class="progress-percentage">{{ currentStatus.progress }}%</div>
              </div>
            </div>

            <!-- 空状态提示 -->
            <div v-if="messages.length === 0 && !currentStatus" class="empty-messages enhanced-empty">
              <div class="empty-animation">
                <div class="ai-avatar">
                  <div class="avatar-circle">
                    <div class="avatar-inner">🤖</div>
                    <div class="avatar-ring"></div>
                    <div class="avatar-pulse"></div>
                  </div>
                </div>
                <div class="loading-dots enhanced-dots">
                  <div class="dot"></div>
                  <div class="dot"></div>
                  <div class="dot"></div>
                  <div class="dot"></div>
                  <div class="dot"></div>
                </div>
              </div>
              <p class="enhanced-text">{{ emptyText || 'AI正在准备中...' }}</p>
              <div class="preparation-steps">
                <div class="step active">
                  <div class="step-icon">⚡</div>
                  <span>初始化连接</span>
                </div>
                <div class="step">
                  <div class="step-icon">🔍</div>
                  <span>等待数据</span>
                </div>
                <div class="step">
                  <div class="step-icon">🎯</div>
                  <span>实时交互</span>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>

      <!-- 侧边栏底部操作 -->
      <div class="sse-footer" v-if="showFooter">
        <slot name="footer">
          <div class="footer-actions">
            <el-button size="small" @click="clearMessages">
              <el-icon><Delete /></el-icon>
              清空消息
            </el-button>
            <el-button size="small" type="primary" @click="reconnect" :loading="connectionStatus === 'connecting'">
              <el-icon><Refresh /></el-icon>
              重新连接
            </el-button>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElScrollbar } from 'element-plus'
import { 
  Close, 
  Connection, 
  Loading, 
  Delete, 
  Refresh 
} from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'SseSidebar'
})

// 消息类型定义
interface SseMessage {
  id?: string
  content: string
  timestamp: Date
  type?: 'info' | 'success' | 'warning' | 'error' | 'processing'
  progress?: number
  status?: 'pending' | 'processing' | 'completed' | 'error'
}

// 当前状态类型定义
interface CurrentStatus {
  content: string
  progress?: number
  type?: 'info' | 'success' | 'warning' | 'error' | 'processing'
}

// Props 定义
interface Props {
  visible?: boolean
  title?: string
  subtitle?: string
  emptyText?: string
  showFooter?: boolean
  autoScroll?: boolean
  maxMessages?: number
  closeOnOverlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: 'AI实时交互',
  subtitle: '实时任务进度跟踪',
  emptyText: 'AI正在准备中...',
  showFooter: true,
  autoScroll: true,
  maxMessages: 100,
  closeOnOverlay: true
})

// Emits 定义
interface Emits {
  'update:visible': [value: boolean]
  'close': []
  'reconnect': []
  'clear': []
}

const emit = defineEmits<Emits>()

// 响应式数据
const messages = ref<SseMessage[]>([])
const currentStatus = ref<CurrentStatus | null>(null)
const connectionStatus = ref<'connected' | 'connecting' | 'disconnected'>('disconnected')
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>()

// SSE 连接相关
let eventSource: EventSource | null = null
let reconnectTimer: NodeJS.Timeout | null = null
let reconnectAttempts = 0
const maxReconnectAttempts = 5

// 方法定义
const formatTime = (date: Date | string | number) => {
  const d = new Date(date)
  return d.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getMessageClass = (message: SseMessage, index: number) => {
  const classes = ['message-item']
  
  if (message.type) {
    classes.push(`${message.type}-message`)
  }
  
  if (message.status === 'processing' || message.type === 'processing') {
    classes.push('processing')
  }
  
  return classes.join(' ')
}

const getIndicatorClass = (message: SseMessage, index: number) => {
  if (message.status === 'processing' || message.type === 'processing') {
    return 'processing'
  }
  if (message.status === 'completed' || message.type === 'success') {
    return 'completed'
  }
  if (message.status === 'error' || message.type === 'error') {
    return 'error'
  }
  return 'default'
}

const getProgressColor = (item: SseMessage | CurrentStatus) => {
  if (item.type === 'error') return 'var(--el-color-danger)'
  if (item.type === 'success') return 'var(--el-color-success)'
  if (item.type === 'warning') return 'var(--el-color-warning)'
  return 'var(--el-color-primary)'
}

const scrollToBottom = async () => {
  if (!props.autoScroll || !scrollbarRef.value) return
  
  await nextTick()
  const scrollbar = scrollbarRef.value
  if (scrollbar && scrollbar.setScrollTop) {
    scrollbar.setScrollTop(scrollbar.wrapRef?.scrollHeight || 0)
  }
}

const addMessage = (message: Omit<SseMessage, 'timestamp'>) => {
  const newMessage: SseMessage = {
    ...message,
    timestamp: new Date()
  }
  
  messages.value.push(newMessage)
  
  // 限制消息数量
  if (messages.value.length > props.maxMessages) {
    messages.value.shift()
  }
  
  scrollToBottom()
}

const updateCurrentStatus = (status: CurrentStatus | null) => {
  currentStatus.value = status
  scrollToBottom()
}

const clearMessages = () => {
  messages.value = []
  currentStatus.value = null
  emit('clear')
}

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose()
  }
}

const connect = (url: string, options?: {
  withCredentials?: boolean
  headers?: Record<string, string>
}) => {
  if (eventSource) {
    eventSource.close()
  }
  
  connectionStatus.value = 'connecting'
  
  try {
    eventSource = new EventSource(url, {
      withCredentials: options?.withCredentials || false
    })
    
    eventSource.onopen = () => {
      connectionStatus.value = 'connected'
      reconnectAttempts = 0
      addMessage({
        content: '连接已建立',
        type: 'success'
      })
    }
    
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleSseMessage(data)
      } catch (error) {
        addMessage({
          content: event.data,
          type: 'info'
        })
      }
    }
    
    eventSource.onerror = () => {
      connectionStatus.value = 'disconnected'
      addMessage({
        content: '连接已断开',
        type: 'error'
      })
      
      // 自动重连
      if (reconnectAttempts < maxReconnectAttempts) {
        reconnectAttempts++
        reconnectTimer = setTimeout(() => {
          connect(url, options)
        }, Math.pow(2, reconnectAttempts) * 1000) // 指数退避
      }
    }
    
  } catch (error) {
    connectionStatus.value = 'disconnected'
    ElMessage.error('SSE连接失败')
  }
}

const disconnect = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
  }
  
  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
    reconnectTimer = null
  }
  
  connectionStatus.value = 'disconnected'
}

const reconnect = () => {
  emit('reconnect')
}

const handleSseMessage = (data: any) => {
  // 处理不同类型的SSE消息
  if (data.type === 'progress') {
    updateCurrentStatus({
      content: data.message || data.content,
      progress: data.progress,
      type: 'processing'
    })
  } else if (data.type === 'message') {
    addMessage({
      content: data.message || data.content,
      type: data.level || 'info',
      progress: data.progress
    })
  } else if (data.type === 'status') {
    addMessage({
      content: data.message || data.content,
      type: data.status === 'error' ? 'error' : 'info'
    })
  } else {
    // 默认处理
    addMessage({
      content: data.message || data.content || JSON.stringify(data),
      type: 'info'
    })
  }
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    scrollToBottom()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  disconnect()
})

// 暴露方法给父组件
defineExpose({
  addMessage,
  updateCurrentStatus,
  clearMessages,
  connect,
  disconnect,
  reconnect,
  scrollToBottom
})
</script>

<style lang="scss" scoped>
/* 遮罩层 */
.sse-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2000;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

/* 侧边栏主体 */
.sse-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 420px;
  height: 100vh;
  background: var(--el-bg-color);
  border-left: 1px solid var(--el-border-color-light);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.12);
  z-index: 2001;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);

  &.sidebar-visible {
    transform: translateX(0);
  }
}

/* 深色主题适配 */
.dark .sse-sidebar {
  background: var(--el-bg-color);
  border-left-color: var(--el-border-color);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.3);
}

/* 头部样式 */
.sse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
  color: white;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
    backdrop-filter: blur(10px);
    opacity: 0.9;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.header-icon {
  font-size: 24px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.header-title h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
  display: block;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: scale(1.05);
  }
}

/* 内容区域 */
.sse-content {
  flex: 1;
  overflow: hidden;
  padding: 20px 24px;
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 消息项样式 */
.message-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  position: relative;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.message-indicator {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 12px;
  z-index: 1;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--el-color-primary);
  border: 2px solid var(--el-color-primary-light-5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
  position: relative;

  &.processing {
    background: var(--el-color-warning);
    border-color: var(--el-color-warning-light-5);
    animation: processingPulse 2s ease-in-out infinite;
  }

  &.completed {
    background: var(--el-color-success);
    border-color: var(--el-color-success-light-5);
  }

  &.error {
    background: var(--el-color-danger);
    border-color: var(--el-color-danger-light-5);
  }
}

@keyframes processingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.dot-inner {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: white;
}

.dot-ripple {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid var(--el-color-primary);
  animation: ripple 2s ease-out infinite;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.processing-loader {
  position: absolute;
  display: flex;
  gap: 2px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loader-dot {
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background: white;
  animation: loaderBounce 1.4s ease-in-out infinite both;
}

@keyframes loaderBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.indicator-line {
  width: 2px;
  height: 50px;
  background: linear-gradient(to bottom, var(--el-color-info-light-3), var(--el-color-info-light-7));
  margin-top: 2px;
  position: relative;
}

.message-content-wrapper {
  flex: 1;
}

.message-time {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.message-content {
  position: relative;
  padding: 12px 16px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
  border-left: 3px solid var(--el-color-primary);
  transition: all 0.3s ease;

  &:hover {
    background: var(--el-fill-color-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  margin: 0;
}

.processing-animation {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.waiting-dots {
  display: flex;
  gap: 4px;
}

.waiting-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--el-color-primary);
  animation: waitingBounce 1.4s ease-in-out infinite both;
}

@keyframes waitingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.progress-wave {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--el-color-primary), transparent);
  border-radius: 1px;
  animation: waveMove 2s ease-in-out infinite;
}

@keyframes waveMove {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.message-decoration {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--el-color-primary-light-7), transparent);
}

.message-progress {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  min-width: 35px;
}

/* 消息类型样式 */
.info-message .message-content {
  border-left-color: var(--el-color-info);
}

.success-message .message-content {
  border-left-color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.warning-message .message-content {
  border-left-color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

.error-message .message-content {
  border-left-color: var(--el-color-danger);
  background: var(--el-color-danger-light-9);
}

.processing-message .message-content {
  border-left-color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

/* 当前状态样式 */
.current-status {
  position: relative;
  padding: 16px;
  background: linear-gradient(135deg, var(--el-fill-color-extra-light) 0%, var(--el-fill-color-light) 100%);
  border-radius: 12px;
  border: 1px solid var(--el-border-color-lighter);
  overflow: hidden;
  animation: statusPulse 3s ease-in-out infinite;
}

.status-background-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--el-color-primary);
  opacity: 0.6;
  animation: particleFloat 5s linear infinite;
}

.particle:nth-child(2n) {
  background: var(--el-color-success);
}

.particle:nth-child(3n) {
  background: var(--el-color-warning);
}

@keyframes particleFloat {
  0% {
    transform: translateY(100%) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
    transform: scale(1);
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100%) translateX(50px) scale(0);
    opacity: 0;
  }
}

@keyframes statusPulse {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
  }
  50% {
    box-shadow: 0 6px 30px rgba(64, 158, 255, 0.25);
  }
}

.status-message {
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
}

.enhanced-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-text {
  flex: 1;
  font-weight: 500;
}

.ai-thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.brain-icon {
  font-size: 18px;
  animation: brainPulse 2s ease-in-out infinite;
}

@keyframes brainPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.thinking-waves {
  display: flex;
  gap: 2px;
}

.wave {
  width: 3px;
  height: 12px;
  background: var(--el-color-primary);
  border-radius: 2px;
  animation: waveAnimation 1.5s ease-in-out infinite;
}

.wave:nth-child(2) {
  animation-delay: 0.2s;
}

.wave:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes waveAnimation {
  0%, 100% {
    transform: scaleY(0.3);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

.status-progress {
  margin-top: 8px;
  position: relative;
}

.enhanced-progress {
  position: relative;
  margin-top: 12px;
}

.progress-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-success), var(--el-color-primary));
  border-radius: 8px;
  opacity: 0.3;
  animation: progressGlow 2s ease-in-out infinite;
  z-index: 0;
}

@keyframes progressGlow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

.progress-percentage {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  font-size: 12px;
  font-weight: 600;
  color: var(--el-color-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* 空状态样式 */
.empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: var(--el-text-color-secondary);
}

.enhanced-empty {
  background: linear-gradient(135deg, var(--el-fill-color-extra-light) 0%, var(--el-fill-color-light) 100%);
  border-radius: 12px;
  border: 1px solid var(--el-border-color-lighter);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.empty-animation {
  margin-bottom: 20px;
  position: relative;
}

.ai-avatar {
  margin-bottom: 16px;
}

.avatar-circle {
  position: relative;
  display: inline-block;
}

.avatar-inner {
  font-size: 32px;
  display: block;
  position: relative;
  z-index: 2;
}

.avatar-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border: 2px solid var(--el-color-primary-light-7);
  border-radius: 50%;
  animation: avatarRotate 3s linear infinite;
}

.avatar-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  border: 1px solid var(--el-color-primary-light-8);
  border-radius: 50%;
  animation: avatarPulse 2s ease-in-out infinite;
}

@keyframes avatarRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes avatarPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.1;
  }
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-bottom: 16px;
}

.enhanced-dots .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--el-color-primary);
  animation: loading 1.4s ease-in-out infinite both;
}

.enhanced-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.enhanced-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.enhanced-dots .dot:nth-child(3) {
  animation-delay: 0s;
}

.enhanced-dots .dot:nth-child(4) {
  animation-delay: 0.16s;
}

.enhanced-dots .dot:nth-child(5) {
  animation-delay: 0.32s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.enhanced-text {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: var(--el-text-color-primary) !important;
  margin-bottom: 20px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.preparation-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
  text-align: left;
  max-width: 200px;
  margin-left: auto;
  margin-right: auto;
}

.step {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  opacity: 0.6;

  &.active {
    background: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary-light-6);
    opacity: 1;
    animation: stepPulse 2s ease-in-out infinite;
  }
}

@keyframes stepPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
  }
}

.step-icon {
  font-size: 16px;
  animation: stepIconSpin 3s linear infinite;
}

@keyframes stepIconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.step span {
  font-size: 12px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.step.active span {
  color: var(--el-color-primary);
  font-weight: 600;
}

/* 底部操作区域 */
.sse-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-fill-color-extra-light);
}

.footer-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .sse-sidebar {
    width: 100vw;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] {
  .sse-sidebar {
    background: var(--el-bg-color);
    border-left-color: var(--el-border-color);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.3);
  }

  .sse-header {
    background: var(--el-bg-color-page);
    border-bottom-color: var(--el-border-color);
  }

  .info-message .message-content {
    background: var(--el-color-info-light-9);
    border-left-color: var(--el-color-info);
    color: var(--el-color-info-light-3);
  }

  .error-message .message-content {
    background: var(--el-color-danger-light-9);
    border-left-color: var(--el-color-danger);
    color: var(--el-color-danger-light-3);
  }
}
</style>