import { ref, reactive, computed } from 'vue'
import type { App } from 'vue'

// SSE消息类型定义
export interface SseMessage {
  id?: string
  content: string
  timestamp: Date
  type?: 'info' | 'success' | 'warning' | 'error' | 'processing'
  progress?: number
  status?: 'pending' | 'processing' | 'completed' | 'error'
}

// 当前状态类型定义
export interface CurrentStatus {
  content: string
  progress?: number
  type?: 'info' | 'success' | 'warning' | 'error' | 'processing'
}

// SSE连接配置
export interface SseConfig {
  url: string
  withCredentials?: boolean
  headers?: Record<string, string>
  autoReconnect?: boolean
  maxReconnectAttempts?: number
  reconnectInterval?: number
}

// 侧边栏配置
export interface SidebarConfig {
  title?: string
  subtitle?: string
  emptyText?: string
  showFooter?: boolean
  autoScroll?: boolean
  maxMessages?: number
  closeOnOverlay?: boolean
}

// 全局状态
const globalState = reactive({
  visible: false,
  messages: [] as SseMessage[],
  currentStatus: null as CurrentStatus | null,
  connectionStatus: 'disconnected' as 'connected' | 'connecting' | 'disconnected',
  config: {
    title: 'AI实时交互',
    subtitle: '实时任务进度跟踪',
    emptyText: 'AI正在准备中...',
    showFooter: true,
    autoScroll: true,
    maxMessages: 100,
    closeOnOverlay: true
  } as SidebarConfig
})

// SSE连接实例
let eventSource: EventSource | null = null
let reconnectTimer: NodeJS.Timeout | null = null
let reconnectAttempts = 0
let currentConfig: SseConfig | null = null

// 事件回调
const callbacks = {
  onMessage: [] as Array<(message: SseMessage) => void>,
  onStatusChange: [] as Array<(status: CurrentStatus | null) => void>,
  onConnectionChange: [] as Array<(status: 'connected' | 'connecting' | 'disconnected') => void>,
  onOpen: [] as Array<() => void>,
  onClose: [] as Array<() => void>,
  onError: [] as Array<(error: Event) => void>
}

/**
 * 全局SSE侧边栏管理器
 */
export function useSseSidebar() {
  // 显示侧边栏
  const show = (config?: Partial<SidebarConfig>) => {
    if (config) {
      Object.assign(globalState.config, config)
    }
    globalState.visible = true
  }

  // 隐藏侧边栏
  const hide = () => {
    globalState.visible = false
  }

  // 切换侧边栏显示状态
  const toggle = (config?: Partial<SidebarConfig>) => {
    if (globalState.visible) {
      hide()
    } else {
      show(config)
    }
  }

  // 连接SSE
  const connect = (config: SseConfig) => {
    disconnect() // 先断开现有连接
    
    currentConfig = config
    globalState.connectionStatus = 'connecting'
    
    try {
      eventSource = new EventSource(config.url, {
        withCredentials: config.withCredentials || false
      })
      
      eventSource.onopen = () => {
        globalState.connectionStatus = 'connected'
        reconnectAttempts = 0
        
        addMessage({
          content: 'SSE连接已建立',
          type: 'success'
        })
        
        // 触发连接成功回调
        callbacks.onConnectionChange.forEach(cb => cb('connected'))
        callbacks.onOpen.forEach(cb => cb())
      }
      
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleSseMessage(data)
        } catch (error) {
          // 如果不是JSON格式，直接作为文本消息处理
          addMessage({
            content: event.data,
            type: 'info'
          })
        }
      }
      
      eventSource.onerror = (error) => {
        globalState.connectionStatus = 'disconnected'
        
        addMessage({
          content: 'SSE连接已断开',
          type: 'error'
        })
        
        // 触发错误回调
        callbacks.onConnectionChange.forEach(cb => cb('disconnected'))
        callbacks.onError.forEach(cb => cb(error))
        
        // 自动重连
        if (config.autoReconnect !== false && 
            reconnectAttempts < (config.maxReconnectAttempts || 5)) {
          reconnectAttempts++
          const interval = config.reconnectInterval || Math.pow(2, reconnectAttempts) * 1000
          
          reconnectTimer = setTimeout(() => {
            if (currentConfig) {
              connect(currentConfig)
            }
          }, interval)
        }
      }
      
    } catch (error) {
      globalState.connectionStatus = 'disconnected'
      console.error('SSE连接失败:', error)
    }
  }

  // 断开SSE连接
  const disconnect = () => {
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }
    
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    globalState.connectionStatus = 'disconnected'
    currentConfig = null
    reconnectAttempts = 0
    
    // 触发关闭回调
    callbacks.onClose.forEach(cb => cb())
  }

  // 重新连接
  const reconnect = () => {
    if (currentConfig) {
      connect(currentConfig)
    }
  }

  // 添加消息
  const addMessage = (message: Omit<SseMessage, 'timestamp'>) => {
    const newMessage: SseMessage = {
      ...message,
      id: message.id || `msg-${Date.now()}-${Math.random()}`,
      timestamp: new Date()
    }
    
    globalState.messages.push(newMessage)
    
    // 限制消息数量
    if (globalState.messages.length > globalState.config.maxMessages) {
      globalState.messages.shift()
    }
    
    // 触发消息回调
    callbacks.onMessage.forEach(cb => cb(newMessage))
  }

  // 更新当前状态
  const updateCurrentStatus = (status: CurrentStatus | null) => {
    globalState.currentStatus = status
    
    // 触发状态变化回调
    callbacks.onStatusChange.forEach(cb => cb(status))
  }

  // 清空消息
  const clearMessages = () => {
    globalState.messages = []
    globalState.currentStatus = null
  }

  // 处理SSE消息
  const handleSseMessage = (data: any) => {
    if (data.type === 'progress') {
      updateCurrentStatus({
        content: data.message || data.content,
        progress: data.progress,
        type: 'processing'
      })
    } else if (data.type === 'message') {
      addMessage({
        content: data.message || data.content,
        type: data.level || 'info',
        progress: data.progress
      })
    } else if (data.type === 'status') {
      addMessage({
        content: data.message || data.content,
        type: data.status === 'error' ? 'error' : 'info'
      })
    } else if (data.type === 'complete') {
      addMessage({
        content: data.message || data.content || '任务完成',
        type: 'success'
      })
      updateCurrentStatus(null)
    } else {
      // 默认处理
      addMessage({
        content: data.message || data.content || JSON.stringify(data),
        type: 'info'
      })
    }
  }

  // 事件监听器
  const onMessage = (callback: (message: SseMessage) => void) => {
    callbacks.onMessage.push(callback)
    return () => {
      const index = callbacks.onMessage.indexOf(callback)
      if (index > -1) {
        callbacks.onMessage.splice(index, 1)
      }
    }
  }

  const onStatusChange = (callback: (status: CurrentStatus | null) => void) => {
    callbacks.onStatusChange.push(callback)
    return () => {
      const index = callbacks.onStatusChange.indexOf(callback)
      if (index > -1) {
        callbacks.onStatusChange.splice(index, 1)
      }
    }
  }

  const onConnectionChange = (callback: (status: 'connected' | 'connecting' | 'disconnected') => void) => {
    callbacks.onConnectionChange.push(callback)
    return () => {
      const index = callbacks.onConnectionChange.indexOf(callback)
      if (index > -1) {
        callbacks.onConnectionChange.splice(index, 1)
      }
    }
  }

  const onOpen = (callback: () => void) => {
    callbacks.onOpen.push(callback)
    return () => {
      const index = callbacks.onOpen.indexOf(callback)
      if (index > -1) {
        callbacks.onOpen.splice(index, 1)
      }
    }
  }

  const onClose = (callback: () => void) => {
    callbacks.onClose.push(callback)
    return () => {
      const index = callbacks.onClose.indexOf(callback)
      if (index > -1) {
        callbacks.onClose.splice(index, 1)
      }
    }
  }

  const onError = (callback: (error: Event) => void) => {
    callbacks.onError.push(callback)
    return () => {
      const index = callbacks.onError.indexOf(callback)
      if (index > -1) {
        callbacks.onError.splice(index, 1)
      }
    }
  }

  // 计算属性
  const isVisible = computed(() => globalState.visible)
  const isConnected = computed(() => globalState.connectionStatus === 'connected')
  const isConnecting = computed(() => globalState.connectionStatus === 'connecting')
  const messageCount = computed(() => globalState.messages.length)
  const hasMessages = computed(() => globalState.messages.length > 0)

  return {
    // 状态
    state: globalState,
    isVisible,
    isConnected,
    isConnecting,
    messageCount,
    hasMessages,
    
    // 方法
    show,
    hide,
    toggle,
    connect,
    disconnect,
    reconnect,
    addMessage,
    updateCurrentStatus,
    clearMessages,
    
    // 事件监听
    onMessage,
    onStatusChange,
    onConnectionChange,
    onOpen,
    onClose,
    onError
  }
}

// 全局安装插件
export function installSseSidebar(app: App) {
  const sseSidebar = useSseSidebar()
  
  app.config.globalProperties.$sseSidebar = sseSidebar
  app.provide('sseSidebar', sseSidebar)
}

// 类型声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $sseSidebar: ReturnType<typeof useSseSidebar>
  }
}

export default useSseSidebar