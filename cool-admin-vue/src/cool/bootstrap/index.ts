import { createPinia } from 'pinia';
import { type App } from 'vue';
import { createModule } from './module';
import { router } from '../router';
import { Loading } from '../utils';
import { createEps } from './eps';
import 'virtual:svg-register';

export async function bootstrap(app: App) {
	try {
		// pinia
		app.use(createPinia());

		// 路由
		app.use(router);

		// 全局错误处理
		app.config.errorHandler = (err: any, instance: any, info: string) => {
			console.error('Vue Error Handler:', {
				error: err,
				message: err?.message,
				stack: err?.stack,
				instance: instance?.$?.type?.name || 'Unknown',
				info,
				timestamp: new Date().toISOString()
			});

			// 特殊处理CRUD组件相关错误
			if (err?.message?.includes('Cannot read properties of undefined')) {
				console.warn('Detected undefined property access, possibly CRUD component initialization issue');
				// 可以在这里添加重试逻辑或错误恢复机制
			}
		};

		// 模块
		const { eventLoop } = createModule(app);

		// eps
		createEps();

		// 加载
		Loading.set([eventLoop()]);

		console.log('COOL-ADMIN bootstrap completed successfully');
	} catch (error) {
		console.error('COOL-ADMIN bootstrap failed:', error);
		throw error;
	}
}
