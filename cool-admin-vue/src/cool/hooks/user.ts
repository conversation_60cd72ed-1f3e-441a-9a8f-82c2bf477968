import { computed } from 'vue';
import { useUserStore } from '/@/modules/base/store/user';

/**
 * 用户相关的组合式函数
 * 提供用户信息、登录状态等功能
 */
export function useUser() {
	const userStore = useUserStore();

	// 计算属性：用户信息
	const user = computed(() => userStore.info);

	// 计算属性：是否已登录
	const isLoggedIn = computed(() => !!userStore.token);

	// 计算属性：用户权限
	const permissions = computed(() => user.value?.permissions || []);

	// 计算属性：用户角色
	const roles = computed(() => user.value?.roles || []);

	// 登出方法
	const logout = () => {
		return userStore.logout();
	};

	// 获取用户信息方法
	const getUserInfo = () => {
		return userStore.get();
	};

	// 设置用户信息方法
	const setUserInfo = (info: any) => {
		userStore.set(info);
	};

	// 检查权限
	const hasPermission = (permission: string) => {
		return permissions.value.includes(permission);
	};

	// 检查角色
	const hasRole = (role: string) => {
		return roles.value.some((r: any) => r.value === role || r.name === role);
	};

	return {
		// 状态
		user,
		isLoggedIn,
		permissions,
		roles,
		// 方法
		logout,
		getUserInfo,
		setUserInfo,
		hasPermission,
		hasRole
	};
} 