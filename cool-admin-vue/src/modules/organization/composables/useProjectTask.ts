
import { ref, nextTick } from 'vue';
import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ElMessage } from 'element-plus';

/**
 * 共享项目任务视图的状态和逻辑
 */
export function useProjectTask() {
    const { service } = useCool();

    // --- 共享状态 ---

    // 视图
    const selectedProjectId = ref<number | undefined>(undefined);
    const projectOptions = ref<{ value: number; label: string }[]>([]);

    // 搜索
    const showAdvancedSearch = ref(false);
    const searchForm = ref({
        keyword: '',
        executionDateRange: [] as string[],
        assigneeName: '',
        assigneePhone: '',
        taskStatus: undefined as number | undefined,
        taskCategory: '',
    });

    // 任务操作对话框
    const showAssignDialog = ref(false);
    const currentAssignTask = ref<any>(null);
    const showCompleteDialog = ref(false);
    const currentCompleteTask = ref<any>(null);
    const showCloseDialog = ref(false);
    const currentCloseTask = ref<any>(null);
    const showAdjustTimeDialog = ref(false);
    const currentAdjustTimeTask = ref<any>(null);

    // --- CRUD 核心 ---

    const Crud = useCrud({
        service: service.organization.project.task,
        onRefresh(params, { next, done, render }) {
            if (!selectedProjectId.value) {
                render([]);
                done();
                return;
            }
            const requestParams: any = { ...params, projectId: selectedProjectId.value };
            
            // 合并搜索条件
            if (searchForm.value.keyword) requestParams.keyWord = searchForm.value.keyword;
            if (searchForm.value.executionDateRange && searchForm.value.executionDateRange.length === 2) {
                requestParams.executionStartDate = searchForm.value.executionDateRange[0];
                requestParams.executionEndDate = searchForm.value.executionDateRange[1];
            }
            if (searchForm.value.assigneeName) requestParams.assigneeName = searchForm.value.assigneeName;
            if (searchForm.value.assigneePhone) requestParams.assigneePhone = searchForm.value.assigneePhone;
            if (searchForm.value.taskStatus !== undefined && searchForm.value.taskStatus !== null) {
                requestParams.taskStatus = searchForm.value.taskStatus;
            }
            if (searchForm.value.taskCategory) requestParams.taskCategory = searchForm.value.taskCategory;
            
            next(requestParams);
        }
    });

    const Table = useTable({
        autoHeight: true,
        contextMenu: ['refresh'],
        columns: [
            { type: "selection", width: 60 },
            { label: "项目名称", prop: "projectName", minWidth: 150 },
            { label: "场景名称", prop: "scenarioName", minWidth: 150 },
            { label: "任务名称", prop: "name", minWidth: 160 },
            { label: "任务描述", prop: "description", minWidth: 200, showOverflowTooltip: true },
            {
                label: "状态",
                prop: "taskStatus",
                minWidth: 100,
                dict: [
                    { label: "待分配", value: 0, type: "info" },
                    { label: "待执行", value: 1, type: "warning" },
                    { label: "执行中", value: 2, type: "primary" },
                    { label: "已完成", value: 3, type: "success" },
                    { label: "已关闭", value: 4, type: "danger" }
                ]
            },
            { label: "执行人", prop: "assigneeName", minWidth: 150, showOverflowTooltip: true },
            { label: "执行人手机", prop: "assigneePhone", minWidth: 150, showOverflowTooltip: true },
            {
                label: "项目角色",
                prop: "projectRoleName",
                minWidth: 120,
                showOverflowTooltip: true,
                dict: [
                    { label: "项目负责人", value: "PROJECT_OWNER", type: "danger" },
                    { label: "项目管理员", value: "PROJECT_ADMIN", type: "warning" },
                    { label: "项目成员", value: "PROJECT_MEMBER", type: "primary" },
                    { label: "项目观察者", value: "PROJECT_VIEWER", type: "info" }
                ]
            },
            {
                label: "任务类型",
                prop: "taskCategory",
                minWidth: 120,
                dict: [
                    { label: "日常任务", value: "RC", type: "primary" },
                    { label: "周期任务", value: "ZQ", type: "success" },
                    { label: "临时任务", value: "LS", type: "warning" },
                    { label: "场景步骤", value: "SOP_STEP", type: "info" }
                ]
            },
            { label: "创建时间", prop: "createTime", minWidth: 160 },
            {
                type: "op",
                buttons: ["info", "edit", "delete"],
                width: 300,
                render: "slot-op-buttons" // 使用插槽渲染自定义操作按钮
            }
        ]
    });

    const Upsert = useUpsert({
        items: [
            { label: "任务名称", prop: "name", required: true, component: { name: "el-input" } },
            { label: "任务描述", prop: "description", component: { name: "el-input", props: { type: "textarea", rows: 3 } } },
            {
                label: "任务类型",
                prop: "taskCategory",
                component: {
                    name: "el-select",
                    options: [
                        { label: "日常任务", value: "RC" },
                        { label: "周期任务", value: "ZQ" },
                        { label: "临时任务", value: "LS" },
                        { label: "场景步骤", value: "SOP_STEP" }
                    ]
                }
            },
            {
                label: "优先级",
                prop: "priority",
                component: {
                    name: "el-select",
                    options: [
                        { label: "低", value: 1 },
                        { label: "普通", value: 2 },
                        { label: "中等", value: 3 },
                        { label: "高", value: 4 },
                        { label: "紧急", value: 5 }
                    ]
                }
            },
            {
                label: "任务状态",
                prop: "taskStatus",
                component: {
                    name: "el-select",
                    options: [
                        { label: "待分配", value: 0 },
                        { label: "待执行", value: 1 },
                        { label: "执行中", value: 2 },
                        { label: "已完成", value: 3 },
                        { label: "已关闭", value: 4 }
                    ]
                }
            }
        ],
        onSubmit(data, { next }) {
            if (!selectedProjectId.value) {
                ElMessage.warning("请先选择一个项目");
                return;
            }
            next({ ...data, projectId: selectedProjectId.value });
        }
    });

    // --- 共享方法 ---

    const getTodayRange = () => {
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        return [todayStr, todayStr];
    };

    const handleSearch = () => {
        if (!selectedProjectId.value) {
            ElMessage.warning('请先选择一个项目');
            return;
        }
        Crud.value?.refresh({ page: 1 });
    };

    const handleReset = () => {
        searchForm.value = {
            keyword: '',
            executionDateRange: getTodayRange(),
            assigneeName: '',
            assigneePhone: '',
            taskStatus: undefined,
            taskCategory: '',
        };
        if (selectedProjectId.value) {
            handleSearch();
        }
    };

    async function fetchProjectOptions() {
        try {
            console.log('开始获取项目列表...');
            // 尝试多种方式获取项目列表
            let response;
            try {
                // 首先尝试使用项目信息接口的list方法
                response = await service.organization.project.info.list();
                console.log('项目信息list接口响应:', response);
            } catch (e) {
                console.warn('项目信息list接口失败，尝试使用page接口:', e);
                try {
                    // 如果list失败，尝试使用page接口
                    response = await service.organization.project.info.page({ page: 1, size: 100 });
                    console.log('项目信息page接口响应:', response);
                } catch (e2) {
                    console.warn('项目信息page接口也失败，尝试任务接口:', e2);
                    // 最后尝试任务接口
                    response = await service.organization.project.task.accessibleProjects();
                    console.log('项目任务接口响应:', response);
                }
            }

            // 处理不同的响应格式
            let list: { value: number; label: string }[] = [];
            console.log('响应数据类型:', typeof response, '是否为数组:', Array.isArray(response));

            if (Array.isArray(response)) {
                // 如果直接返回数组
                console.log('处理数组格式响应，长度:', response.length);
                list = response.map(item => ({
                    value: item.projectId || item.value || item.id,
                    label: item.projectName || item.label || item.name || item.projectName
                }));
            } else if (response && response.data && Array.isArray(response.data)) {
                // 如果返回的是包装对象
                console.log('处理data包装格式响应，长度:', response.data.length);
                list = response.data.map(item => ({
                    value: item.projectId || item.value || item.id,
                    label: item.projectName || item.label || item.name || item.projectName
                }));
            } else if (response && Array.isArray(response.list)) {
                // 如果是分页数据
                console.log('处理list分页格式响应，长度:', response.list.length);
                list = response.list.map(item => ({
                    value: item.projectId || item.value || item.id,
                    label: item.projectName || item.label || item.name || item.projectName
                }));
            } else if (response && response.records && Array.isArray(response.records)) {
                // 如果是MyBatis Plus分页格式
                console.log('处理records分页格式响应，长度:', response.records.length);
                list = response.records.map(item => ({
                    value: item.projectId || item.value || item.id,
                    label: item.projectName || item.label || item.name || item.projectName
                }));
            } else {
                console.warn('未识别的响应格式:', response);
            }

            console.log('处理后的项目选项:', list);
            projectOptions.value = list;

            // 默认选中第一个项目
            if (list && list.length > 0) {
                selectedProjectId.value = list[0].value;
                console.log('默认选中项目ID:', selectedProjectId.value);

                // 等待CRUD组件初始化完成后再触发查询
                nextTick(() => {
                    setTimeout(() => {
                        if (Crud.value) {
                            console.log('CRUD组件已初始化，触发首次查询');
                            Crud.value.refresh();
                        }
                    }, 100);
                });
            } else {
                console.warn('没有可用的项目');
                ElMessage.warning('没有可用的项目，请联系管理员');
            }
        } catch (error) {
            ElMessage.error("获取项目列表失败");
            console.error('获取项目列表错误:', error);
        }
    }

    function onProjectChange() {
        if (selectedProjectId.value) {
             Crud.value?.refresh({ page: 1 });
        } else {
            Crud.value?.render([]);
        }
    }

    // 任务操作
    const handleAssignTask = (row: any) => { currentAssignTask.value = row; showAssignDialog.value = true; };
    const handleCompleteTask = (row: any) => { currentCompleteTask.value = row; showCompleteDialog.value = true; };
    const handleCloseTask = (row: any) => { currentCloseTask.value = row; showCloseDialog.value = true; };
    const handleAdjustTime = (row: any) => { currentAdjustTimeTask.value = row; showAdjustTimeDialog.value = true; };

    const handleAssignConfirm = async (data: { assigneeId?: number; assigneeIds?: number[]; reason: string }) => {
        const assigneeId = data.assigneeId || (data.assigneeIds && data.assigneeIds[0]);
        if (!currentAssignTask.value?.id || !assigneeId) return ElMessage.warning('请选择任务和执行人');
        try {
            await service.task.assignment.manualAssignTask({ taskId: currentAssignTask.value.id, assigneeIds: [assigneeId], reason: data.reason });
            ElMessage.success('任务分配成功');
            showAssignDialog.value = false;
            Crud.value?.refresh();
        } catch (error) { ElMessage.error(error.message); console.error(error); }
    };

    const handleCompleteConfirm = async (data: { taskId: number; assigneeId: number }) => {
        try {
            await service.task.status.completeTaskExecution({ taskId: data.taskId, assigneeId: data.assigneeId });
            ElMessage.success('任务已完成');
            showCompleteDialog.value = false;
            Crud.value?.refresh();
        } catch (error) { ElMessage.error(error.message); console.error(error); }
    };

    const handleCloseConfirm = async (data: { taskId: number; closeReason: string }) => {
        if (!data.taskId || !data.closeReason) return ElMessage.warning('请输入关闭原因');
        try {
            await service.task.status.closeTask({ taskId: data.taskId, closeReason: data.closeReason, operatorId: currentCloseTask.value.assigneeId, operatorName: currentCloseTask.value.assigneeName });
            ElMessage.success('任务已关闭');
            showCloseDialog.value = false;
            Crud.value?.refresh();
        } catch (error) { ElMessage.error('任务关闭失败'); console.error(error); }
    };

    const handleAdjustTimeConfirm = async (data: { taskId: number; startTime: string; endTime: string }) => {
        if (!data.taskId || !data.startTime || !data.endTime) return ElMessage.warning('请选择任务和完整的起止时间');
        try {
            await service.task.info.batchUpdateTaskTime({ taskIds: [data.taskId], startTime: data.startTime, endTime: data.endTime });
            ElMessage.success('任务时间调整成功');
            showAdjustTimeDialog.value = false;
            Crud.value?.refresh();
        } catch (error) { ElMessage.error('任务时间调整失败'); console.error(error); }
    };

    // 初始化方法
    const initialize = () => {
        searchForm.value.executionDateRange = getTodayRange();
        fetchProjectOptions();
    };

    return {
        Crud,
        Table,
        Upsert,
        selectedProjectId,
        projectOptions,
        onProjectChange,
        searchForm,
        showAdvancedSearch,
        handleSearch,
        handleReset,
        // 对话框状态
        showAssignDialog,
        currentAssignTask,
        showCompleteDialog,
        currentCompleteTask,
        showCloseDialog,
        currentCloseTask,
        showAdjustTimeDialog,
        currentAdjustTimeTask,
        // 对话框事件
        handleAssignTask,
        handleAssignConfirm,
        handleCompleteTask,
        handleCompleteConfirm,
        handleCloseTask,
        handleCloseConfirm,
        handleAdjustTime,
        handleAdjustTimeConfirm,
        // 初始化方法
        initialize,
        fetchProjectOptions,
        getTodayRange
    };
}
