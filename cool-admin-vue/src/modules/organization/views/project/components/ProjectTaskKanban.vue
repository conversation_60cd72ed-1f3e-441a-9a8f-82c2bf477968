<template>
    <div class="project-task-kanban">
        <!-- 搜索区域 -->
        <div class="kanban-search">
             <div class="basic-search">
                <el-form :model="searchForm" inline @submit.prevent="handleSearch">
                    <el-form-item label="任务名称">
                        <el-input v-model="searchForm.keyword" placeholder="请输入任务名称" clearable style="width: 200px" @keyup.enter="handleSearch" />
                    </el-form-item>
                    <el-form-item label="执行时间">
                        <el-date-picker v-model="searchForm.executionDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 240px" @change="handleSearch" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">
                            <el-icon><Search /></el-icon>搜索
                        </el-button>
                        <el-button @click="handleReset">重置</el-button>
                        <el-button type="text" @click="showAdvancedSearch = !showAdvancedSearch" style="margin-left: 8px">
                            高级搜索
                            <el-icon>
                                <ArrowDown v-if="!showAdvancedSearch" />
                                <ArrowUp v-else />
                            </el-icon>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div v-show="showAdvancedSearch" class="advanced-search">
                <el-form :model="searchForm" inline @submit.prevent="handleSearch">
                    <el-form-item label="执行人">
                        <el-input v-model="searchForm.assigneeName" placeholder="请输入执行人姓名" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="执行人手机">
                        <el-input v-model="searchForm.assigneePhone" placeholder="请输入手机号" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="任务状态">
                        <el-select v-model="searchForm.taskStatus" placeholder="请选择状态" clearable style="width: 150px">
                            <el-option label="待分配" :value="0" />
                            <el-option label="待执行" :value="1" />
                            <el-option label="执行中" :value="2" />
                            <el-option label="已完成" :value="3" />
                            <el-option label="已关闭" :value="4" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="任务类型">
                        <el-select v-model="searchForm.taskCategory" placeholder="请选择类型" clearable style="width: 150px">
                            <el-option label="日常任务" value="RC" />
                            <el-option label="周期任务" value="ZQ" />
                            <el-option label="临时任务" value="LS" />
                            <el-option label="场景步骤" value="SOP_STEP" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 看板主体 -->
        <div class="kanban-board">
            <div v-for="status in taskStatuses" :key="status.value" class="kanban-column">
                <div class="column-header">
                    <span class="column-title">{{ status.label }}</span>
                    <el-tag :type="status.type" size="small">{{ kanbanData[status.value]?.pagination.total || 0 }}</el-tag>
                </div>
                <div class="column-body">
                    <div v-for="task in kanbanData[status.value]?.list || []" :key="task.id" class="task-card" @click="viewTaskDetail(task)">
                        <div class="task-header">
                            <h4 class="task-title">{{ task.name }}</h4>
                            <el-dropdown trigger="click" @command="(command) => handleTaskAction(command, task)">
                                <el-button type="text" size="small"><el-icon><MoreFilled /></el-icon></el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                                        <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                        <div class="task-content">
                            <p class="task-description">{{ task.description }}</p>
                            <div class="task-meta">
                                <div class="meta-item"><el-icon><Calendar /></el-icon><span>{{ formatDate(task.startTime) }}</span></div>
                                <div v-if="task.assigneeName" class="meta-item"><el-icon><User /></el-icon><span>{{ task.assigneeName }}</span></div>
                                <div v-if="task.projectRoleName" class="meta-item"><el-tag size="small" :type="getRoleType(task.projectRoleName)">{{ task.projectRoleName }}</el-tag></div>
                            </div>
                            <div class="task-footer">
                                <el-tag size="small" :type="getPriorityType(task.priority)">优先级: {{ getPriorityText(task.priority) }}</el-tag>
                                <el-tag size="small" :type="getCategoryType(task.taskCategory)">{{ getCategoryText(task.taskCategory) }}</el-tag>
                            </div>
                            <div class="task-actions">
                                <el-button type="primary" text size="small" @click.stop="handleAssignTask(task)">分配</el-button>
                                <el-button type="success" text size="small" @click.stop="handleCompleteTask(task)">完成</el-button>
                                <el-button type="danger" text size="small" @click.stop="handleCloseTask(task)">关闭</el-button>
                                <el-button type="warning" text size="small" @click.stop="handleAdjustTime(task)">调整时间</el-button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 加载更多 -->
                    <div class="load-more-container" v-if="hasMore(status.value)">
                        <el-button :loading="isLoading[status.value]" @click="() => fetchKanbanData(status.value, true)" link>加载更多</el-button>
                    </div>

                    <div v-if="!kanbanData[status.value] || kanbanData[status.value].list.length === 0" class="empty-column">
                        <el-empty description="暂无任务" :image-size="60" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 隐藏的 Upsert 用于操作 -->
        <cl-upsert ref="Upsert" />

         <!-- 对话框 -->
        <AssigneeSelector v-model="showAssignDialog" :taskName="currentAssignTask?.name" :taskTags="currentAssignTask?.taskTags" filterMode="project" :contextId="selectedProjectId" @confirm="handleAssignConfirm" />
        <TaskCompleter v-model="showCompleteDialog" :taskId="currentCompleteTask?.id" :assigneeId="currentCompleteTask?.assigneeId" @confirm="handleCompleteConfirm" />
        <TaskCloser v-model="showCloseDialog" :taskId="currentCloseTask?.id" @confirm="handleCloseConfirm" />
        <TaskTimeAdjuster v-model="showAdjustTimeDialog" :taskId="currentAdjustTimeTask?.id" :initialStartTime="currentAdjustTimeTask?.startTime" :initialEndTime="currentAdjustTimeTask?.endTime" @confirm="handleAdjustTimeConfirm" />
    </div>
</template>

<script setup lang="ts">
import { defineProps, ref, watch, onMounted } from 'vue';
import { useCool } from "/@/cool";
import { useProjectTask } from '../composables/useProjectTask';
import { Search, ArrowDown, ArrowUp, MoreFilled, Calendar, User } from '@element-plus/icons-vue';
import AssigneeSelector from '/@/components/assignee-selector/AssigneeSelector.vue';
import TaskTimeAdjuster from '/@/modules/task/components/TaskTimeAdjuster.vue';
import TaskCloser from '/@/modules/task/components/TaskCloser.vue';
import TaskCompleter from '/@/modules/task/components/TaskCompleter.vue';

defineOptions({ name: "project-task-kanban" });

const props = defineProps<{ projectTask: ReturnType<typeof useProjectTask> }>();
const { service } = useCool();

const {
    Crud,
    Upsert,
    selectedProjectId,
    searchForm,
    showAdvancedSearch,
    handleSearch: searchAction, // 从hook重命名，避免命名冲突
    handleReset: resetAction,   // 从hook重命名
    showAssignDialog,
    currentAssignTask,
    showCompleteDialog,
    currentCompleteTask,
    showCloseDialog,
    currentCloseTask,
    showAdjustTimeDialog,
    currentAdjustTimeTask,
    handleAssignTask,
    handleCompleteTask,
    handleCloseTask,
    handleAdjustTime,
    handleAssignConfirm,
    handleCompleteConfirm,
    handleCloseConfirm,
    handleAdjustTimeConfirm
} = props.projectTask;

// --- 看板专用状态 ---
type KanbanColumnData = { list: any[]; pagination: { page: number; size: number; total: number } };
const kanbanData = ref<Record<string, KanbanColumnData>>({});
const isLoading = ref<Record<string, boolean>>({});

const taskStatuses = [
    { value: 0, label: '待分配', type: 'info' },
    { value: 1, label: '待执行', type: 'warning' },
    { value: 2, label: '执行中', type: 'primary' },
    { value: 3, label: '已完成', type: 'success' },
    { value: 4, label: '已关闭', type: 'danger' }
];

// --- 数据获取 ---

function initializeKanbanData() {
    taskStatuses.forEach(status => {
        kanbanData.value[status.value] = { list: [], pagination: { page: 1, size: 10, total: 0 } };
        isLoading.value[status.value] = false;
    });
}

async function fetchKanbanData(statusValue?: number, isLoadMore = false) {
    if (!selectedProjectId.value) return;

    const statusesToFetch = statusValue !== undefined ? [statusValue] : taskStatuses.map(s => s.value);

    for (const status of statusesToFetch) {
        if (isLoading.value[status]) continue;

        isLoading.value[status] = true;
        const currentPage = isLoadMore ? kanbanData.value[status].pagination.page + 1 : 1;

        try {
            const params = {
                projectId: selectedProjectId.value,
                taskStatus: status,
                page: currentPage,
                size: 10,
                keyWord: searchForm.value.keyword,
                executionStartDate: searchForm.value.executionDateRange?.[0],
                executionEndDate: searchForm.value.executionDateRange?.[1],
                assigneeName: searchForm.value.assigneeName,
                assigneePhone: searchForm.value.assigneePhone,
                taskCategory: searchForm.value.taskCategory,
            };

            // [重要] 假设新的后端服务路径为 service.organization.project.task.kanbanPage
            const res = await service.organization.project.task.kanbanPage(params);

            if (isLoadMore) {
                kanbanData.value[status].list.push(...res.list);
            } else {
                kanbanData.value[status].list = res.list;
            }
            kanbanData.value[status].pagination = res.pagination;

        } catch (err) {
            console.error(`Failed to fetch data for status ${status}`, err);
        } finally {
            isLoading.value[status] = false;
        }
    }
}

const handleSearch = () => {
    initializeKanbanData();
    fetchKanbanData();
};

const handleReset = () => {
    resetAction(); // 调用hook中的重置方法
    // 重新初始化看板数据并获取
    initializeKanbanData();
    fetchKanbanData();
};

// --- 监听器 ---
watch(selectedProjectId, () => {
    handleSearch();
});

onMounted(() => {
    initializeKanbanData();
    if (selectedProjectId.value) {
        handleSearch();
    }
});

// --- 模板辅助方法 ---

const hasMore = (status: number) => {
    const col = kanbanData.value[status];
    if (!col) return false;
    return col.pagination.page < Math.ceil(col.pagination.total / col.pagination.size);
};

const viewTaskDetail = (task: any) => Upsert.value?.info(task);
const handleTaskAction = (command: string, task: any) => {
    if (command === 'edit') Upsert.value?.edit(task);
    // 删除操作需要刷新看板
    if (command === 'delete') {
        props.projectTask.Crud.value?.rowDelete(task, {
            onSuccess: () => {
                // 只刷新当前列
                fetchKanbanData(task.taskStatus);
            }
        });
    }
};

const formatDate = (d: string) => d ? new Date(d).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }) : '';
const getPriorityType = (p: number) => ['', 'success', 'info', 'warning', 'danger', 'danger'][p] || 'info';
const getPriorityText = (p: number) => ['', '低', '普通', '中等', '高', '紧急'][p] || '普通';
const getCategoryType = (c: string) => ({ RC: 'primary', ZQ: 'success', LS: 'warning', SOP_STEP: 'info' }[c] || 'info');
const getCategoryText = (c: string) => ({ RC: '日常任务', ZQ: '周期任务', LS: '临时任务', SOP_STEP: '场景步骤' }[c] || c);
const getRoleType = (r: string) => ({ '项目负责人': 'danger', '项目管理员': 'warning', '项目成员': 'primary', '项目观察者': 'info' }[r] || 'info');

</script>

<style lang="scss" scoped>
.project-task-kanban {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .kanban-search {
        flex-shrink: 0;
        margin-bottom: 16px;
        background: var(--el-bg-color-page);
        border-radius: 8px;
        padding: 16px;
        border: 1px solid var(--el-border-color-light);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

        .el-form-item { margin-bottom: 0; }
        .advanced-search { margin-top: 16px; padding-top: 16px; border-top: 1px solid var(--el-border-color-lighter); }
    }

    .kanban-board {
        flex: 1;
        display: flex;
        gap: 16px;
        overflow-x: auto;
        padding-bottom: 16px;

        .kanban-column {
            width: 320px;
            flex-shrink: 0;
            background: var(--el-fill-color-light);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            max-height: 100%;

            .column-header {
                padding: 12px 16px;
                border-bottom: 1px solid var(--el-border-color-lighter);
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-shrink: 0;
                .column-title { font-weight: 600; }
            }

            .column-body {
                flex: 1;
                padding: 8px;
                overflow-y: auto;

                .task-card {
                    background: var(--el-bg-color);
                    border: 1px solid var(--el-border-color-lighter);
                    border-radius: 6px;
                    padding: 12px;
                    margin: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

                     &:hover {
                        border-color: var(--el-color-primary);
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    }

                    .task-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        margin-bottom: 8px;
                        .task-title { font-size: 14px; font-weight: 600; margin: 0; flex: 1; line-height: 1.4; }
                    }

                    .task-content {
                        .task-description { font-size: 12px; color: var(--el-text-color-regular); margin: 0 0 12px 0; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; line-height: 1.5;}
                        .task-meta { 
                            margin-bottom: 12px;
                            .meta-item { display: flex; align-items: center; font-size: 12px; color: var(--el-text-color-regular); margin-bottom: 4px; .el-icon { margin-right: 4px; } }
                        }
                        .task-footer { display: flex; gap: 6px; flex-wrap: wrap; margin-bottom: 8px; }
                        .task-actions {
                            display: flex;
                            gap: 4px;
                            flex-wrap: wrap;
                            border-top: 1px solid var(--el-border-color-lighter);
                            padding-top: 8px;
                            margin-top: 8px;
                        }
                    }
                }

                .load-more-container {
                    text-align: center;
                    padding: 10px;
                }

                .empty-column {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 150px;
                }
            }
        }
    }
}
</style>