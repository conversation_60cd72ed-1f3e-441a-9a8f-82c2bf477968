
<template>
    <div class="project-task-list">
        <!-- 搜索区域 -->
        <div class="search-area">
            <div class="basic-search">
                <el-form :model="searchForm" inline>
                    <el-form-item label="任务名称">
                        <el-input v-model="searchForm.keyword" placeholder="请输入任务名称" clearable style="width: 200px" @keyup.enter="handleSearch" />
                    </el-form-item>
                    <el-form-item label="执行时间">
                        <el-date-picker v-model="searchForm.executionDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 240px" @change="handleSearch" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">
                            <el-icon><Search /></el-icon>搜索
                        </el-button>
                        <el-button @click="handleReset">重置</el-button>
                        <el-button type="text" @click="showAdvancedSearch = !showAdvancedSearch" style="margin-left: 8px">
                            高级搜索
                            <el-icon>
                                <ArrowDown v-if="!showAdvancedSearch" />
                                <ArrowUp v-else />
                            </el-icon>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div v-show="showAdvancedSearch" class="advanced-search">
                <el-form :model="searchForm" inline>
                    <el-form-item label="执行人">
                        <el-input v-model="searchForm.assigneeName" placeholder="请输入执行人姓名" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="执行人手机">
                        <el-input v-model="searchForm.assigneePhone" placeholder="请输入手机号" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="任务状态">
                        <el-select v-model="searchForm.taskStatus" placeholder="请选择状态" clearable style="width: 150px">
                            <el-option label="待分配" :value="0" />
                            <el-option label="待执行" :value="1" />
                            <el-option label="执行中" :value="2" />
                            <el-option label="已完成" :value="3" />
                            <el-option label="已关闭" :value="4" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="任务类型">
                        <el-select v-model="searchForm.taskCategory" placeholder="请选择类型" clearable style="width: 150px">
                            <el-option label="日常任务" value="RC" />
                            <el-option label="周期任务" value="ZQ" />
                            <el-option label="临时任务" value="LS" />
                            <el-option label="场景步骤" value="SOP_STEP" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <cl-crud :ref="Crud">
            <cl-row>
                <cl-refresh-btn />
                <cl-add-btn />
                <cl-multi-delete-btn />
                <cl-flex1 />
            </cl-row>

            <cl-row>
                <cl-table :ref="Table">
                    <template #slot-op-buttons="{ scope }">
                        <el-button type="primary" text bg @click="handleAssignTask(scope.row)">调整分配</el-button>
                        <el-button type="success" text bg @click="handleCompleteTask(scope.row)">完成</el-button>
                        <el-button type="danger" text bg @click="handleCloseTask(scope.row)">关闭</el-button>
                        <el-button type="warning" text bg @click="handleAdjustTime(scope.row)">调整时间</el-button>
                    </template>
                </cl-table>
            </cl-row>

            <cl-row>
                <cl-flex1 />
                <cl-pagination />
            </cl-row>

            <cl-upsert :ref="Upsert" />
        </cl-crud>

        <!-- 对话框 -->
        <AssigneeSelector v-model="showAssignDialog" :taskName="currentAssignTask?.name" :taskTags="currentAssignTask?.taskTags" filterMode="project" :contextId="selectedProjectId" @confirm="handleAssignConfirm" />
        <TaskCompleter v-model="showCompleteDialog" :taskId="currentCompleteTask?.id" :assigneeId="currentCompleteTask?.assigneeId" @confirm="handleCompleteConfirm" />
        <TaskCloser v-model="showCloseDialog" :taskId="currentCloseTask?.id" @confirm="handleCloseConfirm" />
        <TaskTimeAdjuster v-model="showAdjustTimeDialog" :taskId="currentAdjustTimeTask?.id" :initialStartTime="currentAdjustTimeTask?.startTime" :initialEndTime="currentAdjustTimeTask?.endTime" @confirm="handleAdjustTimeConfirm" />
    </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { useProjectTask } from '../composables/useProjectTask';
import AssigneeSelector from '/@/components/assignee-selector/AssigneeSelector.vue';
import TaskTimeAdjuster from '/@/modules/task/components/TaskTimeAdjuster.vue';
import TaskCloser from '/@/modules/task/components/TaskCloser.vue';
import TaskCompleter from '/@/modules/task/components/TaskCompleter.vue';

defineOptions({ name: "project-task-list" });

// 从父组件接收共享的composable实例
const props = defineProps<{ projectTask: ReturnType<typeof useProjectTask> }>();

// 直接从props解构，保持响应性
const {
    Crud,
    Table,
    Upsert,
    selectedProjectId,
    searchForm,
    showAdvancedSearch,
    handleSearch,
    handleReset,
    showAssignDialog,
    currentAssignTask,
    showCompleteDialog,
    currentCompleteTask,
    showCloseDialog,
    currentCloseTask,
    showAdjustTimeDialog,
    currentAdjustTimeTask,
    handleAssignTask,
    handleAssignConfirm,
    handleCompleteTask,
    handleCompleteConfirm,
    handleCloseTask,
    handleCloseConfirm,
    handleAdjustTime,
    handleAdjustTimeConfirm
} = props.projectTask;

// 定制列表的操作按钮
Table.value.columns.find(c => c.type === 'op').buttons = ["info", "edit", "delete", "slot-op-buttons"];

</script>

<style lang="scss" scoped>
.project-task-list {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .search-area {
        flex-shrink: 0;
        margin-bottom: 16px;
        background: var(--el-bg-color-page);
        border-radius: 8px;
        padding: 16px;
        border: 1px solid var(--el-border-color-light);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

        .el-form-item {
            margin-bottom: 0;
        }

        .advanced-search {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid var(--el-border-color-lighter);
        }
    }

    :deep(.cl-crud) {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;

        .cl-row {
            &:nth-child(2) { // Table row
                flex: 1;
                min-height: 0;
            }
        }
    }
}
</style>
