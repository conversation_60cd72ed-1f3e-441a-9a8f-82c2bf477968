<template>
  <div class="project-list">
    <cl-crud ref="Crud">
      <cl-row>
        <!-- 刷新按钮 -->
        <cl-refresh-btn />
        <!-- 新增按钮 -->
        <cl-add-btn />
        <!-- 删除按钮 -->
        <cl-multi-delete-btn />
        <!-- 导入按钮 -->
        <cl-import-btn />
        <cl-flex1 />
        <!-- 条件搜索 -->
        <cl-search ref="Search" />
      </cl-row>

      <cl-row>
        <!-- 数据表格 -->
        <cl-table ref="Table" />
      </cl-row>

      <cl-row>
        <cl-flex1 />
        <!-- 分页控件 -->
        <cl-pagination />
      </cl-row>

      <!-- 新增、编辑 -->
      <cl-upsert ref="Upsert" />
    </cl-crud>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
	name: "organization-project",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

const { service } = useCool();
const { t } = useI18n();
const router = useRouter();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("项目名称"),
			prop: "projectName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("项目编码"),
			prop: "projectCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("项目描述"),
			prop: "description",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 3,
					clearable: true,
				},
			},
			span: 24,
		},
		{
			label: t("是否启用"),
			prop: "enabled",
			flex: false,
			value: 1,
			component: {
				name: "el-radio-group",
				options: [
					{
						label: "是",
						value: 1,
					},
					{
						label: "否",
						value: 0,
					},
				],
			},
			span: 12,
		},
		{
			label: t("项目标签"),
			prop: "tags",
			component: {
				name: "el-input",
				props: {
					clearable: true,
					placeholder: "多个标签用逗号分隔",
				},
			},
			span: 12,
		},
		{
			label: t("备注"),
			prop: "remark",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 3,
					clearable: true,
				},
			},
			span: 24,
		},
	],
});

// cl-table
const Table = useTable({
	autoHeight: false,
	contextMenu: ['refresh'],
  columns: [
    { type: "selection" },
    { 
      label: t("项目名称"), 
      prop: "projectName", 
      minWidth: 150,
      showOverflowTooltip: true
    },
    { 
      label: t("项目编码"), 
      prop: "projectCode", 
      minWidth: 120,
      showOverflowTooltip: true
    },
    { 
      label: t("项目描述"), 
      prop: "description", 
      minWidth: 200,
      showOverflowTooltip: true
    },
		{
			label: t("是否启用"),
			prop: "enabled",
			minWidth: 100,
			component: {
				name: "cl-switch",
			},
		},
		{ label: t("项目标签"), prop: "tags", minWidth: 120, showOverflowTooltip: true },
		{ label: t("备注"), prop: "remark", minWidth: 200, showOverflowTooltip: true },
    {
      label: t("创建时间"),
      prop: "createTime",
      minWidth: 170,
      sortable: "desc",
      component: { name: "cl-date-text" },
    },
    { 
      type: "op", 
      buttons: [
        "edit", 
        "delete",
        {
          label: "成员管理",
          type: "primary",
          onClick: ({ scope }) => {
            // 使用Vue Router导航到成员管理页面
            router.push(`/project/member/${scope.row.id}`);
          }
        }
      ]
    },
  ],
});

// cl-search
const Search = useSearch({
  items: [
    {
      label: t("项目名称"),
      prop: "projectName",
      component: { name: "el-input", props: { clearable: true } }
    },
    {
      label: t("项目编码"),
      prop: "projectCode",
      component: { name: "el-input", props: { clearable: true } }
    },
    {
      label: t("项目状态"),
      prop: "status",
      component: { 
        name: "el-select",
        options: [
          { label: "进行中", value: 1 },
          { label: "已完成", value: 2 },
          { label: "已暂停", value: 3 },
          { label: "已取消", value: 4 }
        ],
        props: {
          clearable: true
        }
      }
    },
    {
      label: t("项目优先级"),
      prop: "priority",
      component: { 
        name: "el-select",
        options: [
          { label: "低", value: 1 },
          { label: "普通", value: 2 },
          { label: "中等", value: 3 },
          { label: "高", value: 4 },
          { label: "紧急", value: 5 }
        ],
        props: {
          clearable: true
        }
      }
    }
  ]
});

// cl-crud
const Crud = useCrud(
	{
		// 使用自定义服务配置
		service: {
			page: (params: any) => service.organization.project.info.page(params),
			info: (params: any) => service.organization.project.info.info(params),
			update: (params: any) => service.organization.project.info.update(params),
			delete: (params: any) => service.organization.project.info.delete(params),
			// 使用 create 方法替代 add 方法
			add: (params: any) => service.organization.project.info.create(params)
		}
	},
	(app) => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
  Crud.value?.refresh(params);
}
</script>

<style lang="scss" scoped>
.project-list {
  height: 100%;
}
</style>
