<template>
  <div class="project-member">
    <cl-crud ref="Crud">
      <cl-row>
        <!-- 刷新按钮 -->
        <cl-refresh-btn />
        <!-- 添加成员按钮 -->
        <el-button 
          type="primary" 
          :icon="Plus" 
          @click="handleShowAddMemberDialog"
          v-dual-permission="'project:member:add'"
        >
          添加成员
        </el-button>
        <!-- 批量移除按钮 -->
        <cl-multi-delete-btn v-dual-permission="'project:member:remove'" />
        <cl-flex1 />
        <!-- 快速搜索 -->
        <el-input
          v-model="quickSearchKeyword"
          placeholder="搜索用户名、姓名或邮箱"
          style="width: 300px; margin-right: 12px;"
          clearable
          @input="handleQuickSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <!-- 高级搜索切换按钮 -->
        <el-button 
          :type="showAdvancedSearch ? 'primary' : ''"
          @click="handleToggleAdvancedSearch"
        >
          <el-icon><Filter /></el-icon>
          高级筛选
        </el-button>
      </cl-row>

      <!-- 高级搜索面板 -->
      <cl-row v-show="showAdvancedSearch">
        <div class="advanced-search-panel">
          <div class="search-panel-content">
            <div class="search-filters">
              <el-form 
                v-if="searchForm"
                :model="searchForm" 
                label-width="80px"
                class="search-form"
              >
                <el-row :gutter="20">
                  <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6">
                    <el-form-item label="项目">
                      <el-select 
                        v-model="searchForm.projectId" 
                        placeholder="选择项目"
                        clearable
                        filterable
                        style="width: 100%;"
                      >
                        <el-option
                          v-for="project in projectOptions"
                          :key="project.id"
                          :label="project.projectName"
                          :value="project.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6">
                    <el-form-item label="角色">
                      <el-select 
                        v-model="searchForm.roleCode" 
                        placeholder="选择角色"
                        clearable
                        style="width: 100%;"
                      >
                        <el-option
                          v-for="role in projectRoles"
                          :key="role.label"
                          :label="role.name"
                          :value="role.label"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6">
                    <el-form-item label="状态">
                      <el-select 
                        v-model="searchForm.status" 
                        placeholder="选择状态"
                        clearable
                        style="width: 100%;"
                      >
                        <el-option label="正常" :value="1" />
                        <el-option label="暂停" :value="2" />
                        <el-option label="已移除" :value="3" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6">
                    <el-form-item label="加入时间">
                      <el-date-picker
                        v-model="searchForm.joinTimeRange"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始"
                        end-placeholder="结束"
                        value-format="YYYY-MM-DD"
                        style="width: 100%;"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            
            <div class="search-actions">
              <el-button type="primary" @click="handleAdvancedSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleResetSearch">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </div>
        </div>
      </cl-row>

      <cl-row>
        <!-- 数据表格 -->
        <cl-table ref="Table" />
      </cl-row>

      <cl-row>
        <cl-flex1 />
        <!-- 分页控件 -->
        <cl-pagination />
      </cl-row>
    </cl-crud>

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="showAddMemberDialog"
      title="添加项目成员"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form ref="addMemberFormRef" :model="addMemberForm" :rules="addMemberFormRules" label-width="120px">
        <el-form-item label="选择项目" prop="projectId" required>
          <el-select 
            v-model="addMemberForm.projectId" 
            placeholder="请选择要添加成员的项目"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="project in projectOptions"
              :key="project.id"
              :label="project.projectName"
              :value="project.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ project.projectName }}</span>
                <el-tag 
                  :type="getProjectStatusType(project.status)" 
                  size="small"
                  effect="plain"
                >
                  {{ getProjectStatusText(project.status) }}
                </el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="选择用户" prop="userIds" required>
          <AdvancedUserSelector
            v-model="addMemberForm.userIds"
            :multiple="true"
            title="选择项目成员"
            button-text="选择用户"
            :role-options="projectRoles"
            style="width: 100%"
            @confirm="handleUserSelectionConfirm"
          />
          <div v-if="addMemberForm.userIds.length > 0" class="selected-users-info">
            已选择 {{ addMemberForm.userIds.length }} 个用户
          </div>
        </el-form-item>

        <el-form-item label="项目角色" prop="roleCode" required>
          <el-select v-model="addMemberForm.roleCode" placeholder="请选择角色" style="width: 100%">
            <el-option
              v-for="role in projectRoles"
              :key="role.label"
              :label="role.name"
              :value="role.label"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ role.name }}</span>
                <el-tag v-if="role.type === 1" size="small" effect="light">全局</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权限到期时间" prop="expireTime">
          <el-date-picker
            v-model="addMemberForm.expireTime"
            type="datetime"
            placeholder="选择到期时间（可选）"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="addMemberForm.remark"
            type="textarea"
            :rows="3"
            placeholder="添加备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelAddMember">取消</el-button>
          <el-button 
            type="primary" 
            :loading="addMemberLoading"
            :disabled="!isAddFormValid"
            @click="handleConfirmAddMembers"
          >
            确定添加
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑成员对话框 -->
    <el-dialog
      v-model="showEditMemberDialog"
      title="编辑成员信息"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form 
        v-if="editMemberForm.roleCode !== undefined"
        ref="editMemberFormRef" 
        :model="editMemberForm" 
        :rules="editMemberFormRules" 
        label-width="120px"
      >
        <el-form-item label="项目角色" prop="roleCode" required>
          <el-select v-model="editMemberForm.roleCode" placeholder="请选择角色" style="width: 100%">
            <el-option
              v-for="role in projectRoles"
              :key="role.label"
              :label="role.name"
              :value="role.label"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ role.name }}</span>
                <el-tag v-if="role.type === 1" size="small" effect="light">全局</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权限到期时间" prop="expireTime">
          <el-date-picker
            v-model="editMemberForm.expireTime"
            type="datetime"
            placeholder="选择到期时间（可选）"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editMemberForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="2">暂停</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="editMemberForm.remark"
            type="textarea"
            :rows="3"
            placeholder="添加备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelEditMember">取消</el-button>
          <el-button 
            type="primary" 
            :loading="editMemberLoading"
            @click="handleEditMember"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: "project-member",
});

import { ref, reactive, onMounted, computed } from 'vue';
import { useCrud, useTable } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Plus, Search, Filter, Refresh } from '@element-plus/icons-vue';
import { useI18n } from 'vue-i18n';
import AdvancedUserSelector from '/@/components/advanced-user-selector/index.vue';

const { t } = useI18n();
const { service } = useCool();

// 对话框状态
const showAddMemberDialog = ref(false);
const showEditMemberDialog = ref(false);
const showAdvancedSearch = ref(false);

// 加载状态
const addMemberLoading = ref(false);
const editMemberLoading = ref(false);

// 表单引用
const addMemberFormRef = ref();
const editMemberFormRef = ref();

// 搜索相关
const quickSearchKeyword = ref('');
const searchForm = reactive({
  projectId: null as number | null,
  roleCode: '',
  status: null as number | null,
  joinTimeRange: [] as string[]
});

// 项目角色选项
const projectRoles = ref<any[]>([]);

// 添加成员表单
const addMemberForm = reactive({
  projectId: null as number | null,
  userIds: [] as number[],
  roleCode: null as string | null,
  expireTime: '',
  remark: ''
});

// 编辑成员表单
const editMemberForm = reactive({
  id: null as number | null,
  userId: null as number | null, // 添加userId字段
  projectId: null as number | null,
  roleCode: null as string | null,
  expireTime: '',
  status: 1,
  remark: ''
});

// 工具方法
const getRoleTagType = (roleCode: string) => {
  const typeMap: Record<string, string> = {
    'PROJECT_OWNER': 'danger',
    'PROJECT_ADMIN': 'warning', 
    'PROJECT_MEMBER': 'primary',
    'PROJECT_VIEWER': 'info'
  };
  return typeMap[roleCode] || 'primary';
};

const getProjectStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',    // 规划中
    1: 'success', // 进行中
    2: 'success', // 已完成
    3: 'warning', // 已暂停
    4: 'danger'   // 已取消
  };
  return typeMap[status] || 'info';
};

const getProjectStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    0: '规划中',
    1: '进行中',
    2: '已完成',
    3: '已暂停',
    4: '已取消'
  };
  return textMap[status] || '未知';
};



// 表单验证规则
const addMemberFormRules = {
  projectId: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ],
  userIds: [
    { required: true, message: '请选择用户', trigger: 'change' },
    { type: 'array' as const, min: 1, message: '至少选择一个用户', trigger: 'change' }
  ],
  roleCode: [
    { required: true, message: '请选择项目角色', trigger: 'change' }
  ]
};

const editMemberFormRules = {
  roleCode: [
    { required: true, message: '请选择项目角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 计算属性
const isAddFormValid = computed(() => {
  return addMemberForm.projectId && 
         addMemberForm.userIds.length > 0 && 
         addMemberForm.roleCode;
});

const selectedProjectName = computed(() => {
  const project = projectOptions.value.find(p => p.id === addMemberForm.projectId);
  return project?.projectName || '';
});

// cl-table - 增加项目信息列
const Table = useTable({
  columns: [
    { type: "selection" },
    { 
      label: t("项目名称"), 
      prop: "projectName", 
      minWidth: 150,
      showOverflowTooltip: true
    },
    { 
      label: t("用户名"), 
      prop: "userName", 
      minWidth: 120,
      showOverflowTooltip: true
    },
    { 
      label: t("昵称"), 
      prop: "userNickName", 
      minWidth: 120,
      showOverflowTooltip: true
    },
    { 
      label: t("邮箱"), 
      prop: "userEmail", 
      minWidth: 180,
      showOverflowTooltip: true
    },
    { 
      label: t("手机号"), 
      prop: "userPhone", 
      minWidth: 130,
      showOverflowTooltip: true
    },
    { 
      label: t("项目角色"), 
      prop: "roleCode", 
      minWidth: 120,
      formatter: (row: any) => {
        const role = projectRoles.value.find(r => r.label === row.roleCode);
        return role ? role.name : row.roleCode;
      }
    },
    { 
      label: t("加入时间"), 
      prop: "joinTime", 
      minWidth: 170,
      component: { name: "cl-date-text" }
    },
    { 
      label: t("到期时间"), 
      prop: "expireTime", 
      minWidth: 170,
      component: { name: "cl-date-text" },
      formatter: (row: any) => {
        if (!row.expireTime) return '永久有效';
        const expireDate = new Date(row.expireTime);
        const now = new Date();
        if (expireDate < now) {
          return `已过期 (${row.expireTime})`;
        }
        return row.expireTime;
      }
    },
    { 
      label: t("状态"), 
      prop: "status", 
      minWidth: 100,
      dict: [
        { label: "正常", value: 1, type: "success" },
        { label: "暂停", value: 2, type: "warning" },
        { label: "已移除", value: 3, type: "danger" }
      ]
    },
    { 
      label: t("分配人"), 
      prop: "assignerName", 
      minWidth: 120,
      showOverflowTooltip: true
    },
    { 
      label: t("备注"), 
      prop: "remark", 
      minWidth: 150,
      showOverflowTooltip: true
    },
    { 
      type: "op", 
      width: 160,
      buttons: [
        {
          label: "编辑",
          type: "primary",
          size: "small",
          onClick: ({ scope }: any) => {
            handleEditMemberClick(scope.row);
          }
        },
        {
          label: "移除",
          type: "danger",
          size: "small",
          onClick: ({ scope }: any) => {
            handleRemoveMember(scope.row);
          }
        }
      ]
    },
  ],
});

// 项目选项
const projectOptions = ref<any[]>([]);

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: any;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 搜索处理方法
const handleQuickSearch = debounce((keyword: string) => {
  const params: any = {};
  if (keyword.trim()) {
    params.keyword = keyword.trim();
  }
  Crud.value?.refresh(params);
}, 300);

const handleAdvancedSearch = () => {
  const params: any = {};
  
  if (searchForm.projectId) {
    params.projectId = searchForm.projectId;
  }
  if (searchForm.roleCode) {
    params.roleCode = searchForm.roleCode;
  }
  if (searchForm.status !== null) {
    params.status = searchForm.status;
  }
  if (searchForm.joinTimeRange && Array.isArray(searchForm.joinTimeRange) && searchForm.joinTimeRange.length === 2) {
    // 格式化日期为 yyyy-MM-dd HH:mm:ss 格式
    const formatDate = (date: Date) => {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const seconds = String(d.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    
    params.joinTimeStart = formatDate(searchForm.joinTimeRange[0]);
    params.joinTimeEnd = formatDate(searchForm.joinTimeRange[1]);
  }
  if (quickSearchKeyword.value.trim()) {
    params.keyword = quickSearchKeyword.value.trim();
  }
  
  Crud.value?.refresh(params);
};

const handleResetSearch = () => {
  quickSearchKeyword.value = '';
  searchForm.projectId = null;
  searchForm.roleCode = '';
  searchForm.status = null;
  searchForm.joinTimeRange = [];
  Crud.value?.refresh();
};

// cl-crud - 管理所有项目的成员
const crudService = {
    ...service.organization.project.member,
    page: (params) => service.organization.project.member.pageMembers(params),
};

const Crud = useCrud(
  {
    service: crudService
  },
  (app) => {
    // 不再限制特定项目，获取所有项目成员
    app.refresh();
  },
);

// 确认添加成员
const handleConfirmAddMembers = async () => {
  // 表单验证
  if (!addMemberFormRef.value) {
    ElMessage.warning('表单未初始化');
    return;
  }

  try {
    const valid = await addMemberFormRef.value.validate();
    if (!valid) return;
  } catch (error) {
    ElMessage.warning('请完善表单信息');
    return;
  }

  // 安全检查：验证用户权限和数据完整性
  if (!isAddFormValid.value) {
    ElMessage.warning('请完善表单信息');
    return;
  }

  // 检查是否选择了有效的项目
  const selectedProject = projectOptions.value.find(p => p.id === addMemberForm.projectId);
  if (!selectedProject) {
    ElMessage.error('选择的项目不存在或已被删除');
    return;
  }

  // 检查项目状态是否允许添加成员
  if (selectedProject.status === 4) { // 已取消的项目
    ElMessage.error('无法向已取消的项目添加成员');
    return;
  }

  addMemberLoading.value = true;
  const loadingInstance = ElLoading.service({
    text: '正在添加成员...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 数据清理和验证
    const requestData = {
      projectId: Number(addMemberForm.projectId),
      userIds: addMemberForm.userIds.map(id => Number(id)),
      roleCode: addMemberForm.roleCode.trim(),
      expireTime: addMemberForm.expireTime || null,
      remark: addMemberForm.remark?.trim() || null
    };

    // 使用新的API调用批量添加接口
    console.log('调用批量添加接口，请求数据:', requestData);
    
    // 使用新的API路径格式，通过body传递projectId
    await service.organization.project.member.batchAddMembers(requestData);

    ElMessage.success({
      message: `成功将 ${addMemberForm.userIds.length} 个用户添加到项目 "${selectedProjectName.value}"`,
      duration: 3000
    });
    
    showAddMemberDialog.value = false;
    
    // 重置表单
    resetAddMemberForm();

    // 刷新列表
    Crud.value?.refresh();
  } catch (error: any) {
    console.error('添加成员失败:', error);
    
    // 根据错误类型提供不同的提示
    let errorMessage = '添加成员失败，请重试';
    if (error.code === 403) {
      errorMessage = '权限不足，无法添加成员';
    } else if (error.code === 400) {
      errorMessage = error.message || '请求参数错误';
    } else if (error.code === 409) {
      errorMessage = '部分用户已是该项目成员';
    }
    
    ElMessage.error({
      message: errorMessage,
      duration: 5000
    });
  } finally {
    addMemberLoading.value = false;
    loadingInstance.close();
  }
};

// 重置添加成员表单
const resetAddMemberForm = () => {
  addMemberForm.projectId = null;
  addMemberForm.userIds = [];
  addMemberForm.roleCode = 'PROJECT_MEMBER';
  addMemberForm.expireTime = '';
  addMemberForm.remark = '';
  
  // 清除表单验证状态
  if (addMemberFormRef.value) {
    addMemberFormRef.value.clearValidate();
  }
};

// 编辑成员
const handleEditMember = async () => {
  // 表单验证
  if (!editMemberFormRef.value) {
    ElMessage.warning('表单未初始化');
    return;
  }

  try {
    const valid = await editMemberFormRef.value.validate();
    if (!valid) return;
  } catch (error) {
    ElMessage.warning('请完善表单信息');
    return;
  }

  // 安全检查
  if (!editMemberForm.id) {
    ElMessage.error('成员信息异常，请刷新页面重试');
    return;
  }

  editMemberLoading.value = true;
  
  try {
    // 数据清理和验证
    const requestData = {
      userId: Number(editMemberForm.userId),
      projectId: Number(editMemberForm.projectId),
      roleCode: editMemberForm.roleCode.trim(),
      expireTime: editMemberForm.expireTime || null,
      status: editMemberForm.status,
      remark: editMemberForm.remark?.trim() || null
    };

    // 使用新的API路径格式
    await service.organization.project.member.updateMember(requestData);

    ElMessage.success('更新成员信息成功');
    showEditMemberDialog.value = false;
    
    // 重置表单
    resetEditMemberForm();
    
    // 刷新列表
    Crud.value?.refresh();
  } catch (error: any) {
    console.error('更新成员信息失败:', error);
    
    let errorMessage = '更新成员信息失败';
    if (error.code === 403) {
      errorMessage = '权限不足，无法编辑成员信息';
    } else if (error.code === 404) {
      errorMessage = '成员信息不存在或已被删除';
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    ElMessage.error(errorMessage);
  } finally {
    editMemberLoading.value = false;
  }
};

// 重置编辑成员表单
const resetEditMemberForm = () => {
  editMemberForm.id = null;
  editMemberForm.userId = null; // 重置userId字段
  editMemberForm.projectId = null;
  editMemberForm.roleCode = 'PROJECT_MEMBER';
  editMemberForm.expireTime = '';
  editMemberForm.status = 1;
  editMemberForm.remark = '';
  
  // 清除表单验证状态
  if (editMemberFormRef.value) {
    editMemberFormRef.value.clearValidate();
  }
};

// 显示添加成员对话框
const handleShowAddMemberDialog = () => {
  showAddMemberDialog.value = true;
};

// 切换高级搜索
const handleToggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;
};

// 处理编辑成员点击
const handleEditMemberClick = (row: any) => {
  console.log("Editing member, row data:", row);
  editMemberForm.id = row.id;
  editMemberForm.userId = row.userId; // 设置userId字段
  editMemberForm.projectId = row.projectId;
  editMemberForm.roleCode = row.roleCode || 'PROJECT_MEMBER';
  editMemberForm.expireTime = row.expireTime || '';
  editMemberForm.status = row.status || 1;
  editMemberForm.remark = row.remark || '';
  showEditMemberDialog.value = true;
};

// 取消添加成员
const handleCancelAddMember = () => {
  showAddMemberDialog.value = false;
  resetAddMemberForm();
};

// 取消编辑成员
const handleCancelEditMember = () => {
  showEditMemberDialog.value = false;
  resetEditMemberForm();
};

// 处理用户选择确认
const handleUserSelectionConfirm = (selectedUsers: any[]) => {
  console.log('已选择用户:', selectedUsers);
  
  if (!selectedUsers || selectedUsers.length === 0) {
    addMemberForm.userIds = [];
    console.warn('没有选择任何用户');
    return;
  }
  
  // 提取用户ID并更新表单
  addMemberForm.userIds = selectedUsers.map(user => Number(user.id));
  console.log('更新后的userIds:', addMemberForm.userIds);
  
  // 强制触发表单验证
  if (addMemberFormRef.value) {
    addMemberFormRef.value.validateField('userIds');
  }
};

// 移除成员
const handleRemoveMember = async (member: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要从项目 "${member.projectName}" 中移除成员 "${member.userNickName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 使用新的API路径格式
    await service.organization.project.member.removeMember({
      projectId: Number(member.projectId),
      userId: member.userId
    });

    ElMessage.success('移除成员成功');
    Crud.value?.refresh();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '移除成员失败');
    }
  }
};

// 加载项目选项
const loadProjectOptions = async () => {
  try {
    const result = await service.organization.project.info.options();
    // 将后端返回的 {value, label} 格式转换为前端期望的 {id, projectName} 格式
    projectOptions.value = (result || []).map((item: any) => ({
      id: item.value,
      projectName: item.label,
      // 保留原始数据以备后用
      value: item.value,
      label: item.label
    }));
  } catch (error) {
    console.warn('获取项目选项失败:', error);
    projectOptions.value = [];
  }
};



onMounted(() => {
  // 加载项目选项
  loadProjectOptions();
  loadProjectRoles();
});

// 加载项目角色选项
const loadProjectRoles = async () => {
  try {
    // 使用新的API路径
    const result = await service.organization.project.member.globalProjectRoles();
    projectRoles.value = result || [];
  } catch (error) {
    console.warn('获取项目角色选项失败:', error);
    // 如果API调用失败，提供默认角色选项
    projectRoles.value = [
      { label: "PROJECT_OWNER", name: "项目负责人", level: 1, description: "项目负责人，拥有项目的完全控制权限" },
      { label: "PROJECT_ADMIN", name: "项目管理员", level: 2, description: "项目管理员，拥有项目的管理权限" },
      { label: "PROJECT_MEMBER", name: "项目成员", level: 3, description: "项目成员，拥有项目的参与权限" },
      { label: "PROJECT_VIEWER", name: "项目观察者", level: 4, description: "项目观察者，拥有项目的只读权限" }
    ];
  }
};
</script>

<style lang="scss" scoped>
.project-member {
  height: 100%;

  // 高级搜索面板样式
  .advanced-search-panel {
    width: 100%;
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    
    .search-panel-content {
      display: flex;
      align-items: center;
      gap: 24px;
    }
    
    .search-filters {
      flex: 1;
      min-width: 0; // 防止flex子项溢出
    }
    
    .search-actions {
      display: flex;
      align-items: flex-end;
      gap: 12px;
      flex-shrink: 0; // 防止按钮被压缩
      margin-bottom: 16px; // 与表单项的底部边距对齐
    }
    
    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 24px;
        display: flex;
        align-items: center; // 强制表单项内容垂直居中
        
        .el-form-item__label {
          font-weight: 500;
          color: var(--el-text-color-regular);
          min-width: 80px;
          text-align: right;
        }
        
        .el-form-item__content {
          display: flex;
          align-items: center; // 确保输入控件垂直居中
        }
      }
      
      // 移除最后一个表单项的底部边距
      .el-form-item:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 快速搜索输入框样式
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 6px;
    }
  }

  // 选中用户信息提示
  .selected-users-info {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-7);
    border-radius: 4px;
    font-size: 12px;
    color: var(--el-color-primary);
  }

  // 表格样式优化
  :deep(.el-table) {
    .el-table__row {
      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
  }

  // 对话框底部按钮样式
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  // 表格操作按钮样式优化
  :deep(.cl-table) {
    .el-button + .el-button {
      margin-left: 8px;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .advanced-search-panel {
      .search-panel-content {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
      }
      
      .search-actions {
        justify-content: flex-end;
      }
      
      .search-form {
        .el-form-item {
          margin-right: 16px;
          
          .el-select,
          .el-date-picker {
            width: 160px !important;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .advanced-search-panel {
      padding: 12px;
      
      .search-panel-content {
        gap: 12px;
      }
      
      .search-actions {
        justify-content: flex-end;
        
        .el-button {
          flex: 1;
          max-width: 120px;
        }
      }
      
      .search-form {
        .el-form-item {
          margin-right: 8px;
          margin-bottom: 12px;
          
          .el-select,
          .el-date-picker {
            width: 140px !important;
          }
        }
      }
    }
  }
}
</style>
