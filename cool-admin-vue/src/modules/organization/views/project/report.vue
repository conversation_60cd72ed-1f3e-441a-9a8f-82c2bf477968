<template>
  <div class="project-report" v-loading="loading">
    <!-- 报表头部 -->
    <div class="report-header">
      <div class="header-left">
        <h2>项目报表分析</h2>
        <p>项目进度、任务统计和团队效率分析</p>
      </div>
      
      <div class="header-right">
        <el-select
          v-model="selectedProjects"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="选择项目"
          style="width: 240px; margin-right: 12px;"
          @change="handleProjectChange"
        >
          <el-option
            v-for="project in projectOptions"
            :key="project.id"
            :label="project.projectName"
            :value="project.id"
          />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
        />

        <el-button type="primary" @click="exportReport" :loading="loading">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon projects">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.totalProjects }}</div>
                <div class="card-label">总项目数</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.projectGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon tasks">
                <el-icon><List /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.totalTasks }}</div>
                <div class="card-label">总任务数</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.taskGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon members">
                <el-icon><User /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.totalMembers }}</div>
                <div class="card-label">团队成员</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.memberGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon efficiency">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.efficiency }}%</div>
                <div class="card-label">完成效率</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.efficiencyGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section" v-loading="chartLoading">
      <el-row :gutter="24">
        <!-- 任务分析图表 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>任务分析</span>
                <el-select v-model="progressChartType" size="small" style="width: 120px;" @change="handleChartTypeChange">
                  <el-option label="饼图" value="pie" />
                  <el-option label="柱状图" value="bar" />
                </el-select>
              </div>
            </template>
            
            <div ref="progressChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
        
        <!-- 人员任务排名 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>人员任务排名</span>
              </div>
            </template>
            <div class="table-container">
              <el-table :data="memberTaskRanking" size="small">
                <el-table-column prop="rank" label="排名" width="60" align="center" />
                <el-table-column prop="memberName" label="人员姓名" show-overflow-tooltip />
                <el-table-column prop="totalTasks" label="任务总数" width="80" align="center" />
                <el-table-column prop="completedTasks" label="已完成" width="80" align="center" />
                <el-table-column prop="inProgressTasks" label="进行中" width="80" align="center" />
                <el-table-column prop="completionRate" label="完成率" width="80" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getCompletionRateType(row.completionRate)" size="small">
                      {{ row.completionRate }}%
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 团队效率趋势 -->
        <el-col :span="24">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>团队效率趋势</span>
                <el-radio-group v-model="trendPeriod" size="small" @change="updateTrendChartOnly">
                  <el-radio-button value="week">周</el-radio-button>
                  <el-radio-button value="month">月</el-radio-button>
                  <el-radio-button value="quarter">季度</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            
            <div ref="trendChartRef" class="chart-container large"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-tables">
      <el-row :gutter="24">
        <!-- 项目排行榜 -->
        <el-col :span="12">
          <el-card class="data-card">
            <template #header>
              <div class="chart-header">
                <span>项目完成度排行</span>
              </div>
            </template>
            <div class="table-container">
              <el-table :data="projectRanking" size="small">
                <el-table-column prop="rank" label="排名" width="60" align="center" />
                <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip />
                <el-table-column prop="progress" label="完成度" width="100" align="center">
                  <template #default="{ row }">
                    <el-progress 
                      :percentage="row.progress" 
                      :show-text="false"
                      :stroke-width="6"
                    />
                    <span style="margin-left: 8px;">{{ row.progress }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
        
        <!-- 成员效率排行 -->
        <el-col :span="12">
          <el-card class="data-card">
            <template #header>
              <div class="chart-header">
                <span>成员效率排行</span>
              </div>
            </template>
            <div class="table-container">
              <el-table :data="memberRanking" size="small">
                <el-table-column prop="rank" label="排名" width="60" align="center" />
                <el-table-column prop="memberName" label="成员姓名" show-overflow-tooltip />
                <el-table-column prop="completedTasks" label="完成任务" width="80" align="center" />
                <el-table-column prop="efficiency" label="效率分" width="80" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getEfficiencyType(row.efficiency)" size="small">
                      {{ row.efficiency }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Download, 
  FolderOpened, 
  List, 
  User, 
  TrendCharts,
  ArrowUp,
  Refresh
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import { useCool } from '/@/cool';

// 扩展Window接口以包含自定义属性
declare global {
  interface Window {
    reportChartsResizeListener?: () => void;
  }
}

defineOptions({
  name: "project-report",
});

const { service } = useCool();

// 响应式数据
const dateRange = ref<string[]>([]);
const progressChartType = ref('pie');
const trendPeriod = ref('month');
const selectedProjects = ref<number[]>([]);
const projectOptions = ref<Array<{id: number, projectName: string}>>([]);
const loading = ref(false);
const chartLoading = ref(false);
const isDataLoaded = ref(false); // 防止重复加载标志
const isChartEmptyState = ref(false); // 添加图表空状态标志

// 图表引用
const progressChartRef = ref();
const trendChartRef = ref();

// 图表实例
let progressChart: echarts.ECharts | null = null;
let trendChart: echarts.ECharts | null = null;

// 存储当前图表数据，避免重复API调用
let currentChartData: any = null;

// 图表类型切换处理函数
const handleChartTypeChange = async () => {
  console.log('图表类型切换到:', progressChartType.value);
  
  // 如果有缓存的数据，直接使用缓存数据重新渲染
  if (currentChartData && (currentChartData.progress || currentChartData.barChartData)) {
    console.log('使用缓存数据重新渲染图表');
    updateTaskAnalysisChart(currentChartData.progress || [], currentChartData.barChartData || { xAxis: [], series: [] });
  } else {
    console.log('没有缓存数据，需要重新加载');
    // 如果没有缓存数据，则重新加载任务分析图表数据
    await updateCharts();
  }
};

// 概览数据
const overviewData = reactive({
  totalProjects: 0,
  totalTasks: 0,
  totalMembers: 0,
  efficiency: 0,
  projectGrowth: 0,
  taskGrowth: 0,
  memberGrowth: 0,
  efficiencyGrowth: 0
});

// 排行榜数据
interface ProjectRank {
	rank: number;
	projectName: string;
	progress: number;
}
interface MemberRank {
	rank: number;
	memberName: string;
	completedTasks: number;
	efficiency: number;
}
interface MemberTaskRank {
	rank: number;
	memberName: string;
	totalTasks: number;
	completedTasks: number;
	inProgressTasks: number;
	completionRate: number;
}
const projectRanking = ref<ProjectRank[]>([]);
const memberRanking = ref<MemberRank[]>([]);
const memberTaskRanking = ref<MemberTaskRank[]>([]);

// 方法
const handleDateRangeChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    // 重置数据加载状态并重新加载
    isDataLoaded.value = false;
    isChartEmptyState.value = false; // 重置图表空状态
    // 只调用一次数据加载，图表数据会在loadReportData完成后自动更新
    loadReportData().then(() => {
      updateCharts();
    });
  }
};

const handleProjectChange = () => {
  // 重置数据加载状态并重新加载
  isDataLoaded.value = false;
  isChartEmptyState.value = false; // 重置图表空状态
  // 只调用一次数据加载，图表数据会在loadReportData完成后自动更新
  loadReportData().then(() => {
    updateCharts();
  });
};

const getEfficiencyType = (efficiency: number) => {
  if (efficiency >= 90) return 'success';
  if (efficiency >= 70) return 'warning';
  return 'danger';
};

const getCompletionRateType = (rate: number) => {
  if (rate >= 80) return 'success';
  if (rate >= 60) return 'warning';
  return 'danger';
};

const loadProjectOptions = async () => {
  try {
    const response = await service.organization.project.info.list();
    projectOptions.value = response.map((project: any) => ({
      id: project.id,
      projectName: project.projectName
    }));
  } catch (error) {
    console.error('加载项目选项失败:', error);
  }
};

const exportReport = async () => {
  try {
    if (service.organization?.project?.report?.export) {
      const response = await service.organization.project.report.export({
        dateRange: dateRange.value,
        projectIds: selectedProjects.value.length > 0 ? selectedProjects.value : undefined
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.download = `项目报表_${new Date().toLocaleDateString()}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);

      ElMessage.success('报表导出成功');
    } else {
      console.warn('报表导出服务不可用');
      ElMessage.info('报表导出功能暂不可用');
    }
  } catch (error) {
    console.warn('报表导出失败:', error);
    ElMessage.error('报表导出失败');
  }
};

let refreshTimeout: ReturnType<typeof setTimeout> | null = null;

const refreshData = async () => {
  if (refreshTimeout) {
    clearTimeout(refreshTimeout);
  }
  
  refreshTimeout = setTimeout(async () => {
    console.log('手动刷新数据...');
    isDataLoaded.value = false; // 重置加载状态
    isChartEmptyState.value = false; // 重置图表空状态
    // 先加载基础数据，再更新图表
    await loadReportData();
    await updateCharts();
    console.log('数据刷新完成');
  }, 300); // 防抖300ms
};

const loadReportData = async () => {
  if (loading.value || isDataLoaded.value) return; // 防止重复请求
  
  try {
    loading.value = true;
    const params: Record<string, any> = {};
    
    // 添加日期范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.dateRange = dateRange.value;
    }
    
    // 添加项目筛选参数
    if (selectedProjects.value && selectedProjects.value.length > 0) {
      params.projectIds = selectedProjects.value;
    }
    
    // 添加周期参数
    if (trendPeriod.value) {
      params.period = trendPeriod.value;
    }
    
    console.log('加载报表数据参数:', params);

    // 并行加载概览数据、排行榜数据和人员任务排名数据
    const [overviewRes, rankingRes, memberTaskRankingRes] = await Promise.all([
      service.organization.project.report.overview(params),
      service.organization.project.report.ranking(params),
      service.organization.project.report.memberTaskRanking(params)
    ]);
    
    Object.assign(overviewData, overviewRes);
    projectRanking.value = rankingRes.projects || [];
    memberRanking.value = rankingRes.members || [];
    memberTaskRanking.value = memberTaskRankingRes || [];
    
    console.log('概览数据加载成功:', overviewRes);
    console.log('排行榜数据加载成功:', rankingRes);

    // 标记数据已加载
    isDataLoaded.value = true;
    
  } catch (error) {
    console.error('加载报表数据失败:', error);
    ElMessage.error('加载报表数据失败');
  } finally {
    loading.value = false;
  }
};

const initCharts = () => {
  // 确保容器已渲染
  if (!progressChartRef.value || !trendChartRef.value) {
    console.warn('图表容器未找到，跳过初始化');
    return;
  }
  
  // 初始化任务分析图表
  try {
    if (!progressChart) {
      progressChart = echarts.init(progressChartRef.value);
      console.log('任务分析图表初始化成功');
    }
  } catch (error) {
    console.error('任务分析图表初始化失败:', error);
  }
  
  // 初始化趋势图表
  try {
    if (!trendChart) {
      trendChart = echarts.init(trendChartRef.value);
      console.log('趋势图表初始化成功');
    }
  } catch (error) {
    console.error('趋势图表初始化失败:', error);
  }
  
  // 监听窗口大小变化（只添加一次）
  if (!window.reportChartsResizeListener) {
    window.reportChartsResizeListener = () => {
      progressChart?.resize();
      trendChart?.resize();
    };
    window.addEventListener('resize', window.reportChartsResizeListener);
  }
};

const updateCharts = async () => {
  // 防止重复调用和循环调用
  if (chartLoading.value) {
    console.log('图表正在加载中，跳过重复请求');
    return;
  }
  
  try {
    chartLoading.value = true;
    
    // 获取任务分析图表数据的参数
    const progressParams: Record<string, any> = {};
    
    // 添加日期范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      progressParams.dateRange = dateRange.value;
    }
    
    // 添加项目筛选参数
    if (selectedProjects.value && selectedProjects.value.length > 0) {
      progressParams.projectIds = selectedProjects.value;
    }
    
    // 移除图表类型参数，确保一次性获取所有图表数据
    // if (progressChartType.value) {
    //   progressParams.progressType = progressChartType.value;
    // }
    
    console.log('加载任务分析图表数据参数:', progressParams);
    
    // 获取任务分析图表数据
    const progressData = await service.organization.project.report.charts(progressParams);
    
    console.log('任务分析图表数据加载成功:', progressData);

    // 缓存任务分析图表数据，供图表类型切换时使用
    // 合并新旧数据，避免覆盖趋势数据
    if (currentChartData) {
      currentChartData.progress = progressData.progress || [];
      // 确保趋势数据仍然存在
      if (!currentChartData.trend) {
        currentChartData.trend = { dates: [], completed: [], created: [], efficiency: [] };
      }
    } else {
      currentChartData = {
        progress: progressData.progress || [],
        barChartData: progressData.barChartData || { xAxis: [], series: [] },
        trend: { dates: [], completed: [], created: [], efficiency: [] }
      };
    }

    // 检查图表实例，只在必要时初始化
    if (!progressChart && progressChartRef.value) {
      progressChart = echarts.init(progressChartRef.value);
    }
    
    // 更新任务分析图表
    updateTaskAnalysisChart(progressData.progress || [], progressData.barChartData || { xAxis: [], series: [] });
    
    // 重置空状态标记
    isChartEmptyState.value = false;
    
  } catch (error: any) {
    console.error('更新任务分析图表失败:', error);
    // 只在用户主动操作时显示错误消息，避免静默失败导致的重试
    if (error?.message !== '数据加载已取消') {
      ElMessage.error('更新任务分析图表失败');
    }
  } finally {
    chartLoading.value = false;
  }
};

// 新增方法：只更新趋势图表
const updateTrendChartOnly = async () => {
  // 防止重复调用和循环调用
  if (chartLoading.value) {
    console.log('图表正在加载中，跳过重复请求');
    return;
  }
  
  try {
    chartLoading.value = true;
    
    // 获取趋势图表数据的参数
    const trendParams: Record<string, any> = {};
    
    // 添加日期范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      trendParams.dateRange = dateRange.value;
    }
    
    // 添加项目筛选参数
    if (selectedProjects.value && selectedProjects.value.length > 0) {
      trendParams.projectIds = selectedProjects.value;
    }
    
    // 添加趋势周期参数
    if (trendPeriod.value) {
      trendParams.trendPeriod = trendPeriod.value;
    }
    
    console.log('加载趋势图表数据参数:', trendParams);
    
    // 获取趋势图表数据
    const trendData = await service.organization.project.report.charts(trendParams);
    
    console.log('趋势图表数据加载成功:', trendData);

    // 检查图表实例，只在必要时初始化
    if (!trendChart && trendChartRef.value) {
      trendChart = echarts.init(trendChartRef.value);
    }

    // 更新趋势图表
    updateTrendChart(trendData.trend || { dates: [], completed: [], created: [], efficiency: [] });
    
    // 更新缓存数据中的趋势数据，同时保留任务分析数据
    if (currentChartData) {
      currentChartData.trend = trendData.trend || { dates: [], completed: [], created: [], efficiency: [] };
      // 确保任务分析数据仍然存在
      if (!currentChartData.progress) {
        currentChartData.progress = [];
      }
      if (!currentChartData.barChartData) {
        currentChartData.barChartData = { xAxis: [], series: [] };
      }
    } else {
      currentChartData = { 
        trend: trendData.trend || { dates: [], completed: [], created: [], efficiency: [] },
        progress: [],
        barChartData: { xAxis: [], series: [] }
      };
    }
    
    // 确保任务分析图表不会被清空
    if (progressChart && (currentChartData.progress || currentChartData.barChartData)) {
      updateTaskAnalysisChart(currentChartData.progress || [], currentChartData.barChartData || { xAxis: [], series: [] });
    }
    
    // 重置空状态标记
    isChartEmptyState.value = false;
    
  } catch (error: any) {
    console.error('更新趋势图表失败:', error);
    // 只在用户主动操作时显示错误消息，避免静默失败导致的重试
    if (error?.message !== '数据加载已取消') {
      ElMessage.error('更新趋势图表失败');
    }
  } finally {
    chartLoading.value = false;
  }
};

const updateTaskAnalysisChart = (pieData: any, barData: any) => {
  console.log('updateTaskAnalysisChart 被调用');
  console.log('饼图数据:', pieData);
  console.log('柱状图数据:', barData);
  console.log('当前图表类型:', progressChartType.value);
  
  const textColor = getComputedStyle(document.documentElement).getPropertyValue('--el-text-color-primary').trim();
  const textColorSecondary = getComputedStyle(document.documentElement).getPropertyValue('--el-text-color-regular').trim();
  const bgColor = getComputedStyle(document.documentElement).getPropertyValue('--el-bg-color').trim();
  
  try {
    if (progressChartType.value === 'pie') {
      // 饼图使用饼图数据
      if (!pieData || pieData.length === 0) {
        const emptyOption = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 14,
              color: textColor
            }
          }
        };
        progressChart?.setOption(emptyOption, true);
        return;
      }

      const validData = Array.isArray(pieData) ? pieData.filter(item => {
        const hasValidName = item.name !== undefined && item.name !== null && item.name !== '';
        const hasValidValue = item.value !== undefined && item.value !== null && !isNaN(Number(item.value));
        return hasValidName && hasValidValue;
      }).map(item => ({
        name: String(item.name),
        value: Number(item.value)
      })) : [];

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
          backgroundColor: bgColor,
          borderColor: textColorSecondary,
          textStyle: {
            color: textColor
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: validData.map((item: any) => item.name),
          textStyle: {
            color: textColor
          }
        },
        series: [{
          name: '任务分析',
          type: 'pie',
          radius: '70%',
          center: ['50%', '60%'],
          data: validData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };
      progressChart?.setOption(option, true);
    } else {
      // 柱状图使用柱状图数据
      if (!barData || !barData.xAxis || !barData.series) {
        const emptyOption = {
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: []
          }]
        };
        progressChart?.setOption(emptyOption, true);
        return;
      }

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: bgColor,
          borderColor: textColorSecondary,
          textStyle: {
            color: textColor
          },
          formatter: function(params: any) {
            if (!params || params.length === 0) return '';
            const param = params[0];
            return `${param.name}<br/>${param.seriesName}: ${param.value}`;
          }
        },
        xAxis: {
          type: 'category',
          data: barData.xAxis,
          axisLine: {
            lineStyle: {
              color: textColorSecondary
            }
          },
          axisLabel: {
            color: textColor,
            interval: 0,
            rotate: barData.xAxis.length > 5 ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: textColorSecondary
            }
          },
          axisLabel: {
            color: textColor
          },
          splitLine: {
            lineStyle: {
              color: textColorSecondary + '20'
            }
          }
        },
        series: [{
          name: '任务数量',
          type: 'bar',
          data: barData.series,
          itemStyle: {
            color: '#409eff'
          }
        }]
      };
      progressChart?.setOption(option, true);
    }
  } catch (error) {
    console.error('更新任务分析图表失败:', error);
  }
};



const updateTrendChart = (data: any) => {
  if (!data || !data.dates || data.dates.length === 0) {
    // 如果已经处于空状态，则不再重复设置
    if (isChartEmptyState.value) return;
    
    console.log('趋势数据为空，显示空状态');
    const textColor = getComputedStyle(document.documentElement).getPropertyValue('--el-text-color-secondary').trim();
    const emptyOption = {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 14,
          color: textColor
        }
      }
    };
    trendChart?.setOption(emptyOption, true);
    isChartEmptyState.value = true; // 标记为空状态
    return;
  }
  
  // 有数据时清除空状态标记
  isChartEmptyState.value = false;

  console.log('更新趋势图表，数据:', data);
  
  try {
    const textColor = getComputedStyle(document.documentElement).getPropertyValue('--el-text-color-primary').trim();
    const textColorSecondary = getComputedStyle(document.documentElement).getPropertyValue('--el-text-color-regular').trim();
    const bgColor = getComputedStyle(document.documentElement).getPropertyValue('--el-bg-color').trim();
    
    // 确保数据格式正确
    const validDates = Array.isArray(data.dates) ? data.dates : [];
    const validCompleted = Array.isArray(data.completed) ? data.completed.map(v => Number(v) || 0) : [];
    const validCreated = Array.isArray(data.created) ? data.created.map(v => Number(v) || 0) : [];
    const validEfficiency = Array.isArray(data.efficiency) ? data.efficiency.map(v => Number(v) || 0) : [];
    
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: bgColor,
        borderColor: textColorSecondary,
        textStyle: {
          color: textColor
        },
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';
          
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            const value = param.seriesName === '团队效率' ? `${param.value}%` : param.value;
            result += `${param.marker}${param.seriesName}: ${value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['完成任务', '新增任务', '团队效率'],
        textStyle: {
          color: textColor
        }
      },
      xAxis: {
        type: 'category',
        data: validDates,
        axisLine: {
          lineStyle: {
            color: textColorSecondary
          }
        },
        axisLabel: {
          color: textColor
        }
      },
      yAxis: [{
        type: 'value',
        name: '任务数量',
        nameTextStyle: {
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: textColorSecondary
          }
        },
        axisLabel: {
          color: textColor
        },
        splitLine: {
          lineStyle: {
            color: textColorSecondary + '20'
          }
        }
      }, {
        type: 'value',
        name: '效率(%)',
        position: 'right',
        nameTextStyle: {
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: textColorSecondary
          }
        },
        axisLabel: {
          color: textColor,
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            color: textColorSecondary + '20'
          }
        }
      }],
      series: [
        {
          name: '完成任务',
          type: 'bar',
          data: validCompleted,
          itemStyle: { color: '#67c23a' }
        },
        {
          name: '新增任务',
          type: 'bar',
          data: validCreated,
          itemStyle: { color: '#409eff' }
        },
        {
          name: '团队效率',
          type: 'line',
          yAxisIndex: 1,
          data: validEfficiency,
          itemStyle: { color: '#e6a23c' },
          lineStyle: { color: '#e6a23c' }
        }
      ]
    };
    
    trendChart?.setOption(option, true);
    console.log('趋势图表更新成功');
  } catch (error: any) {
    console.error('更新趋势图表失败:', error);
  }
};

const updateChartTheme = () => {
  // 当主题变化时只重新渲染已有数据，不调用API
  if (progressChart && currentChartData) {
    updateTaskAnalysisChart(currentChartData.progress || [], currentChartData.barChartData || { xAxis: [], series: [] });
  }
  
  if (trendChart) {
    const currentData = trendChart.getOption();
    if (currentData && currentData.series && currentData.series.length > 0) {
      const trendData = {
        dates: currentData.xAxis[0].data || [],
        completed: currentData.series[0].data || [],
        created: currentData.series[1].data || [],
        efficiency: currentData.series[2].data || []
      };
      updateTrendChart(trendData);
    }
  }
};

// 监听主题变化
const observeThemeChange = () => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  
  const observer = new MutationObserver(() => {
    // 防抖处理，避免频繁触发
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(updateChartTheme, 300);
  });
  
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  });
  
  return observer;
};

onMounted(async () => {
  // 设置默认日期范围（最近30天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);

  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ];

  // 加载项目选项
  await loadProjectOptions();

  await nextTick();
  initCharts();
  
  // 先加载基础数据，再加载图表数据（避免并行调用导致的循环）
  await loadReportData();
  await updateCharts();
  
  // 监听主题变化
  const themeObserver = observeThemeChange();
  
  // 清理监听器
  onUnmounted(() => {
    themeObserver.disconnect();
    
    // 清理定时器
    if (refreshTimeout) {
      clearTimeout(refreshTimeout);
      refreshTimeout = null;
    }
    
    // 清理窗口resize监听器
    if (window.reportChartsResizeListener) {
      window.removeEventListener('resize', window.reportChartsResizeListener);
      delete window.reportChartsResizeListener;
    }
    
    // 销毁图表实例
    if (progressChart) {
      progressChart.dispose();
      progressChart = null;
    }
    if (trendChart) {
      trendChart.dispose();
      trendChart = null;
    }
  });
});
</script>

<style lang="scss" scoped>
.project-report {
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
  
  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
      align-items: center;

      .el-select {
        .el-select__tags {
          max-width: 200px;
        }
      }
    }
  }
  
  .overview-cards {
    margin-bottom: 24px;
    
    .overview-card {
      .card-content {
        display: flex;
        align-items: center;
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          color: white;
          
          &.projects {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.tasks {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.members {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.efficiency {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .card-info {
          flex: 1;
          
          .card-value {
            font-size: 28px;
            font-weight: bold;
            color: var(--el-text-color-primary);
            line-height: 1;
            margin-bottom: 4px;
          }
          
          .card-label {
            font-size: 14px;
            color: var(--el-text-color-regular);
            margin-bottom: 8px;
          }
          
          .card-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--el-color-success);
            
            .trend-icon {
              font-size: 14px;
              
              &.up {
                color: var(--el-color-success);
              }
              
              &.down {
                color: var(--el-color-danger);
              }
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 24px;
    
    .chart-card {
      height: 100%;
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 320px;
        min-height: 320px;
        
        &.large {
          height: 420px;
          min-height: 420px;
        }
      }
      
      .table-container {
        height: 320px;
        overflow-y: auto;
        
        .el-table {
          height: 100%;
        }
      }
    }
  }
  
  .data-tables {
    margin-bottom: 24px;
    
    .data-card {
      height: 100%;
      
      .table-container {
        height: 320px;
        overflow-y: auto;
        
        .el-table {
          height: 100%;
          
          .el-progress {
            width: 60px;
          }
        }
      }
    }
  }
}

// Dark模式适配
@media (prefers-color-scheme: dark) {
  .project-report {
    .report-header {
      .header-left {
        h2 {
          color: var(--el-text-color-primary);
        }
        
        p {
          color: var(--el-text-color-regular);
        }
      }
    }
    
    .overview-cards {
      .overview-card {
        .card-content {
          .card-info {
            .card-value {
              color: var(--el-text-color-primary);
            }
            
            .card-label {
              color: var(--el-text-color-secondary);
            }
          }
        }
      }
    }
    
    .charts-section,
    .data-tables {
      .el-card {
        background-color: var(--el-bg-color);
        border-color: var(--el-border-color);
        
        .el-card__header {
          border-bottom-color: var(--el-border-color);
          color: var(--el-text-color-primary);
        }
        
        .el-table {
          color: var(--el-text-color-primary);
          
          th {
            background-color: var(--el-fill-color-light);
            color: var(--el-text-color-primary);
          }
          
          td {
            color: var(--el-text-color-regular);
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .project-report {
    padding: 16px;
    height: auto;
    min-height: 100vh;
    
    .report-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }
    
    .overview-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }
    
    .charts-section {
      .el-col {
        margin-bottom: 24px;
      }
      
      .chart-card {
        .table-container {
          height: auto;
          max-height: 320px;
        }
      }
    }
    
    .data-tables {
      .el-col {
        margin-bottom: 24px;
      }
      
      .data-card {
        .table-container {
          height: auto;
          max-height: 320px;
        }
      }
    }
  }
}
</style>
