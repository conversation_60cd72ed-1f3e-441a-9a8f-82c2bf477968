<template>
	<div class="project-task">
		<!-- 公共头部：项目选择器 -->
		<div class="project-header">
			<el-select v-model="selectedProjectId" placeholder="请选择项目以查看任务" @change="onProjectChange" clearable
				filterable style="width: 300px">
				<el-option v-for="item in projectOptions" :key="item.value" :label="item.label" :value="item.value" />
			</el-select>
		</div>

		<!-- 视图切换控件 -->
		<div class="view-controls">
			<el-button-group>
				<el-button :type="viewMode === 'kanban' ? 'primary' : ''" @click="handleViewModeChange('kanban')">
					<el-icon>
						<Grid />
					</el-icon>
					看板
				</el-button>
				<el-button :type="viewMode === 'list' ? 'primary' : ''" @click="handleViewModeChange('list')">
					<el-icon>
						<List />
					</el-icon>
					列表
				</el-button>
			</el-button-group>
		</div>

		<!-- 任务看板视图 -->
		<div v-if="viewMode === 'kanban'" class="kanban-view">
			<!-- 看板搜索区域 -->
			<div class="kanban-search">
				<!-- 基础搜索行 -->
				<div class="basic-search">
					<el-form :model="searchForm" inline>
						<el-form-item label="任务名称">
							<el-input
								v-model="searchForm.keyword"
								placeholder="请输入任务名称"
								clearable
								style="width: 200px"
								@keyup.enter="handleSearch"
							/>
						</el-form-item>
						<el-form-item label="执行时间">
							<el-date-picker
								v-model="searchForm.executionDateRange"
								type="daterange"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								format="YYYY-MM-DD"
								value-format="YYYY-MM-DD"
								style="width: 240px"
								@change="handleSearch"
							/>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="handleSearch">
								<el-icon><Search /></el-icon>
								搜索
							</el-button>
							<el-button @click="handleReset">重置</el-button>
							<el-button type="text" @click="showAdvancedSearch = !showAdvancedSearch" style="margin-left: 8px">
								高级搜索
								<el-icon>
									<ArrowDown v-if="!showAdvancedSearch" />
									<ArrowUp v-else />
								</el-icon>
							</el-button>
						</el-form-item>
					</el-form>
				</div>

				<!-- 高级搜索区域 -->
				<div v-show="showAdvancedSearch" class="advanced-search">
					<el-form :model="searchForm" inline>
						<el-form-item label="执行人">
							<el-input
								v-model="searchForm.assigneeName"
								placeholder="请输入执行人姓名"
								clearable
								style="width: 180px"
							/>
						</el-form-item>
						<el-form-item label="执行人手机">
							<el-input
								v-model="searchForm.assigneePhone"
								placeholder="请输入手机号"
								clearable
								style="width: 180px"
							/>
						</el-form-item>
						<el-form-item label="任务状态">
							<el-select
								v-model="searchForm.taskStatus"
								placeholder="请选择状态"
								clearable
								style="width: 150px"
							>
								<el-option label="待分配" :value="0" />
								<el-option label="待执行" :value="1" />
								<el-option label="执行中" :value="2" />
								<el-option label="已完成" :value="3" />
								<el-option label="已关闭" :value="4" />
							</el-select>
						</el-form-item>
						<el-form-item label="任务类型">
							<el-select
								v-model="searchForm.taskCategory"
								placeholder="请选择类型"
								clearable
								style="width: 150px"
							>
								<el-option label="日常任务" value="RC" />
								<el-option label="周期任务" value="ZQ" />
								<el-option label="临时任务" value="LS" />
								<el-option label="场景步骤" value="SOP_STEP" />
							</el-select>
						</el-form-item>
					</el-form>
				</div>
			</div>

			<!-- 看板主体 -->
			<div class="kanban-board" v-loading="kanbanLoading">
				<div
					v-for="status in taskStatuses"
					:key="status.value"
					:data-status="status.value"
					class="kanban-column"
					@dragover="handleDragOver"
					@dragenter="handleDragEnter"
					@dragleave="handleDragLeave"
					@drop="handleDrop($event, status.value)"
				>
					<div class="column-header">
						<span class="column-title">{{ status.label }}</span>
						<el-tag :type="status.type" size="small">
							{{ getTasksByStatus(status.value).length }}
						</el-tag>
					</div>

					<div class="column-body">
						<div
							v-for="task in getTasksByStatus(status.value)"
							:key="task.id"
							class="task-card"
							draggable="true"
							@dragstart="handleDragStart($event, task)"
							@dragend="handleDragEnd"
							@click="viewTaskDetail(task)"
							@contextmenu="handleContextMenu($event, task)"
						>
							<div class="task-header">
								<h4 class="task-title">{{ task.name }}</h4>
								<el-dropdown trigger="click" @command="(command) => handleTaskAction(command, task)">
									<el-button type="text" size="small">
										<el-icon><MoreFilled /></el-icon>
									</el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item command="delete">删除</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</div>
							
							<div class="task-content">
								<p class="task-description">{{ task.description }}</p>
								
								<div class="task-meta">
									<div class="meta-item">
										<el-icon><Calendar /></el-icon>
										<span>{{ formatDate(task.startTime) }}</span>
									</div>
									
									<div v-if="task.assigneeName" class="meta-item">
										<el-icon><User /></el-icon>
										<span>{{ task.assigneeName }}</span>
									</div>
									
									<div v-if="task.projectRoleName" class="meta-item">
										<el-tag size="small" :type="getRoleType(task.projectRoleName)">
											{{ getRoleText(task.projectRoleName) }}
										</el-tag>
									</div>
								</div>
								
								<div class="task-footer">
									<el-tag 
										size="small" 
										:type="getPriorityType(task.priority)"
									>
										优先级: {{ getPriorityText(task.priority) }}
									</el-tag>
									
									<el-tag 
										size="small" 
										:type="getCategoryType(task.taskCategory)"
									>
										{{ getCategoryText(task.taskCategory) }}
									</el-tag>
								</div>
							</div>
						</div>
						
						<!-- 空状态 -->
						<div v-if="getTasksByStatus(status.value).length === 0" class="empty-column">
							<el-empty description="暂无任务" :image-size="60" />
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 列表视图 -->
		<div v-else class="list-view">
			<!-- 搜索区域 -->
			<div class="search-area">
				<!-- 基础搜索行 -->
				<div class="basic-search">
					<el-form :model="searchForm" inline>
						<el-form-item label="任务名称">
							<el-input v-model="searchForm.keyword" placeholder="请输入任务名称" clearable style="width: 200px"
								@keyup.enter="handleSearch" />
						</el-form-item>
						<el-form-item label="执行时间">
							<el-date-picker v-model="searchForm.executionDateRange" type="daterange" range-separator="至"
								start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD"
								value-format="YYYY-MM-DD" style="width: 240px" @change="handleSearch" />
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="handleSearch">
								<el-icon>
									<Search />
								</el-icon>
								搜索
							</el-button>
							<el-button @click="handleReset">重置</el-button>
							<el-button type="text" @click="showAdvancedSearch = !showAdvancedSearch"
								style="margin-left: 8px">
								高级搜索
								<el-icon>
									<ArrowDown v-if="!showAdvancedSearch" />
									<ArrowUp v-else />
								</el-icon>
							</el-button>
						</el-form-item>
					</el-form>
				</div>

				<!-- 高级搜索区域 -->
				<div v-show="showAdvancedSearch" class="advanced-search">
					<el-form :model="searchForm" inline>
						<el-form-item label="执行人">
							<el-input v-model="searchForm.assigneeName" placeholder="请输入执行人姓名" clearable
								style="width: 180px" />
						</el-form-item>
						<el-form-item label="执行人手机">
							<el-input v-model="searchForm.assigneePhone" placeholder="请输入手机号" clearable
								style="width: 180px" />
						</el-form-item>
						<el-form-item label="任务状态">
							<el-select v-model="searchForm.taskStatus" placeholder="请选择状态" clearable
								style="width: 150px">
								<el-option label="待分配" :value="0" />
								<el-option label="待执行" :value="1" />
								<el-option label="执行中" :value="2" />
								<el-option label="已完成" :value="3" />
								<el-option label="已关闭" :value="4" />
							</el-select>
						</el-form-item>
						<el-form-item label="任务类型">
							<el-select v-model="searchForm.taskCategory" placeholder="请选择类型" clearable
								style="width: 150px">
								<el-option label="日常任务" value="RC" />
								<el-option label="周期任务" value="ZQ" />
								<el-option label="临时任务" value="LS" />
								<el-option label="场景步骤" value="SOP_STEP" />
							</el-select>
						</el-form-item>
					</el-form>
				</div>
			</div>

			<cl-crud ref="Crud">
				<cl-row>
					<cl-refresh-btn />
					<cl-add-btn />
					<cl-multi-delete-btn />
					<cl-flex1 />
				</cl-row>

				<cl-row>
					<cl-table ref="Table" />
				</cl-row>

				<cl-row>
					<cl-flex1 />
					<cl-pagination />
				</cl-row>

				<!-- Upsert 表单 -->
				<cl-upsert ref="Upsert" />

				<!-- 调整分配对话框 -->
				<AssigneeSelector
					v-model="showAssignDialog"
					:taskName="currentAssignTask?.name"
					:taskTags="currentAssignTask?.taskTags"
					filterMode="project"
					:contextId="selectedProjectId"
					@confirm="handleAssignConfirm"
				/>

				<!-- 调整时间对话框 -->
				<TaskTimeAdjuster
					v-model="showAdjustTimeDialog"
					:taskId="currentAdjustTimeTask?.id"
					:initialStartTime="currentAdjustTimeTask?.startTime"
					:initialEndTime="currentAdjustTimeTask?.endTime"
					@confirm="handleAdjustTimeConfirm"
				/>
			</cl-crud>
		</div>

		<!-- 完成任务对话框 -->
		<TaskCompleter
			v-model="showCompleteDialog"
			:task="currentCompleteTask"
			@confirm="handleCompleteConfirm"
		/>

		<!-- 关闭任务对话框 -->
		<TaskCloser
			v-model="showCloseDialog"
			:task="currentCloseTask"
			@confirm="handleCloseConfirm"
		/>

		<!-- 全局任务详情对话框 -->
		<GlobalTaskDetailDialog />

		<!-- 拖拽分配对话框 -->
		<AssigneeSelector
			v-model="showDragAssignDialog"
			:taskName="draggedTask?.name"
			:taskTags="draggedTask?.taskTags"
			filterMode="project"
			:contextId="selectedProjectId"
			@confirm="handleDragAssignConfirm"
		/>

		<!-- 右键菜单 -->
		<div
			v-if="showContextMenu"
			class="context-menu"
			:style="{
				left: contextMenuPosition.x + 'px',
				top: contextMenuPosition.y + 'px'
			}"
		>
			<div class="context-menu-header">
				<span>{{ contextMenuTask?.name }}</span>
			</div>
			<div class="context-menu-divider"></div>
			<div class="context-menu-section" v-if="availableContextActions.length > 0">
				<div class="context-menu-title">状态操作</div>
				<div
					v-for="action in availableContextActions"
					:key="action.event"
					class="context-menu-item"
					@click="handleContextAction(action)"
				>
					<el-icon :color="action.color">
						<component :is="getActionIcon(action.icon)" />
					</el-icon>
					<span>{{ action.label }}</span>
				</div>
			</div>
			<div class="context-menu-divider" v-if="availableContextActions.length > 0"></div>
			<div class="context-menu-section">
				<div
					class="context-menu-item"
					@click="viewTaskDetail(contextMenuTask); showContextMenu = false"
				>
					<el-icon><View /></el-icon>
					<span>查看详情</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useUserStore } from "/@/modules/base/store/user";
import { ElMessage } from 'element-plus';
import { Grid, List, Search, ArrowDown, ArrowUp, View, Edit, User, VideoPlay, Check, Close, RefreshRight, Unlock } from '@element-plus/icons-vue';
import AssigneeSelector from '/@/components/assignee-selector/AssigneeSelector.vue';
import TaskTimeAdjuster from '/@/modules/task/components/TaskTimeAdjuster.vue';
import TaskCloser from '/@/modules/task/components/TaskCloser.vue';
import TaskCompleter from '/@/modules/task/components/TaskCompleter.vue';
import TaskDetailDialog from '/@/modules/task/components/TaskDetailDialog.vue';
import GlobalTaskDetailDialog from '/@/modules/task/components/GlobalTaskDetailDialog.vue';
import { useTaskStatusTransition } from '/@/modules/task/composables/useTaskStatusTransition';
import { useTaskDetail } from '/@/modules/task/composables/useTaskDetail';

defineOptions({
	name: "project-task",
});

const { service } = useCool();

// --- 核心改造开始 ---

// 1. 响应式状态
const viewMode = ref<'kanban' | 'list'>('list'); // 默认显示列表视图

// 监听视图模式切换
const handleViewModeChange = (mode: 'kanban' | 'list') => {
	console.log('视图模式切换:', mode);
	viewMode.value = mode;

	// 如果切换到看板模式且有选中项目，则加载看板数据
	if (mode === 'kanban' && selectedProjectId.value) {
		fetchKanbanData();
	}
};
const selectedProjectId = ref<number | undefined>(undefined);
const projectOptions = ref<{ value: number; label: string }[]>([]);

// 调整分配相关状态
const showAssignDialog = ref(false);
const currentAssignTask = ref<any>(null); // 用于存储当前要分配的任务对象

// 完成任务相关状态
const showCompleteDialog = ref(false);
const currentCompleteTask = ref<any>(null);

// 关闭任务相关状态
const showCloseDialog = ref(false);
const currentCloseTask = ref<any>(null);

// 调整时间相关状态
const showAdjustTimeDialog = ref(false);
const currentAdjustTimeTask = ref<any>(null);

// 高级搜索相关状态
const showAdvancedSearch = ref(false);
const searchForm = ref({
	// 基础搜索
	keyword: '', // 任务名称模糊搜索
	// 高级搜索
	executionDateRange: [] as string[], // 任务执行时间过滤
	assigneeName: '', // 执行人模糊搜索
	assigneePhone: '', // 执行人手机号模糊搜索
	taskStatus: undefined as number | undefined, // 任务状态搜索
	taskCategory: '', // 任务类型过滤
});

// 获取当前日期，用于默认时间过滤
const getTodayRange = () => {
	const today = new Date();
	const todayStr = today.toISOString().split('T')[0];
	return [todayStr, todayStr];
};

// 搜索处理函数
const handleSearch = () => {
	console.log('执行搜索，搜索条件:', searchForm.value);

	// 确保有选中的项目
	if (!selectedProjectId.value) {
		ElMessage.warning('请先选择一个项目');
		return;
	}

	// 根据当前视图模式执行不同的搜索逻辑
	if (viewMode.value === 'kanban') {
		// 看板模式：直接调用看板数据加载
		console.log('看板模式搜索');
		fetchKanbanData();
	} else {
		// 列表模式：使用CRUD组件刷新
		console.log('列表模式搜索');
		nextTick(() => {
			// 尝试多次直到CRUD组件初始化完成
			const tryRefresh = (attempts = 0) => {
				if (Crud.value && Crud.value.refresh) {
					console.log('CRUD组件已就绪，执行搜索');
					if (Crud.value.pagination) {
						Crud.value.pagination.page = 1; // 重置到第一页
					}
					Crud.value.refresh(); // 手动触发刷新
				} else if (attempts < 10) {
					console.log(`CRUD组件未就绪，重试第${attempts + 1}次`);
					setTimeout(() => tryRefresh(attempts + 1), 100);
				} else {
					console.error('CRUD组件初始化超时');
					ElMessage.error('搜索功能初始化失败，请刷新页面重试');
				}
			};

			tryRefresh();
		});
	}
};

const handleReset = () => {
	console.log('重置搜索条件:', searchForm.value);
	searchForm.value = {
		keyword: '',
		executionDateRange: [] as string[], // 重置为空数组
		assigneeName: '',
		assigneePhone: '',
		taskStatus: undefined,
		taskCategory: '',
	};
	handleSearch();
};

// --- 任务操作相关方法 ---

// 调整分配
const handleAssignTask = (row: any) => {
	console.log('调整分配任务:', row);
	currentAssignTask.value = row;
	showAssignDialog.value = true;
};

const handleAssignConfirm = async (data: any) => {
	const assigneeId = data.assigneeId || (data.assigneeIds && data.assigneeIds[0]);
	if (!currentAssignTask.value?.id || !assigneeId) {
		ElMessage.warning('请选择任务和执行人');
		return;
	}

	try {
		await service.task.assignment.manual({
			taskId: currentAssignTask.value.id,
			assigneeIds: [Number(assigneeId)],
			reason: data.reason
		});
		ElMessage.success('任务分配成功');
		showAssignDialog.value = false;
		refreshData();
	} catch (error) {
		ElMessage.error('任务分配失败');
		console.error('任务分配失败:', error);
	}
};

// 完成任务
const handleCompleteTask = async (row: any) => {
	console.log('完成任务:', row);
	// 确保任务数据包含必要的字段
	currentCompleteTask.value = {
		...row,
		assigneeId: row.assigneeId || 1 // 如果没有assigneeId，使用默认值
	};
	showCompleteDialog.value = true;
};

const handleCompleteConfirm = async (data: { taskId: number; assigneeId: number; remark?: string; attachments?: any[] }) => {
	try {
		// 处理附件数据，将上传组件的文件对象转换为URL数组
		const attachmentUrls = data.attachments?.map(file => {
			if (typeof file === 'string') {
				return file;
			} else if (file && file.url) {
				return file.url;
			} else if (file && file.response && file.response.data) {
				return file.response.data;
			}
			return '';
		}).filter(url => url) || [];

		// 使用新的统一状态流转API
		const result = await service.task.status.request({
			url: '/change',
			method: 'POST',
			data: {
				taskId: data.taskId,
				targetStatus: 3, // 已完成状态
				reason: data.remark || '任务完成',
				operatorId: data.assigneeId,
				operatorName: '执行人',
				completionNote: data.remark || '任务完成',
				attachments: attachmentUrls,
				photos: [] // TaskCompleter组件暂时没有照片字段
			}
		});

		if (result.success) {
			ElMessage.success('任务已完成');
			showCompleteDialog.value = false;
			draggedTask.value = null; // 清空拖拽任务
			refreshData();
		} else {
			ElMessage.error(result.errorMessage || '任务完成失败');
		}
	} catch (error) {
		ElMessage.error(error.message);
		console.error('任务完成失败:', error);
	}
};

// 关闭任务
const handleCloseTask = (row: any) => {
	console.log('关闭任务:', row);
	currentCloseTask.value = row;
	showCloseDialog.value = true;
};

const handleCloseConfirm = async (data: { taskId: number; closeReason: string; operatorId?: number; operatorName?: string }) => {
	if (!data.taskId || !data.closeReason) {
		ElMessage.warning('请输入关闭原因');
		return;
	}

	try {
		// 使用新的统一状态流转API
		const result = await service.task.status.request({
			url: '/change',
			method: 'POST',
			data: {
				taskId: data.taskId,
				targetStatus: 4, // 已关闭状态
				reason: data.closeReason,
				operatorId: data.operatorId || currentCloseTask.value?.assigneeId || 1,
				operatorName: data.operatorName || currentCloseTask.value?.assigneeName || '当前用户',
				closeReason: data.closeReason
			}
		});

		if (result.success) {
			ElMessage.success('任务已关闭');
			showCloseDialog.value = false;
			draggedTask.value = null; // 清空拖拽任务
			refreshData();
		} else {
			ElMessage.error(result.errorMessage || '任务关闭失败');
		}
	} catch (error) {
		ElMessage.error('任务关闭失败');
		console.error('任务关闭失败:', error);
	}
};

// 调整执行时间
const handleAdjustTime = (row: any) => {
	console.log('调整任务时间:', row);
	currentAdjustTimeTask.value = row;
	showAdjustTimeDialog.value = true;
};

const handleAdjustTimeConfirm = async (data: { taskId: number; startTime: string; endTime: string }) => {
	if (!data.taskId || !data.startTime || !data.endTime) {
		ElMessage.warning('请选择任务和完整的起止时间');
		return;
	}

	try {
		await service.task.info.batchUpdateTaskTime({
			ids: [data.taskId],
			startTime: data.startTime,
			endTime: data.endTime,
		});
		ElMessage.success('任务时间调整成功');
		showAdjustTimeDialog.value = false;
		refreshData();
	} catch (error) {
		ElMessage.error('任务时间调整失败');
		console.error('任务时间调整失败:', error);
	}
};

// 2. CRUD 组件配置
const Crud = useCrud({
	service: service.organization.project.task, // 指向新的后端服务
	onRefresh(params, { next, done, render }) {
		console.log('CRUD onRefresh 被调用，参数:', params);
		console.log('当前选中项目ID:', selectedProjectId.value);
		console.log('搜索表单当前值:', searchForm.value);

		if (!selectedProjectId.value) {
			console.log('没有选中项目，清空列表');
			// 没有选中项目时，清空列表并停止请求
			render([]);
			done();
			return;
		}

		// 构建请求参数，包含项目ID和搜索条件
		const requestParams: any = {
			...params,
			projectId: selectedProjectId.value
		};

		// 添加搜索条件
		if (searchForm.value.keyword) {
			requestParams.keyWord = searchForm.value.keyword;
		}

		if (searchForm.value.executionDateRange && searchForm.value.executionDateRange.length === 2) {
			requestParams.executionStartDate = searchForm.value.executionDateRange[0];
			requestParams.executionEndDate = searchForm.value.executionDateRange[1];
		}

		if (searchForm.value.assigneeName) {
			requestParams.assigneeName = searchForm.value.assigneeName;
		}

		if (searchForm.value.assigneePhone) {
			requestParams.assigneePhone = searchForm.value.assigneePhone;
		}

		if (searchForm.value.taskStatus !== undefined && searchForm.value.taskStatus !== null) {
			requestParams.taskStatus = searchForm.value.taskStatus;
		}

		if (searchForm.value.taskCategory) {
			requestParams.taskCategory = searchForm.value.taskCategory;
		}

		console.log('发送请求参数:', requestParams);
		next(requestParams);
	}
}, (app) => {
	console.log('CRUD组件初始化完成:', app);
});

const Table = useTable({
	autoHeight: true,
	contextMenu: ['refresh'],
	columns: [
		{ type: "selection", width: 60 },
		{ label: "项目名称", prop: "projectName", minWidth: 150 },
		{ label: "场景名称", prop: "scenarioName", minWidth: 150 },

		{ label: "任务名称", prop: "name", minWidth: 160 },
		{ label: "任务描述", prop: "description", minWidth: 200, showOverflowTooltip: true },
		{
			label: "状态",
			prop: "taskStatus",
			minWidth: 100,
			dict: [
				{ label: "待分配", value: 0, type: "info" },
				{ label: "待执行", value: 1, type: "warning" },
				{ label: "执行中", value: 2, type: "primary" },
				{ label: "已完成", value: 3, type: "success" },
				{ label: "已关闭", value: 4, type: "danger" }
			]
		},
		{
			label: "执行人",
			prop: "assigneeName",
			minWidth: 150,
			showOverflowTooltip: true
		},
		{
			label: "执行人手机",
			prop: "assigneePhone",
			minWidth: 150,
			showOverflowTooltip: true
		},
		{
			label: "项目角色",
			prop: "projectRoleName",
			minWidth: 120,
			showOverflowTooltip: true,
			dict: [
				{ label: "项目负责人", value: "PROJECT_OWNER", type: "danger" },
				{ label: "项目管理员", value: "PROJECT_ADMIN", type: "warning" },
				{ label: "项目成员", value: "PROJECT_MEMBER", type: "primary" },
				{ label: "项目观察者", value: "PROJECT_VIEWER", type: "info" }
			]
		},
		{
			label: "任务类型",
			prop: "taskCategory",
			minWidth: 120,
			dict: [
				{ label: "日常任务", value: "RC", type: "primary" },
				{ label: "周期任务", value: "ZQ", type: "success" },
				{ label: "临时任务", value: "LS", type: "warning" },
				{ label: "场景步骤", value: "SOP_STEP", type: "info" }
			]
		},


		{ label: "创建时间", prop: "createTime", minWidth: 160 },
				{ type: "op", buttons: ["info", "edit", "delete", { label: "调整分配", type: "primary", onClick: (row) => handleAssignTask(row) }, { label: "完成", type: "success", onClick: (row) => handleCompleteTask(row) }, { label: "关闭", type: "danger", onClick: (row) => handleCloseTask(row) }, { label: "调整时间", type: "warning", onClick: (row) => handleAdjustTime(row) }] }
	]
});

const Upsert = useUpsert({
	items: [
		{ label: "任务名称", prop: "name", required: true, component: { name: "el-input" } },
		{ label: "任务描述", prop: "description", component: { name: "el-input", props: { type: "textarea", rows: 3 } } },
		{
			label: "任务类型",
			prop: "taskCategory",
			component: {
				name: "el-select",
				options: [
					{ label: "日常任务", value: "RC" },
					{ label: "周期任务", value: "ZQ" },
					{ label: "临时任务", value: "LS" },
					{ label: "场景步骤", value: "SOP_STEP" }
				]
			}
		},
		{
			label: "优先级",
			prop: "priority",
			component: {
				name: "el-select",
				options: [
					{ label: "低", value: 1 },
					{ label: "普通", value: 2 },
					{ label: "中等", value: 3 },
					{ label: "高", value: 4 },
					{ label: "紧急", value: 5 }
				]
			}
		},
		{
			label: "任务状态",
			prop: "taskStatus",
			component: {
				name: "el-select",
				options: [
					{ label: "待分配", value: 0 },
					{ label: "待执行", value: 1 },
					{ label: "执行中", value: 2 },
					{ label: "已完成", value: 3 },
					{ label: "已关闭", value: 4 }
				]
			}
		}
	],
	onSubmit(data, { next }) {
		if (!selectedProjectId.value) {
			ElMessage.warning("请先选择一个项目");
			return;
		}
		next({ ...data, projectId: selectedProjectId.value });
	}
});

// 3. 数据获取与交互方法
async function fetchProjectOptions() {
	try {
		// 尝试多种方式获取项目列表
		let response;
		try {
			// 首先尝试使用项目任务接口
			response = await service.organization.project.task.accessibleProjects();
			console.log('项目任务接口响应:', response);
		} catch (e) {
			console.warn('项目任务接口失败，尝试使用项目信息接口:', e);
			// 如果失败，使用项目信息接口
			response = await service.organization.project.info.options();
			console.log('项目信息接口响应:', response);
		}

		// 处理不同的响应格式
		let list: { value: number; label: string }[] = [];
		if (Array.isArray(response)) {
			// 如果直接返回数组
			list = response.map(item => ({
				value: item.projectId || item.value || item.id,
				label: item.projectName || item.label || item.name
			}));
		} else if (response && response.data && Array.isArray(response.data)) {
			// 如果返回的是包装对象
			list = response.data.map(item => ({
				value: item.projectId || item.value || item.id,
				label: item.projectName || item.label || item.name
			}));
		} else if (response && Array.isArray(response.list)) {
			// 如果是分页数据
			list = response.list.map(item => ({
				value: item.projectId || item.value || item.id,
				label: item.projectName || item.label || item.name
			}));
		}

		console.log('处理后的项目选项:', list);
		projectOptions.value = list;

		// 默认选中第一个项目
		if (list && list.length > 0) {
			selectedProjectId.value = list[0].value;
			console.log('默认选中项目ID:', selectedProjectId.value);

			// 等待CRUD组件初始化完成后再触发查询
			nextTick(() => {
				setTimeout(() => {
					if (Crud.value) {
						console.log('CRUD组件已初始化，触发首次查询');
						Crud.value.refresh();
					}
				}, 100);
			});
		} else {
			console.warn('没有可用的项目');
			ElMessage.warning('没有可用的项目，请联系管理员');
		}
	} catch (error) {
		ElMessage.error("获取项目列表失败");
		console.error('获取项目列表错误:', error);
	}
}

function onProjectChange() {
	console.log('项目切换，新项目ID:', selectedProjectId.value);

	if (selectedProjectId.value) {
		// 根据当前视图模式加载数据
		if (viewMode.value === 'kanban') {
			// 看板模式：加载看板数据
			console.log('项目切换 - 看板模式');
			fetchKanbanData();
		} else {
			// 列表模式：刷新CRUD数据
			console.log('项目切换 - 列表模式');
			// 重置分页到第一页
			if (Crud.value) {
				Crud.value.pagination.page = 1;
			}

			// 立即刷新数据
			setTimeout(() => {
				console.log('延迟刷新CRUD');
				Crud.value?.refresh();
			}, 50);
		}
	} else {
		// 清空数据
		if (viewMode.value === 'kanban') {
			kanbanTasks.value = [];
		} else {
			// 清空列表并重置分页
			if (Crud.value) {
				Crud.value.list = [];
				Crud.value.pagination = { page: 1, size: 20, total: 0 };
			}
		}
	}
}

function onAddTask() {
	if (!selectedProjectId.value) {
		ElMessage.warning("请先选择一个项目才能创建任务");
		return;
	}
	Crud.value?.rowAdd();
}

// --- 核心改造结束 ---

// --- 看板视图相关状态和方法 ---
const taskStatuses: { value: number; label: string; type: any }[] = [
	{ value: 0, label: '待分配', type: 'info' },
	{ value: 1, label: '待执行', type: 'warning' },
	{ value: 2, label: '执行中', type: 'primary' },
	{ value: 3, label: '已完成', type: 'success' },
	{ value: 4, label: '已关闭', type: 'danger' }
];

// 看板独立的数据状态
const kanbanTasks = ref<any[]>([]);
const kanbanLoading = ref(false);

// 拖拽相关状态
const draggedTask = ref<any>(null);
const showDragAssignDialog = ref(false);
const dragTargetStatus = ref<number>(0);
const isDragging = ref(false);
const autoScrollTimer = ref<number | null>(null);

// 右键菜单状态
const showContextMenu = ref(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const contextMenuTask = ref<any>(null);

// 任务详情对话框状态
const showTaskDetailDialog = ref(false);
const currentDetailTask = ref<any>(null);

// 使用状态流转组合式函数
const {
	getAvailableTransitions,
	getActionButtonConfig,
	changeTaskStatus
} = useTaskStatusTransition();

// 使用全局任务详情服务
const { showTaskDetail } = useTaskDetail();

// 计算可用的右键菜单操作
const availableContextActions = computed(() => {
	if (!contextMenuTask.value) return []

	const currentStatus = contextMenuTask.value.taskStatus
	const transitions = getAvailableTransitions(currentStatus)

	const actions: Array<{
		event: string;
		label: string;
		icon: string;
		color: string;
		targetStatus: number;
	}> = []

	// 根据状态转换生成操作
	transitions.forEach(transition => {
		const targetStatus = transition.value
		let event = ''

		// 根据状态转换确定事件类型
		if (currentStatus === 0 && targetStatus === 1) event = 'ASSIGN'
		else if (currentStatus === 1 && targetStatus === 2) event = 'START'
		else if (currentStatus === 2 && targetStatus === 3) event = 'COMPLETE'
		else if (targetStatus === 4) event = 'CLOSE'
		else if (currentStatus === 3 && targetStatus === 2) event = 'REACTIVATE'
		else if (currentStatus === 4 && targetStatus === 1) event = 'REOPEN'

		if (event) {
			const config = getActionButtonConfig(event)
			actions.push({
				event,
				label: config.label,
				icon: config.icon,
				color: config.color,
				targetStatus
			})
		}
	})

	return actions
});

// 获取图标组件
const getActionIcon = (iconName: string) => {
	const iconMap = {
		'User': 'User',
		'VideoPlay': 'VideoPlay',
		'Check': 'Check',
		'Close': 'Close',
		'RefreshRight': 'RefreshRight',
		'Unlock': 'Unlock'
	}
	return iconMap[iconName] || 'Operation'
};

const getTasksByStatus = (status: number) => {
	return kanbanTasks.value.filter(task => task.taskStatus === status);
};

// 统一的数据刷新方法
const refreshData = () => {
	console.log('刷新数据 - 当前视图模式:', viewMode.value);
	if (viewMode.value === 'kanban') {
		// 看板模式：刷新看板数据
		fetchKanbanData();
	} else {
		// 列表模式：刷新CRUD数据
		Crud.value?.refresh();
	}
};

// 看板数据加载方法
const fetchKanbanData = async () => {
	if (!selectedProjectId.value) {
		console.log('没有选中项目，清空看板数据');
		kanbanTasks.value = [];
		return;
	}

	kanbanLoading.value = true;
	try {
		console.log('开始加载看板数据，项目ID:', selectedProjectId.value);

		// 构建请求参数，包含项目ID和搜索条件
		const requestParams: any = {
			projectId: selectedProjectId.value,
			page: 1,
			size: 1000 // 看板需要获取所有数据，不分页
		};

		// 添加搜索条件（与列表视图相同的搜索条件）
		if (searchForm.value.keyword) {
			requestParams.keyWord = searchForm.value.keyword;
		}

		if (searchForm.value.executionDateRange && searchForm.value.executionDateRange.length === 2) {
			requestParams.executionStartDate = searchForm.value.executionDateRange[0];
			requestParams.executionEndDate = searchForm.value.executionDateRange[1];
		}

		if (searchForm.value.assigneeName) {
			requestParams.assigneeName = searchForm.value.assigneeName;
		}

		if (searchForm.value.assigneePhone) {
			requestParams.assigneePhone = searchForm.value.assigneePhone;
		}

		if (searchForm.value.taskStatus !== undefined && searchForm.value.taskStatus !== null) {
			requestParams.taskStatus = searchForm.value.taskStatus;
		}

		if (searchForm.value.taskCategory) {
			requestParams.taskCategory = searchForm.value.taskCategory;
		}

		console.log('看板请求参数:', requestParams);

		// 使用看板专用接口
		const response = await service.organization.project.task.kanbanPage(requestParams);
		console.log('看板数据响应:', response);

		// 处理响应数据
		if (response && response.list && Array.isArray(response.list)) {
			kanbanTasks.value = response.list;
		} else if (response && Array.isArray(response)) {
			kanbanTasks.value = response;
		} else {
			console.warn('看板数据格式异常:', response);
			kanbanTasks.value = [];
		}

		console.log('看板数据加载完成，任务数量:', kanbanTasks.value.length);
	} catch (error) {
		console.error('看板数据加载失败:', error);
		ElMessage.error('看板数据加载失败');
		kanbanTasks.value = [];
	} finally {
		kanbanLoading.value = false;
	}
};

// 看板相关辅助方法
const formatDate = (dateStr: string) => {
	if (!dateStr) return '';
	const date = new Date(dateStr);
	return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
};

const getPriorityType = (priority: number): 'success' | 'info' | 'warning' | 'danger' => {
	const types: ('success' | 'info' | 'warning' | 'danger')[] = ['info', 'success', 'info', 'warning', 'danger', 'danger'];
	return types[priority] || 'info';
};

const getPriorityText = (priority: number) => {
	const texts = ['', '低', '普通', '中等', '高', '紧急'];
	return texts[priority] || '普通';
};

const getCategoryType = (category: string): 'primary' | 'success' | 'warning' | 'info' => {
	const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info'> = {
		'RC': 'primary',
		'ZQ': 'success',
		'LS': 'warning',
		'SOP_STEP': 'info'
	};
	return typeMap[category] || 'info';
};

const getCategoryText = (category: string) => {
	const textMap: Record<string, string> = {
		'RC': '日常任务',
		'ZQ': '周期任务',
		'LS': '临时任务',
		'SOP_STEP': '场景步骤'
	};
	return textMap[category] || category;
};

const getRoleType = (roleName: string): 'danger' | 'warning' | 'primary' | 'info' => {
	const typeMap: Record<string, 'danger' | 'warning' | 'primary' | 'info'> = {
		// 支持英文值
		'PROJECT_OWNER': 'danger',
		'PROJECT_ADMIN': 'warning',
		'PROJECT_MEMBER': 'primary',
		'PROJECT_VIEWER': 'info',
		// 支持中文标签
		'项目负责人': 'danger',
		'项目管理员': 'warning',
		'项目成员': 'primary',
		'项目观察者': 'info'
	};
	return typeMap[roleName] || 'info';
};

const getRoleText = (roleName: string): string => {
	const textMap: Record<string, string> = {
		'PROJECT_OWNER': '项目负责人',
		'PROJECT_ADMIN': '项目管理员',
		'PROJECT_MEMBER': '项目成员',
		'PROJECT_VIEWER': '项目观察者'
	};
	return textMap[roleName] || roleName;
};

// 自动滚动处理
const handleAutoScroll = (event: DragEvent) => {
	const kanbanBoard = document.querySelector('.kanban-board') as HTMLElement;
	if (!kanbanBoard || !isDragging.value) return;

	const rect = kanbanBoard.getBoundingClientRect();
	const scrollThreshold = 100; // 触发滚动的边界距离
	const scrollSpeed = 10; // 滚动速度

	const mouseY = event.clientY;
	const mouseX = event.clientX;

	// 清除之前的定时器
	if (autoScrollTimer.value) {
		clearInterval(autoScrollTimer.value);
		autoScrollTimer.value = null;
	}

	// 垂直滚动
	if (mouseY < rect.top + scrollThreshold) {
		// 向上滚动
		autoScrollTimer.value = setInterval(() => {
			kanbanBoard.scrollTop -= scrollSpeed;
		}, 16) as unknown as number;
	} else if (mouseY > rect.bottom - scrollThreshold) {
		// 向下滚动
		autoScrollTimer.value = setInterval(() => {
			kanbanBoard.scrollTop += scrollSpeed;
		}, 16) as unknown as number;
	}

	// 水平滚动
	if (mouseX < rect.left + scrollThreshold) {
		// 向左滚动
		if (!autoScrollTimer.value) {
			autoScrollTimer.value = setInterval(() => {
				kanbanBoard.scrollLeft -= scrollSpeed;
			}, 16) as unknown as number;
		}
	} else if (mouseX > rect.right - scrollThreshold) {
		// 向右滚动
		if (!autoScrollTimer.value) {
			autoScrollTimer.value = setInterval(() => {
				kanbanBoard.scrollLeft += scrollSpeed;
			}, 16) as unknown as number;
		}
	}
};

const stopAutoScroll = () => {
	if (autoScrollTimer.value) {
		clearInterval(autoScrollTimer.value);
		autoScrollTimer.value = null;
	}
};

// 检查任务是否允许拖拽（与右键菜单权限保持一致）
const canDragTask = (task: any) => {
	if (!task) return false;

	const currentStatus = task.taskStatus;
	const currentUserId = 1; // TODO: 从用户状态获取当前用户ID

	// 获取用户信息
	const { service } = useCool();
	const userStore = useUserStore();
	const isSuperAdmin = userStore.info?.username === 'admin'; // 超级管理员判断
	const isAdmin = true; // TODO: 从用户状态获取是否是管理员，暂时设为true便于测试

	// 超级管理员可以拖拽任何任务，包括已关闭的任务
	if (isSuperAdmin) {
		return true;
	}

	// 已关闭的任务只能重新打开，不允许拖拽到其他状态
	if (currentStatus === 4) {
		return false; // 已关闭任务不允许拖拽
	}

	// 待分配任务：管理员可以拖拽
	if (currentStatus === 0) {
		return isAdmin; // 管理员可以拖拽待分配任务
	}

	// 待执行和执行中的任务需要检查是否是本人
	if (currentStatus === 1 || currentStatus === 2) {
		// 检查是否是任务的执行人
		if (task.assigneeId && task.assigneeId !== currentUserId) {
			return false; // 不是本人的任务不允许拖拽
		}
	}

	// 其他状态允许拖拽
	return true;
};

// 检查用户是否有权限执行特定的状态转换
const canPerformTransition = (task: any, targetStatus: number, currentUserId: number) => {
	if (!task) return false;

	const sourceStatus = task.taskStatus;

	// 获取用户信息
	const userStore = useUserStore();
	const isSuperAdmin = userStore.info?.username === 'admin'; // 超级管理员判断

	// 超级管理员拥有所有状态转换权限
	if (isSuperAdmin) {
		return true;
	}

	// 管理员权限检查（这里简化处理，实际应该从用户状态获取）
	const isAdmin = true; // TODO: 从用户状态获取是否是管理员，暂时设为true便于测试

	// 根据源状态和目标状态检查权限
	switch (sourceStatus) {
		case 0: // 待分配
			switch (targetStatus) {
				case 1: // 待分配 -> 待执行（分配任务）
					return isAdmin; // 管理员可以分配任务
				case 4: // 待分配 -> 已关闭（关闭未分配任务）
					return isAdmin; // 管理员可以关闭未分配任务
				default:
					return false;
			}

		case 1: // 待执行
			switch (targetStatus) {
				case 2: // 待执行 -> 执行中（开始执行）
					return task.assigneeId === currentUserId; // 必须是本人
				case 4: // 待执行 -> 已关闭（关闭任务）
					return isAdmin || task.assigneeId === currentUserId; // 管理员或本人
				default:
					return false;
			}

		case 2: // 执行中
			switch (targetStatus) {
				case 3: // 执行中 -> 已完成（完成任务）
					return task.assigneeId === currentUserId; // 必须是本人
				case 4: // 执行中 -> 已关闭（关闭任务）
					return isAdmin || task.assigneeId === currentUserId; // 管理员或本人
				default:
					return false;
			}

		case 3: // 已完成
			switch (targetStatus) {
				case 2: // 已完成 -> 执行中（重新激活）
					return isAdmin || task.assigneeId === currentUserId; // 管理员或本人
				default:
					return false;
			}

		case 4: // 已关闭
			switch (targetStatus) {
				case 1: // 已关闭 -> 待执行（重新打开）
					return isAdmin; // 只有管理员可以重新打开
				default:
					return false;
			}

		default:
			return false;
	}
};

// 获取超级管理员的扩展状态转换选项
const getSuperAdminTransitions = (currentStatus: number) => {
	const userStore = useUserStore();
	const isSuperAdmin = userStore.info?.username === 'admin';

	if (!isSuperAdmin) {
		return getAvailableTransitions(currentStatus);
	}

	// 超级管理员可以进行任意状态转换
	const allStatuses = [
		{ value: 0, label: '待分配', type: 'info', color: '#909399' },
		{ value: 1, label: '待执行', type: 'warning', color: '#e6a23c' },
		{ value: 2, label: '执行中', type: 'primary', color: '#409eff' },
		{ value: 3, label: '已完成', type: 'success', color: '#67c23a' },
		{ value: 4, label: '已关闭', type: 'danger', color: '#f56c6c' }
	];

	// 排除当前状态，返回所有其他状态
	return allStatuses.filter(status => status.value !== currentStatus);
};

// 高亮可放置的列
const highlightValidDropZones = (task: any) => {
	if (!task) return;

	const currentUserId = 1; // TODO: 从用户状态获取当前用户ID
	const userStore = useUserStore();
	const isSuperAdmin = userStore.info?.username === 'admin';

	// 超级管理员使用扩展的转换选项，普通用户使用标准转换选项
	const availableTransitions = isSuperAdmin ?
		getSuperAdminTransitions(task.taskStatus) :
		getAvailableTransitions(task.taskStatus);

	// 清除之前的高亮
	document.querySelectorAll('.kanban-column').forEach(col => {
		col.classList.remove('valid-drop-zone', 'invalid-drop-zone');
	});

	// 为每个状态列添加相应的样式
	const statuses = [
		{ value: 0, label: '待分配' },
		{ value: 1, label: '待执行' },
		{ value: 2, label: '执行中' },
		{ value: 3, label: '已完成' },
		{ value: 4, label: '已关闭' }
	];

	statuses.forEach(status => {
		const columnElement = document.querySelector(`[data-status="${status.value}"]`);
		if (columnElement) {
			const validTransition = availableTransitions.find(t => t.value === status.value);
			const hasPermission = canPerformTransition(task, status.value, currentUserId);

			if (validTransition && hasPermission) {
				columnElement.classList.add('valid-drop-zone');
			} else {
				columnElement.classList.add('invalid-drop-zone');
			}
		}
	});
};

// 清除拖拽高亮
const clearDropZoneHighlight = () => {
	document.querySelectorAll('.kanban-column').forEach(col => {
		col.classList.remove('valid-drop-zone', 'invalid-drop-zone', 'drag-over');
	});
};

// 拖拽处理方法
const handleDragStart = (event: DragEvent, task: any) => {
	console.log('开始拖拽任务:', task);

	// 检查任务是否允许拖拽（与右键菜单权限保持一致）
	if (!canDragTask(task)) {
		console.log('任务不允许拖拽，阻止拖拽操作');
		event.preventDefault();
		return;
	}

	draggedTask.value = task;
	isDragging.value = true;

	if (event.dataTransfer) {
		event.dataTransfer.effectAllowed = 'move';
		event.dataTransfer.setData('text/plain', task.id.toString());
	}

	// 添加拖拽样式
	const target = event.target as HTMLElement;
	target.classList.add('dragging');

	// 高亮可放置的列
	highlightValidDropZones(task);

	// 添加全局鼠标移动监听器用于自动滚动
	document.addEventListener('dragover', handleAutoScroll);
};

const handleDragEnd = (event: DragEvent) => {
	// 重置拖拽状态
	isDragging.value = false;

	// 停止自动滚动
	stopAutoScroll();

	// 移除全局事件监听器
	document.removeEventListener('dragover', handleAutoScroll);

	// 移除拖拽样式
	const target = event.target as HTMLElement;
	target.classList.remove('dragging');

	// 清除拖拽高亮
	clearDropZoneHighlight();
};

const handleDragEnter = (event: DragEvent) => {
	event.preventDefault();
	const target = event.currentTarget as HTMLElement;
	target.classList.add('drag-over');
};

const handleDragLeave = (event: DragEvent) => {
	const target = event.currentTarget as HTMLElement;
	// 只有当鼠标真正离开元素时才移除样式
	if (!target.contains(event.relatedTarget as Node)) {
		target.classList.remove('drag-over');
	}
};

const handleDragOver = (event: DragEvent) => {
	event.preventDefault();
	if (event.dataTransfer) {
		event.dataTransfer.dropEffect = 'move';
	}
};

const handleDrop = async (event: DragEvent, targetStatus: number) => {
	event.preventDefault();

	if (!draggedTask.value) {
		console.warn('没有拖拽的任务');
		return;
	}

	const sourceStatus = draggedTask.value.taskStatus;
	console.log('拖拽任务状态变更:', {
		taskId: draggedTask.value.id,
		from: sourceStatus,
		to: targetStatus
	});

	// 如果状态没有变化，不处理
	if (sourceStatus === targetStatus) {
		console.log('任务状态未变化，不处理');
		draggedTask.value = null;
		return;
	}

	// 移除拖拽样式
	document.querySelectorAll('.kanban-column').forEach(col => {
		col.classList.remove('drag-over');
	});

	// 根据目标状态决定处理方式，与右键菜单逻辑保持一致
	console.log('处理拖拽状态变更:', { sourceStatus, targetStatus });

	try {
		// 检查用户权限
		const currentUserId = 1; // TODO: 从用户状态获取当前用户ID
		if (!canPerformTransition(draggedTask.value, targetStatus, currentUserId)) {
			ElMessage.warning('您没有权限执行此操作');
			return;
		}

		if (targetStatus === 3) {
			// 拖拽到完成状态，弹出完成对话框
			console.log('拖拽到完成状态，准备弹出完成对话框');
			currentCompleteTask.value = {
				...draggedTask.value,
				assigneeId: draggedTask.value.assigneeId || currentUserId
			};
			console.log('设置完成任务数据:', currentCompleteTask.value);
			await nextTick();
			showCompleteDialog.value = true;
			console.log('完成对话框状态:', showCompleteDialog.value);
		} else if (targetStatus === 4) {
			// 拖拽到关闭状态，弹出关闭对话框
			console.log('拖拽到关闭状态，准备弹出关闭对话框');
			currentCloseTask.value = draggedTask.value;
			console.log('设置关闭任务数据:', currentCloseTask.value);
			await nextTick();
			showCloseDialog.value = true;
			console.log('关闭对话框状态:', showCloseDialog.value);
		} else if (sourceStatus === 0 && targetStatus !== 0) {
			// 从待分配拖动到其他状态（非完成/关闭），需要选择分配人
			console.log('从待分配拖动到其他状态，需要选择分配人');
			console.log('设置拖拽目标状态:', targetStatus);
			console.log('当前拖拽任务:', draggedTask.value);
			dragTargetStatus.value = targetStatus;
			showDragAssignDialog.value = true;
			console.log('分配对话框状态:', showDragAssignDialog.value);
		} else {
			// 执行直接状态转换（如开始执行、重新激活等）
			const userStore = useUserStore();
			const isSuperAdmin = userStore.info?.username === 'admin';

			// 超级管理员使用扩展的转换选项，普通用户使用标准转换选项
			const availableTransitions = isSuperAdmin ?
				getSuperAdminTransitions(sourceStatus) :
				getAvailableTransitions(sourceStatus);
			const validTransition = availableTransitions.find(t => t.value === targetStatus);

			if (!validTransition) {
				console.log('不支持的状态变更:', { sourceStatus, targetStatus });
				ElMessage.warning('该状态变更不被允许');
				return;
			}

			await changeTaskStatus({
				taskId: draggedTask.value.id,
				targetStatus,
				reason: `拖拽操作：${validTransition.label}`,
				operatorId: currentUserId,
				operatorName: '当前用户'
			});

			ElMessage.success(`任务已${validTransition.label}`);
			refreshData(); // 刷新数据
			draggedTask.value = null; // 清空拖拽任务
		}
	} catch (error) {
		console.error('拖拽状态变更失败:', error);
		// ElMessage.error('状态变更失败');
		// 只有在出错时才清空draggedTask，成功的情况下由各自的处理函数清空
		draggedTask.value = null;
	}
};

const updateTaskStatus = async (taskId: number, newStatus: number) => {
	try {
		console.log('更新任务状态:', { taskId, newStatus });

		// 这个方法现在只处理简单的状态变更
		// 完成(3)和关闭(4)状态通过对话框处理
		if (newStatus === 3 || newStatus === 4) {
			ElMessage.warning('完成和关闭任务需要通过对话框操作');
			return;
		}

		// 其他状态变更暂时不支持直接拖拽
		ElMessage.warning('该状态变更暂不支持');
	} catch (error) {
		console.error('更新任务状态失败:', error);
		// ElMessage.error('任务状态更新失败');
	}
};

const handleDragAssignConfirm = async (data: any) => {
	console.log('拖拽分配确认 - 接收到的数据:', data);
	console.log('拖拽分配确认 - 当前拖拽任务:', draggedTask.value);
	console.log('拖拽分配确认 - 目标状态:', dragTargetStatus.value);

	const assigneeId = data.assigneeId || (data.assigneeIds && data.assigneeIds[0]);
	console.log('拖拽分配确认 - 提取的assigneeId:', assigneeId);

	if (!draggedTask.value?.id || !assigneeId) {
		console.log('拖拽分配确认 - 验证失败:', {
			taskId: draggedTask.value?.id,
			assigneeId: assigneeId,
			hasTask: !!draggedTask.value,
			hasTaskId: !!draggedTask.value?.id,
			hasAssigneeId: !!assigneeId
		});
		ElMessage.warning('请选择任务和执行人');
		return;
	}

	try {
		console.log('拖拽分配确认 - 开始分配任务:', {
			taskId: draggedTask.value.id,
			assigneeIds: [Number(assigneeId)],
			reason: data.reason
		});

		// 先分配任务
		await service.task.assignment.manual({
			taskId: draggedTask.value.id,
			assigneeIds: [Number(assigneeId)],
			reason: data.reason
		});

		console.log('拖拽分配确认 - 任务分配成功，开始更新状态');

		// 任务分配成功，刷新数据即可
		ElMessage.success('任务分配成功');
		showDragAssignDialog.value = false;
		draggedTask.value = null;
		dragTargetStatus.value = 0;
		refreshData(); // 刷新数据
	} catch (error) {
		// ElMessage.error('任务分配失败');
		console.error('拖拽分配失败:', error);
	}
};

// 右键菜单处理
const handleContextMenu = (event: MouseEvent, task: any) => {
	event.preventDefault();
	event.stopPropagation();

	contextMenuTask.value = task;
	contextMenuPosition.value = {
		x: event.clientX,
		y: event.clientY
	};
	showContextMenu.value = true;

	// 点击其他地方关闭菜单
	const closeMenu = () => {
		showContextMenu.value = false;
		document.removeEventListener('click', closeMenu);
	};
	setTimeout(() => {
		document.addEventListener('click', closeMenu);
	}, 0);
};

// 处理右键菜单操作
const handleContextAction = async (action: any) => {
	if (!contextMenuTask.value) return;

	showContextMenu.value = false;

	const task = contextMenuTask.value;
	const targetStatus = action.targetStatus;

	try {
		// 根据操作类型处理
		switch (action.event) {
			case 'ASSIGN':
				// 分配任务需要选择执行人
				draggedTask.value = task;
				dragTargetStatus.value = targetStatus;
				showDragAssignDialog.value = true;
				break;

			case 'START':
				// 开始执行
				await changeTaskStatus({
					taskId: task.id,
					targetStatus,
					reason: '开始执行任务',
					operatorId: 1, // 当前用户ID
					operatorName: '当前用户'
				});
				// changeTaskStatus函数内部已经显示成功提示
				refreshData();
				break;

			case 'COMPLETE':
				// 完成任务，显示完成对话框
				currentCompleteTask.value = task;
				showCompleteDialog.value = true;
				break;

			case 'CLOSE':
				// 关闭任务，显示关闭对话框
				currentCloseTask.value = task;
				showCloseDialog.value = true;
				break;

			case 'REACTIVATE':
			case 'REOPEN':
				// 重新激活或重新打开
				await changeTaskStatus({
					taskId: task.id,
					targetStatus,
					reason: action.event === 'REACTIVATE' ? '重新激活任务' : '重新打开任务',
					operatorId: 1, // 当前用户ID
					operatorName: '当前用户'
				});
				// changeTaskStatus函数内部已经显示成功提示
				refreshData();
				break;
		}
	} catch (error) {
		console.error('右键菜单操作失败:', error);
		ElMessage.error('操作失败');
	}
};

// 看板操作方法
const viewTaskDetail = (task: any) => {
	// 使用全局任务详情服务
	showTaskDetail(task, (editTask: any) => {
		Crud.value?.rowEdit(editTask);
	});
};

// 处理任务编辑（保留本地对话框的处理）
const handleTaskEdit = (task: any) => {
	showTaskDetailDialog.value = false;
	Crud.value?.rowEdit(task);
};

const handleTaskAction = (command: string, task: any) => {
	switch (command) {
		case 'edit':
			Crud.value?.rowEdit(task);
			break;
		case 'delete':
			Crud.value?.rowDelete(task);
			break;
	}
};

// 处理任务状态流转操作
const handleTaskStatusAction = (action: string, data: any) => {
	console.log('任务状态流转操作:', action, data);

	switch (action) {
		case 'statusChange':
			ElMessage.success('任务状态变更成功');
			// 刷新数据
			refreshData();
			break;
		case 'batchStatusChange':
			ElMessage.success(`批量状态变更完成，成功${data.success}个，失败${data.failed}个`);
			// 刷新数据
			refreshData();
			break;
		default:
			console.log('未处理的状态流转操作:', action);
	}
};



// 组件挂载后，获取项目列表
onMounted(async () => {
	console.log('组件挂载，开始初始化');

	// 初始化搜索条件，默认执行时间为当日
	searchForm.value.executionDateRange = getTodayRange();

	await fetchProjectOptions();

	// 如果默认是看板模式且有选中项目，则加载看板数据
	if (viewMode.value === 'kanban' && selectedProjectId.value) {
		setTimeout(() => {
			fetchKanbanData();
		}, 200);
	}
});

</script>

<style lang="scss" scoped>
.project-task {
	height: 100%;
	display: flex;
	flex-direction: column;
	padding: 16px;

	.project-header {
		margin-bottom: 16px;
		flex-shrink: 0;
	}

	.view-controls {
		margin-bottom: 16px;
		flex-shrink: 0;
	}

	.list-view {
		flex: 1;
		min-height: 0; // 重要：让flex子元素能够正确计算高度
		display: flex;
		flex-direction: column;

		.search-area {
			flex-shrink: 0;
			margin-bottom: 16px;
			background: var(--el-bg-color-page);
			border-radius: 8px;
			padding: 16px;
			border: 1px solid var(--el-border-color-light);
			box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

			.basic-search {
				margin-bottom: 0;

				.el-form {
					margin-bottom: 0;
				}

				.el-form-item {
					margin-bottom: 0;
					margin-right: 16px;

					&:last-child {
						margin-right: 0;
					}

					:deep(.el-form-item__label) {
						color: var(--el-text-color-regular);
					}
				}
			}

			.advanced-search {
				margin-top: 16px;
				padding-top: 16px;
				border-top: 1px solid var(--el-border-color-lighter);

				.el-form-item {
					margin-bottom: 16px;
					margin-right: 16px;

					&:last-child {
						margin-right: 0;
					}

					:deep(.el-form-item__label) {
						color: var(--el-text-color-regular);
					}
				}
			}

			// 高级搜索展开按钮样式
			:deep(.el-button--text) {
				color: var(--el-color-primary);

				&:hover {
					color: var(--el-color-primary-light-3);
				}
			}
		}

		:deep(.cl-crud) {
			flex: 1;
			min-height: 0;
			display: flex;
			flex-direction: column;

			.cl-row {
				&:first-child {
					// 工具栏行
					flex-shrink: 0;
					margin-bottom: 16px;
				}

				&:nth-child(2) {
					// 表格行
					flex: 1;
					min-height: 0;
					overflow: hidden;
				}

				&:last-child {
					// 分页行
					flex-shrink: 0;
					margin-top: 16px;
					display: flex;
					justify-content: center;
				}
			}
		}
	}

	.kanban-view {
		flex: 1;
		min-height: 0;
		display: flex;
		flex-direction: column;

		.kanban-search {
			flex-shrink: 0;
			margin-bottom: 16px;
			background: var(--el-bg-color-page);
			border-radius: 8px;
			padding: 16px;
			border: 1px solid var(--el-border-color-light);
			box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

			.basic-search {
				margin-bottom: 0;

				.el-form {
					margin-bottom: 0;
				}

				.el-form-item {
					margin-bottom: 0;
					margin-right: 16px;

					&:last-child {
						margin-right: 0;
					}

					:deep(.el-form-item__label) {
						color: var(--el-text-color-regular);
					}
				}
			}

			.advanced-search {
				margin-top: 16px;
				padding-top: 16px;
				border-top: 1px solid var(--el-border-color-lighter);

				.el-form-item {
					margin-bottom: 16px;
					margin-right: 16px;

					&:last-child {
						margin-right: 0;
					}

					:deep(.el-form-item__label) {
						color: var(--el-text-color-regular);
					}
				}
			}

			// 高级搜索展开按钮样式
			:deep(.el-button--text) {
				color: var(--el-color-primary);

				&:hover {
					color: var(--el-color-primary-light-3);
				}
			}
		}

		.kanban-board {
			flex: 1;
			display: flex;
			gap: 16px;
			overflow-x: auto;
			overflow-y: auto;
			padding: 16px;

			.kanban-column {
				min-width: 300px;
				flex-shrink: 0;
				background: var(--el-bg-color-page);
				border-radius: 8px;
				border: 1px solid var(--el-border-color-light);
				display: flex;
				flex-direction: column;
				height: fit-content;
				transition: all 0.2s ease;

				&.drag-over {
					border-color: var(--el-color-primary);
					background: var(--el-color-primary-light-9);
				}

				// 可放置区域样式
				&.valid-drop-zone {
					background-color: var(--el-color-success-light-9);
					border-color: var(--el-color-success);
					border-style: dashed;

					.column-header {
						background-color: var(--el-color-success-light-8);
						color: var(--el-color-success-dark-2);
					}
				}

				// 不可放置区域样式
				&.invalid-drop-zone {
					background-color: var(--el-color-danger-light-9);
					border-color: var(--el-color-danger-light-5);
					opacity: 0.6;

					.column-header {
						background-color: var(--el-color-danger-light-8);
						color: var(--el-color-danger-dark-2);
					}
				}

				.column-header {
					padding: 16px;
					border-bottom: 1px solid var(--el-border-color-lighter);
					display: flex;
					justify-content: space-between;
					align-items: center;
					background: var(--el-fill-color-light);
					border-radius: 8px 8px 0 0;
					flex-shrink: 0;

					.column-title {
						font-weight: 600;
						color: var(--el-text-color-primary);
					}
				}

				.column-body {
					padding: 16px;
					display: flex;
					flex-direction: column;
					gap: 12px;

					.task-card {
						background: var(--el-bg-color);
						border: 1px solid var(--el-border-color-lighter);
						border-radius: 6px;
						padding: 12px;
						cursor: grab;
						transition: all 0.2s ease;
						box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

						&:hover {
							border-color: var(--el-color-primary);
							box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
							transform: translateY(-1px);
						}

						&:active {
							cursor: grabbing;
						}

						&.dragging {
							opacity: 0.5;
							transform: rotate(5deg);
							cursor: grabbing;
						}

						.task-header {
							display: flex;
							justify-content: space-between;
							align-items: flex-start;
							margin-bottom: 8px;

							.task-title {
								font-size: 14px;
								font-weight: 600;
								color: var(--el-text-color-primary);
								margin: 0;
								flex: 1;
								line-height: 1.4;
								margin-right: 8px;
							}

							.task-actions {
								display: flex;
								align-items: center;
								gap: 4px;
								flex-shrink: 0;
							}
						}

						.task-content {
							.task-description {
								font-size: 12px;
								color: var(--el-text-color-regular);
								margin: 0 0 12px 0;
								line-height: 1.4;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								-webkit-box-orient: vertical;
								overflow: hidden;
							}

							.task-meta {
								margin-bottom: 12px;

								.meta-item {
									display: flex;
									align-items: center;
									font-size: 12px;
									color: var(--el-text-color-regular);
									margin-bottom: 4px;

									&:last-child {
										margin-bottom: 0;
									}

									.el-icon {
										margin-right: 4px;
										font-size: 12px;
									}

									span {
										margin-right: 8px;
									}
								}
							}

							.task-footer {
								display: flex;
								gap: 6px;
								flex-wrap: wrap;
							}
						}
					}

					.empty-column {
						display: flex;
						justify-content: center;
						align-items: center;
						min-height: 120px;
						color: var(--el-text-color-placeholder);
						border: 2px dashed var(--el-border-color-lighter);
						border-radius: 6px;
						background: var(--el-fill-color-extra-light);
					}
				}
			}
		}
	}
}

// 确保表格自适应高度和滚动
:deep(.cl-table) {
	height: 100%;

	.el-table {
		height: 100%;

		.el-table__body-wrapper {
			overflow-y: auto;
		}
	}
}

// 确保分页组件样式
:deep(.cl-pagination) {
	.el-pagination {
		justify-content: center;
	}
}

// 右键菜单样式
.context-menu {
	position: fixed;
	z-index: 9999;
	background: var(--el-bg-color);
	border: 1px solid var(--el-border-color);
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	min-width: 200px;
	padding: 8px 0;
	user-select: none;

	.context-menu-header {
		padding: 8px 16px;
		font-weight: 600;
		color: var(--el-text-color-primary);
		font-size: 14px;
		max-width: 250px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.context-menu-divider {
		height: 1px;
		background: var(--el-border-color-lighter);
		margin: 4px 0;
	}

	.context-menu-section {
		.context-menu-title {
			padding: 4px 16px;
			font-size: 12px;
			color: var(--el-text-color-regular);
			font-weight: 500;
		}
	}

	.context-menu-item {
		padding: 8px 16px;
		cursor: pointer;
		display: flex;
		align-items: center;
		gap: 8px;
		font-size: 14px;
		color: var(--el-text-color-primary);
		transition: all 0.2s ease;

		&:hover:not(.disabled) {
			background: var(--el-fill-color-light);
		}

		&.disabled {
			cursor: not-allowed;
			opacity: 0.5;
		}

		&.current {
			background: var(--el-color-primary-light-9);
			color: var(--el-color-primary);
		}

		.current-indicator {
			margin-left: auto;
			font-size: 12px;
			color: var(--el-color-primary);
		}

		.el-icon {
			font-size: 16px;
		}
	}
}
</style>
