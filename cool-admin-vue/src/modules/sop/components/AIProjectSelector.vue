<template>
  <el-select v-model="selectedProjects" :multiple="true" :filterable="filterable" :placeholder="placeholder"
    :clearable="clearable" :loading="loading" style="width: 100%;">
    <el-option v-for="project in options" :key="project.value||project.id"
      :label="project.label || project.projectName" :value="project.value || project.id" />
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array as () => number[],
    default: () => []
  },
  options: {
    type: Array as () => any[],
    default: () => []
  },
  filterable: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: String,
    default: '请选择所属项目'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

const selectedProjects = ref<number[]>(props.modelValue as number[]);

// 添加调试信息
watch(
  () => props.options,
  (newOptions) => {
    console.log('AIProjectSelector 接收到的项目选项:', newOptions);
  },
  { immediate: true }
);

watch(
  () => props.modelValue,
  (newVal) => {
    selectedProjects.value = newVal as number[];
  },
  { deep: true }
);

watch(
  selectedProjects,
  (newVal) => {
    emit('update:modelValue', newVal);
  },
  { deep: true }
);
</script>
