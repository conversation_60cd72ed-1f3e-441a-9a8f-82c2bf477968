<template>
  <div class="project-task-list">
    <!-- 项目信息 -->
    <div v-if="project.projectName" class="project-info">
      <h4>{{ project.projectName }}</h4>
      <div class="project-stats">
        <el-tag type="info" size="small">
          共{{ project.tasks?.length || 0 }}个任务
        </el-tag>
        <el-tag type="success" size="small">
          已分配{{ assignedCount }}个
        </el-tag>
        <el-tag type="warning" size="small">
          待分配{{ unassignedCount }}个
        </el-tag>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="tasks-section">
      <div class="tasks-list">
        <div 
          v-for="(task, index) in project.tasks" 
          :key="index"
          class="task-item"
        >
          <el-card>
            <div class="task-header">
              <div class="task-title">
                <h5>{{ task.taskName || task.name }}</h5>
                <el-tag 
                  :type="getPriorityType(task.priority)" 
                  size="small"
                >
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
              <div class="task-actions">
                <el-button 
                  @click="handleAdjustAssignment(index, task)"
                  type="primary" 
                  size="small"
                  :disabled="!task.canReassign"
                >
                  👤 调整分配
                </el-button>
              </div>
            </div>

            <div class="task-content">
              <div class="task-description">
                <p>{{ task.description || task.taskDescription }}</p>
              </div>

              <div class="task-details">
                <div class="detail-row">
                  <span class="label">步骤:</span>
                  <span>{{ task.stepName }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">角色:</span>
                  <span>{{ task.employeeRole }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">预估时长:</span>
                  <span>{{ task.estimatedDuration || 30 }}分钟</span>
                </div>
              </div>

              <!-- 执行人分配信息 -->
              <div class="assignment-info">
                <div v-if="task.isAssigned" class="assigned">
                  <div class="assignee-info">
                    <el-avatar :size="32">
                      {{ task.assigneeName?.charAt(0) || '?' }}
                    </el-avatar>
                    <div class="assignee-details">
                      <span class="assignee-name">{{ task.assigneeName }}</span>
                      <span class="assignee-role">{{ task.assigneeRole }}</span>
                    </div>
                  </div>
                  <div class="assignment-meta">
                    <el-tag 
                      :type="getAssignmentStatusType(task.assignmentStatus)"
                      size="small"
                    >
                      {{ getAssignmentStatusText(task.assignmentStatus) }}
                    </el-tag>
                    <span class="confidence">
                      置信度: {{ task.assignmentConfidence }}%
                    </span>
                  </div>
                  <div v-if="task.assignmentReason" class="assignment-reason">
                    <span class="reason-label">分配原因:</span>
                    <span class="reason-text">{{ task.assignmentReason }}</span>
                  </div>
                </div>
                <div v-else class="unassigned">
                  <el-alert 
                    title="未分配执行人" 
                    type="warning" 
                    :closable="false"
                    show-icon
                  >
                    <template #default>
                      <div class="unassigned-actions">
                        <span>此任务尚未分配执行人</span>
                        <el-button 
                          @click="handleAdjustAssignment(index, task)"
                          type="primary" 
                          size="small"
                        >
                          立即分配
                        </el-button>
                      </div>
                    </template>
                  </el-alert>
                </div>
              </div>

              <!-- 候选人列表 -->
              <div v-if="task.candidates && task.candidates.length > 0" class="candidates-section">
                <h6>推荐候选人</h6>
                <div class="candidates-list">
                  <div 
                    v-for="candidate in task.candidates.slice(0, 3)" 
                    :key="candidate.userId"
                    class="candidate-item"
                    @click="handleQuickAssign(index, candidate)"
                  >
                    <el-avatar :size="24">
                      {{ candidate.userName?.charAt(0) || '?' }}
                    </el-avatar>
                    <span class="candidate-name">{{ candidate.userName }}</span>
                    <span class="candidate-score">{{ candidate.score || candidate.confidence }}分</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineEmits } from 'vue'

// Props
const props = defineProps({
  project: {
    type: Object,
    required: true
  },
  availableUsers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['adjust-assignment', 'quick-assign', 'preview-updated'])

// 计算属性
const assignedCount = computed(() => {
  return props.project.tasks?.filter(task => task.isAssigned).length || 0
})

const unassignedCount = computed(() => {
  return props.project.tasks?.filter(task => !task.isAssigned).length || 0
})

// 方法
const handleAdjustAssignment = (taskIndex, task) => {
  emit('adjust-assignment', {
    taskIndex,
    projectId: props.project.projectId,
    task
  })
}

const handleQuickAssign = (taskIndex, candidate) => {
  emit('quick-assign', {
    taskIndex,
    projectId: props.project.projectId,
    candidate
  })
}

// 工具方法
const getPriorityType = (priority) => {
  const types = { 1: 'info', 2: 'info', 3: 'warning', 4: 'danger', 5: 'danger' }
  return types[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = { 1: '低', 2: '较低', 3: '中', 4: '较高', 5: '高' }
  return texts[priority] || '中'
}

const getAssignmentStatusType = (status) => {
  const types = {
    'ASSIGNED': 'success',
    'PENDING': 'warning',
    'CONFIRMED': 'success',
    'REJECTED': 'danger'
  }
  return types[status] || 'info'
}

const getAssignmentStatusText = (status) => {
  const texts = {
    'ASSIGNED': '已分配',
    'PENDING': '待确认',
    'CONFIRMED': '已确认',
    'REJECTED': '已拒绝'
  }
  return texts[status] || '未知'
}
</script>

<style scoped>
.project-task-list {
  padding: 16px;
}

.project-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.project-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.project-stats {
  display: flex;
  gap: 8px;
}

.tasks-section {
  margin-top: 16px;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-item {
  border-radius: 8px;
  overflow: hidden;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.task-title h5 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-description p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.task-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-row .label {
  font-weight: 600;
  color: #909399;
  min-width: 80px;
}

.assignment-info {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
}

.assigned {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.assignee-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.assignee-name {
  font-weight: 600;
  color: #303133;
}

.assignee-role {
  font-size: 12px;
  color: #909399;
}

.assignment-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.confidence {
  font-size: 12px;
  color: #909399;
}

.assignment-reason {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.reason-label {
  color: #909399;
  font-weight: 600;
}

.reason-text {
  color: #606266;
}

.unassigned-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.candidates-section {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.candidates-section h6 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.candidates-list {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.candidate-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.candidate-item:hover {
  background: #dbeafe;
  border-color: #93c5fd;
}

.candidate-name {
  font-size: 12px;
  color: #1e40af;
  font-weight: 500;
}

.candidate-score {
  font-size: 11px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 4px;
}
</style>