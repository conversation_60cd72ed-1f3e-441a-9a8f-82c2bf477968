<template>
  <div class="ai-guidance-container">
    <!-- 当前情况输入 -->
    <div class="situation-input">
      <el-input
        v-model="currentSituation"
        type="textarea"
        :rows="3"
        placeholder="描述当前执行情况，AI将为您提供针对性指导..."
        @blur="requestGuidance"
        :maxlength="500"
        show-word-limit
      />
    </div>

    <!-- AI指导内容 -->
    <div v-if="guidance" class="guidance-content">
      <div class="guidance-header">
        <el-icon class="guidance-icon"><MagicStick /></el-icon>
        <span class="guidance-title">AI执行指导</span>
        <el-tag :type="getPriorityType(guidance.priority)" size="small">
          {{ guidance.priority || '常规' }}
        </el-tag>
        <el-button size="small" @click="refreshGuidance" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新指导
        </el-button>
      </div>

      <div class="guidance-body">
        <!-- 主要指导建议 -->
        <div class="guidance-section">
          <h4><el-icon><Guide /></el-icon>执行建议</h4>
          <div class="guidance-text">{{ guidance.guidance }}</div>
        </div>

        <!-- 行动项目 -->
        <div v-if="guidance.actionItems?.length" class="guidance-section">
          <h4><el-icon><List /></el-icon>行动清单</h4>
          <ul class="action-list">
            <li v-for="(item, index) in guidance.actionItems" :key="index">
              <el-checkbox v-model="actionStatus[index]" @change="updateActionStatus">
                {{ item }}
              </el-checkbox>
            </li>
          </ul>
        </div>

        <!-- 风险提示 -->
        <div v-if="guidance.risks?.length" class="guidance-section risk-section">
          <h4><el-icon><Warning /></el-icon>风险提示</h4>
          <ul class="risk-list">
            <li v-for="(risk, index) in guidance.risks" :key="index">
              <el-alert :title="risk" type="warning" :closable="false" show-icon />
            </li>
          </ul>
        </div>

        <!-- 额外信息 -->
        <div v-if="guidance.additionalInfo" class="guidance-section">
          <h4><el-icon><InfoFilled /></el-icon>补充信息</h4>
          <div class="additional-info">
            <div 
              v-for="(value, key) in guidance.additionalInfo" 
              :key="key"
              class="info-item"
            >
              <strong>{{ formatKey(key) }}：</strong>
              <span>{{ value }}</span>
            </div>
          </div>
        </div>

        <!-- 时间戳 -->
        <div class="guidance-footer">
          <span class="timestamp">
            生成时间：{{ formatTime(guidance.timestamp) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-state">
      <el-skeleton :rows="5" animated />
      <div class="loading-text">AI正在分析当前情况，生成执行指导...</div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="请描述当前执行情况，获取AI智能指导">
        <el-button type="primary" @click="requestGuidance">获取AI指导</el-button>
      </el-empty>
    </div>

    <!-- 历史指导记录 -->
    <div v-if="guidanceHistory.length" class="history-section">
      <el-collapse v-model="activeHistoryItems">
        <el-collapse-item title="历史指导记录" name="history">
          <div class="history-list">
            <div 
              v-for="(item, index) in guidanceHistory" 
              :key="index"
              class="history-item"
              @click="loadHistoryGuidance(item)"
            >
              <div class="history-header">
                <span class="history-time">{{ formatTime(item.timestamp) }}</span>
                <el-tag size="small">{{ item.priority }}</el-tag>
              </div>
              <div class="history-preview">{{ item.guidance.substring(0, 100) }}...</div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { MagicStick, Refresh, Guide, List, Warning, InfoFilled } from "@element-plus/icons-vue";
import { useCool } from "/@/cool";

// Props
interface Props {
  task: any;
  currentSituation?: string;
}

const props = withDefaults(defineProps<Props>(), {
  currentSituation: "",
});

// Emits
const emit = defineEmits<{
  guidanceUpdated: [guidance: any];
}>();

const { service } = useCool();

// 响应式数据
const currentSituation = ref(props.currentSituation);
const guidance = ref(null);
const loading = ref(false);
const actionStatus = reactive({});
const guidanceHistory = ref([]);
const activeHistoryItems = ref([]);

// 生命周期
onMounted(() => {
  loadGuidanceHistory();
});

// 监听器
watch(() => props.currentSituation, (newVal) => {
  currentSituation.value = newVal;
});

watch(() => props.task, () => {
  guidance.value = null;
  loadGuidanceHistory();
});

// 方法定义
async function requestGuidance() {
  if (!props.task?.id) return;

  loading.value = true;
  try {
    const result = await service.sop.task.getAIGuidance(
      props.task.id,
      currentSituation.value
    );
    
    guidance.value = result;
    
    // 重置行动状态
    Object.keys(actionStatus).forEach(key => {
      delete actionStatus[key];
    });
    
    // 保存到历史记录
    saveToHistory(result);
    
    emit('guidanceUpdated', result);
    
  } catch (error) {
    ElMessage.error("获取AI指导失败");
    console.error("AI guidance error:", error);
  } finally {
    loading.value = false;
  }
}

async function refreshGuidance() {
  await requestGuidance();
}

function getPriorityType(priority: string) {
  const types: Record<string, string> = {
    高: "danger",
    中: "warning",
    低: "info",
    常规: "",
  };
  return types[priority] || "";
}

function formatKey(key: string) {
  const keyMap: Record<string, string> = {
    estimatedTime: "预计时间",
    requiredTools: "所需工具",
    skillLevel: "技能要求",
    dependencies: "依赖项",
    nextSteps: "下一步",
    references: "参考资料",
  };
  return keyMap[key] || key;
}

function formatTime(timestamp: string | Date) {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN");
}

function updateActionStatus() {
  // 记录用户的行动完成情况
  const completedActions = Object.values(actionStatus).filter(Boolean).length;
  const totalActions = guidance.value?.actionItems?.length || 0;
  
  if (completedActions === totalActions && totalActions > 0) {
    ElMessage.success("所有行动项目已完成！");
  }
}

function saveToHistory(guidanceData: any) {
  if (!guidanceData) return;
  
  const historyItem = {
    ...guidanceData,
    timestamp: new Date().toISOString(),
  };
  
  guidanceHistory.value.unshift(historyItem);
  
  // 限制历史记录数量
  if (guidanceHistory.value.length > 10) {
    guidanceHistory.value = guidanceHistory.value.slice(0, 10);
  }
  
  // 保存到本地存储
  localStorage.setItem(
    `ai-guidance-history-${props.task?.id}`, 
    JSON.stringify(guidanceHistory.value)
  );
}

function loadGuidanceHistory() {
  if (!props.task?.id) return;
  
  try {
    const saved = localStorage.getItem(`ai-guidance-history-${props.task.id}`);
    if (saved) {
      guidanceHistory.value = JSON.parse(saved);
    }
  } catch (error) {
    console.error("Load guidance history error:", error);
  }
}

function loadHistoryGuidance(item: any) {
  guidance.value = item;
  currentSituation.value = item.currentSituation || "";
  
  // 重置行动状态
  Object.keys(actionStatus).forEach(key => {
    delete actionStatus[key];
  });
}
</script>

<style scoped>
.ai-guidance-container {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.situation-input {
  margin-bottom: 20px;
}

.guidance-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.guidance-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.guidance-icon {
  font-size: 20px;
}

.guidance-title {
  font-weight: bold;
  font-size: 16px;
}

.guidance-body {
  padding: 20px;
}

.guidance-section {
  margin-bottom: 24px;
}

.guidance-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.guidance-text {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
  color: #606266;
}

.action-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.action-list li {
  margin-bottom: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.action-list li:last-child {
  border-bottom: none;
}

.risk-section .risk-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.risk-list li {
  margin-bottom: 8px;
}

.additional-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.info-item {
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.guidance-footer {
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

.timestamp {
  color: #909399;
  font-size: 12px;
}

.loading-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-text {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.history-section {
  margin-top: 20px;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-time {
  color: #909399;
  font-size: 12px;
}

.history-preview {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

:deep(.el-collapse-item__header) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-alert) {
  margin-bottom: 8px;
}

:deep(.el-alert:last-child) {
  margin-bottom: 0;
}
</style> 