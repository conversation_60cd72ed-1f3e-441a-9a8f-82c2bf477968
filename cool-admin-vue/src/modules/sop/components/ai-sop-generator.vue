<template>
  <div class="ai-sop-generator">
    <div class="generator-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="所属行业" prop="industry">
          <el-select 
            v-model="form.industry" 
            placeholder="请选择行业"
            style="width: 100%"
          >
            <el-option
              v-for="item in industries"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="流程描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="8"
            placeholder="请详细描述您想要创建的SOP流程，包括：
1. 流程的目标和目的
2. 主要执行步骤
3. 质量要求
4. 安全注意事项
5. 所需资源等

例如：创建一个客户投诉处理流程，包括接收投诉、调查问题、提出解决方案、跟进客户满意度等步骤..."
          />
        </el-form-item>

        <el-form-item label="特殊要求" prop="requirements">
          <el-input
            v-model="form.requirements"
            type="textarea"
            :rows="3"
            placeholder="请输入特殊要求，如：合规要求、安全标准、时间限制等（可选）"
          />
        </el-form-item>

        <el-form-item label="AI模型">
          <el-radio-group v-model="form.aiModel">
            <el-radio label="gpt-4">GPT-4（推荐）</el-radio>
            <el-radio label="gpt-3.5-turbo">GPT-3.5 Turbo</el-radio>
            <el-radio label="local">本地模型</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-bar">
      <el-button @click="$emit('cancel')">
        取消
      </el-button>
      <el-button 
        type="primary" 
        :loading="generating"
        @click="generateSOP"
      >
        {{ generating ? 'AI生成中...' : '生成SOP' }}
      </el-button>
    </div>

    <!-- 生成结果展示 -->
    <div v-if="generatedResult" class="result-section">
      <el-divider content-position="left">
        <span class="divider-text">AI生成结果</span>
      </el-divider>

      <div class="result-content">
        <!-- SOP基本信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">SOP基本信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="SOP名称">
              {{ generatedResult.sopInfo?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="所属行业">
              {{ generatedResult.sopInfo?.industry }}
            </el-descriptions-item>
            <el-descriptions-item label="预计时长">
              {{ generatedResult.sopInfo?.estimatedDuration }}分钟
            </el-descriptions-item>
            <el-descriptions-item label="难度等级">
              <el-rate 
                v-model="generatedResult.sopInfo.difficultyLevel" 
                disabled 
                show-score
              />
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              {{ generatedResult.sopInfo?.description }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 执行步骤 -->
        <el-card class="steps-card" shadow="never">
          <template #header>
            <span class="card-title">执行步骤（{{ generatedResult.steps?.length || 0 }}步）</span>
          </template>
          <div class="steps-container">
            <div 
              v-for="(step, index) in generatedResult.steps" 
              :key="index"
              class="step-item"
            >
              <div class="step-header">
                <el-tag :type="getStepTagType(step.stepType)" size="small">
                  步骤 {{ step.stepOrder }}
                </el-tag>
                <span class="step-name">{{ step.stepName }}</span>
                <el-tag size="small" plain>
                  {{ step.estimatedTime }}分钟
                </el-tag>
              </div>
              <div class="step-content">
                <p><strong>执行说明：</strong>{{ step.instructions }}</p>
                <p v-if="step.acceptanceCriteria">
                  <strong>验收标准：</strong>{{ step.acceptanceCriteria }}
                </p>
                <div v-if="step.requiredSkills?.length" class="step-skills">
                  <strong>所需技能：</strong>
                  <el-tag 
                    v-for="skill in step.requiredSkills" 
                    :key="skill"
                    size="small"
                    class="skill-tag"
                  >
                    {{ skill }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 质量标准 -->
        <el-card v-if="generatedResult.qualityStandards" class="quality-card" shadow="never">
          <template #header>
            <span class="card-title">质量标准</span>
          </template>
          <p class="quality-overall">{{ generatedResult.qualityStandards.overallStandards }}</p>
          <ul v-if="generatedResult.qualityStandards.specificCriteria?.length" class="quality-list">
            <li 
              v-for="(criteria, index) in generatedResult.qualityStandards.specificCriteria" 
              :key="index"
            >
              {{ criteria }}
            </li>
          </ul>
        </el-card>

        <!-- 风险评估 -->
        <el-card v-if="generatedResult.riskAssessment" class="risk-card" shadow="never">
          <template #header>
            <span class="card-title">风险评估</span>
          </template>
          <el-alert 
            :title="`整体风险等级：${generatedResult.riskAssessment.overallRiskLevel}`"
            :type="getRiskAlertType(generatedResult.riskAssessment.overallRiskLevel)"
            show-icon
            :closable="false"
          />
          <div v-if="generatedResult.riskAssessment.riskFactors?.length" class="risk-factors">
            <h4>风险因素：</h4>
            <ul>
              <li 
                v-for="(factor, index) in generatedResult.riskAssessment.riskFactors" 
                :key="index"
              >
                {{ factor }}
              </li>
            </ul>
          </div>
          <div v-if="generatedResult.riskAssessment.mitigationMeasures?.length" class="mitigation">
            <h4>缓解措施：</h4>
            <ul>
              <li 
                v-for="(measure, index) in generatedResult.riskAssessment.mitigationMeasures" 
                :key="index"
              >
                {{ measure }}
              </li>
            </ul>
          </div>
        </el-card>

        <!-- AI评估 -->
        <el-card v-if="generatedResult.assessment" class="assessment-card" shadow="never">
          <template #header>
            <span class="card-title">AI评估</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="assessment-item">
                <span class="assessment-label">完整性</span>
                <el-progress 
                  :percentage="generatedResult.assessment.completeness" 
                  :color="getScoreColor(generatedResult.assessment.completeness)"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="assessment-item">
                <span class="assessment-label">可执行性</span>
                <el-progress 
                  :percentage="generatedResult.assessment.executability"
                  :color="getScoreColor(generatedResult.assessment.executability)"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="assessment-item">
                <span class="assessment-label">安全性</span>
                <el-progress 
                  :percentage="generatedResult.assessment.safety"
                  :color="getScoreColor(generatedResult.assessment.safety)"
                />
              </div>
            </el-col>
          </el-row>
          <div v-if="generatedResult.assessment.suggestions?.length" class="suggestions">
            <h4>改进建议：</h4>
            <ul>
              <li 
                v-for="(suggestion, index) in generatedResult.assessment.suggestions" 
                :key="index"
              >
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </el-card>
      </div>

      <div class="result-actions">
        <el-button @click="regenerateSOP">
          重新生成
        </el-button>
        <el-button 
          type="primary" 
          @click="confirmGeneration"
        >
          确认并创建SOP
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useCool } from "/@/cool";

const { service } = useCool();

// 事件定义
const emit = defineEmits<{
  generated: [result: any];
  cancel: [];
}>();

// 响应式数据
const formRef = ref();
const generating = ref(false);
const generatedResult = ref(null);
const industries = ref([]);

const form = reactive({
  industry: "",
  description: "",
  requirements: "",
  aiModel: "gpt-4",
});

const rules = {
  industry: [
    { required: true, message: "请选择行业", trigger: "change" }
  ],
  description: [
    { required: true, message: "请输入流程描述", trigger: "blur" },
    { min: 50, message: "流程描述至少50个字符", trigger: "blur" }
  ],
};

// 生命周期
onMounted(() => {
  loadIndustries();
});

// 方法定义
async function loadIndustries() {
  try {
    const res = await service.sop.industry.list();
    industries.value = res.map((item: any) => ({
      label: item.name,
      value: item.name,
    }));
  } catch (error) {
    console.error("加载行业数据失败:", error);
  }
}

async function generateSOP() {
  const valid = await formRef.value?.validate();
  if (!valid) return;

  generating.value = true;
  try {
    const params = {
      description: form.description,
      industry: form.industry,
      requirements: form.requirements,
      aiModel: form.aiModel,
    };

    const result = await service.sop.template.generateByAI(params);
    generatedResult.value = result;
    ElMessage.success("SOP生成成功！");
  } catch (error) {
    console.error("生成SOP失败:", error);
    ElMessage.error("生成SOP失败，请重试");
  } finally {
    generating.value = false;
  }
}

function regenerateSOP() {
  generatedResult.value = null;
  generateSOP();
}

function confirmGeneration() {
  if (generatedResult.value) {
    emit("generated", generatedResult.value);
  }
}

function getStepTagType(stepType: string) {
  const types: Record<string, string> = {
    preparation: "",
    execution: "success",
    verification: "warning",
    completion: "info",
  };
  return types[stepType] || "";
}

function getRiskAlertType(riskLevel: string) {
  const types: Record<string, any> = {
    "低": "success",
    "中": "warning", 
    "高": "error",
  };
  return types[riskLevel] || "info";
}

function getScoreColor(score: number) {
  if (score >= 80) return "#67c23a";
  if (score >= 60) return "#e6a23c";
  return "#f56c6c";
}
</script>

<style scoped>
.ai-sop-generator {
  max-height: 70vh;
  overflow-y: auto;
}

.generator-form {
  margin-bottom: 20px;
}

.action-bar {
  text-align: right;
  padding: 15px 0;
  border-top: 1px solid #ebeef5;
}

.result-section {
  margin-top: 20px;
}

.divider-text {
  font-weight: bold;
  color: #409eff;
}

.result-content {
  margin: 20px 0;
}

.info-card,
.steps-card,
.quality-card,
.risk-card,
.assessment-card {
  margin-bottom: 15px;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.steps-container {
  max-height: 400px;
  overflow-y: auto;
}

.step-item {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
  background: #fafafa;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.step-name {
  font-weight: bold;
  flex: 1;
}

.step-content p {
  margin: 8px 0;
  line-height: 1.6;
}

.step-skills {
  margin-top: 10px;
}

.skill-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.quality-overall {
  margin-bottom: 15px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 4px;
  line-height: 1.6;
}

.quality-list,
.risk-factors ul,
.mitigation ul,
.suggestions ul {
  margin: 10px 0;
  padding-left: 20px;
}

.quality-list li,
.risk-factors li,
.mitigation li,
.suggestions li {
  margin: 5px 0;
  line-height: 1.6;
}

.risk-factors,
.mitigation,
.suggestions {
  margin-top: 15px;
}

.risk-factors h4,
.mitigation h4,
.suggestions h4 {
  margin: 10px 0;
  color: #606266;
}

.assessment-item {
  text-align: center;
}

.assessment-label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
  color: #606266;
}

.result-actions {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}
</style> 