<template>
  <div class="execution-logger">
    <el-card>
      <template #header>
        <div class="header-content">
          <span>执行日志</span>
          <div class="header-actions">
            <el-button size="small" @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" type="primary" @click="exportLogs">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-bar">
        <el-form :model="filters" inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="small"
              @change="handleFilterChange"
            />
          </el-form-item>
          <el-form-item label="日志级别">
            <el-select v-model="filters.level" size="small" @change="handleFilterChange">
              <el-option label="全部" value="" />
              <el-option label="信息" value="info" />
              <el-option label="警告" value="warning" />
              <el-option label="错误" value="error" />
              <el-option label="成功" value="success" />
            </el-select>
          </el-form-item>
          <el-form-item label="执行人">
            <el-select v-model="filters.executor" size="small" @change="handleFilterChange">
              <el-option label="全部" value="" />
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="王五" value="王五" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日志列表 -->
      <div class="log-container">
        <div class="log-timeline">
          <div 
            v-for="log in filteredLogs" 
            :key="log.id"
            class="log-item"
            :class="log.level"
          >
            <div class="log-time">
              {{ formatTime(log.timestamp) }}
            </div>
            <div class="log-content">
              <div class="log-header">
                <span class="log-title">{{ log.title }}</span>
                <el-tag :type="getTagType(log.level)" size="small">{{ log.level }}</el-tag>
              </div>
              <div class="log-message">{{ log.message }}</div>
              <div class="log-meta">
                <span class="log-executor">执行人: {{ log.executor }}</span>
                <span class="log-step">步骤: {{ log.stepName }}</span>
                <span class="log-duration" v-if="log.duration">耗时: {{ log.duration }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePaginationChange"
            @current-change="handlePaginationChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'

const props = defineProps<{
  taskId?: string | number
}>()

const filters = ref({
  dateRange: [] as any[],
  level: '',
  executor: ''
})

const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

const logs = ref<any[]>([])

const filteredLogs = computed(() => {
  let result = logs.value

  if (filters.value.level) {
    result = result.filter(log => log.level === filters.value.level)
  }

  if (filters.value.executor) {
    result = result.filter(log => log.executor === filters.value.executor)
  }

  if (filters.value.dateRange && filters.value.dateRange.length === 2) {
    const [start, end] = filters.value.dateRange
    result = result.filter(log => {
      const logDate = new Date(log.timestamp)
      return logDate >= start && logDate <= end
    })
  }

  pagination.value.total = result.length
  const startIndex = (pagination.value.current - 1) * pagination.value.size
  const endIndex = startIndex + pagination.value.size
  
  return result.slice(startIndex, endIndex)
})

onMounted(() => {
  loadLogs()
})

const loadLogs = async () => {
  try {
    // 模拟数据
    logs.value = [
      {
        id: 1,
        timestamp: new Date('2024-12-01 09:00:00'),
        level: 'info',
        title: '任务开始',
        message: '开始执行设备维护检查任务',
        executor: '张三',
        stepName: '准备工作',
        duration: null
      },
      {
        id: 2,
        timestamp: new Date('2024-12-01 09:05:00'),
        level: 'success',
        title: '步骤完成',
        message: '完成设备检查准备工作，所有工具和防护用品已准备就绪',
        executor: '张三',
        stepName: '准备工作',
        duration: '5分钟'
      },
      {
        id: 3,
        timestamp: new Date('2024-12-01 09:10:00'),
        level: 'info',
        title: '步骤开始',
        message: '开始执行设备外观检查',
        executor: '张三',
        stepName: '外观检查',
        duration: null
      },
      {
        id: 4,
        timestamp: new Date('2024-12-01 09:15:00'),
        level: 'warning',
        title: '发现问题',
        message: '发现设备外壳有轻微磨损，需要后续关注',
        executor: '张三',
        stepName: '外观检查',
        duration: null
      },
      {
        id: 5,
        timestamp: new Date('2024-12-01 09:25:00'),
        level: 'success',
        title: '步骤完成',
        message: '设备外观检查完成，已记录发现的问题',
        executor: '张三',
        stepName: '外观检查',
        duration: '15分钟'
      },
      {
        id: 6,
        timestamp: new Date('2024-12-01 09:30:00'),
        level: 'error',
        title: '检查异常',
        message: '设备运行测试时发现异常响声，需要进一步检查',
        executor: '张三',
        stepName: '运行测试',
        duration: null
      }
    ]
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  }
}

const refreshLogs = () => {
  loadLogs()
  ElMessage.success('日志已刷新')
}

const exportLogs = () => {
  ElMessage.success('日志导出功能开发中...')
}

const handleFilterChange = () => {
  pagination.value.current = 1
}

const handlePaginationChange = () => {
  // 分页逻辑在computed中处理
}

const formatTime = (timestamp: Date) => {
  return new Date(timestamp).toLocaleString()
}

const getTagType = (level: string) => {
  const types: any = {
    info: 'info',
    success: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return types[level] || 'info'
}
</script>

<style scoped>
.execution-logger {
  height: 100%;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.log-container {
  max-height: 600px;
  overflow-y: auto;
}

.log-timeline {
  position: relative;
  padding-left: 30px;
}

.log-timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dcdfe6;
}

.log-item {
  position: relative;
  margin-bottom: 20px;
  padding: 15px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-item::before {
  content: '';
  position: absolute;
  left: -37px;
  top: 20px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #409eff;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #409eff;
}

.log-item.success::before {
  background: #67c23a;
  box-shadow: 0 0 0 2px #67c23a;
}

.log-item.warning::before {
  background: #e6a23c;
  box-shadow: 0 0 0 2px #e6a23c;
}

.log-item.error::before {
  background: #f56c6c;
  box-shadow: 0 0 0 2px #f56c6c;
}

.log-item.info::before {
  background: #909399;
  box-shadow: 0 0 0 2px #909399;
}

.log-time {
  position: absolute;
  left: -120px;
  top: 15px;
  font-size: 12px;
  color: #999;
}

.log-content {
  margin-left: 20px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-title {
  font-weight: 600;
  color: #333;
}

.log-message {
  color: #666;
  margin-bottom: 10px;
  line-height: 1.5;
}

.log-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #999;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

@media (max-width: 768px) {
  .log-time {
    position: static;
    font-size: 11px;
    margin-bottom: 5px;
  }
  
  .log-timeline {
    padding-left: 10px;
  }
  
  .log-item::before {
    left: -17px;
  }
}
</style> 