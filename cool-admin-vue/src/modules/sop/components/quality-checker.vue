<template>
  <div class="quality-checker">
    <el-card>
      <template #header>
        <div class="header-content">
          <span>质量检查</span>
          <el-tag :type="getOverallScoreType()" size="small">
            综合评分: {{ overallScore }}/100
          </el-tag>
        </div>
      </template>

      <!-- 检查项目列表 -->
      <div class="check-items">
        <div 
          v-for="(item, index) in checkItems" 
          :key="item.id"
          class="check-item"
          :class="{ 'completed': item.status === 'completed' }"
        >
          <div class="item-header">
            <div class="item-info">
              <h4 class="item-title">{{ item.title }}</h4>
              <p class="item-description">{{ item.description }}</p>
            </div>
            <div class="item-status">
              <el-tag :type="getStatusType(item.status)">
                {{ getStatusText(item.status) }}
              </el-tag>
            </div>
          </div>

          <div class="item-content" v-if="item.status !== 'pending'">
            <!-- 检查结果 -->
            <div class="check-result">
              <el-rate 
                v-model="item.score" 
                :max="5"
                :disabled="item.status === 'completed'"
                @change="updateItemScore(item)"
              />
              <span class="score-text">{{ item.score }}/5</span>
            </div>

            <!-- 检查详情 -->
            <div class="check-details">
              <el-form :model="item" label-width="80px" size="small">
                <el-form-item label="检查结果">
                  <el-radio-group 
                    v-model="item.result" 
                    :disabled="item.status === 'completed'"
                    @change="updateItemResult(item)"
                  >
                    <el-radio label="pass">通过</el-radio>
                    <el-radio label="fail">不通过</el-radio>
                    <el-radio label="warning">有问题但可接受</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="问题描述" v-if="item.result !== 'pass'">
                  <el-input 
                    v-model="item.issues"
                    type="textarea"
                    :rows="2"
                    placeholder="请描述发现的问题..."
                    :disabled="item.status === 'completed'"
                  />
                </el-form-item>

                <el-form-item label="改进建议">
                  <el-input 
                    v-model="item.suggestions"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入改进建议..."
                    :disabled="item.status === 'completed'"
                  />
                </el-form-item>

                <el-form-item label="检查照片">
                  <el-upload
                    class="upload-demo"
                    :action="uploadAction"
                    :file-list="item.photos"
                    :disabled="item.status === 'completed'"
                    list-type="picture-card"
                    :on-success="(response: any, file: any) => handleUploadSuccess(response, file, item)"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
              </el-form>
            </div>

            <!-- 操作按钮 -->
            <div class="item-actions" v-if="item.status === 'checking'">
              <el-button type="success" @click="completeCheck(item)">
                完成检查
              </el-button>
              <el-button @click="saveProgress(item)">
                保存进度
              </el-button>
            </div>
          </div>

          <!-- 开始检查按钮 -->
          <div class="item-actions" v-if="item.status === 'pending'">
            <el-button type="primary" @click="startCheck(item)">
              开始检查
            </el-button>
          </div>
        </div>
      </div>

      <!-- 总结报告 -->
      <div class="summary-report" v-if="allItemsCompleted">
        <el-divider>质量检查总结</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="检查项目数">{{ checkItems.length }}</el-descriptions-item>
          <el-descriptions-item label="通过项目">{{ passedItems }}</el-descriptions-item>
          <el-descriptions-item label="失败项目">{{ failedItems }}</el-descriptions-item>
          <el-descriptions-item label="问题项目">{{ warningItems }}</el-descriptions-item>
          <el-descriptions-item label="平均评分">{{ averageScore }}/5</el-descriptions-item>
          <el-descriptions-item label="综合评分">{{ overallScore }}/100</el-descriptions-item>
        </el-descriptions>

        <div class="final-actions">
          <el-button type="success" size="large" @click="submitReport">
            提交质量报告
          </el-button>
          <el-button size="large" @click="exportReport">
            导出报告
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps<{
  taskId?: string | number
  templateId?: string | number
}>()

const emit = defineEmits<{
  'completed': [report: any]
}>()

const uploadAction = '/api/upload'

const checkItems = ref([
  {
    id: 1,
    title: '设备外观检查',
    description: '检查设备外观是否完好，无明显损坏',
    status: 'pending',
    score: 0,
    result: '',
    issues: '',
    suggestions: '',
    photos: []
  },
  {
    id: 2,
    title: '运行状态检查',
    description: '检查设备运行是否正常，无异常声音和振动',
    status: 'pending',
    score: 0,
    result: '',
    issues: '',
    suggestions: '',
    photos: []
  },
  {
    id: 3,
    title: '安全设施检查',
    description: '检查安全防护设施是否到位，警示标识是否清晰',
    status: 'pending',
    score: 0,
    result: '',
    issues: '',
    suggestions: '',
    photos: []
  },
  {
    id: 4,
    title: '维护记录检查',
    description: '检查维护记录是否完整，是否按时执行保养',
    status: 'pending',
    score: 0,
    result: '',
    issues: '',
    suggestions: '',
    photos: []
  }
])

const allItemsCompleted = computed(() => {
  return checkItems.value.every(item => item.status === 'completed')
})

const passedItems = computed(() => {
  return checkItems.value.filter(item => item.result === 'pass').length
})

const failedItems = computed(() => {
  return checkItems.value.filter(item => item.result === 'fail').length
})

const warningItems = computed(() => {
  return checkItems.value.filter(item => item.result === 'warning').length
})

const averageScore = computed(() => {
  const completedItems = checkItems.value.filter(item => item.status === 'completed')
  if (completedItems.length === 0) return 0
  const totalScore = completedItems.reduce((sum, item) => sum + item.score, 0)
  return Math.round((totalScore / completedItems.length) * 10) / 10
})

const overallScore = computed(() => {
  const passWeight = 40
  const scoreWeight = 40
  const completionWeight = 20
  
  const passRate = passedItems.value / checkItems.value.length
  const avgScore = averageScore.value / 5
  const completionRate = allItemsCompleted.value ? 1 : 0
  
  return Math.round((passRate * passWeight + avgScore * scoreWeight + completionRate * completionWeight))
})

const startCheck = (item: any) => {
  item.status = 'checking'
  ElMessage.success(`开始检查: ${item.title}`)
}

const completeCheck = (item: any) => {
  if (!item.result) {
    ElMessage.warning('请选择检查结果')
    return
  }
  if (item.score === 0) {
    ElMessage.warning('请给出评分')
    return
  }
  
  item.status = 'completed'
  ElMessage.success(`${item.title} 检查完成`)
}

const saveProgress = (item: any) => {
  ElMessage.success('进度已保存')
}

const updateItemScore = (item: any) => {
  // 评分更新逻辑
}

const updateItemResult = (item: any) => {
  // 结果更新逻辑
}

const handleUploadSuccess = (response: any, file: any, item: any) => {
  item.photos.push({
    name: file.name,
    url: response.url
  })
  ElMessage.success('照片上传成功')
}

const submitReport = () => {
  const report = {
    taskId: props.taskId,
    templateId: props.templateId,
    items: checkItems.value,
    summary: {
      totalItems: checkItems.value.length,
      passedItems: passedItems.value,
      failedItems: failedItems.value,
      warningItems: warningItems.value,
      averageScore: averageScore.value,
      overallScore: overallScore.value
    },
    submitTime: new Date()
  }
  
  emit('completed', report)
  ElMessage.success('质量报告已提交')
}

const exportReport = () => {
  ElMessage.success('报告导出功能开发中...')
}

const getStatusType = (status: string) => {
  const types: any = {
    pending: 'info',
    checking: 'warning',
    completed: 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: any = {
    pending: '待检查',
    checking: '检查中',
    completed: '已完成'
  }
  return texts[status] || '未知'
}

const getOverallScoreType = () => {
  const score = overallScore.value
  if (score >= 90) return 'success'
  if (score >= 70) return 'warning'
  return 'danger'
}
</script>

<style scoped>
.quality-checker {
  max-height: 800px;
  overflow-y: auto;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.check-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.check-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s;
}

.check-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.check-item.completed {
  background: #f0f9ff;
  border-color: #67c23a;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.item-info {
  flex: 1;
}

.item-title {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.item-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.item-status {
  margin-left: 20px;
}

.item-content {
  margin-top: 20px;
}

.check-result {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.score-text {
  font-weight: 600;
  color: #409eff;
}

.check-details {
  margin-bottom: 20px;
}

.item-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.summary-report {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.final-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.upload-demo {
  max-width: 400px;
}
</style> 