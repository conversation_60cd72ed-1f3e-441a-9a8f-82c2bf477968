<template>
  <div class="sop-step-editor">
    <div class="editor-header">
      <h4>SOP执行步骤</h4>
      <el-button 
        type="primary" 
        size="small" 
        icon="Plus"
        @click="addStep"
      >
        添加步骤
      </el-button>
    </div>

    <div class="steps-container">
      <draggable
        v-model="steps"
        group="steps"
        item-key="id"
        handle=".drag-handle"
        @change="onStepsChange"
      >
        <template #item="{ element: step, index }">
          <div class="step-card" :key="step.id">
            <div class="step-header">
              <div class="step-controls">
                <span class="drag-handle">
                  <el-icon><DCaret /></el-icon>
                </span>
                <span class="step-number">步骤 {{ index + 1 }}</span>
              </div>
              <div class="step-actions">
                <el-button 
                  type="danger" 
                  size="small" 
                  link
                  @click="removeStep(index)"
                >
                  删除
                </el-button>
              </div>
            </div>

            <div class="step-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="步骤名称" required>
                    <el-input 
                      v-model="step.stepName"
                      placeholder="请输入步骤名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="步骤类型">
                    <el-select 
                      v-model="step.stepType"
                      placeholder="请选择步骤类型"
                      style="width: 100%"
                    >
                      <el-option label="准备阶段" value="preparation" />
                      <el-option label="执行阶段" value="execution" />
                      <el-option label="验证阶段" value="verification" />
                      <el-option label="完成阶段" value="completion" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="预计时长(分钟)">
                    <el-input-number 
                      v-model="step.estimatedTime"
                      :min="1"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="执行顺序">
                    <el-input-number 
                      v-model="step.stepOrder"
                      :min="1"
                      style="width: 100%"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="执行说明" required>
                <el-input 
                  v-model="step.instructions"
                  type="textarea"
                  :rows="3"
                  placeholder="请详细描述此步骤的执行方法和要求"
                />
              </el-form-item>

              <el-form-item label="验收标准">
                <el-input 
                  v-model="step.acceptanceCriteria"
                  type="textarea"
                  :rows="2"
                  placeholder="请描述此步骤完成的标准和要求"
                />
              </el-form-item>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="所需技能">
                    <el-select
                      v-model="step.requiredSkills"
                      multiple
                      filterable
                      allow-create
                      placeholder="请选择或输入所需技能"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="skill in commonSkills"
                        :key="skill"
                        :label="skill"
                        :value="skill"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="前置步骤">
                    <el-select
                      v-model="step.dependencies"
                      multiple
                      placeholder="请选择前置步骤"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(depStep, depIndex) in steps"
                        :key="depStep.id"
                        :label="`步骤${depIndex + 1}: ${depStep.stepName}`"
                        :value="depIndex + 1"
                        :disabled="depIndex >= index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="质量检查点">
                <el-select
                  v-model="step.qualityCheckPoints"
                  multiple
                  filterable
                  allow-create
                  placeholder="请输入质量检查要点"
                  style="width: 100%"
                >
                  <el-option
                    v-for="checkpoint in commonCheckpoints"
                    :key="checkpoint"
                    :label="checkpoint"
                    :value="checkpoint"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="风险点">
                <el-select
                  v-model="step.riskPoints"
                  multiple
                  filterable
                  allow-create
                  placeholder="请输入可能的风险点"
                  style="width: 100%"
                >
                  <el-option
                    v-for="risk in commonRisks"
                    :key="risk"
                    :label="risk"
                    :value="risk"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="所需资源">
                <el-select
                  v-model="step.resources"
                  multiple
                  filterable
                  allow-create
                  placeholder="请输入所需的工具、设备、材料等"
                  style="width: 100%"
                >
                  <el-option
                    v-for="resource in commonResources"
                    :key="resource"
                    :label="resource"
                    :value="resource"
                  />
                </el-select>
              </el-form-item>

              <!-- 物业管理专用字段 -->
              <!-- 执行人管理 -->
              <el-form-item label="执行人">
                <assignee-selector v-model="step.assignees" />
                
                <!-- 显示已选执行人 -->
                <div v-if="step.assignees && step.assignees.length > 0" class="selected-assignees">
                  <div class="assignees-header">
                    <span class="assignees-count">已选择 {{ step.assignees.length }} 个执行人</span>
                  </div>
                  <div class="assignees-list">
                    <el-tag 
                      v-for="assignee in step.assignees" 
                      :key="assignee.id"
                      type="info"
                      size="small"
                      class="assignee-tag"
                    >
                      <div class="assignee-info">
                        <el-avatar 
                          :src="assignee.headImg" 
                          :size="20"
                          class="assignee-avatar"
                        >
                          {{ assignee.name?.charAt(0) }}
                        </el-avatar>
                        <span class="assignee-name">{{ assignee.name }}</span>
                        <span v-if="assignee.departmentName" class="assignee-dept">
                          ({{ assignee.departmentName }})
                        </span>
                      </div>
                    </el-tag>
                  </div>
                </div>
                
                <div v-else class="no-assignees">
                  <el-text type="info" size="small">暂未分配执行人</el-text>
                </div>
              </el-form-item>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="责任部门">
                    <el-select
                      v-model="step.responsibleDept"
                      placeholder="请选择责任部门"
                      style="width: 100%"
                    >
                      <el-option label="客服中心" value="customer_service" />
                      <el-option label="工程维修部" value="engineering" />
                      <el-option label="环境管理部" value="environment" />
                      <el-option label="安防部" value="security_dept" />
                      <el-option label="品质管理部" value="quality" />
                      <el-option label="财务部" value="finance" />
                      <el-option label="综合管理部" value="general" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系业主方式">
                    <el-input
                      v-model="step.ownerContactMethod"
                      placeholder="如：电话通知、上门拜访、微信联系等"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="现场照片要求">
                    <el-switch v-model="step.requirePhotos" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="业主签字确认">
                    <el-switch v-model="step.requireOwnerSignature" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="材料费用预算">
                    <el-input-number 
                      v-model="step.materialCost"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="人工费用预算">
                    <el-input-number 
                      v-model="step.laborCost"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="特殊工具设备">
                    <el-select
                      v-model="step.specialEquipment"
                      multiple
                      filterable
                      allow-create
                      placeholder="请输入特殊工具设备"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="equipment in commonEquipment"
                        :key="equipment"
                        :label="equipment"
                        :value="equipment"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="安全注意事项">
                <el-input 
                  v-model="step.safetyNotes"
                  type="textarea"
                  :rows="2"
                  placeholder="请描述执行此步骤时需要注意的安全事项"
                />
              </el-form-item>

              <el-form-item label="质量检查表">
                <el-select
                  v-model="step.qualityChecklist"
                  multiple
                  filterable
                  allow-create
                  placeholder="请输入质量检查项目"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in commonQualityItems"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <div v-if="steps.length === 0" class="empty-state">
      <el-empty description="暂无步骤，请点击上方按钮添加">
        <el-button type="primary" @click="addStep">
          添加第一个步骤
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { DCaret, Plus } from "@element-plus/icons-vue";
import draggable from "vuedraggable";
import AssigneeSelector from "/@/components/assignee-selector";

// Props & Emits
const props = defineProps<{
  modelValue: any[];
}>();

const emit = defineEmits<{
  "update:modelValue": [value: any[]];
}>();

// 响应式数据
const steps = ref<any[]>([]);

// 常用选项数据
const commonSkills = ref([
  "基础操作", "沟通协调", "数据分析", "质量控制", "安全操作",
  "技术维护", "客户服务", "文档编写", "流程管理", "系统操作"
]);

const commonCheckpoints = ref([
  "操作规范性检查", "数据准确性验证", "质量标准符合性",
  "安全要求检查", "完整性验证", "时效性检查",
  "合规性确认", "客户满意度确认"
]);

const commonRisks = ref([
  "操作错误", "数据丢失", "质量不达标", "安全隐患",
  "时间延误", "沟通误解", "系统故障", "资源不足"
]);

const commonResources = ref([
  "电脑设备", "办公软件", "检测工具", "原材料",
  "参考文档", "联系方式", "权限账号", "专业设备"
]);

const commonEquipment = ref([
  "梯子", "电钻", "螺丝刀", "扳手", "测量工具",
  "安全帽", "防护服", "专业仪器", "清洁工具",
  "维修工具包", "测试设备", "照相设备"
]);

const commonQualityItems = ref([
  "外观质量检查", "功能性测试", "安全性检查", "清洁度检查",
  "完整性验证", "合规性检查", "客户满意度确认", "文档完整性检查",
  "时效性检查", "标准符合性检查"
]);

// 计算属性
const stepCounter = ref(1);

// 监听器
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      steps.value = newVal.map((step, index) => ({
        id: step.id || `step_${Date.now()}_${index}`,
        stepOrder: index + 1,
        stepName: step.stepName || "",
        stepType: step.stepType || "execution",
        instructions: step.instructions || "",
        acceptanceCriteria: step.acceptanceCriteria || "",
        estimatedTime: step.estimatedTime || 30,
        requiredSkills: step.requiredSkills || [],
        dependencies: step.dependencies || [],
        qualityCheckPoints: step.qualityCheckPoints || [],
        riskPoints: step.riskPoints || [],
        resources: step.resources || [],
        assignees: step.assignees || [],
        responsibleDept: step.responsibleDept || "",
        ownerContactMethod: step.ownerContactMethod || "",
        requirePhotos: step.requirePhotos || false,
        requireOwnerSignature: step.requireOwnerSignature || false,
        materialCost: step.materialCost || 0,
        laborCost: step.laborCost || 0,
        specialEquipment: step.specialEquipment || [],
        safetyNotes: step.safetyNotes || "",
        qualityChecklist: step.qualityChecklist || [],
      }));
    } else {
      steps.value = [];
    }
  },
  { immediate: true, deep: true }
);

watch(
  steps,
  (newSteps) => {
    // 更新步骤顺序
    const updatedSteps = newSteps.map((step, index) => ({
      ...step,
      stepOrder: index + 1,
    }));
    emit("update:modelValue", updatedSteps);
  },
  { deep: true }
);

// 方法定义
function addStep() {
  const newStep = {
    id: `step_${Date.now()}_${stepCounter.value}`,
    stepOrder: steps.value.length + 1,
    stepName: "",
    stepType: "execution",
    instructions: "",
    acceptanceCriteria: "",
    estimatedTime: 30,
    requiredSkills: [],
    dependencies: [],
    qualityCheckPoints: [],
    riskPoints: [],
    resources: [],
    assignees: [],
    responsibleDept: "",
    ownerContactMethod: "",
    requirePhotos: false,
    requireOwnerSignature: false,
    materialCost: 0,
    laborCost: 0,
    specialEquipment: [],
    safetyNotes: "",
    qualityChecklist: [],
  };
  
  steps.value.push(newStep);
  stepCounter.value++;
}

function removeStep(index: number) {
  steps.value.splice(index, 1);
  
  // 更新其他步骤的依赖关系
  steps.value.forEach(step => {
    if (step.dependencies) {
      step.dependencies = step.dependencies
        .filter((dep: number) => dep <= index + 1 || dep > index + 1)
        .map((dep: number) => dep > index + 1 ? dep - 1 : dep);
    }
  });
}

function onStepsChange() {
  // 拖拽后更新步骤顺序
  steps.value.forEach((step, index) => {
    step.stepOrder = index + 1;
  });
}
</script>

<style scoped>
.sop-step-editor {
  min-height: 300px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.editor-header h4 {
  margin: 0;
  color: #303133;
}

.steps-container {
  max-height: 600px;
  overflow-y: auto;
}

.step-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 15px;
  background: #fff;
  transition: all 0.3s ease;
}

.step-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px 10px;
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
  border-radius: 8px 8px 0 0;
}

.step-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.drag-handle {
  cursor: move;
  color: #909399;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.drag-handle:hover {
  color: #409eff;
}

.step-number {
  font-weight: bold;
  color: #303133;
  font-size: 14px;
}

.step-content {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select__tags) {
  max-height: 80px;
  overflow-y: auto;
}

/* 拖拽时的样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  transform: scale(0.98);
}

.sortable-drag {
  transform: rotate(2deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ========== SELECTED ASSIGNEES STYLE ========== */
.selected-assignees {
  margin-top: 12px;
  padding: 12px;
  background: var(--el-fill-color-extra-light);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.assignees-header {
  margin-bottom: 8px;
}

.assignees-count {
  font-size: 12px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.assignees-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.assignee-tag {
  padding: 4px 8px;
  border-radius: 4px;
  background: var(--el-color-info-light-8);
  border: 1px solid var(--el-color-info-light-6);
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.assignee-avatar {
  flex-shrink: 0;
}

.assignee-name {
  font-size: 12px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.assignee-dept {
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.no-assignees {
  margin-top: 8px;
  padding: 8px 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  text-align: center;
}

/* 暗色主题兼容 */
html.dark .selected-assignees {
  background: var(--el-fill-color-dark);
  border-color: var(--el-border-color-darker);
}

html.dark .assignee-tag {
  background: var(--el-color-info-dark-2);
  border-color: var(--el-color-info);
}

html.dark .no-assignees {
  background: var(--el-fill-color-darker);
}
</style>