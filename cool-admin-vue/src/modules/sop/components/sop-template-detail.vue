<template>
  <div class="sop-template-detail">
    <!-- 基本信息 -->
    <el-card class="info-card" shadow="never">
      <template #header>
        <span class="card-title">基本信息</span>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="模板名称">
          {{ template.name }}
        </el-descriptions-item>
        <el-descriptions-item label="所属行业">
          {{ template.industry }}
        </el-descriptions-item>
        <el-descriptions-item label="版本号">
          {{ template.version }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(template.status)">
            {{ getStatusText(template.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="预计时长">
          {{ template.estimatedDuration }}分钟
        </el-descriptions-item>
        <el-descriptions-item label="难度等级">
          <el-rate :model-value="template.difficultyLevel" disabled show-score />
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ template.description || '暂无描述' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ template.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ template.updateTime }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 执行步骤 -->
    <el-card class="steps-card" shadow="never">
      <template #header>
        <span class="card-title">执行步骤（{{ steps.length }}步）</span>
      </template>
      
      <div v-if="steps.length === 0" class="empty-state">
        <el-empty description="暂无执行步骤" />
      </div>
      
      <div v-else class="steps-timeline">
        <el-timeline>
          <el-timeline-item
            v-for="(step, index) in steps"
            :key="step.id"
            :timestamp="`步骤 ${step.stepOrder}`"
            placement="top"
            :type="getStepType(step.stepType)"
            :size="index === 0 ? 'large' : 'normal'"
          >
            <el-card class="step-card" shadow="hover">
              <div class="step-header">
                <h4 class="step-title">{{ step.stepName }}</h4>
                <div class="step-meta">
                  <el-tag size="small" :type="getStepTagType(step.stepType)">
                    {{ getStepTypeText(step.stepType) }}
                  </el-tag>
                  <el-tag size="small" plain>
                    {{ step.estimatedTime }}分钟
                  </el-tag>
                </div>
              </div>
              
              <div class="step-content">
                <div class="step-section">
                  <h5>执行说明：</h5>
                  <p>{{ step.instructions || '暂无说明' }}</p>
                </div>
                
                <div v-if="step.acceptanceCriteria" class="step-section">
                  <h5>验收标准：</h5>
                  <p>{{ step.acceptanceCriteria }}</p>
                </div>
                
                <div v-if="step.requiredSkills?.length" class="step-section">
                  <h5>所需技能：</h5>
                  <div class="tags-container">
                    <el-tag
                      v-for="skill in step.requiredSkills"
                      :key="skill"
                      size="small"
                      class="skill-tag"
                    >
                      {{ skill }}
                    </el-tag>
                  </div>
                </div>
                
                <div v-if="step.dependencies?.length" class="step-section">
                  <h5>前置步骤：</h5>
                  <div class="dependencies">
                    <span
                      v-for="dep in step.dependencies"
                      :key="dep"
                      class="dependency-item"
                    >
                      步骤{{ dep }}
                    </span>
                  </div>
                </div>
                
                <div v-if="step.qualityCheckPoints?.length" class="step-section">
                  <h5>质量检查点：</h5>
                  <ul class="check-points">
                    <li v-for="point in step.qualityCheckPoints" :key="point">
                      {{ point }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="step.riskPoints?.length" class="step-section">
                  <h5>风险点：</h5>
                  <ul class="risk-points">
                    <li v-for="risk in step.riskPoints" :key="risk">
                      {{ risk }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="step.resources?.length" class="step-section">
                  <h5>所需资源：</h5>
                  <div class="tags-container">
                    <el-tag
                      v-for="resource in step.resources"
                      :key="resource"
                      size="small"
                      type="info"
                      class="resource-tag"
                    >
                      {{ resource }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-card v-if="stats" class="stats-card" shadow="never">
      <template #header>
        <span class="card-title">使用统计</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.totalWorkOrders || 0 }}</div>
            <div class="stat-label">总工单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.completedWorkOrders || 0 }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.averageCompletionTime || 0 }}min</div>
            <div class="stat-label">平均完成时间</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.successRate || 0 }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="$emit('close')">
        关闭
      </el-button>
      <el-button type="primary" @click="createWorkOrder">
        基于此模板创建工单
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useCool } from "/@/cool";

const { service } = useCool();

interface TemplateStats {
	totalWorkOrders: number;
	completedWorkOrders: number;
	averageCompletionTime: number;
	successRate: number;
}

type ElementPlusType = "primary" | "success" | "info" | "warning" | "danger";

// Props & Emits
const props = defineProps<{
	template: any;
}>();

const emit = defineEmits<{
	close: [];
}>();

// 响应式数据
const stats = ref<TemplateStats | null>(null);

// 计算属性
const steps = computed(() => {
  if (!props.template.steps) return [];
  return props.template.steps.sort((a: any, b: any) => a.stepOrder - b.stepOrder);
});

// 生命周期
onMounted(() => {
  loadStats();
});

// 方法定义
async function loadStats() {
  try {
    const result = await service.sop.template.getStats(props.template.id);
    stats.value = result;
  } catch (error) {
    console.error("加载统计信息失败:", error);
  }
}

function getStatusType(status: string): ElementPlusType | undefined {
	const types: Record<string, ElementPlusType> = {
		published: "success",
		archived: "info"
	};
	return types[status];
}

function getStatusText(status: string) {
  const texts: Record<string, string> = {
    draft: "草稿",
    published: "已发布",
    archived: "已归档",
  };
  return texts[status] || status;
}

function getStepType(stepType: string): ElementPlusType {
  const types: Record<string, ElementPlusType> = {
    preparation: "info",
    execution: "primary",
    verification: "warning",
    completion: "success",
  };
  return types[stepType] || "primary";
}

function getStepTagType(stepType: string): ElementPlusType | undefined {
	const types: Record<string, ElementPlusType> = {
		preparation: "info",
		execution: "success",
		verification: "warning"
	};
	return types[stepType];
}

function getStepTypeText(stepType: string) {
  const texts: Record<string, string> = {
    preparation: "准备阶段",
    execution: "执行阶段",
    verification: "验证阶段",
    completion: "完成阶段",
  };
  return texts[stepType] || stepType;
}

function createWorkOrder() {
  // TODO: 实现基于模板创建工单的逻辑
  ElMessage.success("功能开发中...");
}
</script>

<style scoped>
.sop-template-detail {
  max-height: 80vh;
  overflow-y: auto;
}

.info-card,
.steps-card,
.stats-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.steps-timeline {
  padding: 0 20px;
}

.step-card {
  margin-bottom: 15px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.step-title {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.step-meta {
  display: flex;
  gap: 8px;
}

.step-content {
  line-height: 1.6;
}

.step-section {
  margin-bottom: 15px;
}

.step-section h5 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.step-section p {
  margin: 0;
  color: #303133;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.skill-tag,
.resource-tag {
  margin: 0;
}

.dependencies {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dependency-item {
  padding: 4px 8px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
  color: #409eff;
}

.check-points,
.risk-points {
  margin: 0;
  padding-left: 16px;
}

.check-points li,
.risk-points li {
  margin-bottom: 4px;
}

.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-card :deep(.el-card__header) {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.stats-card .card-title {
  color: white;
}

.stat-item {
  text-align: center;
  padding: 20px 10px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.actions {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-timeline-item__timestamp) {
  font-weight: bold;
  color: #409eff;
}

:deep(.el-timeline-item__content) {
  padding-left: 0;
}
</style> 