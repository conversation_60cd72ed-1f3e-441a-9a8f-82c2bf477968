<template>
  <el-dialog 
    v-model="visible"
    title="任务执行"
    width="70%"
    :close-on-click-modal="false"
  >
    <div class="task-execution">
      <!-- 任务信息 -->
      <el-card class="task-info">
        <template #header>
          <span>任务信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务标题">{{ task.title }}</el-descriptions-item>
          <el-descriptions-item label="工单编号">{{ task.workOrderNo }}</el-descriptions-item>
          <el-descriptions-item label="SOP模板">{{ task.templateName }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(task.priority)">{{ task.priority }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 执行步骤 -->
      <el-card class="execution-steps">
        <template #header>
          <div class="step-header">
            <span>执行步骤</span>
            <div class="step-progress">
              <span>进度: {{ completedSteps }}/{{ totalSteps }}</span>
              <el-progress :percentage="progressPercentage" :width="100" />
            </div>
          </div>
        </template>
        
        <el-steps :active="currentStepIndex" direction="vertical" finish-status="success">
          <el-step 
            v-for="(step, index) in steps" 
            :key="step.id"
            :title="step.name"
            :description="step.description"
            :status="getStepStatus(step, index)"
          >
            <template #icon>
              <el-icon v-if="step.status === 'completed'"><Check /></el-icon>
              <el-icon v-else-if="step.status === 'active'"><Clock /></el-icon>
              <span v-else>{{ index + 1 }}</span>
            </template>
          </el-step>
        </el-steps>
      </el-card>

      <!-- 当前步骤详情 -->
      <el-card v-if="currentStep" class="current-step">
        <template #header>
          <span>当前步骤: {{ currentStep.name }}</span>
        </template>
        
        <div class="step-content">
          <div class="step-description">
            <h4>步骤说明</h4>
            <p>{{ currentStep.description }}</p>
          </div>
          
          <div class="step-checklist" v-if="currentStep.checkpoints">
            <h4>检查点</h4>
            <el-checkbox-group v-model="checkedItems">
              <div 
                v-for="checkpoint in currentStep.checkpoints" 
                :key="checkpoint.id"
                class="checkpoint-item"
              >
                <el-checkbox :label="checkpoint.id">{{ checkpoint.text }}</el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <div class="step-notes">
            <h4>执行备注</h4>
            <el-input 
              v-model="stepNotes"
              type="textarea"
              :rows="3"
              placeholder="请输入执行过程中的备注..."
            />
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button 
          v-if="currentStepIndex > 0"
          @click="previousStep"
        >
          上一步
        </el-button>
        <el-button 
          type="primary" 
          @click="nextStep"
          :disabled="!canProceed"
        >
          {{ isLastStep ? '完成任务' : '下一步' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Clock } from '@element-plus/icons-vue'

const props = defineProps<{
  modelValue: boolean
  task?: any
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'complete': [task: any]
}>()

const visible = ref(false)
const currentStepIndex = ref(0)
const checkedItems = ref<string[]>([])
const stepNotes = ref('')

const steps = ref([
  {
    id: 1,
    name: '准备工作',
    description: '检查设备和工具是否齐全',
    status: 'pending',
    checkpoints: [
      { id: '1', text: '检查工具箱' },
      { id: '2', text: '确认安全防护用品' }
    ]
  },
  {
    id: 2,
    name: '执行检查',
    description: '按照规程执行设备检查',
    status: 'pending',
    checkpoints: [
      { id: '3', text: '检查设备外观' },
      { id: '4', text: '检查运行状态' }
    ]
  },
  {
    id: 3,
    name: '记录结果',
    description: '记录检查结果和发现的问题',
    status: 'pending',
    checkpoints: [
      { id: '5', text: '填写检查记录' },
      { id: '6', text: '上传相关照片' }
    ]
  }
])

const currentStep = computed(() => {
  return steps.value[currentStepIndex.value]
})

const totalSteps = computed(() => steps.value.length)
const completedSteps = computed(() => {
  return steps.value.filter(step => step.status === 'completed').length
})

const progressPercentage = computed(() => {
  return Math.round((completedSteps.value / totalSteps.value) * 100)
})

const isLastStep = computed(() => {
  return currentStepIndex.value === steps.value.length - 1
})

const canProceed = computed(() => {
  if (!currentStep.value) return false
  if (currentStep.value.checkpoints) {
    return currentStep.value.checkpoints.every((cp: any) => 
      checkedItems.value.includes(cp.id)
    )
  }
  return true
})

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    initTask()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const initTask = () => {
  currentStepIndex.value = 0
  checkedItems.value = []
  stepNotes.value = ''
  
  // 重置步骤状态
  steps.value.forEach((step, index) => {
    step.status = index === 0 ? 'active' : 'pending'
  })
}

const getStepStatus = (step: any, index: number) => {
  if (step.status === 'completed') return 'finish'
  if (index === currentStepIndex.value) return 'process'
  return 'wait'
}

const getPriorityType = (priority: string) => {
  const types: any = {
    '高': 'danger',
    '中': 'warning', 
    '低': 'info'
  }
  return types[priority] || 'info'
}

const nextStep = () => {
  if (!canProceed.value) {
    ElMessage.warning('请完成当前步骤的所有检查点')
    return
  }

  // 标记当前步骤为完成
  steps.value[currentStepIndex.value].status = 'completed'

  if (isLastStep.value) {
    // 完成任务
    ElMessage.success('任务执行完成！')
    emit('complete', props.task)
    visible.value = false
  } else {
    // 进入下一步
    currentStepIndex.value++
    steps.value[currentStepIndex.value].status = 'active'
    checkedItems.value = []
    stepNotes.value = ''
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    steps.value[currentStepIndex.value].status = 'pending'
    currentStepIndex.value--
    steps.value[currentStepIndex.value].status = 'active'
    checkedItems.value = []
    stepNotes.value = ''
  }
}
</script>

<style scoped>
.task-execution {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.task-info {
  margin-bottom: 20px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.execution-steps {
  max-height: 400px;
  overflow-y: auto;
}

.current-step {
  border: 1px solid #409eff;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-description h4,
.step-checklist h4,
.step-notes h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.checkpoint-item {
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
}
</style> 