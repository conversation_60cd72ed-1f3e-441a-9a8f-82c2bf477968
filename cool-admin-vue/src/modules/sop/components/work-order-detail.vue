<template>
  <el-dialog 
    v-model="visible"
    title="工单详情"
    width="60%"
    :close-on-click-modal="false"
  >
    <div class="work-order-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="工单编号">{{ workOrder.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="工单标题">{{ workOrder.title }}</el-descriptions-item>
        <el-descriptions-item label="所属行业">{{ workOrder.industry }}</el-descriptions-item>
        <el-descriptions-item label="SOP模板">{{ workOrder.templateName }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityType(workOrder.priority)">{{ workOrder.priority }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(workOrder.status)">{{ workOrder.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(workOrder.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="预计完成时间">{{ formatDate(workOrder.estimatedTime) }}</el-descriptions-item>
      </el-descriptions>

      <el-divider>工单描述</el-divider>
      <div class="description">
        {{ workOrder.description || '暂无描述' }}
      </div>

      <el-divider>执行记录</el-divider>
      <el-table :data="executeRecords" size="small">
        <el-table-column prop="stepName" label="步骤名称" />
        <el-table-column prop="executor" label="执行人" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executeTime" label="执行时间" />
        <el-table-column prop="duration" label="耗时" />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: boolean
  workOrderId?: string | number
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'edit': [workOrder: any]
}>()

const visible = ref(false)
const workOrder = ref<any>({})
const executeRecords = ref<any[]>([])

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.workOrderId) {
    loadWorkOrderDetail()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadWorkOrderDetail = async () => {
  // 模拟数据
  workOrder.value = {
    orderNo: 'WO20241201001',
    title: '设备维护工单',
    industry: '制造业',
    templateName: '设备维护SOP',
    priority: '高',
    status: '进行中',
    createTime: new Date(),
    estimatedTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
    description: '对生产线设备进行定期维护检查，确保设备正常运行。'
  }

  executeRecords.value = [
    {
      stepName: '设备检查准备',
      executor: '张三',
      status: '已完成',
      executeTime: '2024-12-01 09:00:00',
      duration: '15分钟'
    },
    {
      stepName: '设备运行检查',
      executor: '张三',
      status: '进行中',
      executeTime: '2024-12-01 09:15:00',
      duration: '30分钟'
    }
  ]
}

const getPriorityType = (priority: string) => {
  const types: any = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return types[priority] || 'info'
}

const getStatusType = (status: string) => {
  const types: any = {
    '待处理': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return types[status] || 'info'
}

const formatDate = (date: any) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

const handleEdit = () => {
  emit('edit', workOrder.value)
  visible.value = false
}
</script>

<style scoped>
.work-order-detail {
  padding: 20px 0;
}

.description {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
  min-height: 80px;
}

.dialog-footer {
  text-align: right;
}
</style> 