import { ModuleConfig } from "/@/cool";

export default (): ModuleConfig => {
  return {
    // 是否启用
    enable: true,

    // 模块名称
    label: "SOP工单管理",

    // 模块描述
    description: "智能化标准作业程序管理系统，支持AI辅助的工单创建、任务执行和质量控制",

    // 作者
    author: "Cool Team",
    version: "1.0.0",
    updateTime: "2024-12-01",

    // 排序，数值越大越靠前
    order: 99,

    // 配置参数
    options: {
      // AI功能配置
      ai: {
        enabled: true,
        model: "gpt-4",
        maxTokens: 2048
      },
      
      // 工单配置
      workOrder: {
        autoAssign: true,
        reminderEnabled: true,
        qualityCheckRequired: true
      },

      // 通知配置
      notification: {
        email: true,
        sms: false,
        webhook: true
      }
    },

    // 视图路由配置
    views: [
      {
        path: "/sop/industry",
        name: "sop-industry",
        meta: {
          label: "行业管理",
          icon: "icon-industry",
          keepAlive: true
        },
        component: () => import("./views/s/o/p/industry.vue")
      },
      {
        path: "/sop/scenario",
        name: "sop-scenario", 
        meta: {
          label: "SOP场景",
          icon: "icon-scenario",
          keepAlive: true
        },
        component: () => import("./views/s/o/p/scenario.vue")
      },
      {
        path: "/sop/template",
        name: "sop-template", 
        meta: {
          label: "SOP模板",
          icon: "icon-template",
          keepAlive: true
        },
        component: () => import("./views/s/o/p/template.vue")
      },
      {
        path: "/sop/step",
        name: "sop-step",
        meta: {
          label: "SOP步骤",
          icon: "icon-step",
          keepAlive: true
        },
        component: () => import("./views/s/o/p/step.vue")
      },
      {
        path: "/sop/workorder",
        name: "sop-workorder",
        meta: {
          label: "工作工单",
          icon: "icon-workorder",
          keepAlive: true
        },
        component: () => import("./views/work/order.vue")
      },
      {
        path: "/sop/ai-assistant",
        name: "sop-ai-assistant",
        meta: {
          label: "AI助手", 
          icon: "icon-ai",
          keepAlive: true
        },
        component: () => import("./views/ai-assistant.vue")
      },
      {
        path: "/sop/ai-report",
        name: "sop-ai-report",
        meta: {
          label: "AI分析报告",
          icon: "icon-report",
          keepAlive: true
        },
        component: () => import("./views/ai-report.vue")
      },
      {
        path: "/sop/manager-dashboard",
        name: "sop-manager-dashboard",
        meta: {
          label: "管理者仪表盘",
          icon: "icon-dashboard", 
          keepAlive: true
        },
        component: () => import("./views/manager-dashboard.vue")
      },
      {
        path: "/sop/personal-workbench",
        name: "sop-personal-workbench",
        meta: {
          label: "个人工作台",
          icon: "icon-workbench",
          keepAlive: true
        },
        component: () => import("./views/personal-workbench.vue")
      },
      {
        path: "/sop/ai-generator",
        name: "sop-ai-generator",
        meta: {
          label: "AI生成器",
          icon: "el-icon-magic-stick",
          keepAlive: true
        },
        component: () => import("./components/AITaskGenerator.vue")
      },
      {
        path: "/sop/import",
        name: "sop-import",
        meta: {
          label: "SOP导入",
          icon: "el-icon-upload",
          keepAlive: true
        },
        component: () => import("./views/import/sop-import.vue")
      },
      {
        path: "/sop/ai-task-history",
        name: "sop-ai-task-history",
        meta: {
          label: "AI生成历史",
          icon: "el-icon-document",
          keepAlive: true
        },
        component: () => import("./views/ai-task-generate-record.vue")
      }
    ],

    ignore: {
      NProgress: ["/admin/sop/ai-task-generator/async-generate"]
    },

    // 注册全局组件
    components: [
      import("./components/work-order-detail.vue"),
      import("./components/task-execution-dialog.vue"),
      import("./components/execution-logger.vue"),
      import("./components/quality-checker.vue"),
      import("./components/sop-step-editor.vue"),
      () => import("../../components/assignee-selector/AssigneeSelector.vue")
    ],

    // 加载时触发
    onLoad(events) {
      console.log("SOP模块已加载");
      
      // 初始化SOP服务
      return Promise.resolve({
        // 导出工具方法
        generateWorkOrderNo() {
          const now = new Date();
          const dateStr = now.getFullYear().toString() + 
                         (now.getMonth() + 1).toString().padStart(2, '0') + 
                         now.getDate().toString().padStart(2, '0');
          const randomStr = Math.random().toString(36).substr(2, 4).toUpperCase();
          return `WO${dateStr}${randomStr}`;
        },

        // 格式化执行时间
        formatDuration(minutes) {
          if (!minutes) return '0分钟';
          const hours = Math.floor(minutes / 60);
          const mins = minutes % 60;
          if (hours > 0) {
            return `${hours}小时${mins > 0 ? mins + '分钟' : ''}`;
          }
          return `${mins}分钟`;
        },

        // 获取优先级颜色
        getPriorityColor(priority) {
          const colors = {
            1: '#67C23A',    // 低优先级
            2: '#E6A23C',    // 中优先级
            3: '#F56C6C',    // 高优先级
            4: '#FF0000'     // 紧急
          };
          return colors[priority] || colors[2];
        },

        // 获取状态颜色
        getStatusColor(status) {
          const colors = {
            0: '#909399',    // 待处理
            1: '#409EFF',    // 已分配
            2: '#E6A23C',    // 进行中
            3: '#67C23A',    // 已完成
            4: '#F56C6C',    // 已取消
            5: '#FF0000'     // 失败
          };
          return colors[status] || colors[0];
        }
      });
    }
  };
};