import { ref, reactive } from 'vue'
import config from "./config";

/**
 * SOP工单管理模块工具函数
 */
export function useSOP() {
  // SOP状态映射
  const sopStatusMap = {
    0: '草稿',
    1: '启用',
    2: '停用',
    3: '归档'
  }

  // 工单状态映射
  const workOrderStatusMap = {
    0: '待分配',
    1: '待处理',
    2: '处理中',
    3: '待验收',
    4: '已完成',
    5: '已取消'
  }

  // 任务状态映射
  const taskStatusMap = {
    'pending': '待处理',
    'in_progress': '进行中',
    'completed': '已完成',
    'paused': '已暂停',
    'cancelled': '已取消'
  }

  // 优先级映射
  const priorityMap = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }

  // 难度等级映射
  const difficultyMap = {
    1: '简单',
    2: '一般',
    3: '中等',
    4: '困难',
    5: '专家'
  }

  /**
   * 获取状态文本
   * @param status 状态值
   * @param type 状态类型 sop|workorder|task
   */
  const getStatusText = (status: any, type: string = 'task') => {
    switch (type) {
      case 'sop':
        return sopStatusMap[status] || '未知'
      case 'workorder':
        return workOrderStatusMap[status] || '未知'
      case 'task':
        return taskStatusMap[status] || '未知'
      default:
        return status
    }
  }

  /**
   * 获取优先级文本
   * @param priority 优先级
   */
  const getPriorityText = (priority: string) => {
    return priorityMap[priority] || '未知'
  }

  /**
   * 获取难度等级文本
   * @param level 难度等级
   */
  const getDifficultyText = (level: number) => {
    return difficultyMap[level] || '未知'
  }

  /**
   * 格式化步骤执行时间
   * @param minutes 分钟数
   */
  const formatStepDuration = (minutes: number) => {
    if (!minutes) return '0分钟'
    
    if (minutes < 60) {
      return `${minutes}分钟`
    }
    
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    
    if (remainingMinutes === 0) {
      return `${hours}小时`
    }
    
    return `${hours}小时${remainingMinutes}分钟`
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   */
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 生成SOP编码
   * @param industryCode 行业编码
   * @param sequence 序号
   */
  const generateSOPCode = (industryCode: string, sequence: number) => {
    const date = new Date()
    const year = date.getFullYear().toString().slice(-2)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const seq = sequence.toString().padStart(4, '0')
    
    return `SOP${industryCode}${year}${month}${day}${seq}`
  }

  /**
   * 生成工单编码
   * @param templateCode 模板编码
   * @param sequence 序号
   */
  const generateWorkOrderCode = (templateCode: string, sequence: number) => {
    const date = new Date()
    const year = date.getFullYear().toString().slice(-2)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const seq = sequence.toString().padStart(4, '0')
    
    return `WO${templateCode}${year}${month}${day}${seq}`
  }

  /**
   * 计算SOP模板完成进度
   * @param completedSteps 已完成步骤数
   * @param totalSteps 总步骤数
   */
  const calculateProgress = (completedSteps: number, totalSteps: number) => {
    if (totalSteps === 0) return 0
    return Math.round((completedSteps / totalSteps) * 100)
  }

  /**
   * 验证SOP步骤依赖关系
   * @param steps 步骤列表
   * @param currentStepId 当前步骤ID
   * @param dependencies 依赖步骤ID列表
   */
  const validateStepDependencies = (steps: any[], currentStepId: string, dependencies: string[]) => {
    if (!dependencies || dependencies.length === 0) return true
    
    const completedSteps = steps.filter(step => step.status === 'completed').map(step => step.id)
    return dependencies.every(depId => completedSteps.includes(depId))
  }

  /**
   * 获取步骤类型图标
   * @param stepType 步骤类型
   */
  const getStepTypeIcon = (stepType: string) => {
    const iconMap = {
      'preparation': 'icon-prepare',
      'execution': 'icon-play',
      'check': 'icon-check-circle',
      'record': 'icon-edit'
    }
    return iconMap[stepType] || 'icon-step'
  }

  /**
   * 获取AI功能状态
   */
  const aiFeatures = reactive({
    sopGeneration: true,
    smartScheduling: true,
    qualityCheck: true,
    executionGuidance: true,
    processOptimization: true
  })

  /**
   * 常用的表单验证规则
   */
  const formRules = {
    sopName: [
      { required: true, message: '请输入SOP名称', trigger: 'blur' },
      { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
    ],
    industryId: [
      { required: true, message: '请选择行业', trigger: 'change' }
    ],
    description: [
      { required: true, message: '请输入描述', trigger: 'blur' },
      { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
    ],
    stepName: [
      { required: true, message: '请输入步骤名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    estimatedTime: [
      { required: true, message: '请输入预估时间', trigger: 'blur' },
      { type: 'number', min: 1, max: 9999, message: '请输入1-9999之间的数字', trigger: 'blur' }
    ]
  }

  return {
    // 状态映射
    sopStatusMap,
    workOrderStatusMap,
    taskStatusMap,
    priorityMap,
    difficultyMap,
    
    // 工具函数
    getStatusText,
    getPriorityText,
    getDifficultyText,
    formatStepDuration,
    formatFileSize,
    generateSOPCode,
    generateWorkOrderCode,
    calculateProgress,
    validateStepDependencies,
    getStepTypeIcon,
    
    // AI功能状态
    aiFeatures,
    
    // 表单验证规则
    formRules
  }
}

export default config;