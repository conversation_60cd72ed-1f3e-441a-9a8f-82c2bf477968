export default {
  sop: {
    // 通用
    name: "SOP工单管理",
    description: "智能SOP工单管理系统",
    
    // 菜单
    menu: {
      industry: "行业管理",
      template: "SOP模板",
      workorder: "工作工单",
      task: "任务执行",
      aiAssistant: "AI助手",
    },
    
    // 行业管理
    industry: {
      name: "行业名称",
      code: "行业代码",
      description: "描述",
      templateCount: "SOP模板数",
      addIndustry: "添加行业",
      editIndustry: "编辑行业",
    },
    
    // SOP模板
    template: {
      name: "模板名称",
      industry: "所属行业",
      version: "版本",
      statusText: "状态",
      estimatedDuration: "预计时长(分钟)",
      difficultyLevel: "难度等级",
      steps: "执行步骤",
      aiGenerate: "AI生成SOP",
      copy: "复制",
      publish: "发布",
      archive: "归档",
      view: "查看",
      
      status: {
        draft: "草稿",
        published: "已发布",
        archived: "已归档",
      },
    },
    
    // AI功能
    ai: {
      generating: "AI生成中...",
      generateSOP: "生成SOP",
      regenerate: "重新生成",
      confirm: "确认并创建SOP",
      selectIndustry: "请选择行业",
      descriptionPlaceholder: "请详细描述您想要创建的SOP流程...",
      requirementsPlaceholder: "请输入特殊要求...",
      
      models: {
        gpt4: "GPT-4（推荐）",
        gpt35: "GPT-3.5 Turbo",
        local: "本地模型",
      },
      
      result: {
        title: "AI生成结果",
        sopInfo: "SOP基本信息",
        steps: "执行步骤",
        qualityStandards: "质量标准",
        riskAssessment: "风险评估",
        aiAssessment: "AI评估",
        
        completeness: "完整性",
        executability: "可执行性",
        safety: "安全性",
        suggestions: "改进建议",
      },
    },
    
    // 步骤编辑器
    stepEditor: {
      title: "SOP执行步骤",
      addStep: "添加步骤",
      stepName: "步骤名称",
      stepType: "步骤类型",
      estimatedTime: "预计时长(分钟)",
      stepOrder: "执行顺序",
      instructions: "执行说明",
      acceptanceCriteria: "验收标准",
      requiredSkills: "所需技能",
      dependencies: "前置步骤",
      qualityCheckPoints: "质量检查点",
      riskPoints: "风险点",
      resources: "所需资源",
      
      stepTypes: {
        preparation: "准备阶段",
        execution: "执行阶段",
        verification: "验证阶段",
        completion: "完成阶段",
      },
    },
    
    // 物业管理字段
    property: {
      serviceType: "服务类型",
      priority: "优先级", 
      areaType: "适用区域类型",
      requireOwnerConfirm: "是否需要业主确认",
      standardTimelimit: "标准处理时效(小时)",
      supportOnline: "是否支持在线处理",
      relatedSystems: "关联系统",
      feeType: "费用类型",
      propertyCategory: "物业管理分类",
      qualityIndicators: "质量考核指标",
      satisfactionRequirement: "客户满意度要求(%)",
      standardProcedure: "标准作业规范",
      
      responsibleDept: "责任部门",
      ownerContactMethod: "联系业主方式",
      requirePhotos: "现场照片要求",
      requireOwnerSignature: "业主签字确认",
      materialCost: "材料费用预算",
      laborCost: "人工费用预算",
      specialEquipment: "特殊工具设备",
      safetyNotes: "安全注意事项",
      qualityChecklist: "质量检查表",
      
      serviceTypes: {
        complaint: "投诉处理",
        repair: "维修服务", 
        cleaning: "清洁保洁",
        security: "安全巡查",
        facility: "设施维护",
        greening: "绿化养护",
        parking: "停车管理",
        decoration: "装修管理",
        moving: "搬家服务",
        emergency: "应急处理",
        visitor: "访客管理",
        package: "快递代收",
        activity: "社区活动",
        payment: "费用收缴",
        inspection: "房屋查验"
      },
      
      priorities: {
        low: "低",
        medium: "中", 
        high: "高",
        urgent: "紧急"
      },
      
      feeTypes: {
        free: "免费",
        charged: "收费",
        conditional: "按情况"
      }
    },

    messages: {
      generateSuccess: "SOP生成成功！",
      generateFailed: "生成SOP失败，请重试",
      copySuccess: "复制成功",
      copyFailed: "复制失败",
      publishSuccess: "发布成功",
      publishFailed: "发布失败",
      archiveSuccess: "归档成功",
      archiveFailed: "归档失败",
      
      confirmPublish: "确定要发布此SOP模板吗？发布后可供工单使用。",
      confirmArchive: "确定要归档此SOP模板吗？归档后将不可用于新工单。",
      inputTemplateName: "请输入新模板名称",
      templateNameRequired: "模板名称不能为空",
    },
  },
}; 