<template>
  <el-scrollbar>
    <div class="ai-assistant-page">
      <el-card>
        <template #header>
          <div class="header">
            <h3>SOP AI助手</h3>
            <p>智能化SOP管理助手，提供AI指导和建议</p>
          </div>
        </template>
        
        <div class="content">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="hover">
                <div class="feature-card">
                  <div class="icon">🎯</div>
                  <h4>AI生成SOP</h4>
                  <p>基于自然语言描述，智能生成标准作业程序</p>
                  <el-button type="primary" @click="goAIGenerate">开始生成</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card shadow="hover">
                <div class="feature-card">
                  <div class="icon">💬</div>
                  <h4>智能对话</h4>
                  <p>与AI助手对话，获取SOP相关的专业建议</p>
                  <el-button type="success" @click="showAIChat = true">开始对话</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card shadow="hover">
                <div class="feature-card">
                  <div class="icon">📊</div>
                  <h4>流程优化</h4>
                  <p>AI分析现有流程，提供优化建议</p>
                  <el-button type="warning" @click="showOptimization = true">优化分析</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card shadow="hover">
                <div class="feature-card">
                  <div class="icon">🔍</div>
                  <h4>质量检查</h4>
                  <p>AI辅助质量检查，确保执行标准</p>
                  <el-button type="danger" @click="showQualityCheck = true">质量检查</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- AI对话框 -->
      <el-dialog v-model="showAIChat" title="AI智能对话" width="800px">
        <div class="ai-chat-container">
          <p>AI助手功能开发中...</p>
        </div>
      </el-dialog>

      <!-- AI生成对话框 -->
      <el-dialog v-model="showAIGenerate" title="AI生成SOP" width="800px">
        <div class="ai-generate-container">
          <p>AI生成功能开发中...</p>
        </div>
      </el-dialog>

      <!-- 优化分析对话框 -->
      <el-dialog v-model="showOptimization" title="流程优化分析" width="800px">
        <div class="optimization-container">
          <p>流程优化功能开发中...</p>
        </div>
      </el-dialog>

      <!-- 质量检查对话框 -->
      <el-dialog v-model="showQualityCheck" title="AI质量检查" width="800px">
        <div class="quality-check-container">
          <p>质量检查功能开发中...</p>
        </div>
      </el-dialog>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
defineOptions({
  name: "sop-ai-assistant"
});

import { ref } from "vue";
import { useRouter } from "vue-router";

// 响应式数据
const showAIChat = ref(false);
const showAIGenerate = ref(false);
const showOptimization = ref(false);
const showQualityCheck = ref(false);

// 路由实例
const router = useRouter();

// 跳转到 AI 任务生成器页面
const goAIGenerate = () => {
  router.push("/sop/ai-generator");
};
</script>

<style scoped>
.ai-assistant-page {
  padding: 20px;
}

.header {
  text-align: center;
}

.header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.header p {
  margin: 0;
  color: #909399;
}

.feature-card {
  text-align: center;
  padding: 20px;
}

.feature-card .icon {
  font-size: 40px;
  margin-bottom: 15px;
}

.feature-card h4 {
  margin: 15px 0 10px 0;
  color: #303133;
}

.feature-card p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
}

.ai-chat-container,
.ai-generate-container,
.optimization-container,
.quality-check-container {
  padding: 20px;
  text-align: center;
  color: #909399;
}
</style> 