<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 导入按钮 -->
			<el-button
				type="success"
				:icon="Upload"
				@click="openImportDialog"
				size="default"
			>
				导入SOP
			</el-button>
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />

		<!-- 步骤详情侧边栏 -->
		<el-drawer
			v-model="stepsDrawerVisible"
			:title="`场景步骤列表 - ${selectedScenario?.scenarioName || ''}`"
			direction="rtl"
			size="70%"
			:before-close="handleCloseDrawer"
			class="steps-drawer"
			:modal="false"
			:with-header="true"
		>
			<template #header>
				<div class="drawer-header">
					<div class="header-info">
						<h3 class="scenario-title">
							<el-icon><Document /></el-icon>
							{{ selectedScenario?.scenarioName || '场景步骤' }}
						</h3>
						<div class="scenario-meta">
							<el-tag size="small" type="info">{{ selectedScenario?.moduleCode }}</el-tag>
							<el-tag size="small" :type="getStatusType(selectedScenario?.status)">
								{{ getStatusText(selectedScenario?.status) }}
							</el-tag>
							<span class="meta-text">共 {{ stepsList.length }} 个步骤</span>
							<span class="meta-text">预计 {{ selectedScenario?.estimatedDuration || 0 }} 分钟</span>
						</div>
					</div>
					<div class="header-actions">
						<el-button size="small" @click="loadStepsList">
							<el-icon><Refresh /></el-icon>
							刷新
						</el-button>
						<el-button type="primary" size="small" @click="handleAddStep">
							<el-icon><Plus /></el-icon>
							添加步骤
						</el-button>
					</div>
				</div>
			</template>

			<div v-if="stepsLoading" class="steps-loading">
				<div class="loading-content">
					<el-icon class="is-loading" :size="24"><Loading /></el-icon>
					<p>正在加载步骤数据...</p>
				</div>
			</div>
			
			<div v-else-if="!stepsList.length" class="steps-empty">
				<el-empty 
					description="该场景还没有配置步骤"
					:image-size="120"
				>
					<template #image>
						<div class="empty-icon">📋</div>
					</template>
					<el-button type="primary" @click="handleAddStep">
						<el-icon><Plus /></el-icon>
						创建第一个步骤
					</el-button>
				</el-empty>
			</div>

			<div v-else class="steps-content">
				<!-- 步骤时间线视图 -->
				<div class="steps-timeline">
					<el-timeline>
						<el-timeline-item
							v-for="(step, index) in stepsList"
							:key="step.id"
							:type="getTimelineType(step.stepType)"
							:size="step.stepType === 'key' ? 'large' : 'normal'"
							:icon="getStepIcon(step.stepType)"
							class="timeline-step"
						>
							<template #dot>
								<div class="custom-dot" :class="[`dot-${step.stepType}`]">
									<span class="dot-number">{{ step.stepOrder || index + 1 }}</span>
								</div>
							</template>
							
							<el-card class="step-card" :class="`step-card--${step.stepType}`">
								<div class="step-card-header">
									<div class="step-title-section">
										<h4 class="step-title">{{ step.stepName || '未命名步骤' }}</h4>
										<div class="step-tags">
											<el-tag
												:type="getStepTypeColor(step.stepType)"
												size="small"
												effect="light"
											>
												{{ getStepTypeText(step.stepType) }}
											</el-tag>
											<el-tag v-if="step.stepCode" size="small" type="info" effect="plain">
												{{ step.stepCode }}
											</el-tag>
										</div>
									</div>
									<div class="step-actions">
										<el-button-group size="small">
											<el-button @click="handleEditStep(step)" :icon="Edit">编辑</el-button>
											<el-button 
												@click="handleDeleteStep(step)" 
												type="danger" 
												:icon="Delete"
											>删除</el-button>
										</el-button-group>
									</div>
								</div>

								<div class="step-card-content">
									<!-- 基础信息行 -->
									<div class="info-row">
										<div class="info-item" v-if="step.employeeRole">
											<el-icon><User /></el-icon>
											<span>{{ step.employeeRole }}</span>
										</div>
										<div class="info-item" v-if="step.estimatedTime">
											<el-icon><Clock /></el-icon>
											<span>{{ step.estimatedTime }} 分钟</span>
										</div>
									</div>
									<!-- 展开详细信息 -->
									<el-collapse v-model="activeStepDetails[step.id]" class="step-details">
										<el-collapse-item :name="`detail-${step.id}`">
											<template #title>
												<div class="collapse-title">
													<el-icon
														class="collapse-arrow"
														:class="{
															'is-active':
																activeStepDetails[step.id]?.length > 0
														}"
													>
														<ArrowRight />
													</el-icon>
													<span>详细信息</span>
												</div>
											</template>
											<el-descriptions :column="2" size="small" border>
												<el-descriptions-item label="实体触点" :span="2">
													{{ step.entityTouchpoint || '-' }}
												</el-descriptions-item>
												<el-descriptions-item label="用户活动" :span="2">
													{{ step.userActivity || '-' }}
												</el-descriptions-item>
												<el-descriptions-item label="员工行为" :span="2">
													{{ step.employeeBehavior || '-' }}
												</el-descriptions-item>
												<el-descriptions-item label="工作亮点" :span="2">
													{{ step.workHighlight || '-' }}
												</el-descriptions-item>
												<el-descriptions-item label="相关附件" :span="2">
													<div class="attachment-tags" v-if="step.relatedAttachments">
														<el-tag 
															v-for="(attachment, idx) in parseAttachments(step.relatedAttachments)" 
															:key="idx"
															size="small"
															type="success"
															effect="light"
														>
															<el-icon><Paperclip /></el-icon>
															{{ attachment }}
														</el-tag>
													</div>
													<span v-else class="no-data">暂无附件</span>
												</el-descriptions-item>
											</el-descriptions>
										</el-collapse-item>
									</el-collapse>
								</div>
							</el-card>
						</el-timeline-item>
					</el-timeline>
				</div>
			</div>
		</el-drawer>
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "sop-s-o-p-scenario",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { ref } from "vue";
import { useRouter } from "vue-router";
import {
	Loading,
	Plus,
	Refresh,
	Edit,
	Delete,
	Document,
	User,
	Clock,
	Paperclip,
	ArrowRight,
	Upload
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";

const { service } = useCool();
const { t } = useI18n();
const router = useRouter();

// 侧边栏相关状态
const stepsDrawerVisible = ref(false);
const selectedScenario = ref<any>(null);
const stepsList = ref<any[]>([]);
const stepsLoading = ref(false);

// 折叠面板和标签页状态
const activeSteps = ref<string[]>([]);
const activeTab = ref<Record<string, string>>({});
const activeStepDetails = ref<Record<string, string[]>>({});

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("选择所属行业"),
			hidden: true,
			prop: "industryId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所属行业名称"),
			prop: "industryName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("阶段（支持多级数据字典）"),
			prop: "stage",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("模块编码"),
			prop: "moduleCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("模块名称"),
			prop: "moduleName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("场景编码"),
			prop: "scenarioCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("场景名称"),
			prop: "scenarioName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("执行周期（自然语言描述，后续AI转换）"),
			prop: "executionCycle",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("AI转换后的执行频率（日/周/月/季度/半年/年）"),
			prop: "executionFrequency",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("执行次数"),
			prop: "executionCount",
			component: { 
				name: "el-input-number",
				props: { 
					min: 0,
					max: 9999,
					placeholder: "请输入执行次数"
				} 
			},
			span: 12,
		},
		{
			label: t("版本号"),
			prop: "version",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("状态"),
			prop: "status",
			component: { 
				name: "el-select",
				props: {
					clearable: true,
					placeholder: "请选择状态"
				},
				options: [
					{ label: "草稿", value: 0 },
					{ label: "启用", value: 1 },
					{ label: "禁用", value: 2 },
					{ label: "已归档", value: 3 }
				]
			},
			span: 12,
		},
		{
			label: t("场景描述"),
			prop: "description",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea",
					rows: 3,
					clearable: true,
					placeholder: "请描述该场景的具体内容和执行要求"
				} 
			},
			span: 24,
		},
		{
			label: t("总步骤数"),
			prop: "totalSteps",
			component: { 
				name: "el-input-number",
				props: { 
					min: 0,
					max: 999,
					placeholder: "请输入总步骤数"
				} 
			},
			span: 12,
		},
		{
			label: t("预计总耗时(分钟)"),
			prop: "estimatedDuration",
			component: { 
				name: "el-input-number",
				props: { 
					min: 1,
					max: 99999,
					placeholder: "请输入预计耗时"
				} 
			},
			span: 12,
		},
		{
			label: t("难度等级"),
			prop: "difficultyLevel",
			component: { 
				name: "el-select",
				props: {
					clearable: true,
					placeholder: "请选择难度等级"
				},
				options: [
					{ label: "1级 - 简单", value: 1 },
					{ label: "2级 - 容易", value: 2 },
					{ label: "3级 - 中等", value: 3 },
					{ label: "4级 - 困难", value: 4 },
					{ label: "5级 - 复杂", value: 5 }
				]
			},
			span: 12,
		},
		{
			label: t("质量标准"),
			prop: "qualityStandard",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请描述质量检查标准和要求"
				} 
			},
			span: 12,
		},
		{
			label: t("成功标准"),
			prop: "successCriteria",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请描述成功完成的标准"
				} 
			},
			span: 12,
		},
		{
			label: t("风险点说明"),
			prop: "riskPoints",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请说明可能存在的风险点"
				} 
			},
			span: 12,
		},
		{
			label: t("注意事项"),
			prop: "attentionPoints",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请填写执行时需要注意的事项"
				} 
			},
			span: 12,
		},
		{
			label: t("适用区域"),
			prop: "applicableArea",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
	],
});

// 侧边栏相关方法
const loadStepsList = async () => {
	if (!selectedScenario.value?.id) {
		console.warn('🚫 场景ID为空，无法加载步骤数据');
		return;
	}
	
	stepsLoading.value = true;
	try {
		console.log('🔄 正在加载场景步骤，场景ID:', selectedScenario.value.id);
		const res = await service.sop.s.o.p.step.page({
			sopId: selectedScenario.value.id,
			size: 100,
			page: 1
		});
		
		console.log('✅ 步骤数据加载成功，响应:', res);
		stepsList.value = res.list || [];
		
		// 初始化每个步骤的标签页状态，默认显示基本信息
		const newActiveTab: Record<string, string> = {};
		const newActiveStepDetails: Record<string, string[]> = {};
		stepsList.value.forEach(step => {
			newActiveTab[step.id] = 'basic';
			newActiveStepDetails[step.id] = [];
		});
		activeTab.value = newActiveTab;
		activeStepDetails.value = newActiveStepDetails;
	} catch (error: any) {
		console.error('❌ 加载步骤失败:', error);
		ElMessage.error('加载步骤数据失败');
		stepsList.value = [];
	} finally {
		stepsLoading.value = false;
	}
};

const handleRowClick = (row: any) => {
	console.log('🖱️ 行点击事件触发，场景数据:', row);
	selectedScenario.value = row;
	stepsDrawerVisible.value = true;
	loadStepsList();
};

const handleCloseDrawer = () => {
	stepsDrawerVisible.value = false;
	selectedScenario.value = null;
	stepsList.value = [];
};

const handleAddStep = () => {
	// TODO: 实现添加步骤功能
	ElMessage.info('添加步骤功能开发中...');
};

// 导入相关方法
const openImportDialog = () => {
	// 跳转到SOP导入页面
	router.push('/sop/import');
};

const handleEditStep = (step: any) => {
	// TODO: 实现编辑步骤功能
	ElMessage.info('编辑步骤功能开发中...');
};

const handleDeleteStep = async (step: any) => {
	try {
		await ElMessageBox.confirm('确定要删除这个步骤吗？', '确认删除', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});
		
		await service.sop.s.o.p.step.delete([step.id]);
		ElMessage.success('删除成功');
		loadStepsList(); // 重新加载列表
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('删除失败');
		}
	}
};

// 状态相关方法
const getStatusType = (status: number) => {
	const statusMap: Record<number, "success" | "warning" | "danger" | "info" | "primary" | undefined> = {
		0: 'info',
		1: 'success',
		2: 'warning',
		3: undefined
	};
	return statusMap[status] || 'info';
};

const getStatusText = (status: number) => {
	const statusMap: Record<number, string> = {
		0: '草稿',
		1: '启用',
		2: '禁用',
		3: '已归档'
	};
	return statusMap[status] || '未知';
};

// 统计相关方法
const getStepCountByType = (type: string) => {
	return stepsList.value.filter(step => step.stepType === type).length;
};

const getTotalEstimatedTime = () => {
	return stepsList.value.reduce((total, step) => {
		return total + (step.estimatedTime || 0);
	}, 0);
};

// 时间线相关方法
const getTimelineType = (stepType: string) => {
	const typeMap: Record<string, "primary" | "success" | "warning" | "danger" | "info" | undefined> = {
		'normal': 'primary',
		'key': 'warning',
		'check': 'success'
	};
	return typeMap[stepType] || 'primary';
};

const getStepIcon = (stepType: string) => {
	const iconMap: Record<string, any> = {
		'normal': undefined,
		'key': undefined,
		'check': undefined
	};
	return iconMap[stepType] || undefined;
};

const getStepTypeColor = (type: string) => {
	const typeMap: Record<string, "success" | "warning" | "danger" | "info" | "primary" | undefined> = {
		'normal': undefined,
		'key': 'warning',
		'check': 'success'
	};
	return typeMap[type] || undefined;
};

const getStepTypeText = (type: string) => {
	const typeMap: Record<string, string> = {
		'normal': '普通',
		'key': '关键', 
		'check': '检查'
	};
	return typeMap[type] || '普通';
};

// 解析附件字符串
const parseAttachments = (attachments: string) => {
	if (!attachments) return [];
	// 假设附件以逗号分隔存储
	return attachments.split(',').map(item => item.trim()).filter(item => item);
};

// cl-table
const Table = useTable({
	contextMenu: [],
	on: {
		onRowClick: handleRowClick
	},
	columns: [
		{ type: "selection" },
		{ label: t("所属行业ID"), prop: "industryId", minWidth: 120 ,hidden: true},
		{ label: t("所属行业名称"), prop: "industryName", minWidth: 120 },
		{ label: t("阶段"), prop: "stage", minWidth: 80 },
		{ label: t("模块编码"), prop: "moduleCode", minWidth: 90 },
		{ label: t("模块名称"), prop: "moduleName", minWidth: 120 },
		{ label: t("场景编码"), prop: "scenarioCode", minWidth: 90 },
		{ label: t("场景名称"), prop: "scenarioName", minWidth: 100 },
		{ label: t("适用区域"), prop: "applicableArea", minWidth: 100 },
		{
			label: t("执行周期"),
			prop: "executionCycle",
			minWidth: 150,
			showOverflowTooltip: true,
			component: {
				name: "cl-text",
				props: {
					lines: 1
				}
			}
		},
		{ label: t("总步骤数"), prop: "totalSteps", minWidth: 90 },
		{
			label: t("AI转换后的执行频率（日/周/月/季度/半年/年）"),
			prop: "executionFrequency",
			hidden: true,
			minWidth: 120,
		},
		{ label: t("执行次数"), prop: "executionCount", minWidth: 100 },
		{ 
			label: t("场景描述"), 
			prop: "description", 
			minWidth: 200,
			showOverflowTooltip: true,
			component: {
				name: "cl-text",
				props: {
					lines: 2
				}
			}
		},
		{ label: t("版本号"), prop: "version", minWidth: 100 },
		{ 
			label: t("状态"), 
			prop: "status", 
			minWidth: 100,
			dict: [
				{ label: "草稿", value: 0, type: "info" },
				{ label: "启用", value: 1, type: "success" },
				{ label: "禁用", value: 2, type: "warning" },
				{ label: "已归档", value: 3, type: "default" }
			]
		},
		{
			label: t("预计总耗时(分钟)"),
			prop: "estimatedDuration",
			minWidth: 140,
		},
		{ 
			label: t("难度等级"), 
			prop: "difficultyLevel", 
			minWidth: 100,
			dict: [
				{ label: "1级", value: 1, type: "success" },
				{ label: "2级", value: 2, type: "primary" },
				{ label: "3级", value: 3, type: "warning" },
				{ label: "4级", value: 4, type: "danger" },
				{ label: "5级", value: 5, type: "info" }
			]
		},
		{ 
			label: t("质量标准"), 
			prop: "qualityStandard", 
			minWidth: 150,
			showOverflowTooltip: true,
			component: {
				name: "cl-text",
				props: {
					lines: 1
				}
			}
		},
		{ 
			label: t("成功标准"), 
			prop: "successCriteria", 
			minWidth: 150,
			showOverflowTooltip: true,
			component: {
				name: "cl-text",
				props: {
					lines: 1
				}
			}
		},
		{ 
			label: t("风险点说明"), 
			prop: "riskPoints", 
			minWidth: 150,
			showOverflowTooltip: true,
			component: {
				name: "cl-text",
				props: {
					lines: 1
				}
			}
		},
		{ 
			label: t("注意事项"), 
			prop: "attentionPoints", 
			minWidth: 150,
			showOverflowTooltip: true,
			component: {
				name: "cl-text",
				props: {
					lines: 1
				}
			}
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.sop.s.o.p.scenario,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>

<style scoped>
/* 表格行指针样式 */
:deep(.el-table__row) {
	cursor: pointer;
}

:deep(.el-table__row:hover) {
	background-color: #f5f7fa;
}

/* Drawer主题适配 */
.steps-drawer {
	:deep(.el-drawer) {
		background-color: var(--el-bg-color);
		color: var(--el-text-color-primary);
	}
	
	:deep(.el-drawer__header) {
		border-bottom: 1px solid var(--el-border-color-extra-light);
		background-color: var(--el-bg-color);
		color: var(--el-text-color-primary);
		padding: 0;
	}
	
	:deep(.el-drawer__body) {
		background-color: var(--el-bg-color);
		padding: 0;
	}
	
	:deep(.el-drawer__title) {
		display: none; /* 隐藏默认标题，使用自定义头部 */
	}
	
	:deep(.el-drawer__close-btn) {
		color: var(--el-text-color-regular);
		
		&:hover {
			color: var(--el-text-color-primary);
		}
	}
}

/* 自定义Drawer头部样式 */
.drawer-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-bg-color) 100%);
	border-bottom: 1px solid var(--el-border-color-extra-light);
}

.header-info {
	flex: 1;
}

.scenario-title {
	display: flex;
	align-items: center;
	gap: 10px;
	margin: 0 0 12px 0;
	font-size: 18px;
	font-weight: 600;
	color: var(--el-text-color-primary);
}

.scenario-meta {
	display: flex;
	align-items: center;
	gap: 12px;
	flex-wrap: wrap;
}

.meta-text {
	font-size: 12px;
	color: var(--el-text-color-regular);
	background-color: var(--el-fill-color-light);
	padding: 4px 8px;
	border-radius: 4px;
}

.header-actions {
	display: flex;
	gap: 8px;
}

/* 加载和空状态样式 */
.steps-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 80px 20px;
	background-color: var(--el-bg-color);
}

.loading-content {
	text-align: center;
	color: var(--el-text-color-regular);
}

.loading-content p {
	margin: 16px 0 0 0;
	font-size: 14px;
}

.steps-empty {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 80px 20px;
	background-color: var(--el-bg-color);
}

.empty-icon {
	font-size: 48px;
	margin-bottom: 16px;
}

/* 步骤内容区域 */
.steps-content {
	background-color: var(--el-bg-color);
	height: 100%;
	overflow-y: auto;
}

/* 步骤时间线 */
.steps-timeline {
	padding: 12px;
	background-color: var(--el-bg-color-page);
	border-radius: 8px;
	margin-top: 12px;
}

.timeline-step {
	padding-bottom: 5px;

	:deep(.el-timeline-item__wrapper) {
		padding-left: 30px;
	}
}

/* 自定义时间线节点 */
.custom-dot {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 14px;
	color: white;
	border: 3px solid var(--el-bg-color);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dot-normal {
	background-color: var(--el-color-primary);
}

.dot-key {
	background-color: var(--el-color-warning);
	animation: pulse 2s infinite;
}

.dot-check {
	background-color: var(--el-color-success);
}

.dot-number {
	font-size: 12px;
	font-weight: bold;
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
	}
	70% {
		box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
	}
}

/* 步骤卡片样式 */
.step-card {
	margin-bottom: 0;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
	overflow: hidden;
}

.step-card:hover {
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
	transform: translateY(-2px);
}

.step-card--key {
	border-left: 4px solid var(--el-color-warning);
}

.step-card--check {
	border-left: 4px solid var(--el-color-success);
}

.step-card--normal {
	border-left: 4px solid var(--el-color-primary);
}

/* 卡片头部 */
.step-card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 0;
	border-bottom: 1px solid var(--el-border-color-extra-light);
	background: linear-gradient(90deg, var(--el-fill-color-lighter) 0%, var(--el-bg-color) 100%);
}

.step-title-section {
	flex: 1;
}

.step-title {
	margin: 0 0 8px 0;
	font-size: 16px;
	font-weight: 600;
	color: var(--el-text-color-primary);
	line-height: 1.4;
}

.step-tags {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
}

.step-actions {
	padding: 12px;
	border-left: 1px solid var(--el-border-color-extra-light);
}

/* 卡片内容 */
.step-card-content {
	padding: 12px;
}

.info-row {
	display: flex;
	gap: 16px;
	flex-wrap: wrap;
}

.info-item {
	display: flex;
	align-items: center;
	gap: 6px;
	font-size: 13px;
	color: var(--el-text-color-regular);
	background-color: var(--el-fill-color-light);
	padding: 4px 8px;
	border-radius: 4px;
}

.info-item .el-icon {
	font-size: 14px;
}

.step-description {
	margin-bottom: 8px;
}

.step-description p {
	margin: 0;
	font-size: 14px;
	line-height: 1.5;
	color: var(--el-text-color-regular);
	background-color: var(--el-fill-color-lighter);
	padding: 12px;
	border-radius: 6px;
	border-left: 3px solid var(--el-color-primary-light-5);
}

/* 详细信息折叠面板 */
.step-details {
	border: none;
	
	:deep(.el-collapse-item) {
		border: 1px solid var(--el-border-color-extra-light);
		border-radius: 6px;
		
		.el-collapse-item__header {
			background-color: var(--el-fill-color-lighter);
			padding: 8px 12px;
			font-size: 13px;
			font-weight: 500;
		}
		
		.el-collapse-item__content {
			padding: 12px;
		}
	}
}

/* 描述表格样式优化 */
:deep(.el-descriptions) {
	.el-descriptions__header {
		margin-bottom: 8px;
	}
	
	.el-descriptions__label {
		font-weight: 500;
		color: var(--el-text-color-regular);
		width: 80px;
	}
	
	.el-descriptions__content {
		color: var(--el-text-color-primary);
		word-break: break-all;
	}
}

/* 附件标签 */
.attachment-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 6px;
}

.attachment-tags .el-tag {
	cursor: pointer;
	transition: all 0.2s;
}

.attachment-tags .el-tag:hover {
	transform: scale(1.05);
}

.no-data {
	font-size: 12px;
	color: var(--el-text-color-placeholder);
	font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.steps-drawer {
		:deep(.el-drawer) {
			width: 90% !important;
		}
	}
	
	.drawer-header {
		flex-direction: column;
		gap: 16px;
		align-items: stretch;
	}
	
	.step-card-header {
		flex-direction: column;
	}
	
	.step-actions {
		border-left: none;
		border-top: 1px solid var(--el-border-color-extra-light);
		padding-top: 12px;
	}
}

.collapse-title {
	display: flex;
	align-items: center;
	gap: 6px;
	font-weight: 500;
}

.collapse-arrow {
	transition: transform 0.3s;
}

.collapse-arrow.is-active {
	transform: rotate(90deg);
}
</style>

