<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "sop-s-o-p-step",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("选择关联场景sop主表"),
			prop: "sopId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("选择所属行业"),
			prop: "industryId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所属行业名称"),
			prop: "industryName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("阶段"),
			prop: "stage",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("模块编码"),
			prop: "moduleCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("模块名称"),
			prop: "moduleName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("场景编码"),
			prop: "scenarioCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("场景名称"),
			prop: "scenarioName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("执行周期"),
			prop: "executionCycle",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("步骤编码"),
			prop: "stepCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("步骤名称"),
			prop: "stepName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("步骤描述"),
			prop: "stepDescription",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("步骤顺序"),
			prop: "stepOrder",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("实体触点"),
			prop: "entityTouchpoint",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("用户活动"),
			prop: "userActivity",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("员工行为"),
			prop: "employeeBehavior",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("工作亮点"),
			prop: "workHighlight",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("员工角色"),
			prop: "employeeRole",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("相关附件"),
			prop: "relatedAttachments",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("步骤类型(normal:普通步骤,key:关键步骤,check:检查步骤)"),
			prop: "stepType",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("是否必需步骤"),
			prop: "isRequired",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("预估执行时间(分钟)"),
			prop: "estimatedTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("技能要求"),
			prop: "skillRequirements",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所需工具"),
			prop: "toolsRequired",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("质量检查点"),
			prop: "qualityCheckPoints",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("风险提醒"),
			prop: "riskWarnings",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("完成标准"),
			prop: "successCriteria",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("失败处理方案"),
			prop: "failureHandling",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("下一步条件"),
			prop: "nextStepCondition",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("可并行执行的步骤编码"),
			prop: "parallelSteps",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("前置步骤编码"),
			prop: "prerequisiteSteps",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("状态(0:禁用"),
			prop: "status",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("版本号"),
			prop: "version",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("关联场景SOP主表ID"), prop: "sopId", minWidth: 120 },
		{ label: t("所属行业ID"), prop: "industryId", minWidth: 120 },
		{ label: t("所属行业名称"), prop: "industryName", minWidth: 120 },
		{ label: t("阶段"), prop: "stage", minWidth: 120 },
		{ label: t("模块编码"), prop: "moduleCode", minWidth: 120 },
		{ label: t("模块名称"), prop: "moduleName", minWidth: 120 },
		{ label: t("场景编码"), prop: "scenarioCode", minWidth: 120 },
		{ label: t("场景名称"), prop: "scenarioName", minWidth: 120 },
		{ label: t("执行周期"), prop: "executionCycle", minWidth: 120 },
		{ label: t("步骤编码"), prop: "stepCode", minWidth: 120 },
		{ label: t("步骤名称"), prop: "stepName", minWidth: 120 },
		{ label: t("步骤描述"), prop: "stepDescription", minWidth: 120 },
		{ label: t("步骤顺序"), prop: "stepOrder", minWidth: 120 },
		{ label: t("实体触点"), prop: "entityTouchpoint", minWidth: 120 },
		{ label: t("用户活动"), prop: "userActivity", minWidth: 120 },
		{ label: t("员工行为"), prop: "employeeBehavior", minWidth: 120 },
		{ label: t("工作亮点"), prop: "workHighlight", minWidth: 120 },
		{ label: t("员工角色"), prop: "employeeRole", minWidth: 120 },
		{ label: t("相关附件"), prop: "relatedAttachments", minWidth: 120 },
		{
			label: t("步骤类型(normal:普通步骤,key:关键步骤,check:检查步骤)"),
			prop: "stepType",
			minWidth: 120,
		},
		{ label: t("是否必需步骤"), prop: "isRequired", minWidth: 120 },
		{
			label: t("预估执行时间(分钟)"),
			prop: "estimatedTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ label: t("技能要求"), prop: "skillRequirements", minWidth: 120 },
		{ label: t("所需工具"), prop: "toolsRequired", minWidth: 120 },
		{ label: t("质量检查点"), prop: "qualityCheckPoints", minWidth: 120 },
		{ label: t("风险提醒"), prop: "riskWarnings", minWidth: 120 },
		{ label: t("完成标准"), prop: "successCriteria", minWidth: 120 },
		{ label: t("失败处理方案"), prop: "failureHandling", minWidth: 120 },
		{ label: t("下一步条件"), prop: "nextStepCondition", minWidth: 120 },
		{
			label: t("可并行执行的步骤编码"),
			prop: "parallelSteps",
			minWidth: 120,
		},
		{ label: t("前置步骤编码"), prop: "prerequisiteSteps", minWidth: 120 },
		{ label: t("状态(0:禁用"), prop: "status", minWidth: 120 },
		{ label: t("版本号"), prop: "version", minWidth: 120 },
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.sop.s.o.p.step,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
