<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 搜索 -->
			<cl-search ref="Search">
				<cl-search-item prop="templateName" label="模板名称" />
				<cl-search-item prop="templateType" label="模板类型" :component="{ 
					name: 'el-select',
					options: [
						{ label: '分类模板', value: 'category' },
						{ label: '标准模板', value: 'standard' },
						{ label: '具体模板', value: 'template' }
					]
				}" />
				<cl-search-item prop="moduleCode" label="模块编号" />
				<cl-search-item prop="scenarioCode" label="场景编号" />
				<cl-search-item prop="stage" label="阶段" :component="{ 
					name: 'el-select',
					options: [
						{ label: '前期导入', value: '前期导入' },
						{ label: '执行阶段', value: '执行阶段' },
						{ label: '监管阶段', value: '监管阶段' },
						{ label: '完成阶段', value: '完成阶段' },
						{ label: '维护阶段', value: '维护阶段' }
					]
				}" />
				<cl-search-item prop="enableStatus" label="启用状态" :component="{ 
					name: 'el-select',
					options: [
						{ label: '启用', value: 1 },
						{ label: '禁用', value: 0 }
					]
				}" />
			</cl-search>

			<!-- 新增、删除、刷新等按钮 -->
			<cl-add-btn />
			<cl-multi-delete-btn />
			<cl-flex1 />
			<cl-refresh-btn />

			<!-- 表格 -->
			<cl-table ref="Table" row-key="id" default-expand-all :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
				<cl-table-column type="selection" width="60" />
				<cl-table-column prop="templateName" label="模板名称" min-width="200" show-overflow-tooltip>
					<template #default="{ scope }">
						<span :style="{ paddingLeft: (scope.row.level - 1) * 20 + 'px' }">
							<el-icon v-if="scope.row.templateType === 'category'" style="color: #409eff; margin-right: 5px;">
								<Folder />
							</el-icon>
							<el-icon v-else style="color: #67c23a; margin-right: 5px;">
								<Document />
							</el-icon>
							{{ scope.row.templateName }}
						</span>
					</template>
				</cl-table-column>
				<cl-table-column prop="templateType" label="模板类型" min-width="100">
					<template #default="{ scope }">
						<el-tag :type="getTemplateTypeTag(scope.row.templateType)" size="small">
							{{ getTemplateTypeText(scope.row.templateType) }}
						</el-tag>
					</template>
				</cl-table-column>
				<cl-table-column prop="sort" label="排序" width="80" />
				<cl-table-column prop="level" label="层级" width="80" />
				<cl-table-column prop="industryName" label="所属行业" min-width="120" show-overflow-tooltip />
				<cl-table-column prop="moduleCode" label="模块编号" min-width="100" />
				<cl-table-column prop="moduleName" label="模块名称" min-width="150" show-overflow-tooltip />
				<cl-table-column prop="scenarioCode" label="场景编号" min-width="100" />
				<cl-table-column prop="scenarioName" label="场景名称" min-width="180" show-overflow-tooltip />
				<cl-table-column prop="executionCycle" label="执行周期" min-width="120" show-overflow-tooltip />
				<cl-table-column prop="stepCode" label="步骤" min-width="80" />
				<cl-table-column prop="entityTouchpoint" label="实体触点" min-width="150" show-overflow-tooltip />
				<cl-table-column prop="userActivity" label="用户活动" min-width="150" show-overflow-tooltip />
				<cl-table-column prop="employeeBehavior" label="员工行为" min-width="150" show-overflow-tooltip />
				<cl-table-column prop="workHighlight" label="工作亮点" min-width="150" show-overflow-tooltip />
				<cl-table-column prop="employeeRole" label="员工角色" min-width="120" show-overflow-tooltip />
				<cl-table-column prop="enableStatus" label="状态" min-width="80">
					<template #default="{ scope }">
						<el-tag :type="scope.row.enableStatus === 1 ? 'success' : 'danger'">
							{{ scope.row.enableStatus === 1 ? '启用' : '禁用' }}
						</el-tag>
					</template>
				</cl-table-column>
				<cl-table-column label="操作" type="op" width="200" />
			</cl-table>

			<!-- 分页 -->
			<cl-pagination ref="Pagination" />

			<!-- 新增、编辑 -->
			<cl-upsert ref="Upsert">
				<template #slot-content="{ scope }">
					<el-row :gutter="20">
						<!-- 层级信息 -->
						<el-col :span="24">
							<el-divider content-position="left">
								<span style="color: #409eff; font-weight: bold;">🏗️ 层级结构</span>
							</el-divider>
						</el-col>
						
						<el-col :span="8">
							<cl-form-item prop="templateType" label="模板类型" required>
								<el-select v-model="scope.templateType" placeholder="请选择模板类型" style="width: 100%">
									<el-option label="分类模板" value="category" />
									<el-option label="标准模板" value="standard" />
									<el-option label="具体模板" value="template" />
								</el-select>
							</cl-form-item>
						</el-col>
						<el-col :span="8">
							<cl-form-item prop="parentId" label="父级模板">
								<el-tree-select
									v-model="scope.parentId"
									:data="parentTemplateOptions"
									placeholder="请选择父级模板"
									style="width: 100%"
									clearable
									check-strictly
									:render-after-expand="false"
									:props="{
										value: 'id',
										label: 'templateName',
										children: 'children'
									}"
								/>
							</cl-form-item>
						</el-col>
						<el-col :span="8">
							<cl-form-item prop="sort" label="排序">
								<el-input-number v-model="scope.sort" :min="0" :max="9999" controls-position="right" style="width: 100%" />
							</cl-form-item>
						</el-col>

						<!-- 基础信息 -->
						<el-col :span="24">
							<el-divider content-position="left">
								<span style="color: #409eff; font-weight: bold;">📋 基础信息</span>
							</el-divider>
						</el-col>
						
						<el-col :span="12">
							<cl-form-item prop="templateName" label="模板名称" required>
								<el-input v-model="scope.templateName" placeholder="SOP模板名称" />
							</cl-form-item>
						</el-col>
						<el-col :span="12">
							<cl-form-item prop="description" label="模板描述">
								<el-input v-model="scope.description" placeholder="模板描述信息" />
							</cl-form-item>
						</el-col>

						<el-col :span="12">
							<cl-form-item prop="moduleCode" label="模块编号" required>
								<el-input v-model="scope.moduleCode" placeholder="如：M05" />
							</cl-form-item>
						</el-col>
						<el-col :span="12">
							<cl-form-item prop="moduleName" label="模块名称" required>
								<el-input v-model="scope.moduleName" placeholder="如：入驻准备与交接保障服务" />
							</cl-form-item>
						</el-col>
						
						<el-col :span="12">
							<cl-form-item prop="scenarioCode" label="场景编号" required>
								<el-input v-model="scope.scenarioCode" placeholder="如：S11" />
							</cl-form-item>
						</el-col>
						<el-col :span="12">
							<cl-form-item prop="scenarioName" label="场景名称" required>
								<el-input v-model="scope.scenarioName" placeholder="如：客户满意度调查" />
							</cl-form-item>
						</el-col>

						<el-col :span="12">
							<cl-form-item prop="stage" label="阶段">
								<el-select v-model="scope.stage" placeholder="请选择阶段" style="width: 100%">
									<el-option label="前期导入" value="前期导入" />
									<el-option label="执行阶段" value="执行阶段" />
									<el-option label="监管阶段" value="监管阶段" />
									<el-option label="完成阶段" value="完成阶段" />
									<el-option label="维护阶段" value="维护阶段" />
								</el-select>
							</cl-form-item>
						</el-col>
						<el-col :span="12">
							<cl-form-item prop="executionCycle" label="执行周期">
								<el-select v-model="scope.executionCycle" placeholder="请选择执行周期" style="width: 100%">
									<el-option label="不定于1次/人次" value="不定于1次/人次" />
									<el-option label="定期更新" value="定期更新" />
									<el-option label="一次性/定期开展" value="一次性/定期开展" />
									<el-option label="不定于1次/件" value="不定于1次/件" />
									<el-option label="月度/中期" value="月度/中期" />
									<el-option label="月度" value="月度" />
								</el-select>
							</cl-form-item>
						</el-col>

						<el-col :span="12">
							<cl-form-item prop="employeeRole" label="员工角色">
								<el-select v-model="scope.employeeRole" placeholder="请选择员工角色" style="width: 100%">
									<el-option label="客服人员" value="客服人员" />
									<el-option label="物业管理人员" value="物业管理人员" />
									<el-option label="社区工作站" value="社区工作站" />
									<el-option label="业委会联络员" value="业委会联络员" />
									<el-option label="宣传员" value="宣传员" />
								</el-select>
							</cl-form-item>
						</el-col>
						<el-col :span="12">
							<cl-form-item prop="priority" label="优先级">
								<el-select v-model="scope.priority" placeholder="请选择优先级" style="width: 100%">
									<el-option label="低" :value="1" />
									<el-option label="中" :value="2" />
									<el-option label="高" :value="3" />
									<el-option label="紧急" :value="4" />
								</el-select>
							</cl-form-item>
						</el-col>

						<!-- 步骤详情 -->
						<el-col :span="24" v-if="scope.templateType === 'template'">
							<el-divider content-position="left">
								<span style="color: #67c23a; font-weight: bold;">📝 步骤详情</span>
							</el-divider>
						</el-col>

						<el-col :span="12" v-if="scope.templateType === 'template'">
							<cl-form-item prop="stepCode" label="步骤编号">
								<el-input v-model="scope.stepCode" placeholder="如：1.1" />
							</cl-form-item>
						</el-col>

						<el-col :span="24" v-if="scope.templateType === 'template'">
							<cl-form-item prop="stepDescription" label="步骤描述">
								<el-input 
									v-model="scope.stepDescription" 
									type="textarea" 
									:rows="3"
									placeholder="详细描述执行步骤"
								/>
							</cl-form-item>
						</el-col>

						<!-- 业务详情 -->
						<el-col :span="24" v-if="scope.templateType === 'template'">
							<el-divider content-position="left">
								<span style="color: #e6a23c; font-weight: bold;">🔗 业务详情</span>
							</el-divider>
						</el-col>

						<el-col :span="24" v-if="scope.templateType === 'template'">
							<cl-form-item prop="entityTouchpoint" label="实体触点">
								<el-input 
									v-model="scope.entityTouchpoint" 
									type="textarea" 
									:rows="2"
									placeholder="如：业委会办公室、社区公告栏等"
								/>
							</cl-form-item>
						</el-col>

						<el-col :span="12" v-if="scope.templateType === 'template'">
							<cl-form-item prop="userActivity" label="用户活动">
								<el-input 
									v-model="scope.userActivity" 
									type="textarea"
									:rows="3"
									placeholder="描述用户需要进行的活动"
								/>
							</cl-form-item>
						</el-col>
						<el-col :span="12" v-if="scope.templateType === 'template'">
							<cl-form-item prop="employeeBehavior" label="员工行为">
								<el-input 
									v-model="scope.employeeBehavior" 
									type="textarea"
									:rows="3"
									placeholder="描述员工需要执行的行为"
								/>
							</cl-form-item>
						</el-col>

						<el-col :span="24">
							<cl-form-item prop="workHighlight" label="工作亮点">
								<el-input 
									v-model="scope.workHighlight" 
									type="textarea" 
									:rows="2"
									placeholder="突出的工作亮点或特色"
								/>
							</cl-form-item>
						</el-col>

						<!-- 设置信息 -->
						<el-col :span="24">
							<el-divider content-position="left">
								<span style="color: #909399; font-weight: bold;">⚙️ 设置信息</span>
							</el-divider>
						</el-col>

						<el-col :span="8">
							<cl-form-item prop="enableStatus" label="启用状态">
								<el-switch 
									v-model="scope.enableStatus" 
									:active-value="1" 
									:inactive-value="0"
									active-text="启用"
									inactive-text="禁用"
								/>
							</cl-form-item>
						</el-col>
						<el-col :span="8">
							<cl-form-item prop="version" label="版本号">
								<el-input v-model="scope.version" placeholder="如：1.0" />
							</cl-form-item>
						</el-col>
						<el-col :span="8">
							<cl-form-item prop="estimatedTime" label="预估工时(分钟)">
								<el-input-number 
									v-model="scope.estimatedTime" 
									:min="1" 
									:max="9999"
									style="width: 100%"
								/>
							</cl-form-item>
						</el-col>

						<el-col :span="24">
							<cl-form-item prop="remark" label="备注">
								<el-input 
									v-model="scope.remark" 
									type="textarea" 
									:rows="2"
									placeholder="其他备注信息"
								/>
							</cl-form-item>
						</el-col>
					</el-row>
				</template>
			</cl-upsert>
		</cl-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ref, reactive, onMounted, computed, h } from "vue";
import { ElMessage } from "element-plus";
import { Document, Folder } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

defineOptions({
	name: "sop-template"
});

const { service } = useCool();
const { t } = useI18n();

// 父级模板选项
const parentTemplateOptions = ref([]);

// 获取父级模板列表
const getParentTemplateOptions = async () => {
	try {
		const res = await service.sop.s.o.p.template.list({
			templateType: ['category', 'standard'],
			status: 1
		});
		parentTemplateOptions.value = buildTreeData(res);
	} catch (error) {
		console.error('获取父级模板失败:', error);
	}
};

// 构建树形数据
const buildTreeData = (list: any[]) => {
	const map = {} as any;
	const roots = [] as any[];
	
	// 先创建映射
	list.forEach(item => {
		map[item.id] = { ...item, children: [] };
	});
	
	// 构建树形结构
	list.forEach(item => {
		if (item.parentId && map[item.parentId]) {
			map[item.parentId].children.push(map[item.id]);
		} else {
			roots.push(map[item.id]);
		}
	});
	
	return roots;
};

// 状态选项
const statusOptions = reactive([
	{ label: "启用", value: 1 },
	{ label: "禁用", value: 0 }
]);

// 服务类型选项
const serviceTypeOptions = reactive([
	{ label: "基础服务", value: "basic" },
	{ label: "增值服务", value: "value_added" },
	{ label: "紧急服务", value: "emergency" },
	{ label: "特殊服务", value: "special" }
]);

// 优先级选项
const priorityOptions = reactive([
	{ label: "低", value: 1, color: "#909399" },
	{ label: "中", value: 2, color: "#409eff" },
	{ label: "高", value: 3, color: "#f56c6c" },
	{ label: "紧急", value: 4, color: "#e6a23c" }
]);

// 费用类型选项
const feeTypeOptions = reactive([
	{ label: "免费", value: 0 },
	{ label: "收费", value: 1 },
	{ label: "按情况", value: 2 }
]);

// 审核状态选项
const auditStatusOptions = reactive([
	{ label: "草稿", value: 0 },
	{ label: "待审核", value: 1 },
	{ label: "已审核", value: 2 },
	{ label: "已拒绝", value: 3 }
]);

// cl-upsert
const Upsert = useUpsert({
	items: [
		// 层级结构字段
		{
			label: "模板类型",
			prop: "templateType",
			component: {
				name: "cl-select",
				props: {
					options: [
						{ label: "分类模板", value: "category" },
						{ label: "标准模板", value: "standard" },
						{ label: "具体模板", value: "template" }
					]
				}
			},
			required: true,
			span: 8,
		},
		{
			label: "父级模板",
			prop: "parentId",
			component: {
				name: "el-tree-select",
				props: {
					data: computed(() => parentTemplateOptions.value),
					clearable: true,
					checkStrictly: true,
					renderAfterExpand: false,
					props: {
						value: 'id',
						label: 'templateName',
						children: 'children'
					}
				}
			},
			span: 8,
		},
		{
			label: "排序",
			prop: "sort",
			component: {
				name: "el-input-number",
				props: {
					min: 0,
					max: 9999,
					controlsPosition: "right"
				}
			},
			span: 8,
		},
		{
			label: "模板名称",
			prop: "templateName",
			component: { name: "el-input", props: { clearable: true } },
			required: true,
			span: 12,
		},
		{
			label: "模板描述",
			prop: "description", 
			component: { name: "el-input", props: { type: "textarea", rows: 2 } },
			span: 12,
		},
		{
			label: "模块编号",
			prop: "moduleCode",
			component: { name: "el-input", props: { clearable: true } },
			required: true,
			span: 12,
		},
		{
			label: "模块名称",
			prop: "moduleName",
			component: { name: "el-input", props: { clearable: true } },
			required: true,
			span: 12,
		},
		{
			label: "场景编号",
			prop: "scenarioCode",
			component: { name: "el-input", props: { clearable: true } },
			required: true,
			span: 12,
		},
		{
			label: "场景名称",
			prop: "scenarioName",
			component: { name: "el-input", props: { clearable: true } },
			required: true,
			span: 12,
		},
		{
			label: "服务类型",
			prop: "serviceType",
			component: {
				name: "cl-select",
				props: {
					options: serviceTypeOptions,
					clearable: true
				}
			},
			span: 12,
		},
		{
			label: "优先级",
			prop: "priority",
			component: {
				name: "cl-select",
				props: {
					options: priorityOptions,
					clearable: true
				}
			},
			span: 12,
		},
	],
});

// cl-table  
const Table = useTable({
	rowKey: "id",
	columns: [
		{ type: "selection" },
		{ 
			label: "模板名称", 
			prop: "templateName", 
			minWidth: 200,
			render: ({ scope }) => {
				const paddingLeft = (scope.row.level - 1) * 20;
				return h('span', { style: { paddingLeft: paddingLeft + 'px' } }, [
					h('el-icon', { style: { color: scope.row.templateType === 'category' ? '#409eff' : '#67c23a', marginRight: '5px' } }, [
						scope.row.templateType === 'category' ? h(Folder) : h(Document)
					]),
					scope.row.templateName
				]);
			}
		},
		{ 
			label: "模板类型", 
			prop: "templateType", 
			minWidth: 100,
			dict: [
				{ label: "分类模板", value: "category", type: "info" },
				{ label: "标准模板", value: "standard", type: "primary" },
				{ label: "具体模板", value: "template", type: "warning" }
			]
		},
		{ label: "排序", prop: "sort", width: 80 },
		{ label: "层级", prop: "level", width: 80 },
		{ label: "所属行业", prop: "industryName", minWidth: 120 },
		{ label: "模块编号", prop: "moduleCode", minWidth: 100 },
		{ label: "模块名称", prop: "moduleName", minWidth: 150 },
		{ label: "场景编号", prop: "scenarioCode", minWidth: 100 },
		{ label: "场景名称", prop: "scenarioName", minWidth: 180 },
		{ label: "执行周期", prop: "executionCycle", minWidth: 120 },
		{ label: "步骤", prop: "stepCode", minWidth: 80 },
		{ 
			label: "状态", 
			prop: "enableStatus", 
			minWidth: 80,
			dict: [
				{ label: "启用", value: 1, type: "success" },
				{ label: "禁用", value: 0, type: "danger" }
			]
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch({
	items: [
		{
			label: "模板名称",
			prop: "templateName",
			component: { name: "el-input", props: { clearable: true } }
		},
		{
			label: "模板类型",
			prop: "templateType",
			component: {
				name: "cl-select",
				props: {
					options: [
						{ label: "分类模板", value: "category" },
						{ label: "标准模板", value: "standard" },
						{ label: "具体模板", value: "template" }
					],
					clearable: true
				}
			}
		},
		{
			label: "模块编号",
			prop: "moduleCode",
			component: { name: "el-input", props: { clearable: true } }
		},
		{
			label: "场景编号",
			prop: "scenarioCode",
			component: { name: "el-input", props: { clearable: true } }
		},
		{
			label: "阶段",
			prop: "stage",
			component: {
				name: "cl-select",
				props: {
					options: [
						{ label: "前期导入", value: "前期导入" },
						{ label: "执行阶段", value: "执行阶段" },
						{ label: "监管阶段", value: "监管阶段" },
						{ label: "完成阶段", value: "完成阶段" },
						{ label: "维护阶段", value: "维护阶段" }
					],
					clearable: true
				}
			}
		},
		{
			label: "启用状态",
			prop: "enableStatus",
			component: {
				name: "cl-select",
				props: {
					options: statusOptions,
					clearable: true
				}
			}
		}
	]
});

// cl-crud
const Crud = useCrud(
	{
		service: service.sop.s.o.p.template,
	},
	(app) => {
		app.refresh();
	},
);

// 获取模板类型标签类型
const getTemplateTypeTag = (templateType: string) => {
	const typeMap: Record<string, string> = {
		'category': 'info',
		'standard': 'primary',
		'template': 'warning'
	};
	return typeMap[templateType] || 'info';
};

// 获取模板类型文本
const getTemplateTypeText = (templateType: string) => {
	const textMap: Record<string, string> = {
		'category': '分类模板',
		'standard': '标准模板',
		'template': '具体模板'
	};
	return textMap[templateType] || '未知';
};

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}

// 组件挂载时获取父级模板选项
onMounted(() => {
	getParentTemplateOptions();
});
</script>
