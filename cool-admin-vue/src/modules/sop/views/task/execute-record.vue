<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <cl-multi-delete-btn />
      <cl-flex1 />
      <cl-search-key />
    </cl-row>

    <cl-row>
      <cl-table ref="Table" />
    </cl-row>

    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>

    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>

<script setup lang="ts">
defineOptions({
  name: "sop-task-execute-record"
});

import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';

const { service } = useCool();

// CRUD配置
const Crud = useCrud({
  service: service.sop.taskExecuteRecord
}, (app) => {
  app.refresh();
});

// 表格配置
const Table = useTable({
  columns: [
    { type: 'selection' },
    { 
      label: '任务名称', 
      prop: 'taskName', 
      minWidth: 180,
      showOverflowTooltip: true
    },
    { 
      label: '任务描述', 
      prop: 'taskDescription', 
      minWidth: 200,
      showOverflowTooltip: true
    },
    { 
      label: '执行人', 
      prop: 'executorName', 
      minWidth: 100 
    },
    { 
      label: '状态', 
      prop: 'status', 
      minWidth: 100,
      dict: [
        { label: '待执行', value: 0, type: 'info' },
        { label: '执行中', value: 1, type: 'warning' },
        { label: '已完成', value: 2, type: 'success' },
        { label: '暂停', value: 3, type: 'danger' },
        { label: '跳过', value: 4, type: 'info' },
        { label: '失败', value: 5, type: 'danger' },
        { label: '质检中', value: 6, type: 'warning' },
        { label: '返工', value: 7, type: 'danger' }
      ]
    },
    { 
      label: '进度', 
      prop: 'progress', 
      minWidth: 100,
      component: {
        name: 'el-progress',
        props: {
          percentage: '{{ scope.row.progress || 0 }}',
          size: 'small'
        }
      }
    },
    { 
      label: '预估时间(分)', 
      prop: 'estimatedTime', 
      minWidth: 120 
    },
    { 
      label: '实际时间(分)', 
      prop: 'actualTime', 
      minWidth: 120 
    },
    { 
      label: '质量评分', 
      prop: 'qualityScore', 
      minWidth: 100 
    },
    { 
      label: '创建时间', 
      prop: 'createTime', 
      minWidth: 160,
      sortable: true
    },
    { 
      type: 'op', 
      buttons: ['edit', 'delete'],
      width: 120
    }
  ]
});

// 新增/编辑配置
const Upsert = useUpsert({
  items: [
    {
      label: '工单ID',
      prop: 'workOrderId',
      component: { name: 'el-input-number' },
      required: true
    },
    {
      label: 'SOP步骤ID',
      prop: 'sopStepId',
      component: { name: 'el-input-number' },
      required: true
    },
    {
      label: '任务名称',
      prop: 'taskName',
      component: { name: 'el-input' },
      required: true
    },
    {
      label: '任务描述',
      prop: 'taskDescription',
      component: { name: 'el-input', props: { type: 'textarea', rows: 3 } }
    },
    {
      label: '执行人ID',
      prop: 'executorId',
      component: { name: 'el-input-number' }
    },
    {
      label: '执行人姓名',
      prop: 'executorName',
      component: { name: 'el-input' }
    },
    {
      label: '状态',
      prop: 'status',
      component: {
        name: 'el-select',
        options: [
          { label: '待执行', value: 0 },
          { label: '执行中', value: 1 },
          { label: '已完成', value: 2 },
          { label: '暂停', value: 3 },
          { label: '跳过', value: 4 },
          { label: '失败', value: 5 },
          { label: '质检中', value: 6 },
          { label: '返工', value: 7 }
        ]
      },
      value: 0
    },
    {
      label: '进度(%)',
      prop: 'progress',
      component: { name: 'el-input-number', props: { min: 0, max: 100 } },
      value: 0
    },
    {
      label: '预估时间(分钟)',
      prop: 'estimatedTime',
      component: { name: 'el-input-number', props: { min: 0 } },
      value: 30
    },
    {
      label: '计划开始时间',
      prop: 'plannedStartTime',
      component: { name: 'el-date-picker', props: { type: 'datetime' } }
    },
    {
      label: '计划结束时间',
      prop: 'plannedEndTime',
      component: { name: 'el-date-picker', props: { type: 'datetime' } }
    }
  ]
});
</script> 