<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "sop-work-order",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("选择sop模板"),
			prop: "sopTemplateId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("工单编号"),
			prop: "orderNo",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("工单标题"),
			prop: "title",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("工单描述"),
			prop: "description",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("优先级(1-5)"),
			prop: "priority",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("紧急程度(1-5)"),
			prop: "urgency",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("业务类型"),
			prop: "businessType",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("业务数据"),
			prop: "businessData",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("选择申请人"),
			prop: "applicantId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("申请人姓名"),
			prop: "applicantName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("申请部门"),
			prop: "applicantDept",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("选择负责人"),
			prop: "assigneeId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("负责人姓名"),
			prop: "assigneeName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("计划开始时间"),
			prop: "plannedStartTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("计划结束时间"),
			prop: "plannedEndTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("实际开始时间"),
			prop: "actualStartTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("实际结束时间"),
			prop: "actualEndTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("状态"),
			prop: "status",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("进度百分比"),
			prop: "progress",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("预估工时(分钟)"),
			prop: "estimatedWorkTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("实际工时(分钟)"),
			prop: "actualWorkTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("质量评分(1-100)"),
			prop: "qualityScore",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("AI调度标识"),
			prop: "aiScheduled",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("AI调度配置"),
			prop: "aiScheduleConfig",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("AI预测完成时间"),
			prop: "aiPredictedEndTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("AI风险评估"),
			prop: "aiRiskAssessment",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("执行团队"),
			prop: "executionTeam",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("备注"),
			prop: "remark",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("选择关联业务"),
			prop: "relatedBusinessId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("关联业务类型"),
			prop: "relatedBusinessType",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("执行结果"),
			prop: "executionResult",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("失败原因"),
			prop: "failureReason",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("客户满意度(1-5)"),
			prop: "customerSatisfaction",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("客户反馈"),
			prop: "customerFeedback",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("SOP模板ID"), prop: "sopTemplateId", minWidth: 120 },
		{ label: t("工单编号"), prop: "orderNo", minWidth: 120 },
		{ label: t("工单标题"), prop: "title", minWidth: 120 },
		{ label: t("工单描述"), prop: "description", minWidth: 120 },
		{ label: t("优先级(1-5)"), prop: "priority", minWidth: 120 },
		{ label: t("紧急程度(1-5)"), prop: "urgency", minWidth: 120 },
		{ label: t("业务类型"), prop: "businessType", minWidth: 120 },
		{ label: t("业务数据"), prop: "businessData", minWidth: 120 },
		{ label: t("申请人ID"), prop: "applicantId", minWidth: 120 },
		{ label: t("申请人姓名"), prop: "applicantName", minWidth: 120 },
		{ label: t("申请部门"), prop: "applicantDept", minWidth: 120 },
		{ label: t("负责人ID"), prop: "assigneeId", minWidth: 120 },
		{ label: t("负责人姓名"), prop: "assigneeName", minWidth: 120 },
		{
			label: t("计划开始时间"),
			prop: "plannedStartTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{
			label: t("计划结束时间"),
			prop: "plannedEndTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{
			label: t("实际开始时间"),
			prop: "actualStartTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{
			label: t("实际结束时间"),
			prop: "actualEndTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ label: t("状态"), prop: "status", minWidth: 120 },
		{ label: t("进度百分比"), prop: "progress", minWidth: 120 },
		{
			label: t("预估工时(分钟)"),
			prop: "estimatedWorkTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{
			label: t("实际工时(分钟)"),
			prop: "actualWorkTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ label: t("质量评分(1-100)"), prop: "qualityScore", minWidth: 120 },
		{ label: t("AI调度标识"), prop: "aiScheduled", minWidth: 120 },
		{ label: t("AI调度配置"), prop: "aiScheduleConfig", minWidth: 120 },
		{
			label: t("AI预测完成时间"),
			prop: "aiPredictedEndTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ label: t("AI风险评估"), prop: "aiRiskAssessment", minWidth: 120 },
		{ label: t("执行团队"), prop: "executionTeam", minWidth: 120 },
		{ label: t("备注"), prop: "remark", minWidth: 120 },
		{ label: t("关联业务ID"), prop: "relatedBusinessId", minWidth: 120 },
		{
			label: t("关联业务类型"),
			prop: "relatedBusinessType",
			minWidth: 120,
		},
		{ label: t("执行结果"), prop: "executionResult", minWidth: 120 },
		{ label: t("失败原因"), prop: "failureReason", minWidth: 120 },
		{
			label: t("客户满意度(1-5)"),
			prop: "customerSatisfaction",
			minWidth: 120,
		},
		{ label: t("客户反馈"), prop: "customerFeedback", minWidth: 120 },
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.sop.work.order,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
