# 任务状态流转功能使用指南

## 概述

任务状态流转功能提供了完整的任务状态管理解决方案，包括状态转换控制、权限校验、历史记录等功能。

## 核心组件

### 1. TaskStatusActions 操作按钮组件

用于显示任务可执行的状态流转操作。

```vue
<template>
  <TaskStatusActions
    :task="task"
    mode="dropdown"
    :show-quick-actions="true"
    @action="handleTaskStatusAction"
    @refresh="refreshData"
  />
</template>

<script setup>
import TaskStatusActions from '/@/modules/task/components/TaskStatusActions.vue'

const handleTaskStatusAction = (action, data) => {
  console.log('状态流转操作:', action, data)
  // 处理状态变更结果
}
</script>
```

#### Props
- `task`: 任务对象
- `mode`: 显示模式 ('quick' | 'dropdown' | 'context' | 'batch')
- `showQuickActions`: 是否显示快捷操作按钮
- `maxQuickActions`: 最大快捷操作数量

#### Events
- `action`: 操作完成事件
- `refresh`: 需要刷新数据事件

### 2. TaskStatusChangeDialog 状态变更对话框

用于执行具体的状态变更操作。

```vue
<template>
  <TaskStatusChangeDialog
    v-model="showDialog"
    :task="selectedTask"
    :target-status="targetStatus"
    @confirm="handleStatusChange"
  />
</template>

<script setup>
import TaskStatusChangeDialog from '/@/modules/task/components/TaskStatusChangeDialog.vue'

const showDialog = ref(false)
const selectedTask = ref(null)
const targetStatus = ref(null)

const handleStatusChange = async (data) => {
  // 处理状态变更
  console.log('状态变更数据:', data)
}
</script>
```

#### Props
- `modelValue`: 对话框显示状态
- `task`: 任务对象
- `targetStatus`: 目标状态

#### Events
- `confirm`: 确认状态变更事件

### 3. useTaskStatusTransition 组合式函数

提供状态流转相关的逻辑和方法。

```typescript
import { useTaskStatusTransition } from '/@/modules/task/composables/useTaskStatusTransition'

const {
  // 状态信息
  getStatusInfo,
  getStatusLabel,
  getStatusColor,
  
  // 状态转换
  isValidTransition,
  getAvailableTransitions,
  
  // API调用
  changeTaskStatus,
  batchChangeStatus,
  getAvailableActions,
  
  // 工具方法
  formatStatusChangeDescription,
  showStatusChangeConfirm
} = useTaskStatusTransition()

// 检查状态转换是否有效
const isValid = isValidTransition(0, 1) // 待分配 -> 待执行

// 获取可转换的状态
const transitions = getAvailableTransitions(0)

// 执行状态变更
const result = await changeTaskStatus({
  taskId: 1,
  targetStatus: 1,
  reason: '分配给执行人'
})
```

## 状态定义

| 状态值 | 状态名称 | 颜色 | 说明 |
|--------|----------|------|------|
| 0 | 待分配 | #909399 | 任务已创建，等待分配执行人 |
| 1 | 待执行 | #e6a23c | 任务已分配，等待开始执行 |
| 2 | 执行中 | #409eff | 任务正在执行中 |
| 3 | 已完成 | #67c23a | 任务执行完成 |
| 4 | 已关闭 | #f56c6c | 任务被关闭或取消 |

## 状态转换规则

```
待分配(0) → 待执行(1) | 已关闭(4)
待执行(1) → 执行中(2) | 已关闭(4)
执行中(2) → 已完成(3) | 已关闭(4)
已完成(3) → 执行中(2)
已关闭(4) → 待执行(1)
```

## API接口

### 状态变更
```typescript
// 单个任务状态变更
POST /admin/task/status/change
{
  taskId: number,
  targetStatus: number,
  reason: string,
  operatorId?: number,
  operatorName?: string
}

// 批量状态变更
POST /admin/task/status/batch-change
{
  taskIds: number[],
  targetStatus: number,
  reason: string
}
```

### 权限检查
```typescript
// 检查状态转换是否有效
GET /admin/task/status/check-transition?sourceStatus=0&targetStatus=1

// 获取可执行操作
GET /admin/task/status/available-actions/{taskId}?operatorId=1
```

### 历史记录
```typescript
// 获取状态变更历史
GET /admin/task/status/history/{taskId}

// 获取完整任务历史
GET /admin/task/status/history/{taskId}/full?limit=50
```

## 权限控制

状态流转操作基于用户权限进行控制：

- **管理员**: 可以执行所有状态转换操作
- **项目经理**: 可以分配任务、关闭任务、重新打开任务
- **任务执行人**: 可以开始执行、完成任务
- **任务创建者**: 可以关闭自己创建的任务

## 错误处理

系统提供了完善的错误处理机制：

```typescript
// 错误类型
const errorTypes = {
  'PERMISSION_DENIED': '权限不足',
  'INVALID_TRANSITION': '无效的状态转换',
  'TASK_NOT_FOUND': '任务不存在',
  'MISSING_ASSIGNEE': '缺少执行人',
  'SYSTEM_ERROR': '系统错误'
}
```

## 最佳实践

1. **状态检查**: 在执行状态变更前先检查转换是否有效
2. **权限验证**: 根据用户权限显示可用操作
3. **用户反馈**: 提供清晰的操作提示和错误信息
4. **历史记录**: 记录详细的状态变更历史
5. **批量操作**: 对于大量任务使用批量操作提高效率

## 示例代码

完整的使用示例请参考 `cool-admin-vue/src/modules/organization/views/project/task.vue` 文件。
