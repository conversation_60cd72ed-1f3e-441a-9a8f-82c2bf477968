<template>
  <div class="assignee-quick-selector">
    <el-select
      v-model="selectedAssigneeId"
      placeholder="请选择执行人"
      filterable
      remote
      reserve-keyword
      :remote-method="searchAssignees"
      :loading="loading"
      clearable
      @change="handleChange"
    >
      <el-option
        v-for="assignee in assigneeOptions"
        :key="assignee.id"
        :label="assignee.label"
        :value="assignee.id"
      >
        <div class="assignee-option">
          <div class="assignee-info">
            <span class="assignee-name">{{ assignee.name }}</span>
            <span class="assignee-dept">{{ assignee.departmentName }}</span>
          </div>
          <div class="assignee-tags">
            <el-tag v-if="assignee.isOnline" type="success" size="small">在线</el-tag>
            <el-tag v-if="assignee.taskCount > 0" type="warning" size="small">
              {{ assignee.taskCount }}个任务
            </el-tag>
          </div>
        </div>
      </el-option>
    </el-select>

    <!-- 快速选择按钮 -->
    <div v-if="showQuickButtons" class="quick-buttons">
      <el-button-group>
        <el-button size="small" @click="selectMyself">
          <el-icon><User /></el-icon>
          分配给我
        </el-button>
        <el-button size="small" @click="showRecommended">
          <el-icon><Star /></el-icon>
          推荐人员
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useCool } from '/@/cool'
import { User, Star } from '@element-plus/icons-vue'

// 执行人选项接口
interface AssigneeOption {
  id: number
  name: string
  label: string
  departmentId?: number
  departmentName?: string
  isOnline?: boolean
  taskCount?: number
  avatar?: string
}

// 组件属性
interface Props {
  modelValue?: number
  contextId?: number
  filterMode?: 'project' | 'department' | 'all'
  showQuickButtons?: boolean
  placeholder?: string
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value?: number): void
  (e: 'change', assignee?: AssigneeOption): void
}

const props = withDefaults(defineProps<Props>(), {
  filterMode: 'department',
  showQuickButtons: true,
  placeholder: '请选择执行人'
})

const emit = defineEmits<Emits>()

const { service, user } = useCool()

// 响应式数据
const selectedAssigneeId = ref<number | undefined>(props.modelValue)
const assigneeOptions = ref<AssigneeOption[]>([])
const loading = ref(false)
const searchKeyword = ref('')

// 计算属性
const currentUser = computed(() => user.info)

// 监听器
watch(() => props.modelValue, (newValue) => {
  selectedAssigneeId.value = newValue
})

watch(selectedAssigneeId, (newValue) => {
  emit('update:modelValue', newValue)
  const selectedAssignee = assigneeOptions.value.find(a => a.id === newValue)
  emit('change', selectedAssignee)
})

// 方法
const handleChange = (value?: number) => {
  selectedAssigneeId.value = value
}

// 搜索执行人 - 简化版本
const searchAssignees = async (keyword: string) => {
  if (!keyword && assigneeOptions.value.length > 0) return

  loading.value = true
  searchKeyword.value = keyword

  try {
    // 暂时使用模拟数据，避免API调用问题
    const mockUsers = [
      {
        id: 1,
        name: '张三',
        userName: '张三',
        departmentId: 1,
        departmentName: '技术部',
        isOnline: true,
        taskCount: 2
      },
      {
        id: 2,
        name: '李四',
        userName: '李四',
        departmentId: 1,
        departmentName: '技术部',
        isOnline: false,
        taskCount: 1
      },
      {
        id: 3,
        name: '王五',
        userName: '王五',
        departmentId: 2,
        departmentName: '运营部',
        isOnline: true,
        taskCount: 0
      }
    ]

    // 根据关键词过滤
    const filteredUsers = keyword
      ? mockUsers.filter(user =>
          user.name.includes(keyword) ||
          user.departmentName.includes(keyword)
        )
      : mockUsers

    console.log('执行人搜索结果:', filteredUsers)

    // 转换数据格式
    assigneeOptions.value = filteredUsers.map((item: any) => {
      const departmentInfo = item.departmentName ? ` (${item.departmentName})` : ''
      return {
        id: item.id || item.userId,
        name: item.name || item.userName,
        label: `${item.name || item.userName}${departmentInfo}`,
        departmentId: item.departmentId,
        departmentName: item.departmentName,
        isOnline: item.isOnline || false,
        taskCount: item.taskCount || 0,
        avatar: item.avatar
      }
    })

  } catch (error) {
    console.error('搜索执行人失败:', error)
    assigneeOptions.value = []
  } finally {
    loading.value = false
  }
}

// 加载默认执行人列表
const loadDefaultAssignees = async () => {
  await searchAssignees('')
}

// 分配给我自己
const selectMyself = () => {
  if (currentUser.value?.id) {
    selectedAssigneeId.value = currentUser.value.id
    
    // 如果当前用户不在选项中，添加进去
    const existingOption = assigneeOptions.value.find(a => a.id === currentUser.value?.id)
    if (!existingOption) {
      const departmentInfo = currentUser.value.departmentName ? ` (${currentUser.value.departmentName})` : ''
      assigneeOptions.value.unshift({
        id: currentUser.value.id,
        name: currentUser.value.name || '我',
        label: `${currentUser.value.name || '我'}${departmentInfo}`,
        departmentId: currentUser.value.departmentId,
        departmentName: currentUser.value.departmentName,
        isOnline: true,
        taskCount: 0
      })
    }
  }
}

// 显示推荐人员 - 简化版本
const showRecommended = async () => {
  loading.value = true

  try {
    // 简单的推荐逻辑：将在线且任务较少的人员排在前面
    const sortedOptions = [...assigneeOptions.value].sort((a, b) => {
      // 在线用户优先
      if (a.isOnline && !b.isOnline) return -1
      if (!a.isOnline && b.isOnline) return 1

      // 任务数量少的优先
      return a.taskCount - b.taskCount
    })

    // 取前3个作为推荐
    const recommendedOptions = sortedOptions.slice(0, 3).map(option => ({
      ...option,
      label: `⭐ ${option.name}${option.departmentName ? ` (${option.departmentName})` : ''}`
    }))

    // 更新选项列表，推荐的排在前面
    const existingIds = new Set(recommendedOptions.map(a => a.id))
    const otherOptions = assigneeOptions.value.filter(option => !existingIds.has(option.id))

    assigneeOptions.value = [...recommendedOptions, ...otherOptions]

  } catch (error) {
    console.error('获取推荐执行人失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载默认数据
onMounted(() => {
  loadDefaultAssignees()
})
</script>

<style scoped>
.assignee-quick-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.assignee-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.assignee-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.assignee-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.assignee-dept {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

.assignee-tags {
  display: flex;
  gap: 4px;
  align-items: center;
}

.quick-buttons {
  display: flex;
  justify-content: flex-start;
}

.quick-buttons .el-button-group {
  display: flex;
}

.quick-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
