<template>
  <div class="organization-filter">
    <!-- 维度切换按钮 -->
    <div class="dimension-switcher">
      <el-radio-group v-model="currentDimension" size="small" @change="handleDimensionChange">
        <el-radio-button label="project">项目维度</el-radio-button>
        <el-radio-button label="department">部门维度</el-radio-button>
      </el-radio-group>
      <el-tooltip content="超级管理员拥有所有部门和项目管理权限" placement="top" v-if="isSuperAdmin">
        <el-icon class="admin-badge"><StarFilled /></el-icon>
      </el-tooltip>
    </div>

    <!-- 部门筛选器 -->
    <div v-if="currentDimension === 'department'" class="department-filter">
      <el-tree-select
        v-model="selectedDepartments"
        :data="departmentTreeData"
        :props="treeProps"
        :placeholder="isSuperAdmin ? '选择部门筛选' : '选择有权限的部门筛选'"
        multiple
        collapse-tags
        collapse-tags-tooltip
        clearable
        filterable
        :max-collapse-tags="2"
        :render-after-expand="false"
        show-checkbox
        check-strictly
        @check="handleDepartmentCheck"
      >
        <template #prefix>
          <el-icon><OfficeBuilding /></el-icon>
        </template>
        <template #default="{ data }">
          <span class="tree-node-content">
            <span>{{ data.name }}</span>
            <!-- <el-tag v-if="data.taskCount > 0" size="small" type="info">
        {{ data.taskCount }}
      </el-tag> -->
            <el-tag v-if="!data.hasPermission && !isSuperAdmin" size="small" type="danger">
              无权限
            </el-tag>
          </span>
        </template>
      </el-tree-select>
    </div>

    <!-- 项目筛选器 -->
    <div v-if="currentDimension === 'project'" class="project-filter">
      <el-select
        v-model="selectedProjects"
        multiple
        :placeholder="isSuperAdmin ? '选择项目筛选' : '选择有权限的项目筛选'"
        collapse-tags
        collapse-tags-tooltip
        clearable
        filterable
        @change="handleProjectChange"
        :max-collapse-tags="2"
      >
        <template #prefix>
          <el-icon><Briefcase /></el-icon>
        </template>
        
        <!-- 具体项目列表 -->
        <el-option-group label="具体项目">
          <el-option
            v-for="project in userProjects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          >
            <div class="project-option">
              <div class="project-info">
                <span class="project-name">{{ project.name }}</span>
              </div>
              <!-- <div class="project-stats" hidden>
                <el-tag
                  :type="getProjectTagType(project)"
                  size="small"
                >
                  {{ getProjectTaskCount(project.id) }}
                </el-tag>
                <el-icon 
                  v-if="project.role === 'admin'" 
                  color="#67c23a"
                  title="管理员"
                >
                  <StarFilled />
                </el-icon>
                <el-icon 
                  v-else-if="project.role === 'member'" 
                  color="#409eff"
                  title="成员"
                >
                  <UserFilled />
                </el-icon>
              </div> -->
            </div>
          </el-option>
        </el-option-group>
      </el-select>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useUserStore } from '/@/modules/base/store/user'
import { service } from '/@/cool'

interface Department {
  id: number
  name: string
  parentName?: string
  permissionLevel?: string
  taskCount?: number
}

interface Project {
  id: number
  name: string
  description?: string
  role?: string
  taskCount?: number
}

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  dimension: {
    type: String,
    default: 'project'
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'update:dimension'])

const userStore = useUserStore()

// 维度切换 - 同步父组件的dimension
const currentDimension = ref<'department' | 'project'>(props.dimension)

// 监听父组件dimension变化
watch(() => props.dimension, (newDimension) => {
  if (newDimension !== currentDimension.value) {
    currentDimension.value = newDimension
  }
})

// 特殊键值（已移除快捷选项）

// 部门和项目数据
const userDepartments = ref<Department[]>([])
const userProjects = ref<Project[]>([])
const currentUserDepartment = ref<Department | null>(null)

// 树形部门数据
const departmentTreeData = ref<any[]>([])
const treeProps = {
  value: 'id', // 关键修复：明确指定id作为唯一值
  label: 'name',
  children: 'children',
  disabled: (data: any) => !data.hasPermission
}

// 选中的值
const selectedDepartments = ref<number[]>([])
const selectedProjects = ref<number[]>([])

// 任务计数（已移除）
// const departmentTaskCounts = ref<Record<number, number>>({})
// const projectTaskCounts = ref<Record<number, number>>({})

// 计算属性
const isSuperAdmin = computed(() => {
  return userStore.user?.isSuper || userStore.user?.role?.includes('admin')
})

// 简化后的计算属性

const displaySelectedOrganizations = computed(() => {
  const getSelectedItems = <T extends { id: number }>(
    selectedIds: number[],
    dataSource: T[]
  ): T[] => {
    if (!selectedIds || selectedIds.length === 0) {
      return [];
    }
    const itemMap = new Map(dataSource.map(item => [item.id, item]));
    return selectedIds.map(id => itemMap.get(id)).filter(Boolean) as T[];
  };

  if (currentDimension.value === 'department') {
    return getSelectedItems(selectedDepartments.value, userDepartments.value);
  } else {
    return getSelectedItems(selectedProjects.value, userProjects.value);
  }
});

// 工具函数：将扁平数组转换为树形结构
const buildTree = (items: any[], parentId: number | null = null): any[] => {
  return items
    .filter(item => (item.parentId === parentId || (parentId === null && !item.parentId)))
    .map(item => ({
      ...item,
      hasPermission: hasDepartmentPermission(item.id),
      // taskCount: getDepartmentTaskCount(item.id), // 移除任务计数
      children: buildTree(items, item.id)
    }))
}

// 检查部门权限
const hasDepartmentPermission = (departmentId: number): boolean => {
  if (isSuperAdmin.value) return true
  return userDepartments.value.some(dept => dept.id === departmentId)
}

// 方法
// const getDepartmentTaskCount = (departmentId: number) => {
//   return departmentTaskCounts.value[departmentId] || 0
// }
// 
// const getProjectTaskCount = (projectId: number) => {
//   return projectTaskCounts.value[projectId] || 0
// }
// 
// const getOrganizationTaskCount = (id: number) => {
//   return currentDimension.value === 'department' 
//     ? getDepartmentTaskCount(id) 
//     : getProjectTaskCount(id)
// }

const getOrganizationTagType = (org: Department | Project) => {
  if (currentDimension.value === 'department') {
    const dept = org as Department
    switch (dept.permissionLevel) {
      case 'full': return 'success'
      case 'department': return 'primary'
      case 'limited': return 'warning'
      default: return 'info'
    }
  } else {
    const project = org as Project
    switch (project.role) {
      case 'admin': return 'success'
      case 'member': return 'primary'
      default: return 'info'
    }
  }
}

const handleDimensionChange = (dimension: 'department' | 'project') => {
  // 清空当前选择
  selectedDepartments.value = []
  selectedProjects.value = []
  emit('update:modelValue', [])
  emit('update:dimension', dimension)
  
  // 加载对应维度的数据
  if (dimension === 'department') {
    loadDepartments()
  } else {
    loadProjects()
  }
}

// 使用 @check 事件来处理 check-strictly 模式下的选择
const handleDepartmentCheck = (currentNode: any, checkedNodes: any) => {
  console.log("currentNode:", currentNode);
  console.log("checkedNodes:", checkedNodes);
  
  // 根据用户指示，直接处理 checkedNodes.checkedNodes 来获取部门 ID
  let departmentIds: number[] = [];
  
  // 直接从 checkedNodes.checkedNodes 提取 ID
  if (checkedNodes && checkedNodes.checkedNodes && Array.isArray(checkedNodes.checkedNodes)) {
    departmentIds = checkedNodes.checkedNodes.map((node: any) => node.id);
  }
  
  console.log("departmentIds:", departmentIds);
  selectedDepartments.value = departmentIds;
 
  emit("update:modelValue", departmentIds);
  emit("change", departmentIds);
};

const handleProjectChange = () => {
	// v-model已经自动更新了selectedProjects.value
	emit("update:modelValue", selectedProjects.value);
	emit("change", selectedProjects.value);
};

const removeOrganization = (id: number) => {
  if (currentDimension.value === 'department') {
    const newValues = selectedDepartments.value.filter(value => value !== id)
    selectedDepartments.value = newValues
    // 直接触发事件而不是调用handleDepartmentChange
    emit("update:modelValue", newValues);
    emit("change", newValues);
  } else {
    const newValues = selectedProjects.value.filter(value => value !== id)
    selectedProjects.value = newValues
    emit("update:modelValue", newValues);
    emit("change", newValues);
  }
}

const clearAll = () => {
  selectedDepartments.value = []
  selectedProjects.value = []
  emit('update:modelValue', [])
  emit('change', [])
}

const getDepartmentTagType = (dept: Department) => {
  switch (dept.permissionLevel) {
    case 'full': return 'success'
    case 'department': return 'primary'
    case 'limited': return 'warning'
    default: return 'info'
  }
}

const getProjectTagType = (project: Project) => {
  switch (project.role) {
    case 'admin': return 'success'
    case 'member': return 'primary'
    default: return 'info'
  }
}

// 数据加载方法
const loadDepartments = async () => {
  try {
    const response = await service.base.sys.department.list()
    if (response && Array.isArray(response)) {
      userDepartments.value = response
      
      // 构建树形结构
      departmentTreeData.value = buildTree(response)
      
      const currentDept = response.find(dept => 
        userStore.user?.departmentId === dept.id
      )
      if (currentDept) {
        currentUserDepartment.value = currentDept
      }
    }
    
    // await loadDepartmentTaskCounts() // 移除任务计数加载
  } catch (error) {
    console.error('加载部门数据失败:', error)
  }
}

const loadProjects = async () => {
  try {
    const response = await service.organization.project.task.accessibleProjects();
    let projects = [];
    if (response?.data && Array.isArray(response.data)) {
      projects = response.data;
    } else if (Array.isArray(response)) {
      projects = response;
    } else {
      console.warn('项目数据格式异常:', response);
      userProjects.value = [];
      return;
    }

    const projectMap = new Map();
    projects.forEach(project => {
      const id = project.projectId || project.id;
      if (id && !projectMap.has(id)) {
        projectMap.set(id, {
          id: id,
          name: project.projectName || project.name,
          role: project.userRole || project.role,
          taskCount: project.taskCount || 0
        });
      }
    });

    userProjects.value = Array.from(projectMap.values());

  } catch (error) {
    console.error('加载项目数据失败:', error);
    userProjects.value = [];
  }
};

// const loadDepartmentTaskCounts = async () => {
//   try {
//     const response = await service.task.info.statistics()
//     if (response && response.departmentCounts) {
//       departmentTaskCounts.value = response.departmentCounts
//     }
//   } catch (error) {
//     console.error('加载部门任务计数失败:', error)
//   }
// }
// 
// const loadProjectTaskCounts = async () => {
//   try {
//     const response = await service.task.info.statistics()
//     if (response && response.projectCounts) {
//       projectTaskCounts.value = response.projectCounts
//     }
//   } catch (error) {
//     console.error('加载项目任务计数失败:', error)
//   }
// }

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (currentDimension.value === 'department') {
    selectedDepartments.value = newValue
  } else {
    selectedProjects.value = newValue
  }
}, { deep: true })

// 初始化
onMounted(async () => {
  await loadDepartments()
  await loadProjects()
})
</script>

<style scoped lang="scss">
.organization-filter {
  .dimension-switcher {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .admin-badge {
      color: #f7ba2a;
      font-size: 16px;
    }
  }

  .department-filter,
  .project-filter {
    margin-bottom: 12px;
  }



  .department-option,
  .project-option,
  .tree-node-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .tree-node-content {
    padding: 0 4px;
  }

  .tree-node-content .el-tag {
    margin-left: 8px;
  }

  .dept-info,
  .project-info {
    flex: 1;
    min-width: 0;
  }

  .dept-name,
  .project-name {
    font-weight: 500;
    color: #333;
  }

  .dept-path,
  .project-desc {
    font-size: 12px;
    color: #666;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
  }

  .dept-stats,
  .project-stats {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .selected-organizations {
    margin-top: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;

    .selected-label {
      color: #666;
      font-size: 13px;
      margin-right: 4px;
    }

    .selected-tag {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .selected-organizations {
      flex-direction: column;
      align-items: flex-start;
    }

    .dept-name,
    .project-name {
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
