<template>
  <div class="global-task-creator">
    <!-- 快速创建对话框 -->
    <el-dialog
      v-model="visible"
      :title="options.title || '快速创建任务'"
      width="700px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      @close="handleClose"
    >
      <!-- 对话框头部信息 -->
      <div v-if="showContextInfo" class="dialog-header-info">
        <div class="context-info">
          <el-tag v-if="options.context?.projectName" type="success" size="small">
            项目: {{ options.context.projectName }}
          </el-tag>
          <el-tag v-if="options.context?.departmentName" type="primary" size="small">
            部门: {{ options.context.departmentName }}
          </el-tag>
          <el-tag v-if="options.mode === 'quick'" type="info" size="small">
            快速创建
          </el-tag>
        </div>
      </div>

      <!-- 任务表单 -->
      <TaskFormSimple
        :mode="options.mode"
        :context="options.context"
        :default-values="options.defaultValues"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />

      <!-- 成功提示 -->
      <div v-if="showSuccessActions" class="success-actions">
        <div class="success-message">
          <el-icon color="#67c23a"><SuccessFilled /></el-icon>
          <span>任务创建成功！</span>
        </div>
        <div class="action-buttons">
          <el-button size="small" @click="handleViewTask">
            <el-icon><View /></el-icon>
            查看任务
          </el-button>
          <el-button size="small" @click="handleContinueCreate">
            <el-icon><Plus /></el-icon>
            继续创建
          </el-button>
          <el-button size="small" @click="handleAssignTask">
            <el-icon><User /></el-icon>
            分配任务
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 任务模板选择器 -->
    <TaskTemplateSelector
      v-model="showTemplateSelector"
      @select="handleTemplateSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { SuccessFilled, View, Plus, User } from '@element-plus/icons-vue'
import { useGlobalTaskCreator } from '../composables/useGlobalTaskCreator'
import TaskFormSimple from './TaskFormSimple.vue'
import TaskTemplateSelector from './TaskTemplateSelector.vue'
import { useGlobalTaskDetail } from '../composables/useTaskDetail'

const { visible, options, close } = useGlobalTaskCreator()
const { show: showTaskDetail } = useGlobalTaskDetail()

// 响应式数据
const showSuccessActions = ref(false)
const showTemplateSelector = ref(false)
const createdTask = ref<any>(null)

// 计算属性
const showContextInfo = computed(() => {
  return options.value.context?.projectName || 
         options.value.context?.departmentName || 
         options.value.mode === 'quick'
})

// 监听对话框显示状态
watch(visible, (newValue) => {
  if (!newValue) {
    // 对话框关闭时重置状态
    showSuccessActions.value = false
    createdTask.value = null
  }
})

// 方法
const handleSubmit = async (formData: any) => {
  console.log('全局对话框接收到提交数据:', formData)
  // TaskFormSimple 组件内部会调用 createTask，这里不需要额外处理
}

const handleCancel = () => {
  if (options.value.onCancel) {
    options.value.onCancel()
  }
  close()
}

const handleClose = () => {
  handleCancel()
}

// 成功后的操作
const handleViewTask = () => {
  if (createdTask.value) {
    showTaskDetail(createdTask.value)
  }
  close()
}

const handleContinueCreate = () => {
  // 重置成功状态，保持对话框打开
  showSuccessActions.value = false
  createdTask.value = null
  
  // 可以保留当前的上下文信息
  // 表单会自动重置
}

const handleAssignTask = () => {
  if (createdTask.value) {
    // 触发分配任务对话框
    // 这里可以集成 AssigneeSelector 或其他分配组件
    console.log('分配任务:', createdTask.value)
  }
  close()
}

// 模板选择处理
const handleTemplateSelect = (template: any) => {
  console.log('选择任务模板:', template)
  // 将模板数据应用到表单
  showTemplateSelector.value = false
}

// 显示模板选择器
const showTemplates = () => {
  showTemplateSelector.value = true
}
</script>

<style scoped>
.global-task-creator {
  /* 全局样式 */
}

.dialog-header-info {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary);
}

.context-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.success-actions {
  margin-top: 24px;
  padding: 20px;
  background: var(--el-color-success-light-9);
  border-radius: 6px;
  border: 1px solid var(--el-color-success-light-7);
}

.success-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-color-success);
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.action-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 对话框样式覆盖 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
    justify-content: center;
  }
}
</style>
