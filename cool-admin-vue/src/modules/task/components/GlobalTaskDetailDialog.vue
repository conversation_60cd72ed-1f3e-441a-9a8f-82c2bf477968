<template>
  <div>
    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="visible"
      :task="task"
      @assign-task="handleAssignTask"
      @start-task="handleStartTask"
      @complete-task="handleCompleteTask"
      @close-task="handleCloseTask"
      @adjust-time="handleAdjustTime"
      @task-updated="handleTaskUpdated"
    />

    <!-- 分配任务对话框 -->
    <AssigneeSelector
      v-model="showAssignDialog"
      :taskName="currentAssignTask?.name || '未知任务'"
      :taskTags="getTaskTags(currentAssignTask)"
      :filterMode="getAssigneeFilterMode()"
      :contextId="getAssigneeContextId()"
      @confirm="handleAssignConfirm"
    />

    <!-- 完成任务对话框 -->
    <TaskCompleter
      v-model="showCompleteDialog"
      :task="currentCompleteTask"
      @confirm="handleCompleteConfirm"
    />

    <!-- 关闭任务对话框 -->
    <TaskCloser
      v-model="showCloseDialog"
      :task="currentCloseTask"
      @confirm="handleCloseConfirm"
    />

    <!-- 调整时间对话框 -->
    <TaskTimeAdjuster
      v-model="showAdjustTimeDialog"
      :taskId="currentAdjustTimeTask?.id"
      :initialStartTime="currentAdjustTimeTask?.startTime"
      :initialEndTime="currentAdjustTimeTask?.endTime"
      @confirm="handleAdjustTimeConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useGlobalTaskDetail } from '../composables/useTaskDetail'
import TaskDetailDialog from './TaskDetailDialog.vue'
import AssigneeSelector from '../../../components/assignee-selector/AssigneeSelector.vue'
import TaskCompleter from './TaskCompleter.vue'
import TaskCloser from './TaskCloser.vue'
import TaskTimeAdjuster from './TaskTimeAdjuster.vue'

// 使用全局任务详情状态
const { visible, task } = useGlobalTaskDetail()

// 状态管理相关
const showAssignDialog = ref(false)
const currentAssignTask = ref<any>(null)

const showCompleteDialog = ref(false)
const currentCompleteTask = ref<any>(null)

const showCloseDialog = ref(false)
const currentCloseTask = ref<any>(null)

const showAdjustTimeDialog = ref(false)
const currentAdjustTimeTask = ref<any>(null)

const selectedProjectId = ref<number | undefined>(undefined)

// 获取任务标签 - 增强归属信息显示
const getTaskTags = (task: any) => {
  if (!task) return []

  const tags = []

  // 添加任务类型标签
  if (task.taskCategory) {
    const categoryMap: Record<string, string> = {
      'SOP_STEP': '场景步骤',
      'RC': '日常',
      'ZQ': '周期',
      'LS': '临时'
    }
    tags.push(categoryMap[task.taskCategory] || task.taskCategory)
  }

  // 添加归属标签 - 优先显示项目归属
  if (task.projectId && task.projectName) {
    tags.push(`项目归属: ${task.projectName}`)
  } else if (task.departmentId && task.departmentName) {
    tags.push(`部门归属: ${task.departmentName}`)
  } else {
    tags.push('归属: 未指定')
  }

  // 添加场景标签
  if (task.scenarioName) {
    tags.push(`场景: ${task.scenarioName}`)
  }

  // 添加步骤标签
  if (task.stepName) {
    tags.push(`步骤: ${task.stepName}`)
  }

  // 添加当前执行人标签
  if (task.assigneeName) {
    tags.push(`当前执行人: ${task.assigneeName}`)
  }

  return tags
}

// 获取分配器的筛选模式 - 根据任务归属智能选择
const getAssigneeFilterMode = () => {
  if (!currentAssignTask.value) {
    console.warn('当前分配任务为空，使用默认部门模式')
    return 'department'
  }

  const task = currentAssignTask.value

  // 判断任务归属：优先检查项目归属
  if (task.projectId && task.projectName) {
    console.log(`任务归属于项目: ${task.projectName} (ID: ${task.projectId})，使用项目筛选模式`)
    return 'project'
  } else if (task.departmentId && task.departmentName) {
    console.log(`任务归属于部门: ${task.departmentName} (ID: ${task.departmentId})，使用部门筛选模式`)
    return 'department'
  } else {
    console.log('任务未指定明确归属，使用默认部门筛选模式')
    return 'department'
  }
}

// 获取分配器的上下文ID - 根据筛选模式返回对应ID
const getAssigneeContextId = () => {
  if (!currentAssignTask.value) {
    console.warn('当前分配任务为空，返回undefined')
    return undefined
  }

  const task = currentAssignTask.value
  const filterMode = getAssigneeFilterMode()

  if (filterMode === 'project') {
    // 项目模式：返回项目ID
    const projectId = task.projectId
    if (projectId) {
      console.log(`使用项目ID作为上下文: ${projectId}`)
      return projectId
    } else {
      console.warn('项目模式但项目ID为空，返回undefined')
      return undefined
    }
  } else {
    // 部门模式：返回部门ID
    const departmentId = task.departmentId
    if (departmentId) {
      console.log(`使用部门ID作为上下文: ${departmentId}`)
      return departmentId
    } else {
      console.log('部门模式但部门ID为空，返回undefined以显示所有执行人')
      return undefined
    }
  }
}

// 状态管理事件处理
const handleAssignTask = (taskData: any) => {
  console.log('全局对话框 - 分配任务:', taskData)
  currentAssignTask.value = taskData
  showAssignDialog.value = true
  // 不关闭详情对话框，保持打开状态
}

const handleStartTask = async (taskData: any) => {
  console.log('全局对话框 - 开始执行任务:', taskData)
  try {
    // 这里调用API开始任务
    // await taskApi.startTask(taskData.id)
    ElMessage.success('任务已开始执行')
    // 不关闭详情对话框，让用户看到状态变化
    // 刷新任务数据
    handleTaskUpdated()
  } catch (error) {
    ElMessage.error('开始任务失败')
    console.error('开始任务失败:', error)
  }
}

const handleCompleteTask = (taskData: any) => {
  console.log('全局对话框 - 完成任务:', taskData)
  currentCompleteTask.value = {
    ...taskData,
    assigneeId: taskData.assigneeId || 1
  }
  showCompleteDialog.value = true
  // 不关闭详情对话框，保持打开状态
}

const handleCloseTask = (taskData: any) => {
  console.log('全局对话框 - 关闭任务:', taskData)
  currentCloseTask.value = taskData
  showCloseDialog.value = true
  // 不关闭详情对话框，保持打开状态
}

const handleAdjustTime = (taskData: any) => {
  console.log('全局对话框 - 调整时间:', taskData)
  currentAdjustTimeTask.value = taskData
  showAdjustTimeDialog.value = true
  // 不关闭详情对话框，保持打开状态
}

const handleTaskUpdated = () => {
  console.log('全局对话框 - 任务已更新，刷新数据')
  // 这里可以触发全局的任务数据刷新
  // 可以通过事件总线或者全局状态管理来通知其他组件刷新
}

// 各种确认处理方法
const handleAssignConfirm = async (data: any) => {
  try {
    console.log('全局对话框 - 确认分配:', data)
    // 调用分配API
    ElMessage.success('任务分配成功')
    showAssignDialog.value = false
    handleTaskUpdated()
  } catch (error) {
    ElMessage.error('任务分配失败')
    console.error('任务分配失败:', error)
  }
}

const handleCompleteConfirm = async (data: any) => {
  try {
    console.log('全局对话框 - 确认完成:', data)
    // 调用完成API
    ElMessage.success('任务已完成')
    showCompleteDialog.value = false
    handleTaskUpdated()
  } catch (error) {
    ElMessage.error('任务完成失败')
    console.error('任务完成失败:', error)
  }
}

const handleCloseConfirm = async (data: any) => {
  try {
    console.log('全局对话框 - 确认关闭:', data)
    // 调用关闭API
    ElMessage.success('任务已关闭')
    showCloseDialog.value = false
    handleTaskUpdated()
  } catch (error) {
    ElMessage.error('任务关闭失败')
    console.error('任务关闭失败:', error)
  }
}

const handleAdjustTimeConfirm = async (data: any) => {
  try {
    console.log('全局对话框 - 确认调整时间:', data)
    // 调用调整时间API
    ElMessage.success('时间调整成功')
    showAdjustTimeDialog.value = false
    handleTaskUpdated()
  } catch (error) {
    ElMessage.error('时间调整失败')
    console.error('时间调整失败:', error)
  }
}
</script>
