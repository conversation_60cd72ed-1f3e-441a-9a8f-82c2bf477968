<template>
	<el-dialog
		v-model="visible"
		width="600px"
		:close-on-click-modal="false"
		:show-close="false"
		class="task-closer-dialog"
		@close="handleClose"
	>
		<!-- 自定义头部 -->
		<template #header>
			<div class="dialog-header">
				<div class="header-icon">
					<el-icon class="warning-icon"><WarningFilled /></el-icon>
				</div>
				<div class="header-content">
					<h3 class="dialog-title">关闭任务</h3>
					<p class="dialog-subtitle">请说明关闭任务的原因</p>
				</div>
			</div>
		</template>

		<!-- 任务信息卡片 -->
		<div class="task-info-card">
			<TaskInfoDisplay :task="task" />
		</div>

		<!-- 关闭表单 -->
		<div class="close-form">
			<el-form
				ref="formRef"
				:model="form"
				:rules="rules"
				label-width="100px"
				class="form-container"
			>
				<el-form-item label="关闭原因" prop="closeReason" class="form-item">
					<el-input
						v-model="form.closeReason"
						type="textarea"
						:rows="5"
						placeholder="请详细说明关闭任务的原因，如：任务取消、需求变更、技术问题等..."
						class="reason-input"
						show-word-limit
						maxlength="500"
					/>
					<div class="required-tip">
						<el-icon><InfoFilled /></el-icon>
						<span>关闭原因为必填项，将记录在任务历史中</span>
					</div>
				</el-form-item>
			</el-form>
		</div>

		<!-- 自定义底部 -->
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose" class="cancel-btn">
					<el-icon><Close /></el-icon>
					取消
				</el-button>
				<el-button type="danger" @click="handleConfirm" class="confirm-btn">
					<el-icon><WarningFilled /></el-icon>
					确认关闭
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { WarningFilled, Close, InfoFilled } from "@element-plus/icons-vue";
import TaskInfoDisplay from "./TaskInfoDisplay.vue";
import type { TaskInfoEntity } from "../types";

defineOptions({
	name: "TaskCloser"
});

interface Props {
	modelValue: boolean;
	task?: TaskInfoEntity;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: false,
	task: undefined
});

const emit = defineEmits<{
	(e: "update:modelValue", value: boolean): void;
	(e: "confirm", data: { taskId: number; closeReason: string }): void;
}>();

const visible = computed({
	get: () => props.modelValue,
	set: (val) => emit("update:modelValue", val)
});

const formRef = ref<FormInstance>();
const form = ref({
	closeReason: ""
});

const rules = ref<FormRules>({
	closeReason: [{ required: true, message: "关闭原因不能为空", trigger: "blur" }]
});

const handleConfirm = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid) => {
		if (valid) {
			if (!props.task || !props.task.id) {
				ElMessage.warning("任务信息不完整，无法关闭");
				return;
			}
			emit("confirm", {
				taskId: props.task.id,
				closeReason: form.value.closeReason
			});
			handleClose();
		} else {
			ElMessage.warning("请填写关闭原因");
		}
	});
};

const handleClose = () => {
	visible.value = false;
	formRef.value?.resetFields();
};

watch(
	() => props.modelValue,
	(val) => {
		if (!val) {
			handleClose();
		}
	}
);
</script>

<style scoped>
.task-closer-dialog {
	.dialog-header {
		display: flex;
		align-items: center;
		padding: 20px 24px 16px;
		border-bottom: 1px solid var(--el-border-color-light);
		background: var(--el-bg-color);

		.header-icon {
			width: 48px;
			height: 48px;
			border-radius: 50%;
			background: var(--el-color-warning);
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16px;

			.warning-icon {
				font-size: 24px;
				color: white;
			}
		}

		.header-content {
			flex: 1;

			.dialog-title {
				margin: 0 0 4px 0;
				font-size: 18px;
				font-weight: 600;
				color: var(--el-text-color-primary);
			}

			.dialog-subtitle {
				margin: 0;
				font-size: 14px;
				color: var(--el-text-color-regular);
			}
		}
	}
}

.task-info-card {
	margin: 20px 0;
	padding: 16px;
	background: var(--el-fill-color-light);
	border-radius: 8px;
	border-left: 4px solid var(--el-color-warning);
}

.close-form {
	.form-container {
		.form-item {
			margin-bottom: 24px;

			:deep(.el-form-item__label) {
				font-weight: 500;
				color: var(--el-text-color-primary);
			}
		}

		.reason-input {
			:deep(.el-textarea__inner) {
				border-radius: 6px;
				border: 1px solid var(--el-border-color);
				background: var(--el-bg-color);
				color: var(--el-text-color-primary);
				transition: all 0.3s;

				&:focus {
					border-color: var(--el-color-warning);
					box-shadow: 0 0 0 2px var(--el-color-warning-light-8);
				}
			}
		}

		.required-tip {
			display: flex;
			align-items: center;
			margin-top: 8px;
			padding: 8px 12px;
			background: var(--el-fill-color-light);
			border-radius: 4px;
			font-size: 12px;
			color: var(--el-text-color-regular);

			.el-icon {
				margin-right: 6px;
				color: var(--el-text-color-regular);
			}
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding: 16px 24px 20px;
	border-top: 1px solid var(--el-border-color-light);
	background: var(--el-bg-color);

	.cancel-btn {
		padding: 10px 20px;
		border-radius: 6px;

		.el-icon {
			margin-right: 6px;
		}
	}

	.confirm-btn {
		padding: 10px 24px;
		border-radius: 6px;

		.el-icon {
			margin-right: 6px;
		}
	}
}

/* 全局对话框样式覆盖 */
:deep(.el-dialog) {
	border-radius: 12px;
	overflow: hidden;
	background: var(--el-bg-color);
}

:deep(.el-dialog__header) {
	padding: 0;
}

:deep(.el-dialog__body) {
	padding: 0 24px;
	background: var(--el-bg-color);
}

:deep(.el-dialog__footer) {
	padding: 0;
}
</style>
