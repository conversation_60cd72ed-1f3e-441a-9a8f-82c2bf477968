<template>
	<el-dialog
		v-model="visible"
		width="600px"
		:close-on-click-modal="false"
		:show-close="false"
		class="task-completer-dialog"
		@close="handleClose"
	>
		<!-- 自定义头部 -->
		<template #header>
			<div class="dialog-header">
				<div class="header-icon">
					<el-icon class="success-icon"><Check /></el-icon>
				</div>
				<div class="header-content">
					<h3 class="dialog-title">完成任务</h3>
					<p class="dialog-subtitle">请填写任务完成信息</p>
				</div>
			</div>
		</template>

		<!-- 任务信息卡片 -->
		<div class="task-info-card">
			<TaskInfoDisplay :task="task" />
		</div>

		<!-- 完成表单 -->
		<div class="completion-form">
			<el-form :model="form" label-width="100px" class="form-container">
				<el-form-item label="完成备注" class="form-item">
					<el-input
						v-model="form.remark"
						type="textarea"
						:rows="4"
						placeholder="请详细描述任务完成情况、遇到的问题或需要说明的事项..."
						class="remark-input"
						show-word-limit
						maxlength="500"
					/>
				</el-form-item>

				<el-form-item label="相关附件" class="form-item">
					<div class="upload-container">
						<cl-upload
							v-model="form.attachments"
							multiple
							:limit="10"
							accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx"
						/>
						<div class="upload-tip">
							<el-icon><InfoFilled /></el-icon>
							<span>支持上传图片、文档等文件，最多10个文件</span>
						</div>
					</div>
				</el-form-item>
			</el-form>
		</div>

		<!-- 自定义底部 -->
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose" class="cancel-btn">
					<el-icon><Close /></el-icon>
					取消
				</el-button>
				<el-button type="primary" @click="handleConfirm" class="confirm-btn">
					<el-icon><Check /></el-icon>
					确认完成
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { Check, Close, InfoFilled } from "@element-plus/icons-vue";
import TaskInfoDisplay from "./TaskInfoDisplay.vue";
import type { TaskInfoEntity } from "../types";

defineOptions({
	name: "TaskCompleter"
});

interface Props {
	modelValue: boolean;
	task?: TaskInfoEntity;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: false,
	task: undefined
});

const emit = defineEmits<{
	(e: "update:modelValue", value: boolean): void;
	(
		e: "confirm",
		data: {
			taskId: number;
			assigneeId: number;
			remark: string;
			attachments: any[];
		}
	): void;
}>();

const visible = computed({
	get: () => props.modelValue,
	set: (val) => emit("update:modelValue", val)
});

const form = ref({
	remark: "",
	attachments: []
});

const handleConfirm = () => {
	if (!props.task || !props.task.id || !props.task.assigneeId) {
		ElMessage.warning("任务信息不完整，无法完成任务");
		return;
	}

	emit("confirm", {
		taskId: props.task.id,
		assigneeId: props.task.assigneeId,
		remark: form.value.remark,
		attachments: form.value.attachments
	});
	handleClose();
};

const handleClose = () => {
	visible.value = false;
	form.value.remark = "";
	form.value.attachments = [];
};

watch(
	() => props.modelValue,
	(val) => {
		if (!val) {
			handleClose();
		}
	}
);
</script>

<style scoped>
.task-completer-dialog {
	.dialog-header {
		display: flex;
		align-items: center;
		padding: 20px 24px 16px;
		border-bottom: 1px solid var(--el-border-color-light);
		background: var(--el-bg-color);

		.header-icon {
			width: 48px;
			height: 48px;
			border-radius: 50%;
			background: var(--el-color-primary);
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16px;

			.success-icon {
				font-size: 24px;
				color: white;
			}
		}

		.header-content {
			flex: 1;

			.dialog-title {
				margin: 0 0 4px 0;
				font-size: 18px;
				font-weight: 600;
				color: var(--el-text-color-primary);
			}

			.dialog-subtitle {
				margin: 0;
				font-size: 14px;
				color: var(--el-text-color-regular);
			}
		}
	}
}

.task-info-card {
	margin: 20px 0;
	padding: 16px;
	background: var(--el-fill-color-light);
	border-radius: 8px;
	border-left: 4px solid var(--el-color-primary);
}

.completion-form {
	.form-container {
		.form-item {
			margin-bottom: 24px;

			:deep(.el-form-item__label) {
				font-weight: 500;
				color: var(--el-text-color-primary);
			}
		}

		.remark-input {
			:deep(.el-textarea__inner) {
				border-radius: 6px;
				border: 1px solid var(--el-border-color);
				background: var(--el-bg-color);
				color: var(--el-text-color-primary);
				transition: all 0.3s;

				&:focus {
					border-color: var(--el-color-primary);
					box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
				}
			}
		}
	}
}

.upload-container {
	.upload-tip {
		display: flex;
		align-items: center;
		margin-top: 8px;
		padding: 8px 12px;
		background: var(--el-fill-color-light);
		border-radius: 4px;
		font-size: 12px;
		color: var(--el-text-color-regular);

		.el-icon {
			margin-right: 6px;
			color: var(--el-text-color-regular);
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding: 16px 24px 20px;
	border-top: 1px solid var(--el-border-color-light);
	background: var(--el-bg-color);

	.cancel-btn {
		padding: 10px 20px;
		border-radius: 6px;

		.el-icon {
			margin-right: 6px;
		}
	}

	.confirm-btn {
		padding: 10px 24px;
		border-radius: 6px;

		.el-icon {
			margin-right: 6px;
		}
	}
}

/* 全局对话框样式覆盖 */
:deep(.el-dialog) {
	border-radius: 12px;
	overflow: hidden;
	background: var(--el-bg-color);
}

:deep(.el-dialog__header) {
	padding: 0;
}

:deep(.el-dialog__body) {
	padding: 0 24px;
	background: var(--el-bg-color);
}

:deep(.el-dialog__footer) {
	padding: 0;
}
</style>
