<template>
  <el-dialog
    v-model="visible"
    :title="task?.name || '任务详情'"
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="task-detail-dialog"
    @close="handleClose"
  >
    <!-- 紧凑头部 -->
    <template #header>
      <div class="dialog-header">
        <div class="header-main">
          <div class="task-title">
            <h3>{{ task?.name || '未命名任务' }}</h3>
            <el-tag 
              :type="getTaskStatusType(task?.taskStatus)" 
              size="small"
              class="status-tag"
            >
              {{ getTaskStatusLabel(task?.taskStatus) }}
            </el-tag>
          </div>
          <div class="task-category">
            <el-tag 
              :type="getTaskCategoryLabel(task?.taskCategory)?.type || 'info'" 
              size="small"
            >
              {{ getTaskCategoryLabel(task?.taskCategory)?.label || '未知类型' }}
            </el-tag>
          </div>
        </div>
        <div class="header-meta">
          <span class="task-id">ID: {{ task?.id || 'N/A' }}</span>
          <span class="task-priority" v-if="task?.priority">
            优先级: {{ task.priority }}
          </span>
        </div>
      </div>
    </template>

      <!-- 任务内容区域 -->
    <div class="task-body">
      <!-- 左侧主要信息 -->
      <div class="main-content">
        <!-- 基本信息卡片 -->
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon">
              <el-icon color="#409EFF"><Document /></el-icon>
            </div>
            <h3 class="card-title">基本信息</h3>
          </div>
          <div class="card-content">
            <!-- 折叠触发器 -->
            <div class="collapse-trigger" @click="toggleCollapse('basic')">
              <span>基本信息</span>
              <el-icon>
                <ArrowUp v-if="!collapsed.basic" />
                <ArrowDown v-else />
              </el-icon>
            </div>
            
            <!-- 折叠内容 -->
            <div v-show="!collapsed.basic" class="collapse-content">
              <!-- 任务描述 -->
              <div class="info-item">
                <div class="info-label">任务描述</div>
                <div class="info-value">
                  <el-tag v-if="task.description" type="info" size="small">{{ task.description }}</el-tag>
                  <el-tag v-else type="info" size="small" effect="plain">暂无描述</el-tag>
                </div>
              </div>
              
              <!-- 具体活动 -->
              <div v-if="task.taskActivity" class="info-item">
                <div class="info-label">具体活动</div>
                <div class="info-value">
                  <el-tag type="primary" size="small">{{ task.taskActivity }}</el-tag>
                </div>
              </div>
              
              <!-- 工作亮点 -->
              <div v-if="task.workHighlight" class="info-item">
                <div class="info-label">工作亮点</div>
                <div class="info-value">
                  <el-tag type="success" size="small">{{ task.workHighlight }}</el-tag>
                </div>
              </div>
              
              <!-- 行为要求 -->
              <div v-if="task.employeeBehavior" class="info-item">
                <div class="info-label">行为要求</div>
                <div class="info-value">
                  <el-tag type="warning" size="small">{{ task.employeeBehavior }}</el-tag>
                </div>
              </div>
              
              <!-- 执行地点 -->
              <div v-if="task.entityTouchpoint" class="info-item">
                <div class="info-label">执行地点</div>
                <div class="info-value">
                  <el-tag type="danger" size="small">{{ task.entityTouchpoint }}</el-tag>
                </div>
              </div>
              
              <!-- 执行角色 -->
              <div v-if="task.employeeRole" class="info-item">
                <div class="info-label">执行角色</div>
                <div class="info-value">
                  <el-tag type="warning" effect="plain">{{ task.employeeRole }}</el-tag>
                </div>
              </div>
              
              <!-- 执行人员 -->
              <div class="info-item">
                <div class="info-label">执行人员</div>
                <div class="info-value">
                  <div v-if="task.assigneeName" class="assignee-card">
                    <el-avatar :size="48" class="assignee-avatar">
                      <span>{{ task.assigneeName?.charAt(0) }}</span>
                    </el-avatar>
                    <div class="assignee-info">
                      <div class="assignee-name">{{ task.assigneeName }}</div>
                      <div class="assignee-id">ID: {{ task.assigneeId }}</div>
                    </div>
                  </div>
                  <div v-else class="no-assignee">
                    <el-icon color="#909399"><UserFilled /></el-icon>
                    <span>待分配执行人</span>
                  </div>
                </div>
              </div>
              

            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="side-panel">
        <!-- 项目信息卡片 -->
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon">
              <el-icon color="#409EFF"><Folder /></el-icon>
            </div>
            <h3 class="card-title">项目信息</h3>
          </div>
          <div class="card-content">
            <!-- 折叠触发器 -->
            <div class="collapse-trigger" @click="toggleCollapse('project')">
              <span>项目信息</span>
              <el-icon>
                <ArrowUp v-if="!collapsed.project" />
                <ArrowDown v-else />
              </el-icon>
            </div>

            <!-- 折叠内容 -->
            <div v-show="!collapsed.project" class="collapse-content">
              <!-- 项目信息 -->
              <div class="info-item">
                <div class="info-label">项目信息</div>
                <div class="info-value project-info">
                  <div v-if="task.projectName" class="project-item">
                    <span class="project-label">项目名称</span>
                    <el-tag type="primary" size="small" class="project-value">{{ task.projectName }}</el-tag>
                  </div>
                  <div v-if="task.scenarioName" class="project-item">
                    <span class="project-label">场景名称</span>
                    <el-tag type="success" size="small" class="project-value">{{ task.scenarioName }}</el-tag>
                  </div>
                  <div v-if="task.stepName" class="project-item">
                    <span class="project-label">步骤名称</span>
                    <el-tag type="warning" size="small" class="project-value">{{ task.stepName }}</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 时间信息卡片 -->
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon">
              <el-icon color="#F56C6C"><Clock /></el-icon>
            </div>
            <h3 class="card-title">时间信息</h3>
          </div>
          <div class="card-content">
            <!-- 折叠触发器 -->
            <div class="collapse-trigger" @click="toggleCollapse('time')">
              <span>时间安排</span>
              <el-icon>
                <ArrowUp v-if="!collapsed.time" />
                <ArrowDown v-else />
              </el-icon>
            </div>
            
            <!-- 折叠内容 -->
            <div v-show="!collapsed.time" class="collapse-content">
              <!-- 时间安排 -->
              <div class="time-section">
                <!-- 计划时间 -->
                <div class="time-group">
                  <div class="time-group-header">
                    <el-icon color="#409EFF"><Calendar /></el-icon>
                    <span class="time-group-title">计划时间</span>
                  </div>
                  <div class="time-items">
                    <div class="time-item">
                      <span class="time-label">开始时间</span>
                      <span class="time-value">{{ task.startTime ? formatDate(task.startTime) : '未设置' }}</span>
                    </div>
                    <div class="time-item">
                      <span class="time-label">结束时间</span>
                      <span class="time-value">{{ task.endTime ? formatDate(task.endTime) : '未设置' }}</span>
                    </div>
                  </div>
                </div>

                <!-- 实际时间 -->
                <div v-if="task.actualStartTime || task.actualEndTime || task.completionTime" class="time-group">
                  <div class="time-group-header">
                    <el-icon color="#67C23A"><Timer /></el-icon>
                    <span class="time-group-title">实际时间</span>
                  </div>
                  <div class="time-items">
                    <div v-if="task.actualStartTime" class="time-item">
                      <span class="time-label">实际开始</span>
                      <span class="time-value">{{ formatDate(task.actualStartTime) }}</span>
                    </div>
                    <div v-if="task.actualEndTime" class="time-item">
                      <span class="time-label">实际结束</span>
                      <span class="time-value">{{ formatDate(task.actualEndTime) }}</span>
                    </div>
                    <div v-if="task.completionTime" class="time-item">
                      <span class="time-label">完成时间</span>
                      <span class="time-value">{{ formatDate(task.completionTime) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 其他时间 -->
                <div class="time-group">
                  <div class="time-group-header">
                    <el-icon color="#909399"><InfoFilled /></el-icon>
                    <span class="time-group-title">其他信息</span>
                  </div>
                  <div class="time-items">
                    <div v-if="task.nextRunTime" class="time-item">
                      <span class="time-label">下次执行</span>
                      <span class="time-value">{{ formatDate(task.nextRunTime) }}</span>
                    </div>
                    <div class="time-item">
                      <span class="time-label">创建时间</span>
                      <span class="time-value">{{ formatDate(task.createTime) }}</span>
                    </div>
                    <div v-if="task.updateTime" class="time-item">
                      <span class="time-label">更新时间</span>
                      <span class="time-value">{{ formatDate(task.updateTime) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态管理区域 -->
    <template #footer>
      <div class="status-management-footer">
        <!-- 当前状态显示 -->
        <div class="current-status-display">
          <span class="status-label">当前状态：</span>
          <el-tag
            :type="getTaskStatusType(task?.taskStatus)"
            size="large"
            class="current-status-tag"
          >
            {{ getTaskStatusLabel(task?.taskStatus) }}
          </el-tag>
        </div>

        <!-- 操作按钮区域 -->
        <div class="footer-actions">
          <!-- 状态操作按钮 -->
          <div class="status-actions-group">
            <!-- 分配任务 -->
            <el-button
              v-if="Number(task?.taskStatus) === 0"
              type="primary"
              @click="handleAssignTask"
              :icon="User"
            >
              分配任务
            </el-button>

            <!-- 开始执行 -->
            <el-button
              v-if="Number(task?.taskStatus) === 1"
              type="warning"
              @click="handleStartTask"
              :icon="VideoPlay"
            >
              开始执行
            </el-button>

            <!-- 完成任务 -->
            <el-button
              v-if="[1, 2].includes(Number(task?.taskStatus))"
              type="success"
              @click="handleCompleteTask"
              :icon="Check"
            >
              完成任务
            </el-button>

            <!-- 关闭任务 -->
            <el-button
              v-if="[0, 1, 2].includes(Number(task?.taskStatus))"
              type="danger"
              @click="handleCloseTask"
              :icon="Close"
            >
              关闭任务
            </el-button>

            <!-- 调整时间 -->
            <el-button
              v-if="[0, 1, 2].includes(Number(task?.taskStatus))"
              type="info"
              @click="handleAdjustTime"
              :icon="Clock"
            >
              调整时间
            </el-button>
          </div>

          <!-- 对话框控制按钮 -->
          <div class="dialog-controls">
            <el-button @click="handleClose">
              关闭
            </el-button>
          </div>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import {
  Document,
  Close,
  User,
  UserFilled,
  Clock,
  Calendar,
  Timer,
  InfoFilled,
  Folder,
  ArrowDown,
  ArrowUp,
  Check,
  VideoPlay
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  task?: any
}

type Emits = {
  'update:modelValue': [value: boolean]
  'assign-task': [task: any]
  'start-task': [task: any]
  'complete-task': [task: any]
  'close-task': [task: any]
  'adjust-time': [task: any]
  'task-updated': []
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: undefined
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 折叠状态
const collapsed = ref({
  basic: false,
  executor: false,
  time: false,
  project: false
})

// 切换折叠状态
const toggleCollapse = (section: 'basic' | 'executor' | 'time' | 'project') => {
  collapsed.value[section] = !collapsed.value[section]
}

// 方法

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未设置'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateStr
  }
}

// 获取任务状态类型
const getTaskStatusType = (status: number) => {
  const statusMap = {
    0: 'info',     // 待分配
    1: 'warning',  // 待执行
    2: 'primary',  // 执行中
    3: 'success',  // 已完成
    4: 'danger'    // 已关闭
  }
  return statusMap[status] || 'info'
}

// 获取任务状态标签
const getTaskStatusLabel = (status: number) => {
  const statusMap = {
    0: '待分配',
    1: '待执行',
    2: '执行中',
    3: '已完成',
    4: '已关闭'
  }
  return statusMap[status] || '未知状态'
}

// 获取任务类型标签
const getTaskCategoryLabel = (value: string) => {
  const categoryMap: Record<string, { label: string; type: string }> = {
    'SOP_STEP': { label: '场景步骤', type: 'primary' },
    'RC': { label: '日常', type: 'success' },
    'ZQ': { label: '周期', type: 'warning' },
    'LS': { label: '临时', type: 'info' }
  }
  return categoryMap[value] || { label: '未知类型', type: 'info' }
}

// 状态管理方法
const handleAssignTask = () => {
  console.log('点击分配任务按钮', props.task)
  if (!props.task) {
    console.warn('任务数据为空，无法分配')
    return
  }
  emit('assign-task', props.task)
}

const handleStartTask = () => {
  console.log('点击开始执行按钮', props.task)
  if (!props.task) {
    console.warn('任务数据为空，无法开始执行')
    return
  }
  emit('start-task', props.task)
}

const handleCompleteTask = () => {
  console.log('点击完成任务按钮', props.task)
  if (!props.task) {
    console.warn('任务数据为空，无法完成任务')
    return
  }
  emit('complete-task', props.task)
}

const handleCloseTask = () => {
  console.log('点击关闭任务按钮', props.task)
  if (!props.task) {
    console.warn('任务数据为空，无法关闭任务')
    return
  }
  emit('close-task', props.task)
}

const handleAdjustTime = () => {
  console.log('点击调整时间按钮', props.task)
  if (!props.task) {
    console.warn('任务数据为空，无法调整时间')
    return
  }
  emit('adjust-time', props.task)
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
// 对话框样式
.task-detail-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--el-border-color-lighter);
    
    // 暗色主题适配
    html.dark & {
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }
  }

  :deep(.el-dialog__header) {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, 
      var(--el-color-primary-light-7) 0%, 
      var(--el-color-primary-light-9) 50%, 
      var(--el-color-primary-light-8) 100%);
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background: linear-gradient(180deg, var(--el-bg-color) 0%, var(--el-fill-color-lighter) 100%);
  }
}

/* 折叠触发器 */
.collapse-trigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  cursor: pointer;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  margin-bottom: 16px;
}

.collapse-trigger .el-icon {
  transition: transform 0.3s ease;
}

/* 折叠内容 */
.collapse-content {
  transition: all 0.3s ease;
}

// 对话框头部样式 - 专业级设计
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 28px;
  background: linear-gradient(135deg, 
    var(--el-color-primary-light-6) 0%, 
    var(--el-color-primary-light-8) 100%);
  color: white;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(255,255,255,0.1) 50%, transparent 51%);
    pointer-events: none;
  }
  
  // 暗色主题适配
  html.dark & {
    background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
  }

  .header-main {
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;

    .task-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      
      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 700;
        line-height: 1.4;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .status-tag {
              background: rgba(255, 255, 255, 0.2) !important;
              border-color: transparent !important;
              color: white !important;
              font-weight: 600;
              border-radius: 20px;
              padding: 6px 16px;
              font-size: 13px;
              letter-spacing: 0.5px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              transition: all 0.3s ease;
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                background: rgba(255, 255, 255, 0.25) !important;
              }
              
              // 根据不同状态类型设置背景色
              &.el-tag--info {
                background: rgba(144, 147, 153, 0.2) !important;
                
                &:hover {
                  background: rgba(144, 147, 153, 0.25) !important;
                }
                
                html.dark & {
                  background: rgba(144, 147, 153, 0.15) !important;
                  
                  &:hover {
                    background: rgba(144, 147, 153, 0.2) !important;
                  }
                }
              }
              
              &.el-tag--warning {
                background: rgba(230, 162, 60, 0.2) !important;
                
                &:hover {
                  background: rgba(230, 162, 60, 0.25) !important;
                }
                
                html.dark & {
                  background: rgba(230, 162, 60, 0.15) !important;
                  
                  &:hover {
                    background: rgba(230, 162, 60, 0.2) !important;
                  }
                }
              }
              
              &.el-tag--primary {
                background: rgba(64, 158, 255, 0.2) !important;
                
                &:hover {
                  background: rgba(64, 158, 255, 0.25) !important;
                }
                
                html.dark & {
                  background: rgba(64, 158, 255, 0.15) !important;
                  
                  &:hover {
                    background: rgba(64, 158, 255, 0.2) !important;
                  }
                }
              }
              
              &.el-tag--success {
                background: rgba(103, 194, 58, 0.2) !important;
                
                &:hover {
                  background: rgba(103, 194, 58, 0.25) !important;
                }
                
                html.dark & {
                  background: rgba(103, 194, 58, 0.15) !important;
                  
                  &:hover {
                    background: rgba(103, 194, 58, 0.2) !important;
                  }
                }
              }
              
              &.el-tag--danger {
                background: rgba(245, 108, 108, 0.2) !important;
                
                &:hover {
                  background: rgba(245, 108, 108, 0.25) !important;
                }
                
                html.dark & {
                  background: rgba(245, 108, 108, 0.15) !important;
                  
                  &:hover {
                    background: rgba(245, 108, 108, 0.2) !important;
                  }
                }
              }
              
              // 暗色主题适配
              html.dark & {
                background: rgba(255, 255, 255, 0.15) !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                
                &:hover {
                  background: rgba(255, 255, 255, 0.2) !important;
                  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                }
              }
      }
    }

    .task-category {
      .el-tag {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: transparent !important;
        color: white !important;
        font-weight: 600;
        border-radius: 20px;
        padding: 6px 16px;
        font-size: 13px;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          background: rgba(255, 255, 255, 0.25) !important;
        }
        
        // 暗色主题适配
        html.dark & {
          background: rgba(255, 255, 255, 0.15) !important;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          
          &:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }

  .header-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 6px;
    font-size: 12px;
    
    .task-id, .task-priority {
      background: rgba(255, 255, 255, 0.3);
      padding: 4px 10px;
      border-radius: 12px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  }
}

// 任务内容区域样式
.task-body {
  display: flex;
  gap: 20px;
  
  .main-content {
    flex: 1;
    min-width: 0;
  }
  
  .side-panel {
    width: 360px;
    flex-shrink: 0;
  }
}

// 底部状态管理样式
.status-management-footer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px 0;
  border-top: 1px solid var(--el-border-color-light);
  background: var(--el-bg-color);

  .current-status-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    .status-label {
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .current-status-tag {
      font-size: 14px;
      font-weight: 600;
      padding: 8px 20px;
      border-radius: 20px;
    }
  }

  .footer-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 20px;
  }

  .status-actions-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
    flex: 1;

    .el-button {
      min-width: 100px;
      height: 36px;
      font-size: 14px;
      border-radius: 18px;

      .el-icon {
        margin-right: 6px;
      }

      // 按钮类型样式优化
      &.el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        &:hover {
          background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
        }
      }

      &.el-button--success {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);

        &:hover {
          background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
        }
      }

      &.el-button--warning {
        background: linear-gradient(135deg, #e6a23c 0%, #f0c78a 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);

        &:hover {
          background: linear-gradient(135deg, #f0c78a 0%, #e6a23c 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
        }
      }

      &.el-button--danger {
        background: linear-gradient(135deg, #f56c6c 0%, #f89898 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);

        &:hover {
          background: linear-gradient(135deg, #f89898 0%, #f56c6c 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
        }
      }

      &.el-button--info {
        background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);

        &:hover {
          background: linear-gradient(135deg, #b1b3b8 0%, #909399 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(144, 147, 153, 0.4);
        }
      }

      // 默认按钮样式
      &:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info) {
        background: var(--el-bg-color-page);
        border: 1px solid var(--el-border-color);
        color: var(--el-text-color-primary);

        &:hover {
          background: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary);
          color: var(--el-color-primary);
          transform: translateY(-1px);
        }
      }

      // 过渡动画
      transition: all 0.3s ease;
    }
  }

  .dialog-controls {
    display: flex;
    align-items: center;

    .el-button {
      min-width: 80px;
      height: 36px;
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color);
      color: var(--el-text-color-primary);
      border-radius: 18px;

      &:hover {
        background: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
        transform: translateY(-1px);
      }

      transition: all 0.3s ease;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .footer-actions {
      flex-direction: column;
      gap: 16px;
    }

    .status-actions-group {
      .el-button {
        min-width: 80px;
        height: 32px;
        font-size: 12px;
      }
    }

    .dialog-controls {
      width: 100%;
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .status-actions-group {
      flex-direction: column;
      width: 100%;

      .el-button {
        width: 100%;
        min-width: auto;
      }
    }

    .dialog-controls {
      .el-button {
        width: 100%;
      }
    }
  }
}

// 任务卡片通用样式 - 简约设计
    .info-card {
      background: var(--el-bg-color-overlay);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      margin-bottom: 16px;
      transition: all 0.2s ease;
      overflow: hidden;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--el-color-primary);
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        border-color: var(--el-border-color-base);
      }

      &:last-child {
        margin-bottom: 0;
      }
      
      // 卡片头部样式
      .card-header {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        background: var(--el-fill-color-light);
        
        .card-icon {
          margin-right: 8px;
          
          .el-icon {
            font-size: 16px;
            color: var(--el-color-primary);
          }
        }
        
        .card-title {
          margin: 0;
          font-size: 14px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }
      
      // 卡片内容样式
      .card-content {
        padding: 16px;
      }
      
      // 暗色主题适配
      html.dark & {
        background: var(--el-bg-color-overlay);
        border-color: var(--el-border-color-dark);
        
        .card-header {
          background: var(--el-fill-color-dark);
          border-color: var(--el-border-color-dark);
        }
        
        &::before {
          background: var(--el-color-primary-dark-3);
        }
      }
    }
    
    // 信息项样式 - 简约设计
    .info-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        font-size: 12px;
        font-weight: 600;
        color: var(--el-text-color-secondary);
        margin-bottom: 6px;
        display: block;
      }
      
      .info-value {
        font-size: 13px;
        color: var(--el-text-color-primary);
        line-height: 1.5;
        background: var(--el-fill-color-lighter);
        padding: 8px 12px;
        border-radius: 4px;
      }
    }
    
    // 执行人员样式 - 简约设计
    .assignee-card {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px;
      background: var(--el-fill-color-lighter);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-lighter);
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      }
      
      .assignee-avatar {
        flex-shrink: 0;
      }
      
      .assignee-info {
        flex: 1;
        min-width: 0;
        
        .assignee-name {
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 2px;
          font-size: 13px;
        }
        
        .assignee-id {
          font-size: 11px;
          color: var(--el-text-color-secondary);
        }
      }
      
      // 暗色主题适配
      html.dark & {
        background: var(--el-fill-color-darker);
      }
    }
    
    .no-assignee {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: var(--el-text-color-placeholder);
      padding: 16px;
      font-size: 13px;
      background: var(--el-fill-color-lighter);
      border-radius: 12px;
      border: 2px dashed var(--el-border-color-lighter);
      
      .el-icon {
        font-size: 18px;
      }
    }

// 任务描述样式 - 简约设计
    .task-description {
      line-height: 1.6;
      color: var(--el-text-color-primary);
      font-size: 13px;
      margin: 0 0 12px 0;
      background: var(--el-fill-color-lighter);
      padding: 12px;
      border-radius: 6px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .task-activity {
      .activity-label {
        font-size: 12px;
        font-weight: 600;
        color: var(--el-text-color-secondary);
        margin-bottom: 6px;
        display: block;
      }
      
      .activity-content {
        font-size: 13px;
        color: var(--el-text-color-primary);
        line-height: 1.5;
        background: var(--el-fill-color-lighter);
        padding: 8px 12px;
        border-radius: 4px;
      }
    }
    
    .no-description {
      color: var(--el-text-color-placeholder);
      text-align: center;
      padding: 16px;
      font-size: 13px;
      background: var(--el-fill-color-lighter);
      border-radius: 6px;
      border: 1px dashed var(--el-border-color-lighter);
    }

// 执行人员样式
    .executor-section {
      .role-section {
        margin-bottom: 15px;
        
        .role-label {
          font-size: 12px;
          font-weight: 600;
          color: var(--el-text-color-secondary);
          margin-bottom: 6px;
          display: block;
        }
        
        .role-tag {
          font-size: 12px;
        }
      }
      
      .assignee-section {
        margin-bottom: 15px;
        
        .assignee-label {
          font-size: 12px;
          font-weight: 600;
          color: var(--el-text-color-secondary);
          margin-bottom: 8px;
          display: block;
        }
        
        .assignee-card {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          background: var(--el-fill-color-lighter);
          border-radius: 8px;
          
          .assignee-avatar {
            flex-shrink: 0;
          }
          
          .assignee-info {
            flex: 1;
            min-width: 0;
            
            .assignee-name {
              font-weight: 500;
              color: var(--el-text-color-primary);
              margin-bottom: 2px;
              font-size: 14px;
            }
            
            .assignee-id {
              font-size: 12px;
              color: var(--el-text-color-secondary);
            }
          }
        }
        
        .no-assignee {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          color: var(--el-text-color-placeholder);
          padding: 20px;
          font-size: 13px;
          
          .el-icon {
            font-size: 18px;
          }
        }
      }
      
      .behavior-section {
        .behavior-label {
          font-size: 12px;
          font-weight: 600;
          color: var(--el-text-color-secondary);
          margin-bottom: 6px;
          display: block;
        }
        
        .behavior-content {
          font-size: 13px;
          color: var(--el-text-color-primary);
          line-height: 1.5;
          padding: 8px 12px;
          background: var(--el-fill-color-lighter);
          border-radius: 4px;
        }
      }
      
      // 暗色主题适配
      html.dark & {
        .assignee-card,
        .behavior-content {
          background: var(--el-fill-color-darker);
        }
      }
    }

// 时间信息样式 - 简约设计
.time-section {
  .time-group {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .time-group-header {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--el-text-color-secondary);
      font-size: 12px;
      background: var(--el-fill-color-lighter);
      padding: 6px 10px;
      border-radius: 4px;
      
      .el-icon {
        font-size: 14px;
      }
    }
    
    .time-items {
      .time-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 10px;
        margin-bottom: 4px;
        background: var(--el-fill-color-lighter);
        border-radius: 4px;
        transition: all 0.2s ease;
        
        &:hover {
          background: var(--el-fill-color-light);
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .time-label {
          font-size: 11px;
          color: var(--el-text-color-secondary);
          font-weight: 500;
        }
        
        .time-value {
          font-size: 11px;
          color: var(--el-text-color-primary);
          font-weight: 500;
        }
      }
    }
  }
}

// 项目信息样式
.project-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .project-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 12px;
    
    &:hover {
      background: var(--el-fill-color-light);
    }
    
    .project-label {
      color: var(--el-text-color-regular);
      font-weight: 500;
    }
    
    .project-value {
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
    
    // 暗色主题适配
    html.dark & {
      background: var(--el-fill-color-darker);
      
      &:hover {
        background: var(--el-fill-color-dark);
      }
    }
  }
}

// 关联信息样式
.relation-section {
  .relation-content {
    .relation-group {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .relation-label {
        font-size: 12px;
        font-weight: 600;
        color: var(--el-text-color-secondary);
        margin-bottom: 6px;
        padding-bottom: 3px;
        border-bottom: 1px solid var(--el-border-color-light);
      }
      
      .relation-list {
        display: flex;
        flex-direction: column;
        gap: 6px;
        
        .relation-item {
          padding: 6px 8px;
          background: var(--el-fill-color-lighter);
          border-radius: 4px;
          transition: all 0.2s ease;
          
          &:hover {
            background: var(--el-fill-color-light);
          }
          
          .relation-name {
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 2px;
            font-size: 12px;
          }
          
          .relation-code {
            font-size: 11px;
            color: var(--el-text-color-secondary);
          }
          
          // 暗色主题适配
          html.dark & {
            background: var(--el-fill-color-darker);
            
            &:hover {
              background: var(--el-fill-color-dark);
            }
          }
        }
      }
    }
  }
}

// 地点信息样式 - 简约设计
    .location-section {
      .location-content {
        .location-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .location-item {
            padding: 10px 12px;
            background: var(--el-fill-color-lighter);
            border-left: 3px solid var(--el-color-warning);
            border-radius: 4px;
            transition: all 0.2s ease;
            
            &:hover {
              transform: translateX(2px);
            }
            
            .location-value {
              font-weight: 500;
              color: var(--el-text-color-primary);
              margin-bottom: 2px;
              font-size: 12px;
            }
            
            .location-detail {
              font-size: 11px;
              color: var(--el-text-color-secondary);
            }
            
            // 暗色主题适配
            html.dark & {
              background: var(--el-fill-color-darker);
            }
          }
        }
        
        .no-location {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          color: var(--el-text-color-placeholder);
          padding: 16px;
          font-size: 12px;
          background: var(--el-fill-color-lighter);
          border-radius: 6px;
          border: 1px dashed var(--el-border-color-lighter);
          
          .el-icon {
            font-size: 16px;
          }
        }
      }
    }

// 工作内容样式 - 简约设计
    .work-section {
      .work-content {
        .work-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .work-item {
            padding: 10px 12px;
            background: var(--el-fill-color-lighter);
            border-left: 3px solid var(--el-color-primary);
            border-radius: 4px;
            transition: all 0.2s ease;
            
            &:hover {
              transform: translateX(2px);
            }
            
            .work-value {
              color: var(--el-text-color-primary);
              line-height: 1.5;
              font-size: 12px;
              font-weight: 500;
            }
            
            // 暗色主题适配
            html.dark & {
              background: var(--el-fill-color-darker);
            }
          }
        }
        
        .no-work {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          color: var(--el-text-color-placeholder);
          padding: 16px;
          font-size: 12px;
          background: var(--el-fill-color-lighter);
          border-radius: 6px;
          border: 1px dashed var(--el-border-color-lighter);
          
          .el-icon {
            font-size: 16px;
          }
        }
      }
    }

// 响应式设计 - 简约适配
@media (max-width: 768px) {
  .task-detail-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 10px auto !important;
      border-radius: 8px;
    }
    
    :deep(.el-dialog__body) {
      padding: 12px;
    }
  }
  
  .dialog-header {
    padding: 12px 16px;
    
    .header-main {
      .task-title {
        h3 {
          font-size: 16px;
        }
        
        .status-tag {
          font-size: 10px;
          padding: 2px 8px;
        }
      }
    }
    
    .header-meta {
      font-size: 10px;
      
      .task-id, .task-priority {
        padding: 2px 6px;
        font-size: 10px;
      }
    }
  }
  
  .task-body {
    flex-direction: column;
    gap: 12px;
    
    .side-panel {
      width: 100%;
    }
  }
  
  // 信息项响应式样式
  .info-item {
    margin-bottom: 8px;
    
    .info-label {
      font-size: 11px;
    }
    
    .info-value {
      font-size: 12px;
      padding: 6px 8px;
    }
  }
  
  // 执行人员响应式样式
  .assignee-card {
    gap: 8px;
    padding: 8px;
    
    .assignee-avatar {
      width: 32px !important;
      height: 32px !important;
    }
    
    .assignee-info {
      .assignee-name {
        font-size: 12px;
      }
      
      .assignee-id {
        font-size: 10px;
      }
    }
  }
  
  .no-assignee {
    padding: 12px;
    font-size: 11px;
    
    .el-icon {
      font-size: 14px;
    }
  }
  
  // 时间信息响应式样式
  .time-section {
    .time-group {
      margin-bottom: 12px;
      
      .time-group-header {
        padding: 4px 8px;
        font-size: 11px;
      }
      
      .time-items {
        .time-item {
          padding: 4px 8px;
          flex-direction: column;
          align-items: flex-start;
          gap: 2px;
          
          .time-label, .time-value {
            font-size: 10px;
          }
        }
      }
    }
  }
  
  // 地点和工作内容响应式样式
  .location-section, .work-section {
    .location-item, .work-item {
      padding: 8px 10px;
    }
    
    .no-location, .no-work {
      padding: 12px;
      font-size: 11px;
    }
  }
}
</style>
