<template>
  <el-dialog
    v-model="visible"
    :show-close="false"
    width="1000px"
    top="3vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    class="task-detail-dialog-compact"
    @close="handleClose"
  >
    <div v-if="task" class="task-detail-content">
      <!-- 紧凑头部 -->
      <div class="dialog-header">
        <div class="header-left">
          <el-icon size="20" color="white"><Document /></el-icon>
          <div class="header-info">
            <h2 class="task-title">{{ task.name }}</h2>
            <div class="task-meta">
              <span class="task-id">#{{ task.id }}</span>
              <span class="task-category">{{ getTaskCategoryLabel(task.taskCategory) }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <el-tag :type="getTaskStatusType(task.taskStatus)" effect="plain" class="status-tag">
            {{ getTaskStatusLabel(task.taskStatus) }}
          </el-tag>
          <el-button type="primary" text class="close-btn" @click="handleClose">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 紧凑内容区域 -->
      <div class="task-body">
        <!-- 左侧 -->
        <div class="main-content">
          <!-- 任务描述 -->
          <div class="compact-card">
            <div class="card-header">
              <el-icon color="#67C23A"><Document /></el-icon>
              <span class="card-title">任务描述</span>
            </div>
            <div class="card-content">
              <div class="description-text">{{ task.description || '暂无描述' }}</div>
              <div v-if="task.taskActivity" class="activity-section">
                <div class="section-label">具体活动</div>
                <div class="section-content">{{ task.taskActivity }}</div>
              </div>
            </div>
          </div>

          <!-- 执行人员 -->
          <div class="compact-card">
            <div class="card-header">
              <el-icon color="#E6A23C"><User /></el-icon>
              <span class="card-title">执行人员</span>
            </div>
            <div class="card-content">
              <div class="executor-info">
                <div v-if="task.employeeRole" class="info-row">
                  <span class="label">执行角色</span>
                  <el-tag type="warning" size="small">{{ task.employeeRole }}</el-tag>
                </div>
                <div class="info-row">
                  <span class="label">执行人员</span>
                  <div v-if="task.assigneeName" class="assignee-info">
                    <el-avatar :size="32">{{ task.assigneeName?.charAt(0) }}</el-avatar>
                    <div class="assignee-details">
                      <div class="name">{{ task.assigneeName }}</div>
                      <div class="id">ID: {{ task.assigneeId }}</div>
                    </div>
                  </div>
                  <span v-else class="no-data">待分配执行人</span>
                </div>
                <div v-if="task.employeeBehavior" class="info-row">
                  <span class="label">行为要求</span>
                  <div class="behavior-text">{{ task.employeeBehavior }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 执行地点 -->
          <div class="compact-card">
            <div class="card-header">
              <el-icon color="#E6A23C"><Location /></el-icon>
              <span class="card-title">执行地点</span>
            </div>
            <div class="card-content">
              <div class="location-text">
                {{ task.entityTouchpoint || '暂无地点信息' }}
              </div>
            </div>
          </div>

          <!-- 工作内容 -->
          <div class="compact-card">
            <div class="card-header">
              <el-icon color="#409EFF"><Tools /></el-icon>
              <span class="card-title">工作内容</span>
            </div>
            <div class="card-content">
              <div v-if="task.workHighlight" class="work-item">
                <div class="section-label">
                  <el-icon><Star /></el-icon>
                  <span>工作亮点</span>
                </div>
                <div class="section-content">{{ task.workHighlight }}</div>
              </div>
              <div v-if="task.employeeBehavior" class="work-item">
                <div class="section-label">
                  <el-icon><Document /></el-icon>
                  <span>具体要求</span>
                </div>
                <div class="section-content">{{ task.employeeBehavior }}</div>
              </div>
              <div v-if="!task.workHighlight && !task.employeeBehavior" class="no-data">
                暂无工作内容信息
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧 -->
        <div class="side-panel">
          <!-- 时间安排 -->
          <div class="compact-card">
            <div class="card-header">
              <el-icon color="#F56C6C"><Clock /></el-icon>
              <span class="card-title">时间安排</span>
            </div>
            <div class="card-content">
              <!-- 计划时间 -->
              <div class="time-group">
                <div class="group-title">
                  <el-icon color="#409EFF"><Calendar /></el-icon>
                  <span>计划时间</span>
                </div>
                <div class="time-list">
                  <div class="time-item">
                    <span class="time-label">开始时间</span>
                    <span class="time-value">{{ task.startTime ? formatDate(task.startTime) : '未设置' }}</span>
                  </div>
                  <div class="time-item">
                    <span class="time-label">结束时间</span>
                    <span class="time-value">{{ task.endTime ? formatDate(task.endTime) : '未设置' }}</span>
                  </div>
                </div>
              </div>

              <!-- 实际时间 -->
              <div v-if="task.actualStartTime || task.actualEndTime || task.completionTime" class="time-group">
                <div class="group-title">
                  <el-icon color="#67C23A"><Timer /></el-icon>
                  <span>实际时间</span>
                </div>
                <div class="time-list">
                  <div v-if="task.actualStartTime" class="time-item">
                    <span class="time-label">实际开始</span>
                    <span class="time-value">{{ formatDate(task.actualStartTime) }}</span>
                  </div>
                  <div v-if="task.actualEndTime" class="time-item">
                    <span class="time-label">实际结束</span>
                    <span class="time-value">{{ formatDate(task.actualEndTime) }}</span>
                  </div>
                  <div v-if="task.completionTime" class="time-item">
                    <span class="time-label">完成时间</span>
                    <span class="time-value">{{ formatDate(task.completionTime) }}</span>
                  </div>
                </div>
              </div>

              <!-- 其他信息 -->
              <div class="time-group">
                <div class="group-title">
                  <el-icon color="#909399"><InfoFilled /></el-icon>
                  <span>其他信息</span>
                </div>
                <div class="time-list">
                  <div v-if="task.nextRunTime" class="time-item">
                    <span class="time-label">下次执行</span>
                    <span class="time-value">{{ formatDate(task.nextRunTime) }}</span>
                  </div>
                  <div class="time-item">
                    <span class="time-label">创建时间</span>
                    <span class="time-value">{{ formatDate(task.createTime) }}</span>
                  </div>
                  <div v-if="task.updateTime" class="time-item">
                    <span class="time-label">更新时间</span>
                    <span class="time-value">{{ formatDate(task.updateTime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 项目信息 -->
          <div class="compact-card">
            <div class="card-header">
              <el-icon color="#909399"><Folder /></el-icon>
              <span class="card-title">项目信息</span>
            </div>
            <div class="card-content">
              <div class="project-list">
                <div v-if="task.projectName" class="project-item">
                  <span class="project-label">项目名称</span>
                  <span class="project-value">{{ task.projectName }}</span>
                </div>
                <div v-if="task.scenarioName" class="project-item">
                  <span class="project-label">场景名称</span>
                  <span class="project-value">{{ task.scenarioName }}</span>
                </div>
                <div v-if="task.stepName" class="project-item">
                  <span class="project-label">步骤名称</span>
                  <span class="project-value">{{ task.stepName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Document,
  Close,
  User,
  UserFilled,
  Clock,
  Calendar,
  Timer,
  InfoFilled,
  Folder,
  Star,
  Location,
  Tools
} from '@element-plus/icons-vue'

interface TaskDetail {
  id: number
  name: string
  description?: string
  taskActivity?: string
  employeeRole?: string
  assigneeName?: string
  assigneeId?: number
  employeeBehavior?: string
  entityTouchpoint?: string
  workHighlight?: string
  startTime?: string
  endTime?: string
  actualStartTime?: string
  actualEndTime?: string
  completionTime?: string
  nextRunTime?: string
  createTime: string
  updateTime?: string
  projectName?: string
  scenarioName?: string
  stepName?: string
  taskStatus: number
  taskCategory: number
}

interface Props {
  visible: boolean
  task: TaskDetail | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleClose = () => {
  visible.value = false
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取任务状态类型
const getTaskStatusType = (status: number) => {
  const statusMap = {
    0: 'info',     // 待分配
    1: 'warning',  // 待执行
    2: 'primary',  // 执行中
    3: 'success',  // 已完成
    4: 'danger'    // 已关闭
  }
  return statusMap[status] || 'info'
}

// 获取任务状态标签
const getTaskStatusLabel = (status: number) => {
  const statusMap = {
    0: '待分配',
    1: '待执行',
    2: '执行中',
    3: '已完成',
    4: '已关闭'
  }
  return statusMap[status] || '未知状态'
}

// 获取任务类型标签
const getTaskCategoryLabel = (category: number) => {
  const categoryMap = {
    1: '普通任务',
    2: '重要任务',
    3: '紧急任务'
  }
  return categoryMap[category] || '普通任务'
}
</script>

<style lang="scss" scoped>
.task-detail-dialog-compact {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    max-height: 90vh;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
  }

  :deep(.el-dialog__header) {
    display: none;
  }

  :deep(.el-dialog__body) {
    padding: 0;
    max-height: calc(90vh - 60px);
    overflow-y: auto;
  }

  .task-detail-content {
    background: var(--el-bg-color);
    display: flex;
    flex-direction: column;
  }

  // 紧凑头部样式
  .dialog-header {
    background: var(--el-color-primary);
    color: var(--el-color-white);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-info {
        .task-title {
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 4px 0;
          color: white;
        }

        .task-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          opacity: 0.9;

          .task-id {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-tag {
        font-size: 12px;
        padding: 4px 8px;
      }

      .close-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  // 紧凑内容区域
  .task-body {
    display: flex;
    gap: 12px;
    padding: 12px;
    overflow-y: auto;
    background: var(--el-bg-color);

    .main-content {
      flex: 2;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .side-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  // 紧凑卡片样式
  .compact-card {
    background: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      background: var(--el-fill-color-light);
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 6px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      .card-title {
        font-size: 13px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .card-content {
      padding: 12px;
    }
  }

  // 描述文本
  .description-text {
    font-size: 13px;
    line-height: 1.5;
    color: var(--el-text-color-regular);
    margin-bottom: 8px;
    background: var(--el-fill-color-lighter);
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid var(--el-color-primary);
  }

  .activity-section {
    .section-label {
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }

    .section-content {
      background: var(--el-color-success-light-9);
      color: var(--el-color-success-dark-2);
      padding: 6px 8px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.4;
    }
  }

  // 执行人员信息
  .executor-info {
    .info-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 12px;
        color: var(--el-text-color-regular);
        font-weight: 500;
        min-width: 60px;
      }

      .assignee-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .assignee-details {
          .name {
            font-size: 12px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }

          .id {
            font-size: 11px;
            color: var(--el-text-color-placeholder);
          }
        }
      }

      .behavior-text {
        font-size: 12px;
        color: var(--el-text-color-regular);
        background: var(--el-fill-color-lighter);
        padding: 4px 6px;
        border-radius: 4px;
        max-width: 200px;
        word-break: break-word;
      }

      .no-data {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
      }
    }
  }

  // 地点文本
  .location-text {
    font-size: 13px;
    color: var(--el-text-color-regular);
    background: var(--el-fill-color-lighter);
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid var(--el-color-warning);
    text-align: center;
  }

  // 工作内容
  .work-item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-label {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }

    .section-content {
      background: var(--el-fill-color-lighter);
      padding: 6px 8px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.4;
      color: var(--el-text-color-regular);
      border-left: 3px solid var(--el-color-primary);
    }
  }

  .no-data {
    text-align: center;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
    padding: 12px;
  }

  // 时间组
  .time-group {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .group-title {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 6px;
      padding-bottom: 4px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    .time-list {
      .time-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 6px;
        background: var(--el-fill-color-lighter);
        border-radius: 4px;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .time-label {
          font-size: 11px;
          color: var(--el-text-color-regular);
        }

        .time-value {
          font-size: 11px;
          color: var(--el-text-color-primary);
          font-weight: 600;
        }
      }
    }
  }

  // 项目列表
  .project-list {
    .project-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 6px;
      background: var(--el-fill-color-lighter);
      border-radius: 4px;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .project-label {
        font-size: 11px;
        color: var(--el-text-color-regular);
      }

      .project-value {
        font-size: 11px;
        color: var(--el-text-color-primary);
        font-weight: 600;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .task-body {
      flex-direction: column;
      padding: 8px;

      .main-content,
      .side-panel {
        flex: none;
      }
    }

    .dialog-header {
      padding: 8px 12px;

      .header-info .task-title {
        font-size: 14px;
      }
    }
  }
}
</style>
