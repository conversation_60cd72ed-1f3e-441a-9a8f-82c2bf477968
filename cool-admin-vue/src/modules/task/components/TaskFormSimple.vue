<template>
  <div class="task-form-simple">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      @submit.prevent="handleSubmit"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        
        <el-form-item label="任务名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入任务名称"
            maxlength="100"
            show-word-limit
            clearable
          />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请描述任务内容和要求"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </div>

      <!-- 快速设置 -->
      <div class="form-section">
        <div class="section-title">快速设置</div>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="formData.priority" placeholder="选择优先级">
                <el-option label="低" :value="1">
                  <div class="priority-option">
                    <span class="priority-dot low"></span>
                    <span>低</span>
                  </div>
                </el-option>
                <el-option label="普通" :value="2">
                  <div class="priority-option">
                    <span class="priority-dot normal"></span>
                    <span>普通</span>
                  </div>
                </el-option>
                <el-option label="中等" :value="3">
                  <div class="priority-option">
                    <span class="priority-dot medium"></span>
                    <span>中等</span>
                  </div>
                </el-option>
                <el-option label="高" :value="4">
                  <div class="priority-option">
                    <span class="priority-dot high"></span>
                    <span>高</span>
                  </div>
                </el-option>
                <el-option label="紧急" :value="5">
                  <div class="priority-option">
                    <span class="priority-dot urgent"></span>
                    <span>紧急</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskCategory">
              <el-select v-model="formData.taskCategory" placeholder="选择任务类型">
                <el-option label="临时任务" value="LS" />
                <el-option label="日常任务" value="RC" />
                <el-option label="周期任务" value="ZQ" />
                <el-option label="场景步骤" value="SOP_STEP" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="执行人" prop="assigneeId">
          <AssigneeQuickSelector
            v-model="formData.assigneeId"
            :context-id="getAssigneeContextId()"
            :filter-mode="getAssigneeFilterMode()"
            placeholder="选择执行人（可选）"
          />
        </el-form-item>
      </div>

      <!-- 时间设置 -->
      <div class="form-section">
        <div class="section-title">时间设置</div>
        
        <el-form-item label="执行时间" prop="timeRange">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <!-- 快速时间选择 -->
        <div class="quick-time-buttons">
          <el-button-group>
            <el-button size="small" @click="setTimeRange('today')">今天</el-button>
            <el-button size="small" @click="setTimeRange('tomorrow')">明天</el-button>
            <el-button size="small" @click="setTimeRange('thisWeek')">本周</el-button>
            <el-button size="small" @click="setTimeRange('nextWeek')">下周</el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import AssigneeQuickSelector from './AssigneeQuickSelector.vue'
import { useGlobalTaskCreator, type QuickTaskForm, type TaskCreationContext } from '../composables/useGlobalTaskCreator'

// 组件属性
interface Props {
  mode?: 'quick' | 'full'
  context?: TaskCreationContext
  defaultValues?: Partial<QuickTaskForm>
}

// 组件事件
interface Emits {
  (e: 'submit', data: QuickTaskForm): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'quick'
})

const emit = defineEmits<Emits>()

const { getSmartDefaults, submitting, createTask } = useGlobalTaskCreator()

// 响应式数据
const formRef = ref<FormInstance>()
const formData = ref<QuickTaskForm>({
  name: '',
  description: '',
  taskCategory: 'LS',
  priority: 3,
  taskStatus: 0
})

const timeRange = ref<[string, string] | null>(null)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 100, message: '任务名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '任务描述不能超过 500 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  taskCategory: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ]
}

// 计算属性
const getAssigneeContextId = () => {
  return props.context?.departmentId || props.context?.projectId
}

const getAssigneeFilterMode = () => {
  if (props.context?.projectId) {
    return 'project'
  } else if (props.context?.departmentId) {
    return 'department'
  } else {
    return 'all'
  }
}

// 监听器
watch(timeRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    formData.value.startTime = newValue[0]
    formData.value.endTime = newValue[1]
  } else {
    formData.value.startTime = undefined
    formData.value.endTime = undefined
  }
})

// 方法
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    console.log('表单验证通过，提交数据:', formData.value)
    
    // 调用创建任务方法
    await createTask(formData.value)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 设置时间范围
const setTimeRange = (type: string) => {
  const now = new Date()
  let startTime: Date
  let endTime: Date

  switch (type) {
    case 'today':
      startTime = new Date(now)
      endTime = new Date(now)
      endTime.setHours(23, 59, 59, 999)
      break
    case 'tomorrow':
      startTime = new Date(now)
      startTime.setDate(now.getDate() + 1)
      startTime.setHours(9, 0, 0, 0)
      endTime = new Date(startTime)
      endTime.setHours(18, 0, 0, 0)
      break
    case 'thisWeek':
      startTime = new Date(now)
      endTime = new Date(now)
      endTime.setDate(now.getDate() + (7 - now.getDay()))
      endTime.setHours(23, 59, 59, 999)
      break
    case 'nextWeek':
      startTime = new Date(now)
      startTime.setDate(now.getDate() + (7 - now.getDay()) + 1)
      startTime.setHours(9, 0, 0, 0)
      endTime = new Date(startTime)
      endTime.setDate(startTime.getDate() + 6)
      endTime.setHours(18, 0, 0, 0)
      break
    default:
      return
  }

  const formatDateTime = (date: Date) => date.toISOString().slice(0, 19).replace('T', ' ')

  timeRange.value = [
    formatDateTime(startTime),
    formatDateTime(endTime)
  ]
}

// 初始化表单数据
const initFormData = () => {
  const smartDefaults = getSmartDefaults(props.context)
  const mergedData = {
    ...smartDefaults,
    ...props.defaultValues
  }

  // 设置表单数据
  Object.assign(formData.value, mergedData)

  // 设置时间范围
  if (mergedData.startTime && mergedData.endTime) {
    timeRange.value = [
      mergedData.startTime.slice(0, 19).replace('T', ' '),
      mergedData.endTime.slice(0, 19).replace('T', ' ')
    ]
  }
}

// 组件挂载时初始化
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.task-form-simple {
  padding: 0;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.priority-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.priority-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.priority-dot.low { background-color: #909399; }
.priority-dot.normal { background-color: #409eff; }
.priority-dot.medium { background-color: #e6a23c; }
.priority-dot.high { background-color: #f56c6c; }
.priority-dot.urgent { background-color: #f56c6c; animation: pulse 1s infinite; }

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.quick-time-buttons {
  margin-top: 8px;
  display: flex;
  justify-content: flex-start;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.form-actions .el-button {
  min-width: 80px;
}
</style>
