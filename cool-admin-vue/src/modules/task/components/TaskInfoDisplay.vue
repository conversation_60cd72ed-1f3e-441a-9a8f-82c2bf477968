<template>
  <div class="task-info-display" v-if="task">
    <el-descriptions :column="1" border size="small">
      <el-descriptions-item label="任务名称">{{ task.name }}</el-descriptions-item>
      <el-descriptions-item label="任务时间">
        <span v-if="task.startTime && task.endTime">{{ task.startTime }} 至 {{ task.endTime }}</span>
        <span v-else>未设置</span>
      </el-descriptions-item>
      <el-descriptions-item label="任务地点">{{ task.entityTouchpoint || '无' }}</el-descriptions-item>
      <el-descriptions-item label="执行人">{{ getAssigneeNames(task.executionEntitys) || '未分配' }}</el-descriptions-item>
      <el-descriptions-item label="所属项目">{{ task.projectName || '无' }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script lang="ts" setup>
import type { TaskInfoEntity } from '../types';

defineOptions({
  name: 'TaskInfoDisplay',
});

interface Props {
  task?: TaskInfoEntity;
}

defineProps<Props>();

const getAssigneeNames = (executions: any[] | undefined) => {
  if (!executions || executions.length === 0) {
    return '';
  }
  return executions.map((e) => e.assigneeName).join(', ');
};
</script>

<style scoped>
.task-info-display {
  margin-bottom: 20px;
}
</style>
