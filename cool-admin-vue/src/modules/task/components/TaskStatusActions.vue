<template>
  <div class="task-status-actions">
    <!-- 快捷操作按钮 -->
    <div class="quick-actions" v-if="showQuickActions">
      <el-button-group>
        <el-button
          v-for="action in quickActions"
          :key="action.event"
          :type="action.type"
          :icon="action.icon"
          size="small"
          @click="handleQuickAction(action)"
          :loading="loading"
          :disabled="!action.enabled"
        >
          {{ action.label }}
        </el-button>
      </el-button-group>
    </div>

    <!-- 下拉菜单操作 -->
    <div class="dropdown-actions" v-if="showDropdown">
      <el-dropdown 
        trigger="click" 
        @command="handleDropdownAction"
        :disabled="loading"
      >
        <el-button type="primary" size="small">
          操作
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="action in availableActions"
              :key="action.event"
              :command="action"
              :disabled="!action.enabled"
              :divided="action.divided"
            >
              <el-icon :color="action.color">
                <component :is="action.icon" />
              </el-icon>
              <span style="margin-left: 8px">{{ action.label }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 右键菜单 -->
    <div 
      class="context-menu" 
      v-if="showContextMenu"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
      @click.stop
    >
      <div class="context-menu-content">
        <div
          v-for="action in availableActions"
          :key="action.event"
          class="context-menu-item"
          :class="{ disabled: !action.enabled }"
          @click="handleContextAction(action)"
        >
          <el-icon :color="action.color">
            <component :is="action.icon" />
          </el-icon>
          <span>{{ action.label }}</span>
        </div>
      </div>
    </div>

    <!-- 状态变更对话框 -->
    <TaskStatusChangeDialog
      v-model="showStatusDialog"
      :task="task"
      :target-status="selectedTargetStatus"
      @confirm="handleStatusChange"
    />

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="showBatchDialog"
      title="批量状态变更"
      width="500px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
    >
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="目标状态">
          <el-select v-model="batchForm.targetStatus" placeholder="请选择目标状态" style="width: 100%">
            <el-option
              v-for="status in batchAvailableStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因">
          <el-input
            v-model="batchForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入批量变更原因..."
          />
        </el-form-item>
        <el-form-item label="选中任务">
          <div class="selected-tasks">
            <el-tag
              v-for="task in selectedTasks"
              :key="task.id"
              closable
              @close="removeSelectedTask(task.id)"
            >
              {{ task.name }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleBatchStatusChange"
          :loading="submitting"
        >
          确认变更
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowDown, User, VideoPlay, Check, Close, RefreshRight, Unlock } from '@element-plus/icons-vue'
import TaskStatusChangeDialog from './TaskStatusChangeDialog.vue'
import { useTaskStatusTransition } from '../composables/useTaskStatusTransition'

interface Props {
  task?: any
  selectedTasks?: any[]
  mode?: 'quick' | 'dropdown' | 'context' | 'batch'
  showQuickActions?: boolean
  showDropdown?: boolean
  maxQuickActions?: number
}

interface Emits {
  (e: 'action', action: string, data: any): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'dropdown',
  showQuickActions: true,
  showDropdown: true,
  maxQuickActions: 3,
  selectedTasks: () => []
})

const emit = defineEmits<Emits>()

// 使用状态流转组合式函数
const {
  loading,
  submitting,
  getAvailableTransitions,
  getAvailableActions,
  changeTaskStatus,
  batchChangeStatus,
  getActionButtonConfig
} = useTaskStatusTransition()

// 响应式数据
const showStatusDialog = ref(false)
const showBatchDialog = ref(false)
const showContextMenu = ref(false)
const selectedTargetStatus = ref<number | null>(null)
const availableActions = ref([])
const contextMenuPosition = ref({ x: 0, y: 0 })

const batchForm = ref({
  targetStatus: null,
  reason: ''
})

// 计算属性
const quickActions = computed(() => {
  return availableActions.value.slice(0, props.maxQuickActions).map(action => ({
    ...action,
    type: getButtonType(action.event)
  }))
})

const batchAvailableStatuses = computed(() => {
  if (!props.selectedTasks || props.selectedTasks.length === 0) return []
  
  // 找出所有选中任务的共同可转换状态
  const commonStatuses = props.selectedTasks.reduce((common, task, index) => {
    const taskTransitions = getAvailableTransitions(task.taskStatus)
    if (index === 0) {
      return taskTransitions
    } else {
      return common.filter(status => 
        taskTransitions.some(t => t.value === status.value)
      )
    }
  }, [])
  
  return commonStatuses
})

// 方法
const getButtonType = (event: string) => {
  const typeMap = {
    'ASSIGN': 'primary',
    'START': 'success',
    'COMPLETE': 'success',
    'CLOSE': 'danger',
    'REACTIVATE': 'warning',
    'REOPEN': 'primary'
  }
  return typeMap[event] || 'default'
}

// 使用本地计算代替API调用，避免频繁请求
const loadAvailableActions = () => {
  if (!props.task) {
    availableActions.value = []
    return
  }

  // 根据当前状态计算可用操作
  const currentStatus = props.task.taskStatus
  const transitions = getAvailableTransitions(currentStatus)

  const actions = []

  // 根据状态转换生成操作
  transitions.forEach(transition => {
    const targetStatus = transition.value
    let event = ''

    // 根据状态转换确定事件类型
    if (currentStatus === 0 && targetStatus === 1) event = 'ASSIGN'
    else if (currentStatus === 1 && targetStatus === 2) event = 'START'
    else if (currentStatus === 2 && targetStatus === 3) event = 'COMPLETE'
    else if (targetStatus === 4) event = 'CLOSE'
    else if (currentStatus === 3 && targetStatus === 2) event = 'REACTIVATE'
    else if (currentStatus === 4 && targetStatus === 1) event = 'REOPEN'

    if (event) {
      const config = getActionButtonConfig(event)
      actions.push({
        event,
        label: config.label,
        icon: config.icon,
        color: config.color,
        targetStatus,
        enabled: true,
        divided: event === 'CLOSE'
      })
    }
  })

  availableActions.value = actions
}

const getTargetStatusByEvent = (event: string) => {
  const eventStatusMap = {
    'ASSIGN': 1,
    'START': 2,
    'COMPLETE': 3,
    'CLOSE': 4,
    'REACTIVATE': 2,
    'REOPEN': 1
  }
  return eventStatusMap[event]
}

const handleQuickAction = (action: any) => {
  if (!action.enabled) return
  
  selectedTargetStatus.value = action.targetStatus
  showStatusDialog.value = true
}

const handleDropdownAction = (action: any) => {
  if (!action.enabled) return
  
  selectedTargetStatus.value = action.targetStatus
  showStatusDialog.value = true
}

const handleContextAction = (action: any) => {
  if (!action.enabled) return
  
  showContextMenu.value = false
  selectedTargetStatus.value = action.targetStatus
  showStatusDialog.value = true
}

const handleStatusChange = async (data: any) => {
  try {
    const result = await changeTaskStatus(data)
    if (result) {
      showStatusDialog.value = false
      emit('action', 'statusChange', result)
      emit('refresh')
    }
  } catch (error) {
    console.error('状态变更失败:', error)
  }
}

const handleBatchStatusChange = async () => {
  if (!batchForm.value.targetStatus || !batchForm.value.reason.trim()) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  if (!props.selectedTasks || props.selectedTasks.length === 0) {
    ElMessage.warning('请选择要操作的任务')
    return
  }
  
  try {
    const taskIds = props.selectedTasks.map(task => task.id)
    const result = await batchChangeStatus(taskIds, batchForm.value.targetStatus, batchForm.value.reason)
    
    if (result) {
      showBatchDialog.value = false
      batchForm.value = { targetStatus: null, reason: '' }
      emit('action', 'batchStatusChange', result)
      emit('refresh')
    }
  } catch (error) {
    console.error('批量状态变更失败:', error)
  }
}

const removeSelectedTask = (taskId: number) => {
  const index = props.selectedTasks.findIndex(task => task.id === taskId)
  if (index > -1) {
    props.selectedTasks.splice(index, 1)
  }
}

const showContextMenuAt = (x: number, y: number) => {
  contextMenuPosition.value = { x, y }
  showContextMenu.value = true
}

const hideContextMenu = () => {
  showContextMenu.value = false
}

// 监听task变化
watch(() => props.task, () => {
  loadAvailableActions()
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu)
})

// 暴露方法给父组件
defineExpose({
  showBatchDialog: () => { showBatchDialog.value = true },
  showContextMenuAt,
  refresh: loadAvailableActions
})
</script>

<style lang="scss" scoped>
.task-status-actions {
  position: relative;
  
  .quick-actions {
    display: inline-block;
    margin-right: 10px;
  }
  
  .dropdown-actions {
    display: inline-block;
  }
  
  .context-menu {
    position: fixed;
    z-index: 9999;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 4px 0;
    min-width: 120px;
    
    .context-menu-content {
      .context-menu-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        cursor: pointer;
        font-size: 14px;
        color: #606266;
        
        &:hover:not(.disabled) {
          background-color: #f5f7fa;
          color: #409eff;
        }
        
        &.disabled {
          color: #c0c4cc;
          cursor: not-allowed;
        }
        
        span {
          margin-left: 8px;
        }
      }
    }
  }
  
  .selected-tasks {
    max-height: 100px;
    overflow-y: auto;
    
    .el-tag {
      margin: 2px 4px 2px 0;
    }
  }
}
</style>
