<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :show-close="true"
    :destroy-on-close="true"
    :append-to-body="true"
    class="task-status-change-dialog"
    @close="handleClose"
    @opened="handleOpened"
  >
    <!-- 任务信息展示 -->
    <div class="task-info-section" v-if="task">
      <el-card class="task-info-card" shadow="never">
        <div class="task-header">
          <h4 class="task-title">{{ task.name }}</h4>
          <task-status-tag :status="task.taskStatus" />
        </div>
        <div class="task-meta">
          <span class="meta-item">
            <el-icon><User /></el-icon>
            执行人：{{ task.assigneeName || '未分配' }}
          </span>
          <span class="meta-item">
            <el-icon><Calendar /></el-icon>
            创建时间：{{ formatDate(task.createTime) }}
          </span>
        </div>
      </el-card>
    </div>

    <!-- 状态变更表单 -->
    <div class="status-change-section">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="目标状态" prop="targetStatus">
          <el-select v-model="form.targetStatus" placeholder="请选择目标状态" style="width: 100%">
            <el-option
              v-for="status in availableStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            >
              <span style="float: left">{{ status.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ status.description }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="变更原因" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入状态变更原因..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 分配执行人（仅在分配任务时显示） -->
        <el-form-item 
          v-if="form.targetStatus === 1 && task?.taskStatus === 0" 
          label="执行人" 
          prop="assigneeIds"
        >
          <el-select
            v-model="form.assigneeIds"
            multiple
            placeholder="请选择执行人"
            style="width: 100%"
            filterable
            remote
            :remote-method="searchUsers"
            :loading="userLoading"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            >
              <span style="float: left">{{ user.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ user.departmentName }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 附件上传（完成任务时显示） -->
        <el-form-item 
          v-if="form.targetStatus === 3 && (task?.attachmentRequired || task?.photoRequired)" 
          label="附件"
        >
          <div class="upload-section">
            <el-upload
              v-if="task?.attachmentRequired"
              class="upload-demo"
              drag
              multiple
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="form.attachments"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持jpg/png/pdf等格式，单个文件不超过10MB
                </div>
              </template>
            </el-upload>

            <el-upload
              v-if="task?.photoRequired"
              class="upload-demo"
              drag
              multiple
              accept="image/*"
              :auto-upload="false"
              :on-change="handlePhotoChange"
              :file-list="form.photos"
            >
              <el-icon class="el-icon--upload"><camera /></el-icon>
              <div class="el-upload__text">
                上传照片
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持jpg/png格式，单个文件不超过5MB
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作提示 -->
    <div class="operation-tips" v-if="operationTips">
      <el-alert
        :title="operationTips"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="submitting"
          :disabled="!canSubmit"
        >
          确认{{ actionText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Calendar, UploadFilled, Camera } from '@element-plus/icons-vue'
import TaskStatusTag from './task-status-tag.vue'
import { service } from '/@/cool'

interface Props {
  modelValue: boolean
  task?: any
  targetStatus?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: undefined,
  targetStatus: undefined
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const formRef = ref()
const submitting = ref(false)
const userLoading = ref(false)
const userOptions = ref([])

const form = ref({
  targetStatus: null,
  reason: '',
  assigneeIds: [],
  attachments: [],
  photos: []
})

// 状态选项
const statusOptions = [
  { value: 0, label: '待分配', description: '任务已创建，等待分配执行人' },
  { value: 1, label: '待执行', description: '任务已分配，等待开始执行' },
  { value: 2, label: '执行中', description: '任务正在执行中' },
  { value: 3, label: '已完成', description: '任务执行完成' },
  { value: 4, label: '已关闭', description: '任务被关闭或取消' }
]

// 计算属性
const dialogTitle = computed(() => {
  if (!props.task) return '状态变更'
  const currentStatus = statusOptions.find(s => s.value === props.task.taskStatus)?.label || '未知'
  if (!form.value.targetStatus) return `状态变更：${currentStatus}`
  const targetStatus = statusOptions.find(s => s.value === form.value.targetStatus)?.label || '未知'
  return `状态变更：${currentStatus} → ${targetStatus}`
})

const actionText = computed(() => {
  const statusMap = {
    1: '分配',
    2: '开始',
    3: '完成',
    4: '关闭'
  }
  return statusMap[form.value.targetStatus] || '变更'
})

const availableStatuses = computed(() => {
  if (!props.task) return []
  
  // 根据当前状态返回可转换的状态
  const transitions = {
    0: [1, 4], // 待分配 -> 待执行, 已关闭
    1: [2, 4], // 待执行 -> 执行中, 已关闭
    2: [3, 4], // 执行中 -> 已完成, 已关闭
    3: [2],    // 已完成 -> 执行中
    4: [1]     // 已关闭 -> 待执行
  }
  
  const allowedStatuses = transitions[props.task.taskStatus] || []
  return statusOptions.filter(status => allowedStatuses.includes(status.value))
})

const operationTips = computed(() => {
  if (!form.value.targetStatus) return ''
  
  const tips = {
    1: '分配任务后，执行人将收到通知',
    2: '开始执行后，任务状态将变为执行中',
    3: '完成任务后，将自动更新任务统计',
    4: '关闭任务后，将停止相关调度'
  }
  
  return tips[form.value.targetStatus] || ''
})

const canSubmit = computed(() => {
  if (!form.value.targetStatus || !form.value.reason.trim()) return false
  
  // 分配任务时需要选择执行人
  if (form.value.targetStatus === 1 && props.task?.taskStatus === 0) {
    return form.value.assigneeIds.length > 0
  }
  
  return true
})

// 表单验证规则
const rules = {
  targetStatus: [
    { required: true, message: '请选择目标状态', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' },
    { min: 5, message: '变更原因至少5个字符', trigger: 'blur' }
  ],
  assigneeIds: [
    { 
      required: true, 
      message: '请选择执行人', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.value.targetStatus === 1 && props.task?.taskStatus === 0 && (!value || value.length === 0)) {
          callback(new Error('分配任务时必须选择执行人'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 方法
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

const searchUsers = async (query: string) => {
  if (!query) return
  
  userLoading.value = true
  try {
    const res = await service.base.sys.user.page({
      keyword: query,
      page: 1,
      size: 20
    })
    userOptions.value = res.list || []
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

const handleFileChange = (file: any, fileList: any[]) => {
  form.value.attachments = fileList
}

const handlePhotoChange = (file: any, fileList: any[]) => {
  form.value.photos = fileList
}

const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    const data = {
      taskId: props.task.id,
      targetStatus: form.value.targetStatus,
      reason: form.value.reason,
      assigneeIds: form.value.assigneeIds,
      attachments: form.value.attachments.map(f => f.name),
      photos: form.value.photos.map(f => f.name)
    }
    
    emit('confirm', data)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  // 延迟重置表单，避免闪动
  setTimeout(() => {
    resetForm()
  }, 200)
}

const handleOpened = () => {
  // 对话框打开后的处理
  if (props.targetStatus && form.value.targetStatus !== props.targetStatus) {
    form.value.targetStatus = props.targetStatus
  }
}

const resetForm = () => {
  if (!props.modelValue) {
    form.value = {
      targetStatus: null,
      reason: '',
      assigneeIds: [],
      attachments: [],
      photos: []
    }
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }
}

// 监听器
watch(() => props.modelValue, (val) => {
  if (val) {
    // 重置表单状态
    resetForm()
    // 设置目标状态
    nextTick(() => {
      if (props.targetStatus) {
        form.value.targetStatus = props.targetStatus
      }
    })
  }
}, { immediate: false })

watch(() => props.targetStatus, (val) => {
  if (val && props.modelValue && form.value.targetStatus !== val) {
    form.value.targetStatus = val
  }
}, { immediate: false })
</script>

<style lang="scss" scoped>
.task-status-change-dialog {
  // 添加过渡动画
  :deep(.el-dialog) {
    transition: all 0.3s ease;
  }

  :deep(.el-dialog__body) {
    transition: opacity 0.2s ease;
  }

  .task-info-section {
    margin-bottom: 20px;
    opacity: 1;
    transition: opacity 0.2s ease;

    .task-info-card {
      border: 1px solid #e4e7ed;
      
      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .task-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }
      
      .task-meta {
        display: flex;
        gap: 20px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  .status-change-section {
    margin-bottom: 20px;
    opacity: 1;
    transition: opacity 0.2s ease;
  }

  .upload-section {
    .upload-demo {
      margin-bottom: 10px;
    }
  }

  .operation-tips {
    margin-bottom: 20px;
    opacity: 1;
    transition: opacity 0.2s ease;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
