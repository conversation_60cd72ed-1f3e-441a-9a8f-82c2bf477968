<template>
  <el-dialog
    v-model="visible"
    title="选择任务模板"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="template-selector">
      <!-- 搜索框 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索模板..."
          clearable
          :prefix-icon="Search"
        />
      </div>

      <!-- 模板分类 -->
      <div class="template-categories">
        <el-button-group>
          <el-button
            v-for="category in categories"
            :key="category.value"
            :type="selectedCategory === category.value ? 'primary' : 'default'"
            size="small"
            @click="selectedCategory = category.value"
          >
            {{ category.label }}
          </el-button>
        </el-button-group>
      </div>

      <!-- 模板列表 -->
      <div class="template-list">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="template-item"
          @click="selectTemplate(template)"
        >
          <div class="template-header">
            <div class="template-icon">
              <el-icon :color="template.color">
                <component :is="template.icon" />
              </el-icon>
            </div>
            <div class="template-info">
              <div class="template-name">{{ template.name }}</div>
              <div class="template-category">{{ template.categoryLabel }}</div>
            </div>
            <div class="template-priority">
              <el-tag :type="getPriorityType(template.priority)" size="small">
                {{ getPriorityLabel(template.priority) }}
              </el-tag>
            </div>
          </div>
          <div class="template-description">
            {{ template.description }}
          </div>
          <div class="template-tags">
            <el-tag
              v-for="tag in template.tags"
              :key="tag"
              size="small"
              type="info"
              effect="plain"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTemplates.length === 0" class="empty-state">
        <el-empty description="暂无匹配的模板" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="createCustomTask">
          自定义创建
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Search, Tools, Bell, Calendar, Lightning } from '@element-plus/icons-vue'

// 模板接口
interface TaskTemplate {
  id: string
  name: string
  description: string
  category: string
  categoryLabel: string
  priority: number
  taskCategory: string
  icon: any
  color: string
  tags: string[]
  defaultValues: {
    name: string
    description: string
    taskCategory: string
    priority: number
    estimatedDuration?: number
  }
}

// 组件属性
interface Props {
  modelValue: boolean
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'select', template: TaskTemplate): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(props.modelValue)
const searchKeyword = ref('')
const selectedCategory = ref('all')

// 模板分类
const categories = [
  { label: '全部', value: 'all' },
  { label: '维修', value: 'maintenance' },
  { label: '巡检', value: 'inspection' },
  { label: '清洁', value: 'cleaning' },
  { label: '安全', value: 'security' },
  { label: '其他', value: 'other' }
]

// 预定义模板
const templates: TaskTemplate[] = [
  {
    id: 'urgent-repair',
    name: '紧急维修',
    description: '设备故障需要紧急维修处理',
    category: 'maintenance',
    categoryLabel: '维修',
    priority: 5,
    taskCategory: 'LS',
    icon: Lightning,
    color: '#f56c6c',
    tags: ['紧急', '维修', '设备'],
    defaultValues: {
      name: '紧急维修任务',
      description: '设备故障需要紧急维修处理，请及时响应',
      taskCategory: 'LS',
      priority: 5,
      estimatedDuration: 2
    }
  },
  {
    id: 'daily-inspection',
    name: '日常巡检',
    description: '定期设备巡检，确保设备正常运行',
    category: 'inspection',
    categoryLabel: '巡检',
    priority: 2,
    taskCategory: 'RC',
    icon: Search,
    color: '#409eff',
    tags: ['日常', '巡检', '设备'],
    defaultValues: {
      name: '日常巡检任务',
      description: '定期设备巡检，检查设备运行状态',
      taskCategory: 'RC',
      priority: 2,
      estimatedDuration: 1
    }
  },
  {
    id: 'safety-check',
    name: '安全检查',
    description: '安全隐患排查和处理',
    category: 'security',
    categoryLabel: '安全',
    priority: 4,
    taskCategory: 'RC',
    icon: Bell,
    color: '#e6a23c',
    tags: ['安全', '检查', '隐患'],
    defaultValues: {
      name: '安全检查任务',
      description: '进行安全隐患排查，确保工作环境安全',
      taskCategory: 'RC',
      priority: 4,
      estimatedDuration: 1
    }
  },
  {
    id: 'maintenance-work',
    name: '维护保养',
    description: '设备定期维护保养工作',
    category: 'maintenance',
    categoryLabel: '维修',
    priority: 3,
    taskCategory: 'ZQ',
    icon: Tools,
    color: '#67c23a',
    tags: ['维护', '保养', '定期'],
    defaultValues: {
      name: '维护保养任务',
      description: '设备定期维护保养，延长设备使用寿命',
      taskCategory: 'ZQ',
      priority: 3,
      estimatedDuration: 3
    }
  },
  {
    id: 'scheduled-task',
    name: '计划任务',
    description: '按计划执行的常规任务',
    category: 'other',
    categoryLabel: '其他',
    priority: 3,
    taskCategory: 'ZQ',
    icon: Calendar,
    color: '#909399',
    tags: ['计划', '常规', '周期'],
    defaultValues: {
      name: '计划任务',
      description: '按计划执行的常规工作任务',
      taskCategory: 'ZQ',
      priority: 3,
      estimatedDuration: 2
    }
  }
]

// 计算属性
const filteredTemplates = computed(() => {
  let result = templates

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    result = result.filter(t => t.category === selectedCategory.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(t => 
      t.name.toLowerCase().includes(keyword) ||
      t.description.toLowerCase().includes(keyword) ||
      t.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }

  return result
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
})

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
const selectTemplate = (template: TaskTemplate) => {
  emit('select', template)
  visible.value = false
}

const createCustomTask = () => {
  // 发送空模板，表示自定义创建
  emit('select', null as any)
  visible.value = false
}

const getPriorityType = (priority: number) => {
  const types = ['info', 'primary', 'success', 'warning', 'danger']
  return types[priority - 1] || 'info'
}

const getPriorityLabel = (priority: number) => {
  const labels = ['低', '普通', '中等', '高', '紧急']
  return labels[priority - 1] || '普通'
}
</script>

<style scoped>
.template-selector {
  padding: 0;
}

.search-bar {
  margin-bottom: 16px;
}

.template-categories {
  margin-bottom: 20px;
  text-align: center;
}

.template-list {
  max-height: 400px;
  overflow-y: auto;
}

.template-item {
  padding: 16px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-item:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.template-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.template-icon {
  margin-right: 12px;
  font-size: 20px;
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.template-category {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.template-priority {
  margin-left: 12px;
}

.template-description {
  font-size: 13px;
  color: var(--el-text-color-regular);
  margin-bottom: 12px;
  line-height: 1.4;
}

.template-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
