<template>
	<el-dialog
		title="调整任务执行时间"
		v-model="visible"
		width="500px"
		:close-on-click-modal="false"
		@close="handleClose"
	>
		<task-info-display :task="task" />

		<el-form
			ref="formRef"
			:model="form"
			:rules="rules"
			label-width="80px"
			class="form-plus"
		>
			<el-form-item label="开始时间" prop="startTime">
				<el-date-picker
					v-model="form.startTime"
					type="datetime"
					placeholder="选择开始日期时间"
					value-format="YYYY-MM-DD HH:mm:ss"
					style="width: 100%"
				/>
			</el-form-item>
			<el-form-item label="结束时间" prop="endTime">
				<el-date-picker
					v-model="form.endTime"
					type="datetime"
					placeholder="选择结束日期时间"
					value-format="YYYY-MM-DD HH:mm:ss"
					style="width: 100%"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="handleConfirm">确定调整</el-button>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import TaskInfoDisplay from "./TaskInfoDisplay.vue";
import type { TaskInfoEntity } from "../types";

defineOptions({
	name: "TaskTimeAdjuster"
});

interface Props {
	modelValue: boolean;
	task?: TaskInfoEntity;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: false,
	task: undefined
});

const emit = defineEmits<{
	(e: "update:modelValue", value: boolean): void;
	(e: "confirm", data: { taskId: number; startTime: string; endTime: string }): void;
}>();

const visible = computed({
	get: () => props.modelValue,
	set: (val) => emit("update:modelValue", val)
});

const formRef = ref<FormInstance>();
const form = ref({
	startTime: "",
	endTime: ""
});

const rules = ref<FormRules>({
	startTime: [{ required: true, message: "开始时间不能为空", trigger: "change" }],
	endTime: [{ required: true, message: "结束时间不能为空", trigger: "change" }]
});

const handleConfirm = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid) => {
		if (valid) {
			if (!props.task || !props.task.id) {
				ElMessage.warning("任务信息不完整，无法调整时间");
				return;
			}
			if (new Date(form.value.endTime) <= new Date(form.value.startTime)) {
				ElMessage.warning("结束时间必须在开始时间之后");
				return;
			}
			emit("confirm", {
				taskId: props.task.id,
				startTime: form.value.startTime,
				endTime: form.value.endTime
			});
			handleClose();
		} else {
			ElMessage.warning("请填写完整的起止时间");
		}
	});
};

const handleClose = () => {
	visible.value = false;
	formRef.value?.resetFields();
};

watch(
	() => props.task,
	(newTask) => {
		if (newTask) {
			form.value.startTime = newTask.startTime || "";
			form.value.endTime = newTask.endTime || "";
		} else {
			formRef.value?.resetFields();
		}
	}
);
</script>

<style scoped>
.form-plus {
	margin-top: 20px;
}
</style>
