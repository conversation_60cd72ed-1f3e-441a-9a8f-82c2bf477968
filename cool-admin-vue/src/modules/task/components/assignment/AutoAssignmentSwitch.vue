<template>
  <div class="auto-assignment-switch">
    <div class="switch-container">
      <el-switch
        v-model="autoAssignEnabled"
        :disabled="disabled"
        active-text="自动分配执行人"
        inactive-text="手动分配"
        active-color="#409eff"
        inactive-color="#dcdfe6"
        @change="handleSwitchChange"
      />
      
      <el-tooltip
        content="开启后，AI将根据任务要求和人员情况智能推荐最合适的执行人"
        placement="top"
      >
        <el-icon class="help-icon">
          <QuestionFilled />
        </el-icon>
      </el-tooltip>
    </div>

    <!-- 分配策略配置 -->
    <div v-if="autoAssignEnabled" class="strategy-config">
      <el-divider content-position="left">分配策略</el-divider>
      
      <el-form :model="strategyConfig" label-width="120px" size="small">
        <el-form-item label="匹配策略">
          <el-select v-model="strategyConfig.matchStrategy" placeholder="请选择匹配策略">
            <el-option label="智能推荐" value="AI_RECOMMEND" />
            <el-option label="角色优先" value="ROLE_FIRST" />
            <el-option label="负载均衡" value="LOAD_BALANCE" />
            <el-option label="绩效优先" value="PERFORMANCE_FIRST" />
          </el-select>
        </el-form-item>

        <el-form-item label="协作模式">
          <el-switch
            v-model="strategyConfig.collaborationAllowed"
            active-text="允许多人协作"
            inactive-text="单人执行"
          />
        </el-form-item>

        <el-form-item label="工作负载限制">
          <el-slider
            v-model="strategyConfig.maxWorkload"
            :min="50"
            :max="100"
            :step="5"
            show-stops
            show-tooltip
            :format-tooltip="formatWorkloadTooltip"
          />
          <span class="workload-text">最大工作负载: {{ strategyConfig.maxWorkload }}%</span>
        </el-form-item>

        <el-form-item label="优先级权重">
          <div class="weight-config">
            <div class="weight-item">
              <span>角色匹配</span>
              <el-input-number
                v-model="strategyConfig.roleWeight"
                :min="0"
                :max="100"
                :step="5"
                size="small"
              />
              <span>%</span>
            </div>
            <div class="weight-item">
              <span>工作负载</span>
              <el-input-number
                v-model="strategyConfig.workloadWeight"
                :min="0"
                :max="100"
                :step="5"
                size="small"
              />
              <span>%</span>
            </div>
            <div class="weight-item">
              <span>历史绩效</span>
              <el-input-number
                v-model="strategyConfig.performanceWeight"
                :min="0"
                :max="100"
                :step="5"
                size="small"
              />
              <span>%</span>
            </div>
          </div>
          <div class="weight-total">
            总权重: {{ totalWeight }}% 
            <el-tag v-if="totalWeight !== 100" type="warning" size="small">
              权重总和应为100%
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  config: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'config-change']);

// 响应式数据
const autoAssignEnabled = ref(props.modelValue);

const strategyConfig = ref({
  matchStrategy: 'AI_RECOMMEND',
  collaborationAllowed: true,
  maxWorkload: 90,
  roleWeight: 40,
  workloadWeight: 25,
  performanceWeight: 20
});

// 计算属性
const totalWeight = computed(() => {
  return strategyConfig.value.roleWeight + 
         strategyConfig.value.workloadWeight + 
         strategyConfig.value.performanceWeight;
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  autoAssignEnabled.value = newVal;
});

watch(() => props.config, (newConfig) => {
  if (newConfig) {
    Object.assign(strategyConfig.value, newConfig);
  }
}, { immediate: true });

watch(strategyConfig, (newConfig) => {
  emit('config-change', newConfig);
}, { deep: true });

// 方法
const handleSwitchChange = (value: boolean) => {
  emit('update:modelValue', value);
  
  if (value) {
    // 开启自动分配时，发送配置
    emit('config-change', strategyConfig.value);
  }
};

const formatWorkloadTooltip = (value: number) => {
  return `${value}%`;
};

// 自动调整权重
watch([
  () => strategyConfig.value.roleWeight,
  () => strategyConfig.value.workloadWeight,
  () => strategyConfig.value.performanceWeight
], () => {
  const total = totalWeight.value;
  if (total > 100) {
    // 如果总权重超过100%，按比例缩减
    const scale = 100 / total;
    strategyConfig.value.roleWeight = Math.round(strategyConfig.value.roleWeight * scale);
    strategyConfig.value.workloadWeight = Math.round(strategyConfig.value.workloadWeight * scale);
    strategyConfig.value.performanceWeight = Math.round(strategyConfig.value.performanceWeight * scale);
  }
});
</script>

<style lang="scss" scoped>
.auto-assignment-switch {
  .switch-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .help-icon {
      color: #909399;
      cursor: help;
      
      &:hover {
        color: #409eff;
      }
    }
  }
  
  .strategy-config {
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;

    .el-divider {
      margin: 0 0 16px 0;
      border-color: var(--el-border-color-lighter);
    }
    
    .workload-text {
      margin-left: 12px;
      font-size: 12px;
      color: var(--el-text-color-regular);
    }

    .weight-config {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .weight-item {
        display: flex;
        align-items: center;
        gap: 12px;

        span:first-child {
          min-width: 60px;
          font-size: 12px;
          color: var(--el-text-color-regular);
        }

        span:last-child {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
    
    .weight-total {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #ebeef5;
      font-size: 12px;
      color: #606266;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}
</style>
