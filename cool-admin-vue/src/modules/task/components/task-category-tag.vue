<template>
  <el-tag 
    :type="getCategoryColor(category)" 
    :icon="getCategoryIcon(category)"
    size="small"
  >
    {{ getCategoryLabel(category) }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getTaskCategoryLabel, getTaskCategoryColor } from '../dict'

interface Props {
  category: string
}

const props = defineProps<Props>()

const getCategoryLabel = computed(() => (category: string) => {
  return getTaskCategoryLabel(category)
})

const getCategoryColor = computed(() => (category: string) => {
  return getTaskCategoryColor(category)
})

const getCategoryIcon = computed(() => (category: string) => {
  const iconMap = {
    'SOP_STEP': 'el-icon-guide',     // 场景步骤
    'RC': 'el-icon-calendar',        // 日常
    'ZQ': 'el-icon-refresh',         // 周期
    'LS': 'el-icon-lightning'        // 临时
  }
  return iconMap[category] || 'el-icon-document'
})
</script>
