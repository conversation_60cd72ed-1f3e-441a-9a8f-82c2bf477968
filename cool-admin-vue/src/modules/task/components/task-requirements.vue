<template>
  <div class="task-requirements">
    <el-tag 
      v-if="photoRequired" 
      type="warning" 
      size="small"
      icon="el-icon-camera"
      class="requirement-tag"
    >
      需拍照
    </el-tag>
    <el-tag 
      v-if="attachmentRequired" 
      type="info" 
      size="small"
      icon="el-icon-paperclip"
      class="requirement-tag"
    >
      需附件
    </el-tag>
    <span v-if="!photoRequired && !attachmentRequired" class="no-requirements">
      无特殊要求
    </span>
  </div>
</template>

<script setup lang="ts">
interface Props {
  photoRequired?: boolean
  attachmentRequired?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  photoRequired: false,
  attachmentRequired: false
})
</script>

<style scoped>
.task-requirements {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.requirement-tag {
  align-self: flex-start;
}

.no-requirements {
  color: #909399;
  font-size: 12px;
}
</style>
