<template>
  <el-tag
    :type="statusColor"
    size="small"
  >
    <el-icon style="margin-right: 4px;">
      <component :is="statusIcon" />
    </el-icon>
    {{ statusLabel }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { User, Clock, Loading, Check, Close, QuestionFilled } from '@element-plus/icons-vue'
import { getTaskStatusLabel, getTaskStatusColor } from '../dict'

interface Props {
  status: number
}

const props = defineProps<Props>()

const statusLabel = computed(() => {
  return getTaskStatusLabel(props.status)
})

const statusColor = computed(() => {
  return getTaskStatusColor(props.status)
})

const statusIcon = computed(() => {
  const iconMap = {
    0: User,           // 待分配
    1: Clock,          // 待执行
    2: Loading,        // 执行中
    3: Check,          // 已完成
    4: Close           // 已关闭
  }
  return iconMap[props.status] || QuestionFilled
})
</script>
