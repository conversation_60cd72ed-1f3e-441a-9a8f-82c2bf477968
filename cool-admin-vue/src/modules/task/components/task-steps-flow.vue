<template>
  <div class="task-steps-flow">
    <!-- 任务包信息头部 -->
    <div class="package-header">
      <div class="package-info">
        <div class="package-title-row">
          <h3>{{ packageData.packageName }}</h3>
          <el-tag :type="getStatusType(packageData.packageStatus)" size="small">
            {{ getStatusLabel(packageData.packageStatus) }}
          </el-tag>
        </div>
        <div class="package-desc">{{ packageData.description }}</div>

        <!-- 整体进度条 -->
        <div class="package-progress">
          <div class="progress-info">
            <span class="progress-label">整体进度</span>
            <span class="progress-text">{{ overallProgress }}%</span>
          </div>
          <el-progress
            :percentage="overallProgress"
            :stroke-width="12"
            :color="getProgressColor(overallProgress)"
            :show-text="false"
          />
        </div>
      </div>

      <div class="package-stats">
        <div class="stat-item">
          <div class="stat-icon">📋</div>
          <div class="stat-content">
            <span class="stat-value">{{ packageData.totalTasks || 0 }}</span>
            <span class="stat-label">总任务</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <span class="stat-value">{{ packageData.completedTasks || 0 }}</span>
            <span class="stat-label">已完成</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">🔄</div>
          <div class="stat-content">
            <span class="stat-value">{{ packageData.inProgressTasks || 0 }}</span>
            <span class="stat-label">进行中</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">⏳</div>
          <div class="stat-content">
            <span class="stat-value">{{ pendingTasksCount }}</span>
            <span class="stat-label">待分配</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤流程图 -->
    <div class="steps-container">
      <div class="steps-flow" ref="stepsFlowRef">
        <!-- 步骤行 -->
        <div
          v-for="(row, rowIndex) in stepRows"
          :key="rowIndex"
          class="step-row"
        >
          <!-- 步骤节点 -->
          <template v-for="(step, stepIndex) in row" :key="step?.id || stepIndex">
            <div
              v-if="step"
              class="step-node"
              :class="(getStepClass && getStepClass(step)) || ''"
              @click="handleStepClick(step)"
            >
            <!-- 精美步骤卡片 -->
            <div class="step-card">
              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="step-badge">
                  <span class="step-number">{{ step?.stepCode || 'N/A' }}</span>
                </div>
                <div class="status-indicator">
                  <div class="status-dot" :class="getStepStatusClass(step)"></div>
                  <span class="status-text">{{ getStepStatusText(step) }}</span>
                </div>
                <!-- 卡片操作按钮 -->
                <div class="card-actions" @click.stop>
                  <el-button
                    v-if="canCompleteStepTasks(step)"
                    type="success"
                    size="small"
                    circle
                    :icon="Check"
                    @click="showStepCompleteDialog(step)"
                    title="完成步骤" />
                  <el-button
                    v-if="canForceCloseStepTasks(step)"
                    type="warning"
                    size="small"
                    circle
                    :icon="Close"
                    @click="showStepForceCloseDialog(step)"
                    title="强制关闭步骤" />
                  <el-button
                    v-if="canReopenStepTasks(step)"
                    type="info"
                    size="small"
                    circle
                    :icon="Refresh"
                    @click="showStepReopenDialog(step)"
                    title="重新开启" />
                </div>
              </div>

              <!-- 卡片主体 -->
              <div class="card-body">
                <h3 class="step-title">{{ step?.stepName || '未知步骤' }}</h3>

                <!-- 信息网格 -->
                <div class="info-grid">
                  <!-- 执行人员（角色 → 人员） -->
                  <div class="info-item">
                    <div class="info-icon">👥</div>
                    <div class="info-content">
                      <div class="info-label">
                        执行人员
                        <el-button
                          type="primary"
                          text
                          bg
                          size="small"
                          :icon="Edit"
                          @click="showStepAssigneeEditor(step)"
                          class="inline-edit-btn"
                        >
                          分配/更换
                        </el-button>
                      </div>
                      <div class="info-value">
                        <div class="assignment-display">
                          <!-- 有指派人员时显示：角色 → 人员 -->
                          <div v-if="getStepAssignees(step).length > 0" class="assignment-flow">
                            <span class="role-tag">{{ getExecutorRoles(step) }}</span>
                            <span class="arrow-symbol">→</span>
                            <div class="assignee-tags">
                              <div 
                                v-for="assignee in getStepAssignees(step)"
                                :key="assignee.id"
                                class="assignee-item"
                              >
                                <el-tag
                                  size="small"
                                  class="assignee-tag-white"
                                >
                                  {{ assignee.name }}
                                </el-tag>
                                <el-tag
                                  :type="getExecutionStatusType(assignee.status)"
                                  size="small"
                                  class="status-tag"
                                >
                                  {{ getExecutionStatusLabel(assignee.status) }}
                                </el-tag>
                              </div>
                            </div>
                          </div>

                          <!-- 未指派状态 -->
                          <div v-else class="unassigned-display">
                            <span class="role-tag">{{ getExecutorRoles(step) }}</span>
                            <span class="arrow-symbol">→</span>
                            <span class="unassigned-tag">待分配</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 时间安排 -->
                  <div class="info-item" v-if="getStepTimeInfo(step)">
                    <div class="info-icon">⏰</div>
                    <div class="info-content">
                      <div class="info-label">
                        时间安排
                        <el-button
                          type="primary"
                          text
                          bg
                          size="small"
                          :icon="Edit"
                          @click="showStepTimeEditor(step)"
                          class="inline-edit-btn"
                        >
                          调整时间
                        </el-button>
                      </div>
                      <div class="info-value">
                        <div class="time-tags">
                          <div v-if="getStepTimeInfo(step)?.startTime" class="time-tag start-time-tag">
                            开始：{{ formatDate(getStepTimeInfo(step)?.startTime || '') }}
                          </div>
                          <div v-if="getStepTimeInfo(step)?.endTime" class="time-tag end-time-tag">
                            结束：{{ formatDate(getStepTimeInfo(step)?.endTime || '') }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 员工行为 -->
                  <div class="info-item" v-if="getEmployeeBehavior(step)">
                    <div class="info-icon">🎯</div>
                    <div class="info-content">
                      <div class="info-label">员工行为</div>
                      <div class="info-value">{{ getEmployeeBehavior(step) }}</div>
                    </div>
                  </div>

                  <!-- 工作亮点 -->
                  <div class="info-item" v-if="getWorkHighlight(step)">
                    <div class="info-icon">⭐</div>
                    <div class="info-content">
                      <div class="info-label">工作亮点</div>
                      <div class="info-value">{{ getWorkHighlight(step) }}</div>
                    </div>
                  </div>

                  <!-- 执行地点 -->
                  <div class="info-item" v-if="getStepLocation(step)">
                    <div class="info-icon">📍</div>
                    <div class="info-content">
                      <div class="info-label">执行地点</div>
                      <div class="info-value">{{ getStepLocation(step) }}</div>
                    </div>
                  </div>

                  <!-- 特殊要求 -->
                  <div class="info-item">
                    <div class="info-icon">📋</div>
                    <div class="info-content">
                      <div class="info-label">图片与附件</div>
                      <div class="info-value">
                        <div class="requirement-badges">
                          <span v-if="hasPhotoRequirement(step)" class="req-badge photo">📷 需拍照</span>
                          <span v-if="hasAttachmentRequirement(step)" class="req-badge attachment">📎 需附件</span>
                          <span v-if="!hasPhotoRequirement(step) && !hasAttachmentRequirement(step)" class="req-badge none">无要求</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 连接线已移除 -->
            </div>
          </template>
        </div>

        <!-- 行间连接线已移除 -->
      </div>
    </div>

    <!-- 步骤任务详情弹窗 -->
    <el-dialog
      v-model="taskDialogVisible"
      :title="`步骤任务 - ${selectedStep?.stepName}`"
      width="90%"
      top="3vh"
      :close-on-click-modal="false"
    >
      <div v-if="selectedStep" class="step-tasks-detail">
        <div class="tasks-header">
          <h4>{{ selectedStep.stepName }} ({{ selectedStep.stepCode }})</h4>
          <p>{{ selectedStep.description }}</p>
        </div>

        <!-- 任务卡片列表 -->
        <div class="tasks-grid">
          <div
            v-for="task in stepTasks"
            :key="task.id"
            class="task-card"
            :class="getTaskCardClass(task.taskStatus)"
          >
            <!-- 任务头部 -->
            <div class="task-header">
              <div class="task-title-row">
                <h5 class="task-name">{{ task.name }}</h5>
                <el-tag :type="getTaskStatusType(task.taskStatus)" size="small">
                  {{ getTaskStatusLabel(task.taskStatus) }}
                </el-tag>
              </div>
              <div class="task-category">
                <el-tag type="info" size="small" effect="plain">
                  {{ getTaskCategoryLabel(task.taskCategory) }}
                </el-tag>
              </div>
            </div>

            <!-- 任务操作按钮 -->
            <div class="task-card-actions">
              <!-- 编辑按钮暂时注释掉
              <el-button
                v-if="canEditTask(task)"
                type="primary"
                size="small"
                @click="showTaskEditDialog(task)"
                :icon="Edit">
                编辑
              </el-button>
              -->
              <el-button
                v-if="canCompleteTask(task)"
                type="success"
                size="small"
                @click="showTaskCompleteDialog(task)"
                :icon="Check">
                完成任务
              </el-button>
              <el-button
                v-if="canCloseTaskSingle(task)"
                type="warning"
                size="small"
                @click="showTaskCloseDialog(task)"
                :icon="Close">
                关闭任务
              </el-button>
              <el-button
                v-if="canReopenTaskSingle(task)"
                type="info"
                size="small"
                @click="showTaskReopenDialog(task)"
                :icon="Refresh">
                重新开启
              </el-button>
            </div>

            <!-- 任务基本信息 -->
            <div class="task-content">
              <!-- 什么事 -->
              <div class="task-section">
                <div class="section-title">📋 任务内容</div>
                <div class="section-content">
                  <p class="task-description">{{ task.description || '暂无描述' }}</p>
                  <div v-if="task.taskActivity" class="task-activity">
                    <strong>具体活动：</strong>{{ task.taskActivity }}
                  </div>
                </div>
              </div>

              <!-- 需要什么人 -->
              <div class="task-section">
                <div class="section-title">
                  👤 执行人员
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Plus"
                    @click="showAssigneeSelector(task)"
                    class="add-assignee-btn"
                  >
                    添加
                  </el-button>
                </div>
                <div class="section-content">
                  <div class="executor-info">
                    <span class="role-badge">{{ task.employeeRole || '未指定角色' }}</span>
                    <div class="assignee-container">
                      <!-- 显示执行人信息 -->
                      <div v-if="task.executions && task.executions.filter(exec => !['CANCELLED', 'REJECTED'].includes(exec.executionStatus)).length > 0" class="execution-list">
                        <div v-for="(execution, index) in task.executions.filter(exec => !['CANCELLED', 'REJECTED'].includes(exec.executionStatus))" :key="execution.id" class="execution-item">
                          <div class="assignee-info">
                            <el-button
                              type="danger"
                              size="small"
                              :icon="Close"
                              circle
                              @click="removeAssignee(task, execution.assigneeId)"
                              class="remove-assignee-btn"
                              title="移除执行人"
                            />
                            <el-avatar :size="32" :src="execution.headImg" class="assignee-avatar">
                              <span>{{ execution.assigneeName?.charAt(0) }}</span>
                            </el-avatar>
                            <div class="assignee-details">
                              <div class="assignee-name-row">
                                <span class="assignee-name">{{ execution.assigneeName || `用户${execution.assigneeId}` }}</span>
                                <el-tag :type="getExecutionStatusType(execution.executionStatus)" size="small" class="status-tag-inline">
                                  {{ getExecutionStatusLabel(execution.executionStatus) }}
                                </el-tag>
                              </div>
                              <div class="assignee-badges">
                                <span v-if="index === 0" class="primary-badge">主要负责人</span>
                                <span v-else class="assistant-badge">协助人员</span>
                                <span v-if="execution.acceptTime" class="accept-time">
                                  {{ formatDate(execution.acceptTime) }}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div class="execution-details">
                            <!-- 任务操作按钮 -->
                            <div class="task-actions">
                              <el-button
                                v-if="canCompleteExecution(execution)"
                                type="success"
                                size="small"
                                @click="showCompleteDialog(task.id, execution)"
                                :icon="Check">
                                完成任务
                              </el-button>
                              <el-button
                                v-if="canCancelExecution(execution)"
                                type="warning"
                                size="small"
                                @click="showCancelExecutionDialog(task.id, execution)"
                                :icon="Close">
                                取消执行
                              </el-button>
                              <el-button
                                v-if="canReopenTask(task)"
                                type="info"
                                size="small"
                                @click="showReopenDialog(task)"
                                :icon="Refresh">
                                重新开启
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- 没有执行人时显示 -->
                      <div v-else class="no-assignee">
                        <span class="assignee-name">{{ task.assigneeName || '待分配' }}</span>
                        <el-button
                          type="primary"
                          size="small"
                          @click="showAssigneeSelector(task)"
                        >
                          分配执行人
                        </el-button>
                      </div>
                    </div>
                  </div>
                  <div v-if="task.employeeBehavior" class="employee-behavior">
                    <strong>行为要求：</strong>{{ task.employeeBehavior }}
                  </div>
                </div>
              </div>

              <!-- 什么时间 -->
              <div class="task-section">
                <div class="section-title">
                  ⏰ 时间安排
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Edit"
                    @click="toggleTimeEdit(task)"
                    class="edit-time-btn"
                  >
                    {{ task.isEditingTime ? '保存' : '编辑' }}
                  </el-button>
                </div>
                <div class="section-content">
                  <div class="time-info">
                    <!-- 计划时间 -->
                    <div class="time-item planned">
                      <span class="time-label">📅 计划开始：</span>
                      <div v-if="!task.isEditingTime" class="time-value">
                        {{ task.startTime ? formatDate(task.startTime) : '未设置' }}
                      </div>
                      <el-date-picker
                        v-else
                        v-model="task.editStartTime"
                        type="datetime"
                        placeholder="选择开始时间"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        size="small"
                        class="time-picker"
                      />
                    </div>
                    <div class="time-item planned">
                      <span class="time-label">📅 计划结束：</span>
                      <div v-if="!task.isEditingTime" class="time-value">
                        {{ task.endTime ? formatDate(task.endTime) : '未设置' }}
                      </div>
                      <el-date-picker
                        v-else
                        v-model="task.editEndTime"
                        type="datetime"
                        placeholder="选择结束时间"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        size="small"
                        class="time-picker"
                      />
                    </div>

                    <!-- 实际时间 -->
                    <div v-if="task.actualStartTime" class="time-item actual">
                      <span class="time-label">🚀 实际开始：</span>
                      <span class="time-value">{{ formatDate(task.actualStartTime) }}</span>
                    </div>
                    <div v-if="task.actualEndTime" class="time-item actual">
                      <span class="time-label">🏁 实际结束：</span>
                      <span class="time-value">{{ formatDate(task.actualEndTime) }}</span>
                    </div>
                    <div v-if="task.completionTime" class="time-item completed">
                      <span class="time-label">✅ 完成时间：</span>
                      <span class="time-value">{{ formatDate(task.completionTime) }}</span>
                    </div>

                    <!-- 其他时间 -->
                    <div v-if="task.nextRunTime" class="time-item next">
                      <span class="time-label">⏭️ 下次执行：</span>
                      <span class="time-value">{{ formatDate(task.nextRunTime) }}</span>
                    </div>
                    <div class="time-item created">
                      <span class="time-label">📝 创建时间：</span>
                      <span class="time-value">{{ formatDate(task.createTime) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 去哪 -->
              <div v-if="task.entityTouchpoint" class="task-section">
                <div class="section-title">📍 执行地点</div>
                <div class="section-content">
                  <div class="location-info">{{ task.entityTouchpoint }}</div>
                </div>
              </div>

              <!-- 工作亮点 -->
              <div v-if="task.workHighlight" class="task-section">
                <div class="section-title">⭐ 工作亮点</div>
                <div class="section-content">
                  <div class="highlight-info">{{ task.workHighlight }}</div>
                </div>
              </div>

              <!-- 附加要求 -->
              <div class="task-section">
                <div class="section-title">📎 附加要求</div>
                <div class="section-content">
                  <div class="requirements">
                    <span class="requirement-item" :class="{ active: task.photoRequired }">
                      📷 {{ task.photoRequired ? '需要拍照' : '无需拍照' }}
                    </span>
                    <span class="requirement-item" :class="{ active: task.attachmentRequired }">
                      📎 {{ task.attachmentRequired ? '需要附件' : '无需附件' }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 备注信息 -->
              <div v-if="task.remark" class="task-section">
                <div class="section-title">📝 备注</div>
                <div class="section-content">
                  <div class="remark-info">{{ task.remark }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 完成任务对话框 -->
    <el-dialog
      v-model="completeDialogVisible"
      title="完成任务确认"
      width="500px"
      :before-close="() => completeDialogVisible = false">
      <div class="complete-progress" v-if="completeForm.taskId">
        <div class="progress-info">
          <span class="progress-label">完成进度</span>
          <span class="progress-text">{{ getTaskCompletionProgress(completeForm.taskId) }}%</span>
        </div>
        <el-progress
          :percentage="getTaskCompletionProgress(completeForm.taskId)"
          :stroke-width="8"
          :color="getProgressColor(getTaskCompletionProgress(completeForm.taskId))"
        />
      </div>

      <el-form :model="completeForm" label-width="100px">
        <el-form-item label="完成说明" required>
          <el-input
            v-model="completeForm.completionNote"
            type="textarea"
            :rows="3"
            placeholder="请详细描述任务完成情况、遇到的问题及解决方案..." />
        </el-form-item>
        <el-form-item label="附件上传">
          <el-upload
            v-model:file-list="completeForm.attachmentFiles"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleAttachmentSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            multiple
            :show-file-list="true">
            <el-button type="primary" :icon="Upload">选择附件</el-button>
            <template #tip>
              <div class="el-upload__tip">支持多种格式文件，单个文件不超过10MB</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="照片上传">
          <el-upload
            v-model:file-list="completeForm.photoFiles"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handlePhotoSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeImageUpload"
            list-type="picture-card"
            multiple
            accept="image/*">
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">支持jpg/png格式，单张图片不超过5MB</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="completeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCompleteTask">确认完成</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 关闭任务对话框 -->
    <el-dialog
      v-model="closeDialogVisible"
      title="关闭任务确认"
      width="500px"
      :before-close="() => closeDialogVisible = false">
      <el-alert type="warning" title="关闭后任务将无法继续执行" show-icon style="margin-bottom: 20px" />
      <el-form :model="closeForm" label-width="100px">
        <el-form-item label="关闭原因" required>
          <el-select v-model="closeForm.closeReason" placeholder="请选择关闭原因" style="width: 100%">
            <el-option
              v-for="option in closeReasonOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialogVisible = false">取消</el-button>
          <el-button type="warning" @click="handleCloseTask">确认关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 步骤完成对话框 -->
    <el-dialog
      v-model="stepCompleteDialogVisible"
      title="批量完成步骤任务"
      width="500px"
      :close-on-click-modal="false">
      <div v-if="currentStep">
        <div class="step-operation-info">
          <h4>{{ currentStep.stepName }} ({{ currentStep.stepCode }})</h4>
          <p>将完成该步骤下所有人的待执行和执行中的任务</p>
        </div>

        <el-form :model="stepOperationForm" label-width="100px">
          <el-form-item label="完成原因">
            <el-input
              v-model="stepOperationForm.reason"
              type="textarea"
              aria-required="true"
              :rows="3"
              placeholder="请输入批量完成的原因..." />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="stepOperationForm.forceComplete">
              强制关闭（跳过执行人确认）
            </el-checkbox>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="stepCompleteDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleStepComplete">确认完成</el-button>
      </template>
    </el-dialog>

    <!-- 步骤关闭对话框 -->
    <el-dialog
      v-model="stepCloseDialogVisible"
      title="批量关闭步骤任务"
      width="500px"
      :close-on-click-modal="false">
      <div v-if="currentStep">
        <div class="step-operation-info">
          <h4>{{ currentStep.stepName }} ({{ currentStep.stepCode }})</h4>
          <p>将关闭该步骤下所有已完成的任务</p>
        </div>

        <el-form :model="stepOperationForm" label-width="100px">
          <el-form-item label="关闭原因">
            <el-input
              v-model="stepOperationForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入批量关闭的原因..." />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="stepCloseDialogVisible = false">取消</el-button>
        <el-button type="warning" @click="handleStepClose">确认关闭</el-button>
      </template>
    </el-dialog>

    <!-- 步骤重新开启对话框 -->
    <el-dialog
      v-model="stepReopenDialogVisible"
      title="批量重新开启步骤任务"
      width="500px"
      :close-on-click-modal="false">
      <div v-if="currentStep">
        <div class="step-operation-info">
          <h4>{{ currentStep.stepName }} ({{ currentStep.stepCode }})</h4>
          <p>将重新开启该步骤下所有已关闭的任务</p>
        </div>

        <el-form :model="stepOperationForm" label-width="100px">
          <el-form-item label="开启原因">
            <el-input
              v-model="stepOperationForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入批量重新开启的原因..." />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="stepReopenDialogVisible = false">取消</el-button>
        <el-button type="info" @click="handleStepReopen">确认开启</el-button>
      </template>
    </el-dialog>

    <!-- 单个任务完成对话框 -->
    <el-dialog
      v-model="taskCompleteDialogVisible"
      title="完成任务"
      width="500px"
      :close-on-click-modal="false">
      <div v-if="currentTask">
        <div class="task-operation-info">
          <h4>{{ currentTask.name }}</h4>
          <p>{{ currentTask.description }}</p>
        </div>

        <el-form :model="taskOperationForm" label-width="100px">
          <el-form-item label="完成说明" required>
            <el-input
              v-model="taskOperationForm.completionNote"
              type="textarea"
              :rows="3"
              placeholder="请详细描述任务完成情况..." />
          </el-form-item>
          <el-form-item label="附件上传">
            <el-input
              v-model="taskOperationForm.attachments"
              placeholder="附件文件路径（多个用逗号分隔）" />
          </el-form-item>
          <el-form-item label="照片上传">
            <el-input
              v-model="taskOperationForm.photos"
              placeholder="照片文件路径（多个用逗号分隔）" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="taskCompleteDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTaskComplete">确认完成</el-button>
      </template>
    </el-dialog>

    <!-- 单个任务关闭对话框 -->
    <el-dialog
      v-model="taskCloseDialogVisible"
      title="关闭任务"
      width="500px"
      :close-on-click-modal="false">
      <div v-if="currentTask">
        <div class="task-operation-info">
          <h4>{{ currentTask.name }}</h4>
          <p>{{ currentTask.description }}</p>
        </div>

        <el-form :model="taskOperationForm" label-width="100px">
          <el-form-item label="关闭原因">
            <el-input
              v-model="taskOperationForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入关闭任务的原因..." />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="taskCloseDialogVisible = false">取消</el-button>
        <el-button type="warning" @click="handleTaskClose">确认关闭</el-button>
      </template>
    </el-dialog>

    <!-- 单个任务重新开启对话框 -->
    <el-dialog
      v-model="taskReopenDialogVisible"
      title="重新开启任务"
      width="500px"
      :close-on-click-modal="false">
      <div v-if="currentTask">
        <div class="task-operation-info">
          <h4>{{ currentTask.name }}</h4>
          <p>{{ currentTask.description }}</p>
        </div>

        <el-form :model="taskOperationForm" label-width="100px">
          <el-form-item label="开启原因">
            <el-input
              v-model="taskOperationForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入重新开启任务的原因..." />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="taskReopenDialogVisible = false">取消</el-button>
        <el-button type="info" @click="handleTaskReopen">确认开启</el-button>
      </template>
    </el-dialog>

    <!-- 执行人选择器组件 -->


    <!-- 新增：时间编辑对话框 -->
    <el-dialog
      v-model="timeEditorVisible"
      title="调整步骤时间"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-if="currentEditingStep">
        <div class="step-operation-info">
          <h4>{{ currentEditingStep.stepName }}</h4>
          <p>您正在为该步骤下的所有任务统一调整计划时间。</p>
        </div>
        <el-form :model="editTimeForm" label-position="top">
          <el-form-item label="计划开始时间">
            <el-date-picker
              v-model="editTimeForm.startTime"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="计划结束时间">
            <el-date-picker
              v-model="editTimeForm.endTime"
              type="datetime"
              placeholder="选择结束时间"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="timeEditorVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTimeUpdate">确认更新</el-button>
      </template>
    </el-dialog>

    <assignee-selector
      ref="assigneeSelectorRef"
      v-model="assigneeSelectorVisible"
      :task-name="`步骤：${currentEditingStep?.stepName}`"
      :filter-mode="isProjectMode ? 'project' : 'department'"
      :context-id="isProjectMode ? projectId : undefined"
      @confirm="handleAssigneeConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, withDefaults, watch } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Check, Close, Refresh, Upload, Plus, Edit } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import AssigneeSelector from '/@/components/assignee-selector';


const { service } = useCool();
import { taskBusinessStatusOptions, taskCategoryOptions } from '../dict/index';

interface Step {
	id: number;
	stepCode: string;
	stepName: string;
	[key: string]: any;
}

interface Task {
	id: number;
	stepCode: string;
	[key: string]: any;
}

interface Props {
  packageData: any;
  tasks: Task[];
  steps: Step[];
}

const props = withDefaults(defineProps<Props>(), {
  tasks: () => [],
  steps: () => []
});

// 响应式数据
const stepsFlowRef = ref<HTMLElement | null>(null);
const taskDialogVisible = ref(false);
const selectedStep = ref<any>(null);
const stepTasks = ref<any[]>([]);

// 新增：用于卡片内联编辑
const timeEditorVisible = ref(false);
const currentEditingStep = ref<any>(null);
const editTimeForm = ref({
	startTime: '',
	endTime: ''
});

// 本地任务数据，用于实时更新
const localTasks = ref<any[]>([]);

// 监听props.tasks变化，同步到localTasks
watch(() => props.tasks, (newTasks) => {
  localTasks.value = [...newTasks];
}, { immediate: true, deep: true });

// 计算属性：当前使用的任务数据（优先使用localTasks，如果为空则使用props.tasks）
const currentTasks = computed(() => {
  return localTasks.value.length > 0 ? localTasks.value : props.tasks;
});

// 将步骤按行排列（每行最多4个步骤）
const stepRows = computed(() => {
  if (!props.steps || !Array.isArray(props.steps)) {
    return [];
  }

  const rows: Step[][] = [];
  const stepsPerRow = 3; // 减少每行步骤数，因为卡片变宽了

  for (let i = 0; i < props.steps.length; i += stepsPerRow) {
    rows.push(props.steps.slice(i, i + stepsPerRow));
  }

  return rows;
});

// 计算整体进度
const overallProgress = computed(() => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value)) return 0;

  const totalTasks = currentTasks.value.length;
  if (totalTasks === 0) return 0;
  const completedTasks = currentTasks.value.filter(task => task && task.taskStatus === 3).length;
  return Math.round((completedTasks / totalTasks) * 100);
});

// 计算待分配任务数
const pendingTasksCount = computed(() => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value)) return 0;
  return currentTasks.value.filter(task => task && task.taskStatus === 0).length;
});

// 计算是否为项目模式
const isProjectMode = computed(() => {
  return props.packageData && props.packageData.projectId;
});

// 获取项目ID
const projectId = computed(() => {
  return props.packageData?.projectId;
});

// 获取步骤对应的任务数量
const getStepTaskCount = (stepCode: string) => {
  if (!localTasks.value || !Array.isArray(localTasks.value) || !stepCode) return 0;
  return localTasks.value.filter(task => task && task.stepCode === stepCode).length;
};

// 获取步骤进度
const getStepProgress = (stepCode: string) => {
  if (!localTasks.value || !Array.isArray(localTasks.value) || !stepCode) return 0;

  const stepTasks = localTasks.value.filter(task => task && task.stepCode === stepCode);
  if (stepTasks.length === 0) return 0;
  const completedTasks = stepTasks.filter(task => task && task.taskStatus === 3).length;
  return Math.round((completedTasks / stepTasks.length) * 100);
};

// 获取步骤特定状态的任务数量
const getStepTasksByStatus = (stepCode: string, status: number) => {
  if (!localTasks.value || !Array.isArray(localTasks.value) || !stepCode) return 0;
  return localTasks.value.filter(task => task && task.stepCode === stepCode && task.taskStatus === status).length;
};

// 获取步骤进度条颜色
const getStepProgressColor = (stepCode: string) => {
  const progress = getStepProgress(stepCode);
  if (progress === 0) return '#dcdfe6';
  if (progress < 30) return '#f56c6c';
  if (progress < 70) return '#e6a23c';
  return '#67c23a';
};

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c';
  if (percentage < 70) return '#e6a23c';
  return '#67c23a';
};

// 获取步骤执行角色
const getStepExecutorRoles = (stepCode: string) => {
  if (!props.tasks || !Array.isArray(props.tasks) || !stepCode) return '';

  const stepTasks = props.tasks.filter(task => task && task.stepCode === stepCode);
  const roles = [...new Set(stepTasks.map(task => task && task.employeeRole).filter(Boolean))];
  return roles.length > 0 ? roles.join(', ') : '';
};



// 获取步骤执行地点
const getStepLocations = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return '';

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  const locations = [...new Set(stepTasks.map(task => task && task.entityTouchpoint).filter(Boolean))];
  return locations.length > 0 ? locations.join(', ') : '';
};

// 获取步骤是否需要拍照
const getStepPhotoRequired = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return false;

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  return stepTasks.some(task => task && task.photoRequired);
};

// 获取步骤是否需要附件
const getStepAttachmentRequired = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return false;

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  return stepTasks.some(task => task && task.attachmentRequired);
};

// 获取步骤员工行为
const getStepEmployeeBehavior = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return '';

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  const behaviors = [...new Set(stepTasks.map(task => task && task.employeeBehavior).filter(Boolean))];
  return behaviors.length > 0 ? behaviors.join('; ') : '';
};

// 获取步骤工作亮点
const getStepWorkHighlight = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return '';

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  const highlights = [...new Set(stepTasks.map(task => task && task.workHighlight).filter(Boolean))];
  return highlights.length > 0 ? highlights.join('; ') : '';
};

// 获取步骤主要状态类型
const getStepMainStatusType = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return 'info';

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  if (stepTasks.length === 0) return 'info';

  const completedCount = stepTasks.filter(task => task && task.taskStatus === 3).length; // TaskBusinessStatusEnum.COMPLETED
  const inProgressCount = stepTasks.filter(task => task && task.taskStatus === 2).length; // TaskBusinessStatusEnum.EXECUTING
  const pendingCount = stepTasks.filter(task => task && task.taskStatus === 1).length; // TaskBusinessStatusEnum.PENDING_EXECUTE

  if (completedCount === stepTasks.length) return 'success'; // 全部完成
  if (inProgressCount > 0) return 'primary'; // 有进行中
  if (pendingCount > 0) return 'warning'; // 有待执行
  return 'danger'; // 待分配
};

// 获取步骤主要状态标签
const getStepMainStatusLabel = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return '无任务';

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  if (stepTasks.length === 0) return '无任务';

  const completedCount = stepTasks.filter(task => task && task.taskStatus === 3).length;
  const inProgressCount = stepTasks.filter(task => task && task.taskStatus === 2).length;
  const pendingCount = stepTasks.filter(task => task && task.taskStatus === 1).length;
  const pendingAssignCount = stepTasks.filter(task => task && task.taskStatus === 0).length;

  if (completedCount === stepTasks.length) return '已完成';
  if (inProgressCount > 0) return '执行中';
  if (pendingCount > 0) return '待执行';
  if (pendingAssignCount > 0) return '待分配';
  return '未知';
};

// 获取步骤主要状态样式类
const getStepMainStatusClass = (stepCode: string) => {
  if (!currentTasks.value || !Array.isArray(currentTasks.value) || !stepCode) return 'status-unknown';

  const stepTasks = currentTasks.value.filter(task => task && task.stepCode === stepCode);
  if (stepTasks.length === 0) return 'status-unknown';

  const completedCount = stepTasks.filter(task => task && task.taskStatus === 3).length; // TaskBusinessStatusEnum.COMPLETED
  const inProgressCount = stepTasks.filter(task => task && task.taskStatus === 2).length; // TaskBusinessStatusEnum.EXECUTING
  const pendingCount = stepTasks.filter(task => task && task.taskStatus === 1).length; // TaskBusinessStatusEnum.PENDING_EXECUTE

  if (completedCount === stepTasks.length) return 'status-completed';
  if (inProgressCount > 0) return 'status-in-progress';
  if (pendingCount > 0) return 'status-pending';
  return 'status-pending-assign';
};

// 安全的方法 - 获取步骤状态样式类
const getStepStatusClass = (step: any) => {
  if (!step || !step.stepCode) return 'status-unknown';
  return getStepMainStatusClass(step.stepCode);
};

// 安全的方法 - 获取步骤状态文本
const getStepStatusText = (step: any) => {
  if (!step || !step.stepCode) return '未知';
  return getStepMainStatusLabel(step.stepCode);
};

// 安全的方法 - 获取执行角色
const getExecutorRoles = (step: any) => {
  if (!step || !step.stepCode) return '待分配';
  return getStepExecutorRoles(step.stepCode) || '待分配';
};

// 安全的方法 - 获取员工行为
const getEmployeeBehavior = (step: any) => {
  if (!step || !step.stepCode) return '';
  return getStepEmployeeBehavior(step.stepCode) || '';
};

// 安全的方法 - 获取工作亮点
const getWorkHighlight = (step: any) => {
  if (!step || !step.stepCode) return '';
  return getStepWorkHighlight(step.stepCode) || '';
};

// 安全的方法 - 获取执行地点
const getStepLocation = (step: any) => {
  if (!step || !step.stepCode) return '';
  return getStepLocations(step.stepCode) || '';
};

// 安全的方法 - 检查是否需要拍照
const hasPhotoRequirement = (step: any) => {
  if (!step || !step.stepCode) return false;
  return getStepPhotoRequired(step.stepCode) || false;
};

// 安全的方法 - 检查是否需要附件
const hasAttachmentRequirement = (step: any) => {
  if (!step || !step.stepCode) return false;
  return getStepAttachmentRequired(step.stepCode) || false;
};

// 获取步骤样式类
const getStepClass = (step: any) => {
  if (!step || !step.stepCode) return 'step-empty';

  const taskCount = getStepTaskCount(step.stepCode);
  const completedTasks = currentTasks.value?.filter(task =>
    task && task.stepCode === step.stepCode && task.taskStatus === 3
  ).length || 0;

  if (taskCount === 0) return 'step-empty';
  if (completedTasks === taskCount) return 'step-completed';
  if (completedTasks > 0) return 'step-in-progress';
  return 'step-pending';
};

// 废弃旧的弹窗方法，改用新的占位方法
const handleStepClick = (step: Step) => {
	// 目前点击卡片主体不执行任何操作
	// 后续可以用于打开您期望的"操作记录"或"生命周期"弹窗
	console.log("点击了步骤卡片:", step);
};

// 显示步骤任务 (保留，以备将来使用)
const showStepTasks = (step: Step) => {
  if (!step || !step.stepCode) return;

  selectedStep.value = step;
  stepTasks.value = currentTasks.value?.filter(task => task && task.stepCode === step.stepCode) || [];
  taskDialogVisible.value = true;
};

// 新增：显示步骤执行人编辑器
const showStepAssigneeEditor = (step: Step) => {
	currentEditingStep.value = step;
	
	// 获取当前执行人信息
	const assignees = getStepAssignees(step);
	selectedAssignees.value = assignees.map(a => ({ id: a.id, name: a.name }));
	
	// 分析执行状态，准备提示信息
	const stepTasks = localTasks.value.filter(task => task && task.stepCode === step.stepCode);
	const protectedExecutors = new Set<number>(); // 不能移除的执行人
	const executorStatuses = new Map<number, string>(); // 执行人状态映射
	const warnings: string[] = []; // 警告信息
	
	stepTasks.forEach(task => {
		if (task.executions && task.executions.length > 0) {
			task.executions.forEach(execution => {
				const status = execution.executionStatus;
				// 只考虑有效状态的执行人，忽略已取消和已拒绝的
				if (status !== 'ASSIGNED') {
						protectedExecutors.add(execution.assigneeId);
						executorStatuses.set(execution.assigneeId, status);
						warnings.push(`${execution.assigneeName} 已开始执行，无法移除`);
					}
			});
		}
	});
	
	// 如果有受保护的执行人，显示简化警告
	if (warnings.length > 0) {
		ElMessage.warning(`有${warnings.length}个执行人收保护，将无法移除`);
	}
	
	// 直接打开人员选择器
	assigneeSelectorVisible.value = true;
};

// 新增：处理执行人更新
const handleStepAssigneeUpdate = async (step: any, selectedUserIds: number[]) => {
	if (!step) {
		ElMessage.error("未指定要操作的步骤");
		return;
	}
	
	try {
		// 找到该步骤下的所有任务
		const stepTasks = localTasks.value.filter(task => task && task.stepCode === step.stepCode);
		
		if (stepTasks.length === 0) {
			ElMessage.warning("该步骤下没有任务需要分配执行人");
			return;
		}
		
		// 显示调整进度
		const loading = ElLoading.service({
			lock: true,
			text: '正在智能调整执行人分配...',
			background: 'rgba(0, 0, 0, 0.7)'
		});
		
		try {
			// 批量为每个任务分配执行人
			const assignmentPromises = stepTasks.map(task => 
				service.request({
					url: 'admin/task/assignment/manual',
					method: 'POST',
					data: {
						taskId: task.id, // 使用任务ID而不是步骤ID
						assigneeIds: selectedUserIds,
						collaborationAllowed: true,
						reason: `手动分配执行人 - 步骤: ${step.stepName}`
					}
				})
			);
			
			// 等待所有分配完成
			await Promise.all(assignmentPromises);
			
			// 立即更新本地任务数据状态和执行人信息
			for (const task of stepTasks) {
				// 更新任务状态为待执行（如果之前是待分配）
				if (task.taskStatus === 0) {
					task.taskStatus = 1; // TaskBusinessStatusEnum.PENDING_EXECUTE
				}
				
				// 更新执行人信息
				try {
					const updatedExecutions = await service.request({
						url: `/admin/task/execution/byTask/${task.id}`,
						method: 'GET'
					});
					task.executions = updatedExecutions.data || [];
				} catch (error) {
					console.error(`获取任务${task.id}的执行人信息失败:`, error);
				}
			}
			
			ElMessage.success({
				message: `执行人分配调整完成 - 步骤"${step.stepName}"下的${stepTasks.length}个任务`,
				type: 'success',
				duration: 3000
			});
			
			// 触发外部刷新以确保数据同步
			emit("refresh");
			
		} finally {
			loading.close();
		}
		
	} catch (error) {
		console.error("执行人更新失败:", error);
		ElMessage.error({
			message: "执行人分配调整失败，请查看详细错误信息",
			type: 'error',
			duration: 5000
		});
	}
};

// 新增：显示步骤时间编辑器
const showStepTimeEditor = (step: Step) => {
	currentEditingStep.value = step;
	const timeInfo = getStepTimeInfo(step);
	editTimeForm.value.startTime = timeInfo?.startTime || '';
	editTimeForm.value.endTime = timeInfo?.endTime || '';
	timeEditorVisible.value = true;
};

// 新增：处理时间更新
const handleTimeUpdate = async () => {
	if (!currentEditingStep.value) return;

	try {
		await ElMessageBox.confirm(
			`确认要更新步骤 "${currentEditingStep.value.stepName}" 的时间吗？该步骤下的所有任务时间都将被同步修改。`,
			'时间更新确认',
			{ type: 'warning' }
		);

		const step = currentEditingStep.value;
		const stepTasksToUpdate = localTasks.value.filter(t => t.stepCode === step.stepCode);
		const taskIds = stepTasksToUpdate.map(t => t.id);

		if (taskIds.length === 0) {
			ElMessage.info("该步骤下没有需要更新时间的任务。");
			timeEditorVisible.value = false;
			return;
		}

		// 调用批量更新任务时间的RestController API
		await service.request({
			url: 'admin/task/info/batch-update',
			method: 'POST',
			data: {
				ids: taskIds, 
				startTime: editTimeForm.value.startTime,
				endTime: editTimeForm.value.endTime
			}
		});

		// 修复：手动更新本地数据以实现即时刷新
		stepTasksToUpdate.forEach(task => {
			task.startTime = editTimeForm.value.startTime;
			task.endTime = editTimeForm.value.endTime;
		});

		ElMessage.success("时间更新成功！");
		timeEditorVisible.value = false;
		emit('refresh'); // 触发刷新

	} catch (error) {
		if (error !== 'cancel') {
			console.error("时间更新失败:", error);
			ElMessage.error("时间更新失败，请稍后再试。");
		}
	}
};

// 行间连接线样式函数已移除 - 用户不需要行与行间的连接线

// 状态相关方法
const getStatusType = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const types: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = { 0: 'warning', 1: 'primary', 2: 'success', 3: 'info' };
  return types[status] || 'info';
};

const getStatusLabel = (status: number) => {
  const labels: Record<number, string> = { 0: '待分配', 1: '执行中', 2: '已完成', 3: '已关闭' };
  return labels[status] || '未知';
};

const getStepStatusType = (status: number) => {
  const types: Record<number, string> = { 0: 'info', 1: 'success' };
  return types[status] || 'info';
};

const getStepStatusLabel = (status: number) => {
  const labels: Record<number, string> = { 0: '禁用', 1: '启用' };
  return labels[status] || '未知';
};

const getTaskStatusType = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const types: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = { 1: 'warning', 2: 'primary', 3: 'success', 4: 'danger' };
  return types[status] || 'info';
};

const getTaskStatusLabel = (status: number) => {
  const option = taskBusinessStatusOptions.find(opt => opt.value === status);
  return option ? option.label : '未知';
};

const getTaskCategoryLabel = (category: string) => {
  const option = taskCategoryOptions.find(opt => opt.value === category);
  return option ? option.label : category || '未知';
};

const getTaskCardClass = (status: number) => {
  const statusMap: Record<number, string> = {
    0: 'task-pending-assign',
    1: 'task-pending-execute',
    2: 'task-executing',
    3: 'task-completed',
    4: 'task-closed'
  };
  return statusMap[status] || 'task-unknown';
};

const getExecutionStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const types: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'ASSIGNED': 'warning',
    'ACCEPTED': 'primary',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'REJECTED': 'danger',
    'CANCELLED': 'info'
  };
  return types[status] || 'info';
};

const getExecutionStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'ASSIGNED': '已分配',
    'ACCEPTED': '已接受',
    'IN_PROGRESS': '执行中',
    'COMPLETED': '已完成',
    'REJECTED': '已拒绝',
    'CANCELLED': '已取消'
  };
  return labels[status] || status || '未知';
};

// 获取步骤的执行人信息（过滤掉已取消和已拒绝的）
const getStepAssignees = (step: any) => {
  if (!step || !step.stepCode || !localTasks.value) return [];

  const stepTasks = localTasks.value.filter(task => task && task.stepCode === step.stepCode);
  console.log(`步骤 ${step.stepCode} 的任务:`, stepTasks);

  const assigneesMap = new Map();
  // 定义有效的执行状态（排除已取消和已拒绝）
  const validStatuses = ['ASSIGNED', 'ACCEPTED', 'IN_PROGRESS', 'COMPLETED'];

  stepTasks.forEach(task => {
    console.log(`任务 ${task.name} 的执行信息:`, {
      executions: task.executions,
      assigneeName: task.assigneeName,
      assigneeId: task.assigneeId
    });

    if (task.executions && task.executions.length > 0) {
      task.executions.forEach(execution => {
        // 只显示有效状态的执行人，过滤掉已取消(CANCELLED)和已拒绝(REJECTED)
        if (validStatuses.includes(execution.executionStatus) && !assigneesMap.has(execution.assigneeId)) {
          assigneesMap.set(execution.assigneeId, {
            id: execution.assigneeId,
            name: execution.assigneeName || `用户${execution.assigneeId}`, // 如果姓名为空，显示默认值
            status: execution.executionStatus
          });
        }
      });
    } else if (task.assigneeName) {
      // 兼容旧的assigneeName字段
      if (!assigneesMap.has(task.assigneeId || task.assigneeName)) {
        assigneesMap.set(task.assigneeId || task.assigneeName, {
          id: task.assigneeId || task.assigneeName,
          name: task.assigneeName,
          status: 'ASSIGNED'
        });
      }
    }
  });

  const result = Array.from(assigneesMap.values());
  console.log(`步骤 ${step.stepCode} 的有效执行人:`, result);
  return result;
};

// 获取步骤的时间信息
const getStepTimeInfo = (step: any) => {
  if (!step || !step.stepCode || !localTasks.value) return null;

  const stepTasks = localTasks.value.filter(task => task && task.stepCode === step.stepCode);
  if (stepTasks.length === 0) return null;

  // 获取最早的开始时间和最晚的结束时间
  let earliestStart: string | null = null;
  let latestEnd: string | null = null;

  stepTasks.forEach(task => {
    if (task.startTime) {
      if (!earliestStart || new Date(task.startTime) < new Date(earliestStart)) {
        earliestStart = task.startTime;
      }
    }
    if (task.endTime) {
      if (!latestEnd || new Date(task.endTime) > new Date(latestEnd)) {
        latestEnd = task.endTime;
      }
    }
  });

  if (!earliestStart && !latestEnd) return null;

  return {
    startTime: earliestStart,
    endTime: latestEnd
  };
};

const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString('zh-CN');
};

// 任务操作相关方法
const canCompleteExecution = (execution: any) => {
  return execution && ['ASSIGNED', 'IN_PROGRESS'].includes(execution.executionStatus);
};

const canCancelExecution = (execution: any) => {
  return execution && ['ASSIGNED', 'IN_PROGRESS'].includes(execution.executionStatus);
};

const canCloseTask = (task: any) => {
  return task && task.taskStatus === 3; // 已完成状态才能关闭 (TaskBusinessStatusEnum.COMPLETED)
};

const canReopenTask = (task: any) => {
  return task && task.taskStatus === 4; // 已关闭状态才能重新开启 (TaskBusinessStatusEnum.CLOSED)
};

// 单个任务操作权限判断
const canCompleteTask = (task: any) => {
  if (!task) return false;

  // 管理员可以完成任何待执行或执行中的任务
  if (isManager()) {
    return [1, 2].includes(task.taskStatus); // 待执行(1)或执行中(2)
  }

  // 普通用户只能完成自己被分配的任务
  if (task.executions && task.executions.length > 0) {
    const currentUserId = getCurrentUserId();
    return task.executions.some((exec: any) =>
      exec.assigneeId === currentUserId &&
      ['ASSIGNED', 'IN_PROGRESS'].includes(exec.executionStatus)
    );
  }

  return false;
};

const canCloseTaskSingle = (task: any) => {
  if (!task) return false;

  // 管理员可以关闭已完成的任务
  if (isManager()) {
    return task.taskStatus === 3; // TaskBusinessStatusEnum.COMPLETED
  }

  // 普通用户可以关闭自己完成的任务
  if (task.executions && task.executions.length > 0) {
    const currentUserId = getCurrentUserId();
    return task.taskStatus === 3 && task.executions.some((exec: any) =>
      exec.assigneeId === currentUserId && exec.executionStatus === 'COMPLETED'
    );
  }

  return false;
};

// 检查是否可以编辑任务
const canEditTask = (task: any) => {
  if (!task) return false;

  // 管理员可以编辑任何状态的任务
  if (isManager()) {
    return [0, 1, 2, 3].includes(task.taskStatus); // 除了已关闭状态
  }

  return false;
};

const canReopenTaskSingle = (task: any) => {
  if (!task) return false;

  // 只有管理员可以重新开启已关闭的任务
  return isManager() && task.taskStatus === 4; // TaskBusinessStatusEnum.CLOSED
};

// 获取当前用户ID（需要根据实际系统实现）
const getCurrentUserId = () => {
  // TODO: 从用户状态或localStorage中获取当前用户ID
  return 1; // 暂时返回固定值，实际项目中需要获取真实用户ID
};

// 单个任务操作对话框
const taskCompleteDialogVisible = ref(false);
const taskCloseDialogVisible = ref(false);
const taskReopenDialogVisible = ref(false);
const currentTask = ref<any>(null);
const taskOperationForm = ref({
  reason: '',
  completionNote: '',
  attachments: '',
  photos: ''
});

// 执行人管理相关
const selectedAssignees = ref<any[]>([]);
const currentEditingTask = ref<any>(null);
const assigneeSelectorRef = ref();

// 显示执行人选择器
const showAssigneeSelector = async (task: any) => {
  currentEditingTask.value = task;

  try {
    // 获取当前任务的执行人
    const currentAssignees = await getCurrentTaskAssignees(task.id);
    selectedAssignees.value = currentAssignees.map((a: any) => ({
      id: a.assigneeId,
      name: a.assigneeName,
      phone: a.phone || '',
      headImg: a.headImg || '',
      departmentName: a.departmentName || '',
      roleName: a.roleName || ''
    }));

    console.log('获取到的执行人数据:', currentAssignees);
    console.log('转换后的selectedAssignees:', selectedAssignees.value);

    console.log('设置已选择的执行人:', selectedAssignees.value);

    // 直接打开人员选择器
    assigneeSelectorVisible.value = true;
  } catch (error) {
    console.error('获取当前执行人失败:', error);
    ElMessage.error('获取当前执行人失败');
  }
};

// 监听执行人选择变化（暂时禁用自动保存，改为手动确认）
// watch(selectedAssignees, async (newAssignees, oldAssignees) => {
//   // 只有在有当前编辑任务且选择发生实际变化时才处理
//   if (currentEditingTask.value && newAssignees !== oldAssignees) {
//     console.log('执行人选择发生变化:', newAssignees);
//     await handleAssigneeConfirm();
//   }
// }, { deep: true });

// 移除执行人
const removeAssignee = async (task: any, assigneeId: number) => {
  try {
    await ElMessageBox.confirm('确定要移除此执行人吗？', '确认操作', {
      type: 'warning'
    });

    // 从当前任务的执行人列表中找到要删除的执行记录
    const executionToDelete = task.executions?.find((a: any) => a.assigneeId === assigneeId);

    if (executionToDelete) {
      console.log('准备删除执行记录:', executionToDelete);
      // 直接删除执行记录 - 使用正确的参数格式
      await service.task.execution.delete({ ids: [executionToDelete.id] });

      // 立即更新本地任务数据
      await refreshTaskExecutions(task.id);

      ElMessage.success('执行人移除成功');
      emit('refresh');
    } else {
      console.error('未找到要删除的执行记录，assigneeId:', assigneeId, 'task.executions:', task.executions);
      ElMessage.error('未找到要删除的执行记录');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除执行人失败:', error);
      ElMessage.error('移除执行人失败');
    }
  }
};

// 切换时间编辑模式
const toggleTimeEdit = async (task: any) => {
  if (task.isEditingTime) {
    // 保存时间
    try {
      await updateTaskTime(task.id, {
        startTime: task.editStartTime,
        endTime: task.editEndTime
      });

      task.startTime = task.editStartTime;
      task.endTime = task.editEndTime;
      task.isEditingTime = false;

      ElMessage.success('时间更新成功');
      emit('refresh');
    } catch (error) {
      console.error('更新时间失败:', error);
      ElMessage.error('更新时间失败');
    }
  } else {
    // 进入编辑模式
    task.editStartTime = task.startTime;
    task.editEndTime = task.endTime;
    task.isEditingTime = true;
  }
};

const showTaskCompleteDialog = (task: any) => {
  currentTask.value = task;
  taskOperationForm.value = {
    reason: '',
    completionNote: '',
    attachments: '',
    photos: ''
  };
  taskCompleteDialogVisible.value = true;
};

const showTaskCloseDialog = (task: any) => {
  currentTask.value = task;
  taskOperationForm.value = {
    reason: '',
    completionNote: '',
    attachments: '',
    photos: ''
  };
  taskCloseDialogVisible.value = true;
};

const showTaskReopenDialog = (task: any) => {
  currentTask.value = task;
  taskOperationForm.value = {
    reason: '',
    completionNote: '',
    attachments: '',
    photos: ''
  };
  taskReopenDialogVisible.value = true;
};

// 更新任务时间
const updateTaskTime = async (taskId: number, timeData: { startTime: string; endTime: string }) => {
  try {
    await service.task.info.update({
      id: taskId,
      startTime: timeData.startTime,
      endTime: timeData.endTime
    });
  } catch (error) {
    console.error('更新任务时间失败:', error);
    throw error;
  }
};

// 获取当前任务的执行人（过滤掉已取消和已拒绝的）
const getCurrentTaskAssignees = async (taskId: number) => {
  try {
    console.log('正在获取任务执行人数据，taskId:', taskId);
    const response = await service.request({
      url: `/admin/task/execution/byTask/${taskId}`,
      method: 'GET'
    });
    console.log('获取到的执行人数据:', response);
    let executions = response.data || [];
    
    // 过滤掉已取消和已拒绝的执行人
    executions = executions.filter((exec: any) => !['CANCELLED', 'REJECTED'].includes(exec.executionStatus));
    console.log('过滤后的执行人数据:', executions);

    // 如果API返回空数据，尝试从原始任务数据中获取
    if (executions.length === 0) {
      console.log('API返回空数据，尝试从原始任务数据中获取执行人信息');
      const originalTask = props.tasks?.find(task => task.id === taskId);
      if (originalTask && originalTask.executions && originalTask.executions.length > 0) {
        // 同样过滤原始数据
        const filteredOriginal = originalTask.executions.filter((exec: any) => !['CANCELLED', 'REJECTED'].includes(exec.executionStatus));
        console.log('从原始任务数据中找到的有效执行人:', filteredOriginal);
        return filteredOriginal;
      }
    }

    return executions;
  } catch (error) {
    console.error('获取任务执行人失败:', error);

    // 发生错误时也尝试从原始数据获取
    const originalTask = props.tasks?.find(task => task.id === taskId);
    if (originalTask && originalTask.executions) {
      const filteredOriginal = originalTask.executions.filter((exec: any) => !['CANCELLED', 'REJECTED'].includes(exec.executionStatus));
      console.log('错误时从原始任务数据中获取的有效执行人:', filteredOriginal);
      return filteredOriginal;
    }

    return [];
  }
};

// 刷新指定任务的执行人数据
const refreshTaskExecutions = async (taskId: number) => {
  try {
    console.log('开始刷新任务执行人数据，taskId:', taskId);

    // 只获取最新的执行人数据，不获取任务基本信息
    const updatedExecutions = await getCurrentTaskAssignees(taskId);
    console.log('获取到的最新执行人数据:', updatedExecutions);

    // 更新localTasks中对应任务的执行人数据
    const taskIndex = localTasks.value.findIndex(task => task.id === taskId);
    console.log('找到的任务索引:', taskIndex);

    if (taskIndex !== -1) {
      console.log('更新前的任务数据:', {
        taskId: localTasks.value[taskIndex].id,
        taskStatus: localTasks.value[taskIndex].taskStatus,
        executionsLength: localTasks.value[taskIndex].executions?.length
      });

      // 只更新执行人数据，保持任务其他信息不变
      localTasks.value[taskIndex].executions = updatedExecutions;

      console.log('更新后的任务数据:', {
        taskId: localTasks.value[taskIndex].id,
        taskStatus: localTasks.value[taskIndex].taskStatus,
        executionsLength: localTasks.value[taskIndex].executions?.length
      });

      console.log('已更新执行人数据:', {
        taskId,
        executionsLength: updatedExecutions?.length,
        executions: updatedExecutions
      });
    } else {
      console.error('未找到对应的任务，taskId:', taskId);
    }
  } catch (error) {
    console.error('刷新任务执行人数据失败:', error);
  }
};

// 更新任务执行人
const updateTaskAssignees = async (taskId: number, assigneeIds: number[]) => {
  try {
    console.log('开始更新任务执行人:', { taskId, assigneeIds });

    // 获取当前的执行记录
    const currentExecutions = await getCurrentTaskAssignees(taskId);
    console.log('当前执行记录:', currentExecutions);

    // 删除现有的执行记录
    for (const execution of currentExecutions) {
      console.log('删除执行记录:', execution.id);
      await service.task.execution.delete({ ids: [execution.id] });
    }

    // 创建新的执行记录
    for (const assigneeId of assigneeIds) {
      // 从selectedAssignees中找到对应的用户信息
      const assigneeInfo = selectedAssignees.value.find(a => a.id === assigneeId);
      const assigneeName = assigneeInfo ? assigneeInfo.name : '';

      console.log('查找执行人信息:', {
        assigneeId,
        assigneeInfo,
        assigneeName,
        selectedAssignees: selectedAssignees.value
      });

      const executionData = {
        taskId: taskId,
        assigneeId: assigneeId,
        assigneeName: assigneeName, // 添加执行人姓名
        executionStatus: 'ASSIGNED',
        assignmentType: 'MANUAL',
        acceptTime: new Date().toISOString()
      };
      console.log('创建执行记录:', executionData);
      await service.task.execution.add(executionData);
    }

    console.log('执行人更新完成');
  } catch (error) {
    console.error('更新任务执行人失败:', error);
    throw error;
  }
};

// 处理单个任务完成
const handleTaskComplete = async () => {
  if (!currentTask.value) return;

  try {
    await ElMessageBox.confirm(
      `确认完成任务"${currentTask.value.name}"？`,
      '任务完成确认',
      {
        confirmButtonText: '确认完成',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 使用正确的任务状态管理接口 - 直接调用API
    await service.request({
      url: '/admin/task/status/task/force-complete',
      method: 'POST',
      data: {
        taskId: currentTask.value.id,
        reason: taskOperationForm.value.completionNote || '任务完成',
        operatorId: getCurrentUserId() || 1
      }
    });

    ElMessage.success('任务完成成功');
    taskCompleteDialogVisible.value = false;
    emit('refresh');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('任务完成失败');
      console.error('Task complete error:', error);
    }
  }
};

// 处理单个任务关闭
const handleTaskClose = async () => {
  if (!currentTask.value) return;

  try {
    await ElMessageBox.confirm(
      `确认关闭任务"${currentTask.value.name}"？`,
      '任务关闭确认',
      {
        confirmButtonText: '确认关闭',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 使用正确的任务状态管理接口 - 直接调用API
    await service.request({
      url: '/admin/task/status/task/close',
      method: 'POST',
      data: {
        taskId: currentTask.value.id,
        closeReason: taskOperationForm.value.reason || '任务关闭',
        operatorId: getCurrentUserId() || 1,
        operatorName: '管理员'
      }
    });

    ElMessage.success('任务关闭成功');
    taskCloseDialogVisible.value = false;
    emit('refresh');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('任务关闭失败');
      console.error('Task close error:', error);
    }
  }
};

// 处理单个任务重新开启
const handleTaskReopen = async () => {
  if (!currentTask.value) return;

  try {
    await ElMessageBox.confirm(
      `确认重新开启任务"${currentTask.value.name}"？`,
      '任务重新开启确认',
      {
        confirmButtonText: '确认开启',
        cancelButtonText: '取消',
        type: 'info',
      }
    );

    // 使用正确的任务状态管理接口 - 直接调用API
    await service.request({
      url: '/admin/task/status/task/reopen',
      method: 'POST',
      data: {
        taskId: currentTask.value.id,
        reason: taskOperationForm.value.reason || '任务重新开启',
        operatorId: getCurrentUserId() || 1
      }
    });

    ElMessage.success('任务重新开启成功');
    taskReopenDialogVisible.value = false;
    emit('refresh');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('任务重新开启失败');
      console.error('Task reopen error:', error);
    }
  }
};

// 步骤级别的操作权限判断
const canCompleteStepTasks = (step: any) => {
  if (!step || !step.stepCode) return false;

  // 检查是否是管理员
  if (!isManager()) return false;

  // 获取该步骤的所有任务
  const stepTasks = getStepTasks(step.stepCode);
  if (!stepTasks || stepTasks.length === 0) return false;

  // 只有待执行或执行中的任务才能完成
  return stepTasks.some(task => [1, 2].includes(task.taskStatus));
};

const canCloseStepTasks = (step: any) => {
  if (!step || !step.stepCode) return false;

  // 检查是否是管理员
  if (!isManager()) return false;

  // 获取该步骤的所有任务
  const stepTasks = getStepTasks(step.stepCode);
  if (!stepTasks || stepTasks.length === 0) return false;

  // 只有已完成的任务才能关闭
  return stepTasks.some(task => task.taskStatus === 3);
};

// 检查是否可以强制关闭步骤任务（不需要完成任务也可以关闭）
const canForceCloseStepTasks = (step: any) => {
  if (!step || !step.stepCode) return false;

  // 检查是否是管理员
  if (!isManager()) return false;

  const stepTasks = getStepTasks(step.stepCode);
  if (!stepTasks || stepTasks.length === 0) return false;

  // 有任何未关闭的任务都可以强制关闭
  return stepTasks.some(task => [0, 1, 2, 3].includes(task.taskStatus)); // 除了已关闭状态
};

const canReopenStepTasks = (step: any) => {
  if (!step || !step.stepCode) return false;

  // 检查是否是管理员
  if (!isManager()) return false;

  // 获取该步骤的所有任务
  const stepTasks = getStepTasks(step.stepCode);
  if (!stepTasks || stepTasks.length === 0) return false;

  // 只有已关闭的任务才能重新开启
  return stepTasks.some(task => task.taskStatus === 4);
};

// 检查是否是管理员
const isManager = () => {
  // 这里应该根据实际的权限系统来判断
  // 暂时返回true，实际项目中需要检查用户角色
  return true; // TODO: 实现真实的管理员权限检查
};

// 获取步骤下的所有任务
const getStepTasks = (stepCode: string) => {
  if (!props.tasks || !Array.isArray(props.tasks)) return [];
  return props.tasks.filter(task => task && task.stepCode === stepCode);
};

// 步骤级别的操作对话框
const stepCompleteDialogVisible = ref(false);
const stepCloseDialogVisible = ref(false);
const stepReopenDialogVisible = ref(false);
const currentStep = ref<any>(null);
const stepOperationForm = ref({
  reason: '',
  forceComplete: false
});

const showStepCompleteDialog = (step: any) => {
  currentStep.value = step;
  stepOperationForm.value = {
    reason: '',
    forceComplete: false
  };
  stepCompleteDialogVisible.value = true;
};

const showStepCloseDialog = (step: any) => {
  currentStep.value = step;
  stepOperationForm.value = {
    reason: '',
    forceComplete: false
  };
  stepCloseDialogVisible.value = true;
};

const showStepForceCloseDialog = (step: any) => {
  currentStep.value = step;
  stepOperationForm.value = {
    reason: '',
    forceComplete: true // 强制关闭标记
  };
  stepCloseDialogVisible.value = true;
};

const showStepReopenDialog = (step: any) => {
  currentStep.value = step;
  stepOperationForm.value = {
    reason: '',
    forceComplete: false
  };
  stepReopenDialogVisible.value = true;
};

// 处理步骤完成
const handleStepComplete = async () => {
  if (!currentStep.value) return;

  try {
    await ElMessageBox.confirm(
      `确认完成步骤"${currentStep.value.stepName}"下的所有任务？`,
      '批量完成确认',
      {
        confirmButtonText: '确认完成',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const stepTasks = getStepTasks(currentStep.value.stepCode);
    const taskIds = stepTasks
      .filter(task => [1, 2].includes(task.taskStatus)) // 只处理待执行(1)和执行中(2)的任务
      .map(task => task.id);

    if (taskIds.length === 0) {
      ElMessage.warning('没有可完成的任务');
      return;
    }

    // 逐个调用单个任务完成接口
    try {
      // 使用批量强制完成任务接口 - 直接调用API
      await service.request({
        url: '/admin/task/status/task/batch/force-complete',
        method: 'POST',
        data: {
          taskIds: taskIds,
          reason: '批量完成',
          operatorId: getCurrentUserId() || 1
        }
      });

      ElMessage.success(`批量完成任务成功，共处理 ${taskIds.length} 个任务`);
    } catch (error) {
      console.error('批量完成任务失败:', error);
      ElMessage.error('批量完成任务失败');
    }

    stepCompleteDialogVisible.value = false;
    emit('refresh');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量完成失败');
      console.error('Batch complete error:', error);
    }
  }
};

// 处理步骤关闭
const handleStepClose = async () => {
  if (!currentStep.value) return;

  try {
    await ElMessageBox.confirm(
      `确认关闭步骤"${currentStep.value.stepName}"下的所有任务？`,
      '批量关闭确认',
      {
        confirmButtonText: '确认关闭',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const stepTasks = getStepTasks(currentStep.value.stepCode);
    const taskIds = stepTasks
      .filter(task => task.taskStatus === 3) // 只处理已完成(3)的任务
      .map(task => task.id);

    if (taskIds.length === 0) {
      ElMessage.warning('没有可关闭的任务');
      return;
    }

    try {
      // 使用批量关闭任务接口 - 直接调用API
      await service.request({
        url: '/admin/task/status/task/batch/close',
        method: 'POST',
        data: {
          taskIds: taskIds,
          reason: stepOperationForm.value.reason || '管理员批量关闭',
          operatorId: getCurrentUserId() || 1
        }
      });

      ElMessage.success(`批量关闭任务成功，共处理 ${taskIds.length} 个任务`);
      stepCloseDialogVisible.value = false;
      emit('refresh');
    } catch (error) {
      console.error('批量关闭任务失败:', error);
      ElMessage.error('批量关闭任务失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量关闭失败');
      console.error('Batch close error:', error);
    }
  }
};

// 处理步骤重新开启
const handleStepReopen = async () => {
  if (!currentStep.value) return;

  try {
    await ElMessageBox.confirm(
      `确认重新开启步骤"${currentStep.value.stepName}"下的所有任务？`,
      '批量重新开启确认',
      {
        confirmButtonText: '确认开启',
        cancelButtonText: '取消',
        type: 'info',
      }
    );

    const stepTasks = getStepTasks(currentStep.value.stepCode);
    const taskIds = stepTasks
      .filter(task => task.taskStatus === 4) // 只处理已关闭(4)的任务
      .map(task => task.id);

    if (taskIds.length === 0) {
      ElMessage.warning('没有可重新开启的任务');
      return;
    }

    try {
      // 使用批量重新开启任务接口 - 直接调用API
      await service.request({
        url: '/admin/task/status/task/batch/reopen',
        method: 'POST',
        data: {
          taskIds: taskIds,
          reason: stepOperationForm.value.reason || '管理员批量重新开启',
          operatorId: getCurrentUserId() || 1
        }
      });

      ElMessage.success(`批量重新开启任务成功，共处理 ${taskIds.length} 个任务`);
    } catch (error) {
      console.error('批量重新开启任务失败:', error);
      ElMessage.error('批量重新开启任务失败');
    }

    stepReopenDialogVisible.value = false;
    emit('refresh');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量重新开启失败');
      console.error('Batch reopen error:', error);
    }
  }
};

// 完成任务对话框相关
const completeDialogVisible = ref(false);
const completeForm = ref({
  taskId: null as number | null,
  assigneeId: null as number | null,
  executionId: null as number | null, // 添加执行记录ID
  completionNote: '',
  attachments: [] as string[],
  photos: [] as string[],
  attachmentFiles: [] as any[],
  photoFiles: [] as any[]
});

const showCompleteDialog = (taskId: number, execution: any) => {
  completeForm.value = {
    taskId,
    assigneeId: execution.assigneeId,
    executionId: execution.id, // 添加执行记录ID
    completionNote: '',
    attachments: [],
    photos: [],
    attachmentFiles: [],
    photoFiles: []
  };
  completeDialogVisible.value = true;
};

const showCancelExecutionDialog = async (taskId: number, execution: any) => {
  try {
    await ElMessageBox.confirm(
      `确认取消"${execution.assigneeName || '该执行人'}"的任务执行？`,
      '取消执行确认',
      {
        confirmButtonText: '确认取消',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 调用取消执行的API
    await service.request({
      url: '/admin/task/execution/delete',
      method: 'POST',
      data: {
        ids: [execution.id]
      }
    });

    ElMessage.success('取消执行成功');

    // 刷新数据
    await refreshTaskExecutions(taskId);
    emit('refresh');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('取消执行失败');
      console.error('Cancel execution error:', error);
    }
  }
};

const handleCompleteTask = async () => {
  // 验证必填项
  if (!completeForm.value.completionNote.trim()) {
    ElMessage.warning('请填写完成说明');
    return;
  }

  try {
    // 确认完成
    await ElMessageBox.confirm(
      '确认完成此任务？完成后将无法撤销。',
      '完成确认',
      {
        confirmButtonText: '确认完成',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 准备请求数据
    const requestData = {
      taskId: completeForm.value.taskId,
      assigneeId: completeForm.value.assigneeId,
      completionNote: completeForm.value.completionNote,
      attachments: completeForm.value.attachments,
      photos: completeForm.value.photos
    };

    // 使用正确的任务状态管理接口 - 直接调用API
    await service.request({
      url: '/admin/task/status/task/execution/complete',
      method: 'POST',
      data: {
        taskId: requestData.taskId,
        assigneeId: requestData.assigneeId,
        completionNote: requestData.completionNote,
        attachments: requestData.attachments,
        photos: requestData.photos
      }
    });

    ElMessage.success('任务完成成功');
    completeDialogVisible.value = false;

    // 立即刷新当前任务的执行人数据
    if (requestData.taskId) {
      await refreshTaskExecutions(requestData.taskId);
    }

    // 强制刷新整个组件数据
    emit('refresh');

    // 额外等待一下再次刷新，确保数据同步
    setTimeout(() => {
      emit('refresh');
    }, 500);

    // 再次等待并强制重新渲染
    setTimeout(() => {
      console.log('强制重新渲染组件');
      emit('refresh');
    }, 1000);
  } catch (error) {
    ElMessage.error('任务完成失败');
    console.error('Complete task error:', error);
  }
};

// 文件上传相关
const uploadUrl = '/admin/file/upload';
const uploadHeaders = {
  'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
};

const beforeUpload = (file: any) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!');
  }
  return isLt10M;
};

const beforeImageUpload = (file: any) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB!');
    return false;
  }
  return true;
};

const handleAttachmentSuccess = (response: any, file: any) => {
  if (response.code === 1000) {
    completeForm.value.attachments.push(response.data.url);
    ElMessage.success('附件上传成功');
  } else {
    ElMessage.error('附件上传失败');
  }
};

const handlePhotoSuccess = (response: any, file: any) => {
  if (response.code === 1000) {
    completeForm.value.photos.push(response.data.url);
    ElMessage.success('照片上传成功');
  } else {
    ElMessage.error('照片上传失败');
  }
};

const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败');
  console.error('Upload error:', error);
};

// 获取任务完成进度（只统计有效的执行人）
const getTaskCompletionProgress = (taskId: number | null) => {
  if (!taskId || !props.tasks) return 0;

  const task = props.tasks.find(t => t.id === taskId);
  if (!task || !task.executions) return 0;

  // 只统计有效状态的执行人
  const validExecutions = task.executions.filter(
    (exec: any) => !['CANCELLED', 'REJECTED'].includes(exec.executionStatus)
  );
  
  const totalExecutions = validExecutions.length;
  if (totalExecutions === 0) return 0;

  const completedExecutions = validExecutions.filter(
    (exec: any) => exec.executionStatus === 'COMPLETED'
  ).length;

  return Math.round((completedExecutions / totalExecutions) * 100);
};

// 关闭任务对话框相关
const closeDialogVisible = ref(false);
const closeForm = ref({
  taskId: null,
  closeReason: '',
  operatorId: 1, // 这里应该从用户信息中获取
  operatorName: '管理员' // 这里应该从用户信息中获取
});

const closeReasonOptions = [
  { label: '任务已完成验收', value: 'completed' },
  { label: '任务已取消', value: 'cancelled' },
  { label: '其他原因', value: 'other' }
];

const showCloseDialog = (task: any) => {
  closeForm.value = {
    taskId: task.id,
    closeReason: '',
    operatorId: 1,
    operatorName: '管理员'
  };
  closeDialogVisible.value = true;
};

const handleCloseTask = async () => {
  try {
    // 使用正确的任务状态管理接口 - 直接调用API
    await service.request({
      url: '/admin/task/status/task/close',
      method: 'POST',
      data: {
        taskId: closeForm.value.taskId,
        closeReason: closeForm.value.closeReason || '任务关闭',
        operatorId: getCurrentUserId() || 1,
        operatorName: '管理员'
      }
    });

    ElMessage.success('任务关闭成功');
    closeDialogVisible.value = false;
    // 刷新数据
    emit('refresh');
  } catch (error) {
    ElMessage.error('任务关闭失败');
    console.error('Close task error:', error);
  }
};

// 重新开启任务
const showReopenDialog = async (task: any) => {
  try {
    await ElMessageBox.confirm('确定要重新开启此任务吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const response = await fetch('/admin/task/reopen', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskId: task.id,
        reason: '重新开启任务',
        operatorId: 1
      })
    });

    const result = await response.json();
    if (result.code === 1000) {
      ElMessage.success('任务重新开启成功');
      // 刷新数据
      emit('refresh');
    } else {
      ElMessage.error(result.message || '任务重新开启失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('任务重新开启失败');
      console.error('Reopen task error:', error);
    }
  }
};

// 添加emit定义
const emit = defineEmits(['refresh']);

const assigneeSelectorVisible = ref(false);

const openAssigneeSelector = (step: any) => {
  currentEditingStep.value = step;
  assigneeSelectorVisible.value = true;
};

const handleAssigneeConfirm = (data: { assigneeId: string | number; assigneeName: string; reason: string }) => {
  if (currentEditingStep.value) {
    handleStepAssigneeUpdate(currentEditingStep.value, [Number(data.assigneeId)]);
  }
  assigneeSelectorVisible.value = false;
};

const openTimeEditor = (step: any) => {
  showStepTimeEditor(step);
};
</script>

<style scoped>
.task-steps-flow {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100%;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  background: var(--el-bg-color);
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-lighter);
}

.package-info {
  flex: 1;
  margin-right: 32px;
}

.package-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.package-title-row h3 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 20px;
  font-weight: 600;
}

.package-desc {
  color: var(--el-text-color-regular);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.package-progress {
  margin-top: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.progress-text {
  font-size: 14px;
  color: var(--el-color-primary);
  font-weight: 600;
}

.package-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.stat-icon {
  font-size: 20px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

.steps-container {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 30px;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-lighter);
  position: relative;
}

.no-steps-message {
  text-align: center;
  padding: 40px;
  color: var(--el-text-color-secondary);
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px dashed var(--el-border-color);
}

.no-steps-message p {
  margin: 8px 0;
  font-size: 14px;
}

.steps-flow {
  position: relative;
  min-height: 400px;
}

.step-row {
  display: flex;
  align-items: center;
  margin-bottom: 120px;
  position: relative;
}

.step-node {
  position: relative;
  width: 380px;
  max-height: 420px;
  margin-right: 60px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-node:hover {
  transform: translateY(-8px) scale(1.02);
}

.step-node:hover .step-card {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
}

/* Dark模式下的hover效果 */
.dark .step-node:hover .step-card {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(99, 102, 241, 0.3);
  border-color: rgba(99, 102, 241, 0.5);
}

/* 精美步骤卡片样式 */
.step-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 20px;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* Dark模式下的卡片样式 */
.dark .step-card {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border: 1px solid rgba(99, 102, 241, 0.3);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2);
}

.step-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

/* Dark模式下的卡片装饰更亮一些 */
.dark .step-card::before {
  background: linear-gradient(90deg, #818cf8, #a78bfa, #22d3ee);
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

/* flow 动画已移除 */

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 10px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.step-badge {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 12px;
  font-weight: 700;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.step-number {
  letter-spacing: 0.5px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
}

.status-dot.status-completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2), 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-dot.status-in-progress {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2), 0 0 8px rgba(59, 130, 246, 0.4);
}

.status-dot.status-pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2), 0 0 8px rgba(245, 158, 11, 0.4);
}

.status-dot.status-pending-assign {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2), 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-dot.status-unknown {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2), 0 0 8px rgba(107, 114, 128, 0.4);
}

.status-text {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 卡片主体样式 */
.card-body {
  padding: 12px 16px 14px;
}

.step-title {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 14px 0;
  line-height: 1.3;
  background: linear-gradient(135deg, #4f6077 100%, #374151 50%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 信息网格样式 */
.info-grid {
  display: grid;
  gap: 6px
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 6px;
  border: 1px solid rgba(99, 102, 241, 0.08);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
}

.info-icon {
  font-size: 14px;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-radius: 6px;
  flex-shrink: 0;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  font-size: 10px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-value {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-word;
}

/* 需求徽章样式 */
.requirement-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.req-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.req-badge.photo {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
  color: #1d4ed8;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.req-badge.attachment {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.req-badge.none {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.1) 0%, rgba(75, 85, 99, 0.1) 100%);
  color: #4b5563;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.req-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 执行人员样式 */
.assignees-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.assignee-tag {
  font-size: 12px;
  font-weight: 500;
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-border-color-light) !important;
  color: var(--el-text-color-primary) !important;
}

/* 强制覆盖Element Plus的tag样式 */
.assignees-tags .el-tag {
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-border-color-light) !important;
  color: var(--el-text-color-primary) !important;
}

.assignees-tags .el-tag.el-tag--primary {
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-color-primary-light-7) !important;
  color: var(--el-color-primary) !important;
}

.assignees-tags .el-tag.el-tag--success {
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-color-success-light-7) !important;
  color: var(--el-color-success) !important;
}

.assignees-tags .el-tag.el-tag--warning {
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-color-warning-light-7) !important;
  color: var(--el-color-warning) !important;
}

.assignees-tags .el-tag.el-tag--danger {
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-color-danger-light-7) !important;
  color: var(--el-color-danger) !important;
}

.assignees-tags .el-tag.el-tag--info {
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-color-info-light-7) !important;
  color: var(--el-color-info) !important;
}

/* 执行人员指派显示样式 */
.assignment-display {
  font-size: 12px;
  line-height: 1.4;
}

.assignment-flow {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.unassigned-display {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.role-tag {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  color: var(--el-text-color-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.arrow-symbol {
  color: var(--el-color-primary);
  font-weight: bold;
  font-size: 14px;
}

.assignee-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.assignee-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.assignee-item .assignee-tag-white {
  margin: 0;
  background: transparent !important;
  border: none !important;
  padding: 0 4px !important;
  font-weight: 600;
}

.assignee-item .status-tag {
  margin: 0;
  font-size: 10px;
  padding: 1px 4px;
  height: auto;
  line-height: 1.2;
}

/* 强制覆盖执行人员标签样式为白色 */
.assignee-tag-white {
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-border-color-light) !important;
  color: var(--el-text-color-primary) !important;
}

.unassigned-tag {
  background: var(--el-color-warning-light-9);
  border: 1px solid var(--el-color-warning-light-7);
  color: var(--el-color-warning-dark-2);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-style: italic;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.time-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  padding: 2px 0;
}

.time-label {
  color: var(--el-text-color-secondary);
  margin-right: 6px;
  min-width: 36px;
  font-weight: 500;
}

.time-value {
  color: var(--el-text-color-primary);
  font-weight: 600;
  font-size: 12px;
}

/* 时间标签样式 - 白色背景标签 */
.time-tags {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.time-tag {
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
}

.start-time-tag {
  border-left: 3px solid var(--el-color-success);
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.end-time-tag {
  border-left: 3px solid var(--el-color-warning);
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}



/* Dark模式下保持特定时间标签的左边框颜色 */
.dark .start-time-tag {
  border-left: 3px solid var(--el-color-success) !important;
}

.dark .end-time-tag {
  border-left: 3px solid var(--el-color-warning) !important;
}

/* Dark模式下的执行人员标签 - 使用CSS变量确保主题一致性 */
.dark .role-tag {
  background: var(--el-bg-color-page) !important;
  border: 1px solid var(--el-border-color) !important;
  color: var(--el-text-color-primary) !important;
}

.dark .assignee-tag-white {
  background: var(--el-bg-color-page) !important;
  border: 1px solid var(--el-border-color) !important;
  color: var(--el-text-color-primary) !important;
}

.dark .unassigned-tag {
  background: var(--el-color-warning-light-9) !important;
  border: 1px solid var(--el-color-warning-light-7) !important;
  color: var(--el-color-warning-dark-2) !important;
}

.step-node.step-pending .step-content {
  border-color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

.step-node.step-in-progress .step-content {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.step-node.step-completed .step-content {
  border-color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.step-node.step-empty .step-content {
  border-color: var(--el-border-color);
  background: var(--el-bg-color-page);
  opacity: 0.6;
}





.task-progress .progress-text {
  font-size: 12px;
  color: var(--el-color-success);
  font-weight: 600;
}

.step-details {
  margin: 12px 0;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.step-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 4px 0;
}

.step-detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
  min-width: 70px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 11px;
  color: var(--el-text-color-primary);
  font-weight: 500;
  text-align: right;
  flex: 1;
  line-height: 1.4;
  word-break: break-word;
}

.requirements-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.req-tag {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.req-tag.photo {
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

.req-tag.attachment {
  background: var(--el-color-warning-light-8);
  color: var(--el-color-warning);
}

.req-tag.none {
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
}

.step-progress-bar {
  margin-bottom: 8px;
}

.task-distribution {
  display: flex;
  justify-content: center;
  gap: 6px;
  flex-wrap: wrap;
}

.task-status-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 3px;
  background: var(--el-bg-color-page);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-dot.completed {
  background: var(--el-color-success);
}

.status-dot.in-progress {
  background: var(--el-color-primary);
}

.status-dot.pending {
  background: var(--el-color-warning);
}

.status-dot.pending-assign {
  background: var(--el-color-danger);
}

.status-count {
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.status-label {
  color: var(--el-text-color-secondary);
  font-size: 9px;
}

/* 连接线样式已移除 */

.step-tasks-detail {
  padding: 0;
}

.tasks-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.tasks-header h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 18px;
}

.tasks-header p {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.task-card {
  background: var(--el-bg-color);
  border: 2px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: var(--el-box-shadow-lighter);
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--el-box-shadow-light);
}

.task-card.task-pending-assign {
  border-color: var(--el-color-danger);
  background: var(--el-color-danger-light-9);
}

.task-card.task-pending-execute {
  border-color: var(--el-color-info);
  background: var(--el-color-info-light-9);
}

.task-card.task-executing {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.task-card.task-completed {
  border-color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.task-card.task-closed {
  border-color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

.task-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.task-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-name {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
  flex: 1;
  margin-right: 12px;
  line-height: 1.4;
}

.task-category {
  margin-top: 4px;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-section {
  background: var(--el-bg-color-page);
  border-radius: 8px;
  padding: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 6px;

  .add-assignee-btn,
  .edit-time-btn {
    margin-left: auto;
    font-size: 12px;
  }
}

.section-content {
  font-size: 13px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.task-description {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.task-activity,
.employee-behavior {
  margin-top: 6px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.executor-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 6px;
}

.assignee-container {
  flex: 1;
}

.execution-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.execution-item {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  padding: 8px;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;

  .remove-assignee-btn {
    flex-shrink: 0;
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }

  .assignee-avatar {
    flex-shrink: 0;
  }

  .assignee-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .assignee-name-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .assignee-name {
    font-weight: 600;
    color: var(--el-text-color-primary);
    font-size: 14px;
  }

  .status-tag-inline {
    margin: 0;
    font-size: 10px;
    padding: 2px 6px;
    height: auto;
    line-height: 1.2;
  }

  .assignee-badges {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .accept-time {
    color: var(--el-text-color-secondary);
    font-size: 11px;
    font-weight: 400;
  }
}

.execution-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  flex-wrap: wrap;
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
}

.task-actions .el-button {
  padding: 4px 8px;
  font-size: 11px;
  height: auto;
  min-height: 24px;
}

.primary-badge {
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.assistant-badge {
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.role-badge {
  background: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  border: 1px solid var(--el-border-color);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  margin-bottom: 8px;
}

.assignee-name {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.no-assignee .assignee-name {
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);

  .time-label {
    min-width: 80px;
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }

  .time-value {
    color: var(--el-text-color-primary);
    font-weight: 500;
    font-size: 12px;
  }

  .time-picker {
    flex: 1;
    max-width: 200px;
  }
}

.time-item.planned {
  border-left: 3px solid var(--el-color-info);
}

.time-item.actual {
  border-left: 3px solid var(--el-color-primary);
}

.time-item.completed {
  border-left: 3px solid var(--el-color-success);
}

.time-item.next {
  border-left: 3px solid var(--el-color-warning);
}

.time-item.created {
  border-left: 3px solid var(--el-border-color);
}

.time-label {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  font-weight: 500;
}

.time-value {
  color: var(--el-text-color-primary);
  font-weight: 600;
  font-size: 12px;
}

.location-info,
.highlight-info,
.remark-info {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

/* 完成对话框样式 */
.complete-progress {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
  border-radius: 8px;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.complete-progress .progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.complete-progress .progress-label {
  font-size: 14px;
  font-weight: 600;
  color: #059669;
}

.complete-progress .progress-text {
  font-size: 14px;
  font-weight: 700;
  color: #047857;
}

.el-upload__tip {
  color: #6b7280;
  font-size: 12px;
  margin-top: 4px;
}

/* 卡片操作按钮样式 */
.card-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: all 0.3s ease;
}

.step-card:hover .card-actions {
  opacity: 1;
}

.card-actions .el-button {
  width: 28px;
  height: 28px;
  padding: 0;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.card-actions .el-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 步骤操作对话框样式 */
.step-operation-info {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-radius: 8px;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.step-operation-info h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.step-operation-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 任务卡片操作按钮样式 */
.task-card-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(139, 92, 246, 0.03) 100%);
  border-radius: 8px;
  border: 1px solid rgba(99, 102, 241, 0.08);
}

.task-card-actions .el-button {
  flex: 1;
  font-size: 12px;
  padding: 8px 12px;
}

.task-card-actions .el-button + .el-button {
  margin-left: 0;
}

/* 任务操作对话框样式 */
.task-operation-info {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-radius: 8px;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.task-operation-info h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.task-operation-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.requirements {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.requirement-item {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
  border: 1px solid var(--el-color-info-light-5);
}

.requirement-item.active {
  background: var(--el-color-success-light-8);
  color: var(--el-color-success);
  border-color: var(--el-color-success-light-5);
}

.inline-edit-btn {
  padding: 2px 4px;
  height: auto;
  font-size: 10px;
}

/* 暗色主题适配 */
html.dark {
  /* 卡片头部状态指示器在暗色主题下的适配 */
  .status-indicator {
    background: rgba(31, 41, 55, 0.8) !important;
    border: 1px solid rgba(75, 85, 99, 0.3) !important;
    backdrop-filter: blur(10px);
  }

  .status-text {
    color: var(--el-text-color-primary) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  }

  /* 步骤标题在暗色主题下的适配 */
  .step-title {
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
  }

  /* 信息标签在暗色主题下的适配 */
  .info-label {
    color: var(--el-text-color-regular) !important;
  }

  /* 操作对话框在暗色主题下的适配 */
  .step-operation-info h4,
  .task-operation-info h4 {
    color: var(--el-text-color-primary) !important;
  }

  .step-operation-info p,
  .task-operation-info p {
    color: var(--el-text-color-regular) !important;
  }

  /* 角色标签在暗色主题下的适配 - 使用系统变量 */
  .role-badge {
    background: var(--el-bg-color-page) !important;
    color: var(--el-text-color-primary) !important;
    border: 1px solid var(--el-border-color) !important;
  }

  /* 主要/助理标签在暗色主题下的适配 */
  .primary-badge {
    background: rgba(99, 102, 241, 0.15) !important;
    color: #a5b4fc !important;
    border: 1px solid rgba(99, 102, 241, 0.3);
  }

  .assistant-badge {
    background: rgba(107, 114, 128, 0.15) !important;
    color: #d1d5db !important;
    border: 1px solid rgba(107, 114, 128, 0.3);
  }

  /* 需求标签在暗色主题下的适配 */
  .requirement-item {
    background: rgba(107, 114, 128, 0.15) !important;
    color: #d1d5db !important;
    border-color: rgba(107, 114, 128, 0.3) !important;
  }

  .requirement-item.active {
    background: rgba(16, 185, 129, 0.15) !important;
    color: #6ee7b7 !important;
    border-color: rgba(16, 185, 129, 0.3) !important;
  }

  /* 需求徽章在暗色主题下的适配 */
  .req-badge.photo {
    background: rgba(59, 130, 246, 0.15) !important;
    color: #93c5fd !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
  }

  .req-badge.attachment {
    background: rgba(245, 158, 11, 0.15) !important;
    color: #fcd34d !important;
    border-color: rgba(245, 158, 11, 0.3) !important;
  }

  .req-badge.none {
    background: rgba(107, 114, 128, 0.15) !important;
    color: #d1d5db !important;
    border-color: rgba(107, 114, 128, 0.3) !important;
  }

  /* 任务类别和状态标签的暗色主题适配 */
  :deep(.el-tag) {
    background: var(--el-bg-color) !important;
    border-color: var(--el-border-color) !important;
    color: var(--el-text-color-primary) !important;
  }

  :deep(.el-tag.el-tag--primary) {
    background: rgba(99, 102, 241, 0.15) !important;
    border-color: rgba(99, 102, 241, 0.3) !important;
    color: #a5b4fc !important;
  }

  :deep(.el-tag.el-tag--success) {
    background: rgba(16, 185, 129, 0.15) !important;
    border-color: rgba(16, 185, 129, 0.3) !important;
    color: #6ee7b7 !important;
  }

  :deep(.el-tag.el-tag--warning) {
    background: rgba(245, 158, 11, 0.15) !important;
    border-color: rgba(245, 158, 11, 0.3) !important;
    color: #fcd34d !important;
  }

  :deep(.el-tag.el-tag--danger) {
    background: rgba(239, 68, 68, 0.15) !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
    color: #fca5a5 !important;
  }

  :deep(.el-tag.el-tag--info) {
    background: rgba(107, 114, 128, 0.15) !important;
    border-color: rgba(107, 114, 128, 0.3) !important;
    color: #d1d5db !important;
  }

  /* 卡片主体信息项在暗色主题下的适配 */
  .info-item {
    border-color: rgba(75, 85, 99, 0.2) !important;
    background: rgba(31, 41, 55, 0.3) !important;
  }

  .info-item:hover {
    border-color: rgba(99, 102, 241, 0.4) !important;
    background: rgba(31, 41, 55, 0.5) !important;
  }

  .info-icon {
    background: rgba(99, 102, 241, 0.15) !important;
    border-color: rgba(99, 102, 241, 0.3) !important;
  }

  .info-value {
    color: var(--el-text-color-primary) !important;
  }

  /* 卡片头部在暗色主题下的适配 */
  .card-header {
    background: rgba(75, 85, 99, 0.1) !important;
    border-bottom-color: rgba(75, 85, 99, 0.2) !important;
  }

  /* 执行人项目在暗色主题下的适配 */
  .assignee-item {
    background: var(--el-bg-color) !important;
    border-color: var(--el-border-color) !important;
  }

  .assignee-item .assignee-tag-white {
    color: var(--el-text-color-primary) !important;
  }

  .status-tag-inline {
    background: var(--el-bg-color-page) !important;
    border-color: var(--el-border-color) !important;
  }

  .assignee-name-row .assignee-name {
    color: var(--el-text-color-primary) !important;
  }

  .accept-time {
    color: var(--el-text-color-secondary) !important;
  }

  /* 完成进度对话框在暗色主题下的适配 */
  .complete-progress {
    background: rgba(16, 185, 129, 0.1) !important;
    border-color: rgba(16, 185, 129, 0.2) !important;
  }

  .complete-progress .progress-label {
    color: #6ee7b7 !important;
  }

  .complete-progress .progress-text {
    color: #34d399 !important;
  }
}
</style>
