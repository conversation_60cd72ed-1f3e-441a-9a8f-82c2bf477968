<template>
  <div class="task-timeline">
    <!-- 场景概览 -->
    <div class="scenario-overview">
      <div class="overview-header">
        <h3>{{ scenario.packageName }}</h3>
        <div class="overview-stats">
          <el-tag type="success">{{ completedCount }}个已完成</el-tag>
          <el-tag type="primary">{{ inProgressCount }}个进行中</el-tag>
          <el-tag type="warning">{{ pendingCount }}个待分配</el-tag>
        </div>
      </div>
      
      <div class="overall-progress">
        <div class="progress-label">
          <span>整体进度</span>
          <span class="progress-text">{{ overallProgress }}%</span>
        </div>
        <el-progress 
          :percentage="overallProgress" 
          :stroke-width="12"
          :color="getProgressColor(overallProgress)"
        />
      </div>
    </div>

    <!-- 时间轴容器 -->
    <div class="timeline-container">
      <div class="timeline-header">
        <h4>任务执行时间轴</h4>
        <div class="timeline-legend">
          <div class="legend-item">
            <div class="legend-color pending"></div>
            <span>待分配</span>
          </div>
          <div class="legend-item">
            <div class="legend-color in-progress"></div>
            <span>执行中</span>
          </div>
          <div class="legend-item">
            <div class="legend-color completed"></div>
            <span>已完成</span>
          </div>
        </div>
      </div>

      <!-- 时间轴主体 -->
      <div class="timeline-content" ref="timelineRef">
        <div class="timeline-track">
          <!-- 时间刻度 -->
          <div class="time-scale">
            <div 
              v-for="(timePoint, index) in timeScale" 
              :key="index"
              class="time-point"
              :style="{ left: timePoint.position + '%' }"
            >
              <div class="time-label">{{ timePoint.label }}</div>
              <div class="time-line"></div>
            </div>
          </div>

          <!-- 任务卡片轨道 -->
          <div class="task-tracks">
            <div 
              v-for="(track, trackIndex) in taskTracks" 
              :key="trackIndex"
              class="task-track"
            >
              <div 
                v-for="task in track" 
                :key="task.id"
                class="task-card"
                :class="getTaskStatusClass(task.taskStatus)"
                :style="getTaskCardStyle(task)"
                @click="showTaskDetail(task)"
              >
                <div class="task-card-header">
                  <div class="task-title">{{ task.name }}</div>
                  <div class="task-status">
                    <el-tag 
                      :type="getTaskStatusType(task.taskStatus)" 
                      size="small"
                    >
                      {{ getTaskStatusLabel(task.taskStatus) }}
                    </el-tag>
                  </div>
                </div>
                
                <div class="task-card-body">
                  <div class="task-info">
                    <div class="info-item">
                      <span class="label">步骤:</span>
                      <span class="value">{{ task.stepName || '通用任务' }}</span>
                    </div>
                    <div class="info-item" v-if="task.employeeRole">
                      <span class="label">角色:</span>
                      <span class="value">{{ task.employeeRole }}</span>
                    </div>
                  </div>
                  
                  <div class="task-time" v-if="task.startTime || task.endTime">
                    <div v-if="task.startTime" class="time-item">
                      <el-icon><Clock /></el-icon>
                      <span>{{ formatTime(task.startTime) }}</span>
                    </div>
                    <div v-if="task.endTime" class="time-item">
                      <el-icon><Check /></el-icon>
                      <span>{{ formatTime(task.endTime) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog
      v-model="taskDetailDialog.visible"
      :title="taskDetailDialog.task?.name"
      width="600px"
      class="task-detail-dialog"
    >
      <div v-if="taskDetailDialog.task" class="task-detail-content">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">任务名称:</span>
              <span class="value">{{ taskDetailDialog.task.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">任务状态:</span>
              <el-tag :type="getTaskStatusType(taskDetailDialog.task.taskStatus)">
                {{ getTaskStatusLabel(taskDetailDialog.task.taskStatus) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <span class="label">步骤名称:</span>
              <span class="value">{{ taskDetailDialog.task.stepName || '通用任务' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">员工角色:</span>
              <span class="value">{{ taskDetailDialog.task.employeeRole || '-' }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="taskDetailDialog.task.description">
          <h4>任务描述</h4>
          <p class="description">{{ taskDetailDialog.task.description }}</p>
        </div>

        <div class="detail-section">
          <h4>时间信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatTime(taskDetailDialog.task.createTime) }}</span>
            </div>
            <div class="detail-item" v-if="taskDetailDialog.task.startTime">
              <span class="label">开始时间:</span>
              <span class="value">{{ formatTime(taskDetailDialog.task.startTime) }}</span>
            </div>
            <div class="detail-item" v-if="taskDetailDialog.task.endTime">
              <span class="label">结束时间:</span>
              <span class="value">{{ formatTime(taskDetailDialog.task.endTime) }}</span>
            </div>
            <div class="detail-item" v-if="taskDetailDialog.task.completionTime">
              <span class="label">完成时间:</span>
              <span class="value">{{ formatTime(taskDetailDialog.task.completionTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { Clock, Check } from '@element-plus/icons-vue';
import { 
  taskBusinessStatusOptions,
  getTaskStatusLabel,
  getTaskStatusColor
} from '../dict';
// import TaskDetailCard from './task-detail-card.vue';

// Props
const props = defineProps({
  scenario: {
    type: Object,
    required: true
  },
  tasks: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['task-updated']);

// 响应式数据
const timelineRef = ref();
const taskDetailDialog = reactive({
  visible: false,
  task: null
});

// 计算属性
const completedCount = computed(() => 
  props.tasks.filter(task => task.taskStatus === 3).length
);

const inProgressCount = computed(() => 
  props.tasks.filter(task => task.taskStatus === 2).length
);

const pendingCount = computed(() => 
  props.tasks.filter(task => task.taskStatus === 0).length
);

const overallProgress = computed(() => {
  if (props.tasks.length === 0) return 0;
  return Math.round((completedCount.value / props.tasks.length) * 100);
});

// 生成时间刻度
const timeScale = computed(() => {
  const points = [];
  const now = new Date();
  
  // 生成过去7天到未来7天的时间点
  for (let i = -7; i <= 7; i++) {
    const date = new Date(now);
    date.setDate(date.getDate() + i);
    
    points.push({
      label: formatDate(date),
      position: ((i + 7) / 14) * 100
    });
  }
  
  return points;
});

// 生成任务轨道
const taskTracks = computed(() => {
  const tracks = [];
  const sortedTasks = [...props.tasks].sort((a, b) => {
    // 按步骤顺序排序，如果没有步骤顺序则按创建时间
    if (a.stepOrder && b.stepOrder) {
      return a.stepOrder - b.stepOrder;
    }
    return new Date(a.createTime) - new Date(b.createTime);
  });

  // 将任务分配到不同轨道，避免重叠
  sortedTasks.forEach(task => {
    let placed = false;
    
    for (let i = 0; i < tracks.length; i++) {
      if (canPlaceInTrack(task, tracks[i])) {
        tracks[i].push(task);
        placed = true;
        break;
      }
    }
    
    if (!placed) {
      tracks.push([task]);
    }
  });

  return tracks;
});

// 检查任务是否可以放在指定轨道
const canPlaceInTrack = (task, track) => {
  const taskStart = getTaskTimePosition(task, 'start');
  const taskEnd = getTaskTimePosition(task, 'end');
  
  return !track.some(existingTask => {
    const existingStart = getTaskTimePosition(existingTask, 'start');
    const existingEnd = getTaskTimePosition(existingTask, 'end');
    
    return !(taskEnd <= existingStart || taskStart >= existingEnd);
  });
};

// 获取任务时间位置（百分比）
const getTaskTimePosition = (task, type) => {
  const now = new Date();
  const baseTime = new Date(now);
  baseTime.setDate(baseTime.getDate() - 7); // 时间轴起始点
  
  let targetTime;
  if (type === 'start') {
    targetTime = task.startTime ? new Date(task.startTime) : new Date(task.createTime);
  } else {
    targetTime = task.endTime ? new Date(task.endTime) : 
                 task.startTime ? new Date(new Date(task.startTime).getTime() + 24 * 60 * 60 * 1000) :
                 new Date(new Date(task.createTime).getTime() + 24 * 60 * 60 * 1000);
  }
  
  const totalDuration = 14 * 24 * 60 * 60 * 1000; // 14天的毫秒数
  const taskOffset = targetTime.getTime() - baseTime.getTime();
  
  return Math.max(0, Math.min(100, (taskOffset / totalDuration) * 100));
};

// 获取任务卡片样式
const getTaskCardStyle = (task) => {
  const startPos = getTaskTimePosition(task, 'start');
  const endPos = getTaskTimePosition(task, 'end');
  const width = Math.max(8, endPos - startPos); // 最小宽度8%
  
  return {
    left: startPos + '%',
    width: width + '%'
  };
};

// 获取任务状态类名
const getTaskStatusClass = (status) => {
  const classes = {
    0: 'status-pending',
    1: 'status-assigned', 
    2: 'status-in-progress',
    3: 'status-completed',
    4: 'status-closed'
  };
  return classes[status] || 'status-unknown';
};

// 获取任务状态类型
const getTaskStatusType = (status) => {
  return getTaskStatusColor(status);
};

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 30) return '#f56c6c';
  if (percentage < 70) return '#e6a23c';
  return '#67c23a';
};

// 显示任务详情
const showTaskDetail = (task) => {
  taskDetailDialog.task = task;
  taskDetailDialog.visible = true;
};

// 任务更新回调
const handleTaskUpdated = (updatedTask) => {
  emit('task-updated', updatedTask);
  taskDetailDialog.visible = false;
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化日期
const formatDate = (date) => {
  const today = new Date();
  const diffDays = Math.floor((date - today) / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return '今天';
  if (diffDays === 1) return '明天';
  if (diffDays === -1) return '昨天';
  
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  });
};

onMounted(() => {
  // 组件挂载后的初始化逻辑
});
</script>

<style lang="scss" scoped>
.task-timeline {
  .scenario-overview {
    margin-bottom: 32px;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    
    .overview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }
      
      .overview-stats {
        display: flex;
        gap: 12px;
      }
    }
    
    .overall-progress {
      .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
        
        .progress-text {
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
  }
  
  .timeline-container {
    .timeline-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      
      h4 {
        margin: 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }
      
      .timeline-legend {
        display: flex;
        gap: 16px;
        
        .legend-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #606266;
          
          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            
            &.pending { background-color: #e6a23c; }
            &.in-progress { background-color: #409eff; }
            &.completed { background-color: #67c23a; }
          }
        }
      }
    }
    
    .timeline-content {
      position: relative;
      min-height: 400px;
      background: #fafbfc;
      border-radius: 8px;
      padding: 20px;
      overflow-x: auto;
      
      .timeline-track {
        position: relative;
        min-width: 800px;
        
        .time-scale {
          position: relative;
          height: 40px;
          margin-bottom: 20px;
          
          .time-point {
            position: absolute;
            transform: translateX(-50%);
            
            .time-label {
              font-size: 12px;
              color: #909399;
              text-align: center;
              margin-bottom: 8px;
            }
            
            .time-line {
              width: 1px;
              height: 20px;
              background-color: #dcdfe6;
            }
          }
        }
        
        .task-tracks {
          .task-track {
            position: relative;
            height: 120px;
            margin-bottom: 16px;
            
            .task-card {
              position: absolute;
              height: 100px;
              background: white;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              cursor: pointer;
              transition: all 0.3s ease;
              border-left: 4px solid;
              
              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
              }
              
              &.status-pending {
                border-left-color: #e6a23c;
              }
              
              &.status-in-progress {
                border-left-color: #409eff;
              }
              
              &.status-completed {
                border-left-color: #67c23a;
              }
              
              .task-card-header {
                padding: 8px 12px;
                border-bottom: 1px solid #f0f2f5;
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .task-title {
                  font-size: 14px;
                  font-weight: 600;
                  color: #303133;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  flex: 1;
                  margin-right: 8px;
                }
              }
              
              .task-card-body {
                padding: 8px 12px;
                
                .task-info {
                  margin-bottom: 8px;
                  
                  .info-item {
                    display: flex;
                    margin-bottom: 4px;
                    font-size: 12px;
                    
                    .label {
                      color: #909399;
                      margin-right: 4px;
                      min-width: 30px;
                    }
                    
                    .value {
                      color: #606266;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    }
                  }
                }
                
                .task-time {
                  .time-item {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 11px;
                    color: #909399;
                    margin-bottom: 2px;
                    
                    .el-icon {
                      font-size: 12px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.task-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .task-detail-content {
    .detail-section {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }

      .detail-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .detail-item {
          display: flex;
          align-items: center;

          .label {
            color: #909399;
            margin-right: 8px;
            min-width: 80px;
            font-size: 14px;
          }

          .value {
            color: #606266;
            font-size: 14px;
          }
        }
      }

      .description {
        color: #606266;
        line-height: 1.6;
        margin: 0;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
      }
    }
  }
}
</style>
