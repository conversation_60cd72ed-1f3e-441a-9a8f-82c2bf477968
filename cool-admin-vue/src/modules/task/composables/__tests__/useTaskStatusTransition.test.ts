import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useTaskStatusTransition } from '../useTaskStatusTransition'

// Mock service
const mockService = {
  task: {
    status: {
      getAvailableActions: vi.fn(),
      checkTransition: vi.fn(),
      changeStatus: vi.fn(),
      batchChangeStatus: vi.fn(),
      getStatusChangeHistory: vi.fn(),
      getTaskHistory: vi.fn()
    }
  }
}

// Mock cool service
vi.mock('/@/cool', () => ({
  service: mockService
}))

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn()
  }
}))

describe('useTaskStatusTransition', () => {
  let composable: ReturnType<typeof useTaskStatusTransition>

  beforeEach(() => {
    composable = useTaskStatusTransition()
    vi.clearAllMocks()
  })

  describe('状态信息获取', () => {
    it('应该正确获取状态信息', () => {
      const statusInfo = composable.getStatusInfo(0)
      expect(statusInfo).toEqual({
        value: 0,
        label: '待分配',
        type: 'info',
        color: '#909399'
      })

      const statusInfo2 = composable.getStatusInfo(3)
      expect(statusInfo2).toEqual({
        value: 3,
        label: '已完成',
        type: 'success',
        color: '#67c23a'
      })
    })

    it('应该正确获取状态标签', () => {
      expect(composable.getStatusLabel(0)).toBe('待分配')
      expect(composable.getStatusLabel(1)).toBe('待执行')
      expect(composable.getStatusLabel(2)).toBe('执行中')
      expect(composable.getStatusLabel(3)).toBe('已完成')
      expect(composable.getStatusLabel(4)).toBe('已关闭')
    })

    it('应该正确获取状态颜色', () => {
      expect(composable.getStatusColor(0)).toBe('#909399')
      expect(composable.getStatusColor(1)).toBe('#e6a23c')
      expect(composable.getStatusColor(2)).toBe('#409eff')
      expect(composable.getStatusColor(3)).toBe('#67c23a')
      expect(composable.getStatusColor(4)).toBe('#f56c6c')
    })
  })

  describe('状态转换验证', () => {
    it('应该正确验证有效的状态转换', () => {
      // 待分配的有效转换
      expect(composable.isValidTransition(0, 1)).toBe(true) // 待分配 -> 待执行
      expect(composable.isValidTransition(0, 4)).toBe(true) // 待分配 -> 已关闭

      // 待执行的有效转换
      expect(composable.isValidTransition(1, 2)).toBe(true) // 待执行 -> 执行中
      expect(composable.isValidTransition(1, 4)).toBe(true) // 待执行 -> 已关闭

      // 执行中的有效转换
      expect(composable.isValidTransition(2, 3)).toBe(true) // 执行中 -> 已完成
      expect(composable.isValidTransition(2, 4)).toBe(true) // 执行中 -> 已关闭

      // 已完成的有效转换
      expect(composable.isValidTransition(3, 2)).toBe(true) // 已完成 -> 执行中

      // 已关闭的有效转换
      expect(composable.isValidTransition(4, 1)).toBe(true) // 已关闭 -> 待执行
    })

    it('应该正确验证无效的状态转换', () => {
      // 跨级转换（无效）
      expect(composable.isValidTransition(0, 2)).toBe(false) // 待分配 -> 执行中
      expect(composable.isValidTransition(0, 3)).toBe(false) // 待分配 -> 已完成
      expect(composable.isValidTransition(1, 3)).toBe(false) // 待执行 -> 已完成

      // 逆向转换（无效）
      expect(composable.isValidTransition(3, 0)).toBe(false) // 已完成 -> 待分配
      expect(composable.isValidTransition(3, 1)).toBe(false) // 已完成 -> 待执行
      expect(composable.isValidTransition(4, 0)).toBe(false) // 已关闭 -> 待分配
    })

    it('应该正确获取可转换的状态列表', () => {
      const transitions0 = composable.getAvailableTransitions(0)
      expect(transitions0).toHaveLength(2)
      expect(transitions0.map(t => t.value)).toEqual([1, 4])

      const transitions1 = composable.getAvailableTransitions(1)
      expect(transitions1).toHaveLength(2)
      expect(transitions1.map(t => t.value)).toEqual([2, 4])

      const transitions2 = composable.getAvailableTransitions(2)
      expect(transitions2).toHaveLength(2)
      expect(transitions2.map(t => t.value)).toEqual([3, 4])

      const transitions3 = composable.getAvailableTransitions(3)
      expect(transitions3).toHaveLength(1)
      expect(transitions3.map(t => t.value)).toEqual([2])

      const transitions4 = composable.getAvailableTransitions(4)
      expect(transitions4).toHaveLength(1)
      expect(transitions4.map(t => t.value)).toEqual([1])
    })
  })

  describe('API调用', () => {
    it('应该正确调用获取可执行操作API', async () => {
      const mockActions = ['ASSIGN', 'CLOSE']
      mockService.task.status.getAvailableActions.mockResolvedValue(mockActions)

      const result = await composable.getAvailableActions(1, 1)

      expect(mockService.task.status.getAvailableActions).toHaveBeenCalledWith(1, 1)
      expect(result).toEqual(mockActions)
    })

    it('应该正确调用状态变更API', async () => {
      const mockResult = {
        success: true,
        taskId: 1,
        oldStatus: 0,
        newStatus: 1
      }
      mockService.task.status.changeStatus.mockResolvedValue(mockResult)

      const request = {
        taskId: 1,
        targetStatus: 1,
        reason: '分配任务'
      }

      const result = await composable.changeTaskStatus(request)

      expect(mockService.task.status.changeStatus).toHaveBeenCalledWith(request)
      expect(result).toEqual(mockResult)
    })

    it('应该正确调用批量状态变更API', async () => {
      const mockResult = {
        success: 2,
        failed: 0,
        total: 2
      }
      mockService.task.status.batchChangeStatus.mockResolvedValue(mockResult)

      const result = await composable.batchChangeStatus([1, 2], 3, '批量完成')

      expect(mockService.task.status.batchChangeStatus).toHaveBeenCalledWith({
        taskIds: [1, 2],
        targetStatus: 3,
        reason: '批量完成'
      })
      expect(result).toEqual(mockResult)
    })

    it('应该正确处理API调用失败', async () => {
      mockService.task.status.changeStatus.mockRejectedValue(new Error('网络错误'))

      const request = {
        taskId: 1,
        targetStatus: 1,
        reason: '测试失败'
      }

      const result = await composable.changeTaskStatus(request)

      expect(result).toBeNull()
    })
  })

  describe('工具方法', () => {
    it('应该正确格式化状态变更描述', () => {
      const description1 = composable.formatStatusChangeDescription(0, 1, '分配给张三')
      expect(description1).toBe('待分配 → 待执行：分配给张三')

      const description2 = composable.formatStatusChangeDescription(2, 3)
      expect(description2).toBe('执行中 → 已完成')
    })

    it('应该正确获取操作按钮配置', () => {
      const assignConfig = composable.getActionButtonConfig('ASSIGN')
      expect(assignConfig).toEqual({
        label: '分配任务',
        icon: 'User',
        color: '#409eff'
      })

      const completeConfig = composable.getActionButtonConfig('COMPLETE')
      expect(completeConfig).toEqual({
        label: '完成任务',
        icon: 'Check',
        color: '#67c23a'
      })

      const unknownConfig = composable.getActionButtonConfig('UNKNOWN')
      expect(unknownConfig).toEqual({
        label: 'UNKNOWN',
        icon: 'Operation',
        color: '#409eff'
      })
    })
  })

  describe('状态管理', () => {
    it('应该正确管理loading状态', () => {
      expect(composable.loading.value).toBe(false)
      expect(composable.submitting.value).toBe(false)
    })

    it('应该包含正确的状态定义', () => {
      expect(composable.taskStatuses).toHaveLength(5)
      expect(composable.taskStatuses[0]).toEqual({
        value: 0,
        label: '待分配',
        type: 'info',
        color: '#909399'
      })
    })

    it('应该包含正确的转换规则', () => {
      expect(composable.transitionRules[0]).toEqual([1, 4])
      expect(composable.transitionRules[1]).toEqual([2, 4])
      expect(composable.transitionRules[2]).toEqual([3, 4])
      expect(composable.transitionRules[3]).toEqual([2])
      expect(composable.transitionRules[4]).toEqual([1])
    })

    it('应该包含正确的操作类型定义', () => {
      expect(composable.actionTypes.ASSIGN).toEqual({
        label: '分配任务',
        icon: 'User',
        color: '#409eff'
      })
      expect(composable.actionTypes.COMPLETE).toEqual({
        label: '完成任务',
        icon: 'Check',
        color: '#67c23a'
      })
    })
  })
})
