import { ref, nextTick, watch } from 'vue'

/**
 * 防止对话框闪动的组合式函数
 */
export function useDialogStable() {
  const isStable = ref(true)
  const stabilizeTimer = ref<number | null>(null)

  /**
   * 稳定化对话框显示
   * @param visible 对话框可见性
   * @param callback 稳定后的回调
   */
  const stabilizeDialog = (visible: boolean, callback?: () => void) => {
    if (stabilizeTimer.value) {
      clearTimeout(stabilizeTimer.value)
    }

    if (visible) {
      // 显示对话框时立即设置为稳定
      isStable.value = true
      nextTick(() => {
        callback?.()
      })
    } else {
      // 隐藏对话框时延迟设置为不稳定
      isStable.value = false
      stabilizeTimer.value = window.setTimeout(() => {
        callback?.()
        stabilizeTimer.value = null
      }, 300) // 等待动画完成
    }
  }

  /**
   * 防抖更新函数
   * @param fn 要执行的函数
   * @param delay 延迟时间
   */
  const debounceUpdate = (fn: () => void, delay = 100) => {
    if (stabilizeTimer.value) {
      clearTimeout(stabilizeTimer.value)
    }
    
    stabilizeTimer.value = window.setTimeout(() => {
      fn()
      stabilizeTimer.value = null
    }, delay)
  }

  /**
   * 平滑的状态更新
   * @param updateFn 更新函数
   */
  const smoothUpdate = async (updateFn: () => void) => {
    isStable.value = false
    await nextTick()
    updateFn()
    await nextTick()
    isStable.value = true
  }

  /**
   * 监听对话框可见性变化
   * @param visible 可见性ref
   * @param onShow 显示时的回调
   * @param onHide 隐藏时的回调
   */
  const watchDialogVisible = (
    visible: any,
    onShow?: () => void,
    onHide?: () => void
  ) => {
    watch(
      visible,
      (newVal, oldVal) => {
        if (newVal && !oldVal) {
          // 对话框显示
          stabilizeDialog(true, onShow)
        } else if (!newVal && oldVal) {
          // 对话框隐藏
          stabilizeDialog(false, onHide)
        }
      },
      { immediate: false }
    )
  }

  /**
   * 清理定时器
   */
  const cleanup = () => {
    if (stabilizeTimer.value) {
      clearTimeout(stabilizeTimer.value)
      stabilizeTimer.value = null
    }
  }

  return {
    isStable,
    stabilizeDialog,
    debounceUpdate,
    smoothUpdate,
    watchDialogVisible,
    cleanup
  }
}

/**
 * 防闪动的表单重置
 */
export function useStableFormReset() {
  const isResetting = ref(false)

  const stableReset = async (resetFn: () => void, delay = 50) => {
    if (isResetting.value) return

    isResetting.value = true
    
    // 延迟重置，避免闪动
    await new Promise(resolve => setTimeout(resolve, delay))
    
    resetFn()
    
    await nextTick()
    isResetting.value = false
  }

  return {
    isResetting,
    stableReset
  }
}

/**
 * 防闪动的计算属性
 */
export function useStableComputed<T>(
  computeFn: () => T,
  defaultValue: T,
  delay = 50
) {
  const value = ref<T>(defaultValue)
  const updateTimer = ref<number | null>(null)

  const updateValue = () => {
    if (updateTimer.value) {
      clearTimeout(updateTimer.value)
    }

    updateTimer.value = window.setTimeout(() => {
      const newValue = computeFn()
      if (JSON.stringify(newValue) !== JSON.stringify(value.value)) {
        value.value = newValue
      }
      updateTimer.value = null
    }, delay)
  }

  const cleanup = () => {
    if (updateTimer.value) {
      clearTimeout(updateTimer.value)
      updateTimer.value = null
    }
  }

  return {
    value,
    updateValue,
    cleanup
  }
}
