import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useCool } from '/@/cool'
import { useUserStore } from '/@/modules/base/store/user'

// 任务创建上下文接口
export interface TaskCreationContext {
  departmentId?: number
  departmentName?: string
  projectId?: number
  projectName?: string
  suggestedAssignee?: number
  suggestedAssigneeName?: string
  parentTaskId?: number
  scenarioId?: number
  stepId?: number
}

// 任务创建选项接口
export interface TaskCreationOptions {
  title?: string
  mode?: 'quick' | 'full'
  context?: TaskCreationContext
  defaultValues?: Partial<QuickTaskForm>
  onSuccess?: (task: any) => void
  onCancel?: () => void
}

// 快速任务表单接口
export interface QuickTaskForm {
  name: string
  description?: string
  taskCategory: string
  priority: number
  departmentId?: number
  projectId?: number
  assigneeId?: number
  startTime?: string
  endTime?: string
  taskStatus: number
}

// 全局任务创建器状态
const visible = ref(false)
const options = ref<TaskCreationOptions>({})
const submitting = ref(false)

/**
 * 全局任务创建器 Composable
 */
export function useGlobalTaskCreator() {
  const { service } = useCool()
  const userStore = useUserStore()

  // 添加错误处理
  if (!service) {
    console.error('service 未初始化')
    throw new Error('service 未初始化')
  }

  // 显示快速创建对话框
  const showQuickCreate = (opts?: TaskCreationOptions) => {
    options.value = {
      title: '快速创建任务',
      mode: 'quick',
      ...opts
    }
    visible.value = true
  }

  // 显示完整创建对话框
  const showFullCreate = (opts?: TaskCreationOptions) => {
    options.value = {
      title: '创建任务',
      mode: 'full',
      ...opts
    }
    visible.value = true
  }

  // 关闭对话框
  const close = () => {
    visible.value = false
    submitting.value = false
    // 清理选项
    setTimeout(() => {
      options.value = {}
    }, 300)
  }

  // 获取智能默认值
  const getSmartDefaults = (context?: TaskCreationContext): Partial<QuickTaskForm> => {
    const now = new Date()
    const endOfDay = new Date(now)
    endOfDay.setHours(23, 59, 59, 999)

    return {
      // 任务分类
      taskCategory: 'LS', // 临时任务
      priority: 3,        // 中等优先级
      taskStatus: 0,      // 待分配

      // 时间设置
      startTime: now.toISOString(),
      endTime: endOfDay.toISOString(),

      // 归属推断
      departmentId: context?.departmentId || getCurrentUserDepartment(),
      projectId: context?.projectId,

      // 执行人推断
      assigneeId: context?.suggestedAssignee
    }
  }

  // 获取当前用户部门ID
  const getCurrentUserDepartment = (): number | undefined => {
    // 从用户信息中获取部门ID
    return userStore.info?.departmentId
  }

  // 获取场景化默认值
  const getScenarioDefaults = (scenario: string, context?: TaskCreationContext) => {
    const baseDefaults = getSmartDefaults(context)

    const scenarioDefaults = {
      // 项目页面创建
      project: {
        ...baseDefaults,
        projectId: context?.projectId,
        departmentId: context?.departmentId,
        taskCategory: 'LS'
      },

      // 部门页面创建
      department: {
        ...baseDefaults,
        departmentId: context?.departmentId,
        taskCategory: 'RC' // 日常任务
      },

      // 紧急任务创建
      urgent: {
        ...baseDefaults,
        priority: 5, // 紧急
        taskCategory: 'LS',
        startTime: new Date().toISOString()
      },

      // 维修任务
      maintenance: {
        ...baseDefaults,
        taskCategory: 'LS',
        priority: 4,
        description: '设备维修任务'
      },

      // 巡检任务
      inspection: {
        ...baseDefaults,
        taskCategory: 'RC',
        priority: 2,
        description: '定期巡检任务'
      }
    }

    return scenarioDefaults[scenario as keyof typeof scenarioDefaults] || baseDefaults
  }

  // 创建任务
  const createTask = async (formData: QuickTaskForm): Promise<any> => {
    submitting.value = true

    try {
      // 数据预处理
      const taskData = preprocessTaskData(formData)

      console.log('创建任务数据:', taskData)

      // 调用创建API - 使用更安全的方式
      let result
      try {
        result = await service.task.info.add(taskData)
      } catch (apiError) {
        console.error('API调用失败:', apiError)
        // 尝试使用通用请求方法
        result = await service.request({
          url: '/admin/task/info',
          method: 'POST',
          data: taskData
        })
      }

      console.log('任务创建结果:', result)

      // 后处理
      if (result?.id && formData.assigneeId) {
        try {
          await postProcessTask(result.id, formData)
        } catch (postError) {
          console.warn('任务后处理失败:', postError)
          // 不阻断主流程
        }
      }

      ElMessage.success('任务创建成功')

      // 调用成功回调
      if (options.value.onSuccess) {
        options.value.onSuccess(result)
      }

      // 关闭对话框
      close()

      return result

    } catch (error) {
      console.error('任务创建失败:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      ElMessage.error('任务创建失败: ' + errorMessage)
      throw error
    } finally {
      submitting.value = false
    }
  }

  // 数据预处理
  const preprocessTaskData = (data: QuickTaskForm) => {
    return {
      ...data,
      taskCategory: data.taskCategory || 'LS',
      priority: data.priority || 3,
      taskStatus: data.taskStatus || 0,
      scheduleStatus: 1, // 启用调度
      scheduleType: 1,   // 立即执行
      type: 1,          // 普通任务
      // 时间字段处理
      startTime: data.startTime ? new Date(data.startTime).toISOString() : undefined,
      endTime: data.endTime ? new Date(data.endTime).toISOString() : undefined
    }
  }

  // 任务后处理
  const postProcessTask = async (taskId: number, data: QuickTaskForm) => {
    try {
      // 如果指定了执行人，自动分配任务
      if (data.assigneeId) {
        await autoAssignTask(taskId, data.assigneeId)
      }
    } catch (error) {
      console.warn('任务后处理失败:', error)
      // 不抛出错误，因为任务已经创建成功
    }
  }

  // 自动分配任务
  const autoAssignTask = async (taskId: number, assigneeId: number) => {
    try {
      // 使用通用请求方法
      await service.request({
        url: '/admin/task/status/change',
        method: 'POST',
        data: {
          taskId,
          targetStatus: 1, // 待执行状态
          reason: '创建任务时自动分配',
          assigneeIds: [assigneeId],
          operatorId: userStore.info?.id,
          operatorName: userStore.info?.name || '系统'
        }
      })
      console.log('任务自动分配成功')
    } catch (error) {
      console.error('任务自动分配失败:', error)
      // 不抛出错误，因为任务已经创建成功
      console.warn('任务创建成功但自动分配失败，请手动分配')
    }
  }

  // 计算属性
  const isVisible = computed(() => visible.value)
  const currentOptions = computed(() => options.value)
  const isSubmitting = computed(() => submitting.value)

  return {
    // 状态
    visible: isVisible,
    options: currentOptions,
    submitting: isSubmitting,

    // 方法
    showQuickCreate,
    showFullCreate,
    close,
    createTask,
    getSmartDefaults,
    getScenarioDefaults
  }
}

// 导出单例实例供全局使用
export const globalTaskCreator = useGlobalTaskCreator()
