import { ref } from 'vue'

/**
 * 全局任务详情管理
 */
class TaskDetailManager {
  private showDialog = ref(false)
  private currentTask = ref<any>(null)
  private editCallback = ref<((task: any) => void) | null>(null)

  /**
   * 显示任务详情
   * @param task 任务对象
   * @param onEdit 编辑回调函数
   */
  showTaskDetail(task: any, onEdit?: (task: any) => void) {
    this.currentTask.value = task
    this.editCallback.value = onEdit || null
    this.showDialog.value = true
  }

  /**
   * 隐藏任务详情
   */
  hideTaskDetail() {
    this.showDialog.value = false
    setTimeout(() => {
      this.currentTask.value = null
      this.editCallback.value = null
    }, 300)
  }

  /**
   * 处理编辑任务
   */
  handleEdit() {
    if (this.currentTask.value && this.editCallback.value) {
      this.editCallback.value(this.currentTask.value)
      this.hideTaskDetail()
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      visible: this.showDialog,
      task: this.currentTask,
      onEdit: this.handleEdit.bind(this)
    }
  }
}

// 创建全局实例
const taskDetailManager = new TaskDetailManager()

/**
 * 任务详情组合式函数
 */
export function useTaskDetail() {
  return {
    // 显示任务详情
    showTaskDetail: taskDetailManager.showTaskDetail.bind(taskDetailManager),
    
    // 隐藏任务详情
    hideTaskDetail: taskDetailManager.hideTaskDetail.bind(taskDetailManager),
    
    // 获取状态（用于组件绑定）
    getTaskDetailState: taskDetailManager.getState.bind(taskDetailManager)
  }
}

/**
 * 全局任务详情状态（用于组件）
 */
export function useGlobalTaskDetail() {
  const state = taskDetailManager.getState()
  
  return {
    visible: state.visible,
    task: state.task,
    onEdit: state.onEdit,
    onClose: taskDetailManager.hideTaskDetail.bind(taskDetailManager),
    show: taskDetailManager.showTaskDetail.bind(taskDetailManager)
  }
}

/**
 * 快速显示任务详情的工具函数
 * @param task 任务对象
 * @param editHandler 编辑处理函数
 */
export function showTaskDetailDialog(task: any, editHandler?: (task: any) => void) {
  taskDetailManager.showTaskDetail(task, editHandler)
}

/**
 * 任务详情快捷操作
 */
export const TaskDetailActions = {
  /**
   * 在右键菜单中显示任务详情
   */
  showInContextMenu(task: any, editHandler?: (task: any) => void) {
    showTaskDetailDialog(task, editHandler)
  },

  /**
   * 在卡片点击时显示任务详情
   */
  showOnCardClick(task: any, editHandler?: (task: any) => void) {
    showTaskDetailDialog(task, editHandler)
  },

  /**
   * 在列表中显示任务详情
   */
  showInList(task: any, editHandler?: (task: any) => void) {
    showTaskDetailDialog(task, editHandler)
  }
}
