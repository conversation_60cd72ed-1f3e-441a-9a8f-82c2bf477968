import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { service } from '/@/cool'

/**
 * 任务状态流转组合式函数
 */
export function useTaskStatusTransition() {
  const loading = ref(false)
  const submitting = ref(false)

  // 状态定义
  const taskStatuses = [
    { value: 0, label: '待分配', type: 'info', color: '#909399' },
    { value: 1, label: '待执行', type: 'warning', color: '#e6a23c' },
    { value: 2, label: '执行中', type: 'primary', color: '#409eff' },
    { value: 3, label: '已完成', type: 'success', color: '#67c23a' },
    { value: 4, label: '已关闭', type: 'danger', color: '#f56c6c' }
  ]

  // 状态转换规则
  const transitionRules = {
    0: [1, 4], // 待分配 -> 待执行, 已关闭
    1: [2, 4], // 待执行 -> 执行中, 已关闭
    2: [3, 4], // 执行中 -> 已完成, 已关闭
    3: [2],    // 已完成 -> 执行中
    4: [1]     // 已关闭 -> 待执行
  }

  // 操作类型定义
  const actionTypes = {
    ASSIGN: { label: '分配任务', icon: 'User', color: '#409eff' },
    START: { label: '开始执行', icon: 'VideoPlay', color: '#67c23a' },
    COMPLETE: { label: '完成任务', icon: 'Check', color: '#67c23a' },
    CLOSE: { label: '关闭任务', icon: 'Close', color: '#f56c6c' },
    REACTIVATE: { label: '重新激活', icon: 'RefreshRight', color: '#e6a23c' },
    REOPEN: { label: '重新打开', icon: 'Unlock', color: '#409eff' }
  }

  /**
   * 获取状态信息
   */
  const getStatusInfo = (status: number) => {
    return taskStatuses.find(s => s.value === status) || { value: status, label: '未知', type: 'info', color: '#909399' }
  }

  /**
   * 获取状态标签
   */
  const getStatusLabel = (status: number) => {
    return getStatusInfo(status).label
  }

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: number) => {
    return getStatusInfo(status).color
  }

  /**
   * 检查状态转换是否有效
   */
  const isValidTransition = (sourceStatus: number, targetStatus: number): boolean => {
    const allowedTargets = transitionRules[sourceStatus] || []
    return allowedTargets.includes(targetStatus)
  }

  /**
   * 获取可转换的状态列表
   */
  const getAvailableTransitions = (currentStatus: number) => {
    const allowedTargets = transitionRules[currentStatus] || []
    return taskStatuses.filter(status => allowedTargets.includes(status.value))
  }

  /**
   * 获取任务可执行的操作
   */
  const getAvailableActions = async (taskId: number, operatorId?: number) => {
    try {
      loading.value = true
      const params = operatorId ? { operatorId } : {}
      const res = await service.task.status.request({
        url: `/available-actions/${taskId}`,
        method: 'GET',
        params
      })
      return res || []
    } catch (error) {
      console.error('获取可执行操作失败:', error)
      ElMessage.error('获取可执行操作失败')
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 检查状态转换权限
   */
  const checkTransitionPermission = async (sourceStatus: number, targetStatus: number) => {
    try {
      const res = await service.task.status.request({
        url: '/check-transition',
        method: 'GET',
        params: { sourceStatus, targetStatus }
      })
      return res === true
    } catch (error) {
      console.error('检查状态转换权限失败:', error)
      return false
    }
  }

  /**
   * 执行状态变更
   */
  const changeTaskStatus = async (request: any) => {
    try {
      submitting.value = true
      const res = await service.task.status.request({
        url: '/change',
        method: 'POST',
        data: request
      })

      if (res.success) {
        ElMessage.success('状态变更成功')
        return res
      } else {
        ElMessage.error(res.errorMessage || '状态变更失败')
        return null
      }
    } catch (error: any) {
      console.error('状态变更失败:', error)
      ElMessage.error(error.message || '状态变更失败')
      return null
    } finally {
      submitting.value = false
    }
  }

  /**
   * 批量状态变更
   */
  const batchChangeStatus = async (taskIds: number[], targetStatus: number, reason: string) => {
    try {
      submitting.value = true

      const res = await service.task.status.request({
        url: '/batch-change',
        method: 'POST',
        data: {
          taskIds,
          targetStatus,
          reason
        }
      })

      if (res.success > 0) {
        ElMessage.success(`批量操作完成，成功${res.success}个，失败${res.failed}个`)
        return res
      } else {
        ElMessage.error('批量操作失败')
        return null
      }
    } catch (error: any) {
      console.error('批量状态变更失败:', error)
      ElMessage.error(error.message || '批量状态变更失败')
      return null
    } finally {
      submitting.value = false
    }
  }

  /**
   * 获取状态变更历史
   */
  const getStatusChangeHistory = async (taskId: number) => {
    try {
      loading.value = true
      const res = await service.task.status.request({
        url: `/history/${taskId}`,
        method: 'GET'
      })
      return res || []
    } catch (error) {
      console.error('获取状态变更历史失败:', error)
      ElMessage.error('获取状态变更历史失败')
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取任务完整历史
   */
  const getTaskHistory = async (taskId: number, limit?: number) => {
    try {
      loading.value = true
      const params = limit ? { limit } : {}
      const res = await service.task.status.request({
        url: `/history/${taskId}/full`,
        method: 'GET',
        params
      })
      return res || []
    } catch (error) {
      console.error('获取任务历史失败:', error)
      ElMessage.error('获取任务历史失败')
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 显示状态变更确认对话框
   */
  const showStatusChangeConfirm = async (task: any, targetStatus: number, reason?: string) => {
    const sourceStatusInfo = getStatusInfo(task.taskStatus)
    const targetStatusInfo = getStatusInfo(targetStatus)
    
    const message = `确认将任务"${task.name}"的状态从"${sourceStatusInfo.label}"变更为"${targetStatusInfo.label}"吗？`
    
    try {
      await ElMessageBox.confirm(message, '状态变更确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      return true
    } catch {
      return false
    }
  }

  /**
   * 格式化状态变更描述
   */
  const formatStatusChangeDescription = (oldStatus: number, newStatus: number, reason?: string) => {
    const oldStatusInfo = getStatusInfo(oldStatus)
    const newStatusInfo = getStatusInfo(newStatus)
    
    let description = `${oldStatusInfo.label} → ${newStatusInfo.label}`
    if (reason) {
      description += `：${reason}`
    }
    
    return description
  }

  /**
   * 获取操作按钮配置
   */
  const getActionButtonConfig = (action: string) => {
    return actionTypes[action] || { label: action, icon: 'Operation', color: '#409eff' }
  }

  return {
    // 状态
    loading,
    submitting,
    taskStatuses,
    transitionRules,
    actionTypes,
    
    // 方法
    getStatusInfo,
    getStatusLabel,
    getStatusColor,
    isValidTransition,
    getAvailableTransitions,
    getAvailableActions,
    checkTransitionPermission,
    changeTaskStatus,
    batchChangeStatus,
    getStatusChangeHistory,
    getTaskHistory,
    showStatusChangeConfirm,
    formatStatusChangeDescription,
    getActionButtonConfig
  }
}
