import { type ModuleConfig } from '/@/cool';

export default (): ModuleConfig => {
  return {
    // 服务配置
    services: [
      {
        path: '/admin/task/package',
        name: 'taskPackage'
      },
      {
        path: '/admin/task/info',
        name: 'info',
        options: {
          // 自定义API方法
          api: {
            executionDetails: 'GET /execution-details/:taskId',
            batchExecutionDetails: 'POST /batch-execution-details',
            batchUpdateTaskTime: 'POST /batch-update'
          }
        }
      },
      {
        path: '/admin/sop/step',
        name: 'step'
      },
      {
        path: '/admin/task/assignment',
        name: 'assignment',
        options: {
          // 自定义API方法
          api: {
            execute: 'POST /execute',
            single: 'POST /single/:taskId',
            package: 'POST /package/:packageId',
            candidates: {
              task: 'GET /candidates/:taskId',
              all: 'GET /candidates',
              byRoles: 'POST /candidates/by-roles'
            },
            validate: 'POST /validate/:taskId',
            manual: 'POST /manual'
          }
        }
      },
      {
        path: '/admin/task/execution',
        name: 'execution'
      },
      {
        path: '/admin/task/status',
        name: 'status',
        options: {
          // 自定义API方法
          api: {
            // 原有的状态管理接口
            completeTaskExecution: 'POST /task/execution/complete',
            forceCompleteTask: 'POST /task/force-complete',
            closeTask: 'POST /task/close',
            reopenTask: 'POST /task/reopen',
            canCompleteTask: 'GET /task/canComplete',
            canCloseTask: 'GET /task/canClose',
            batchForceCompleteTask: 'POST /task/batch/force-complete',
            batchCloseTask: 'POST /task/batch/close',
            batchReopenTask: 'POST /task/batch/reopen',

            // 新增的状态流转接口
            changeStatus: 'POST /change',
            checkTransition: 'GET /check-transition',
            getAvailableActions: 'GET /available-actions/:taskId',
            getStatusChangeHistory: 'GET /history/:taskId',
            getTaskHistory: 'GET /history/:taskId/full',
            batchChangeStatus: 'POST /batch-change'
          }
        }
      }
    ],

    // 组件配置
    components: [
      () => import('./components/logs.vue')
    ],

    // 视图配置
    views: [
      {
        path: '/task/list',
        meta: {
          label: '任务调度'
        },
        component: () => import('./views/list.vue')
      },
      {
        path: '/task/info',
        meta: {
          label: '任务管理'
        },
        component: () => import('./views/info.vue')
      },
      {
        path: '/task/package',
        meta: {
          label: '场景任务包'
        },
        component: () => import('./views/package-management.vue')
      }
    ]
  };
};
