/**
 * 任务模块数据字典
 */

// 任务类型
export const taskCategoryOptions = [
  { label: '场景步骤', value: 'SOP_STEP', color: 'primary', icon: 'el-icon-guide' },
  { label: '日常', value: 'RC', color: 'success', icon: 'el-icon-calendar' },
  { label: '周期', value: 'ZQ', color: 'warning', icon: 'el-icon-refresh' },
  { label: '临时', value: 'LS', color: 'info', icon: 'el-icon-lightning' }
]

// 任务业务状态
export const taskBusinessStatusOptions = [
  { label: '待分配', value: 0, color: 'danger', icon: 'el-icon-user' },      // 红色
  { label: '待执行', value: 1, color: 'info', icon: 'el-icon-time' },        // 灰色
  { label: '执行中', value: 2, color: 'primary', icon: 'el-icon-loading' },  // 蓝色
  { label: '已完成', value: 3, color: 'success', icon: 'el-icon-check' },    // 绿色
  { label: '已关闭', value: 4, color: 'warning', icon: 'el-icon-close' }     // 黄色/橙色
]

// 任务来源
export const taskSourceOptions = [
  { label: '系统', value: 0, color: 'info' },
  { label: '用户', value: 1, color: 'primary' }
]

// 任务调度状态
export const taskScheduleStatusOptions = [
  { label: '停止', value: 0, color: 'danger' },
  { label: '运行', value: 1, color: 'success' }
]

// 是否选项（整数）
export const yesNoIntOptions = [
  { label: '否', value: 0, color: 'info' },
  { label: '是', value: 1, color: 'success' }
]

// 是否选项（布尔）
export const yesNoBoolOptions = [
  { label: '否', value: false, color: 'info' },
  { label: '是', value: true, color: 'success' }
]

// 获取任务类型标签
export const getTaskCategoryLabel = (value: string) => {
  const option = taskCategoryOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 获取任务类型颜色
export const getTaskCategoryColor = (value: string) => {
  const option = taskCategoryOptions.find(item => item.value === value)
  return option ? option.color : 'info'
}

// 获取任务状态标签
export const getTaskStatusLabel = (value: number) => {
  const option = taskBusinessStatusOptions.find(item => item.value === value)
  return option ? option.label : '未知'
}

// 获取任务状态颜色
export const getTaskStatusColor = (value: number) => {
  const option = taskBusinessStatusOptions.find(item => item.value === value)
  return option ? option.color : 'info'
}

// 获取来源标签
export const getTaskSourceLabel = (value: number) => {
  const option = taskSourceOptions.find(item => item.value === value)
  return option ? option.label : '未知'
}

// 获取来源颜色
export const getTaskSourceColor = (value: number) => {
  const option = taskSourceOptions.find(item => item.value === value)
  return option ? option.color : 'info'
}

// 获取调度状态标签
export const getScheduleStatusLabel = (value: number) => {
  const option = taskScheduleStatusOptions.find(item => item.value === value)
  return option ? option.label : '未知'
}

// 获取调度状态颜色
export const getScheduleStatusColor = (value: number) => {
  const option = taskScheduleStatusOptions.find(item => item.value === value)
  return option ? option.color : 'info'
}

// 获取是否标签（整数）
export const getYesNoIntLabel = (value: number) => {
  const option = yesNoIntOptions.find(item => item.value === value)
  return option ? option.label : '未知'
}

// 获取是否标签（布尔）
export const getYesNoBoolLabel = (value: boolean) => {
  const option = yesNoBoolOptions.find(item => item.value === value)
  return option ? option.label : '未知'
}

// 获取是否颜色（整数）
export const getYesNoIntColor = (value: number) => {
  const option = yesNoIntOptions.find(item => item.value === value)
  return option ? option.color : 'info'
}

// 获取是否颜色（布尔）
export const getYesNoBoolColor = (value: boolean) => {
  const option = yesNoBoolOptions.find(item => item.value === value)
  return option ? option.color : 'info'
}
