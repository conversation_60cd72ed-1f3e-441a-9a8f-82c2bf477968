import { App, createApp, h } from 'vue'
import GlobalTaskDetailDialog from '../components/GlobalTaskDetailDialog.vue'

/**
 * 任务详情插件
 */
export default {
  install(app: App) {
    // 创建全局任务详情对话框容器
    const container = document.createElement('div')
    container.id = 'global-task-detail-dialog'
    document.body.appendChild(container)

    // 创建全局任务详情对话框实例
    const dialogApp = createApp({
      render() {
        return h(GlobalTaskDetailDialog)
      }
    })

    // 挂载到容器
    dialogApp.mount(container)

    // 在应用卸载时清理
    app.config.globalProperties.$taskDetailCleanup = () => {
      dialogApp.unmount()
      document.body.removeChild(container)
    }
  }
}
