/**
 * LLM任务调度服务
 * 智能分配任务执行人员
 */

export interface TaskInfo {
  id: number;
  name: string;
  description: string;
  stepCode: string;
  stepName: string;
  requiredSkills: string[];
  location?: string;
  priority: number;
  estimatedHours: number;
  deadline?: string;
  specialRequirements?: string[];
}

export interface UserProfile {
  id: number;
  name: string;
  role: string;
  skills: string[];
  currentWorkload: number; // 0-100
  location: string;
  performance: number; // 0-100
  availability: boolean;
  preferences?: string[];
  recentTasks: number[];
}

export interface AssignmentResult {
  userId: number;
  userName: string;
  confidence: number; // 0-100
  reasons: string[];
  alternatives: Array<{
    userId: number;
    userName: string;
    confidence: number;
  }>;
}

export class LLMTaskScheduler {
  private apiKey: string;
  private baseURL: string;

  constructor(config: { apiKey: string; baseURL: string }) {
    this.apiKey = config.apiKey;
    this.baseURL = config.baseURL;
  }

  /**
   * 智能分配任务执行人
   */
  async assignTask(task: TaskInfo, availableUsers: UserProfile[]): Promise<AssignmentResult> {
    try {
      // 1. 预过滤：基础条件筛选
      const candidates = this.preFilterCandidates(task, availableUsers);
      
      if (candidates.length === 0) {
        throw new Error('没有符合条件的候选人');
      }

      // 2. 规则评分：基于规则的初步评分
      const scoredCandidates = this.scoreByRules(task, candidates);

      // 3. LLM优化：使用AI进行最终决策
      const llmResult = await this.llmOptimize(task, scoredCandidates);

      return llmResult;
    } catch (error) {
      console.error('任务分配失败:', error);
      // 降级到规则引擎
      return this.fallbackToRules(task, availableUsers);
    }
  }

  /**
   * 预过滤候选人
   */
  private preFilterCandidates(task: TaskInfo, users: UserProfile[]): UserProfile[] {
    return users.filter(user => {
      // 基础条件检查
      if (!user.availability) return false;
      if (user.currentWorkload > 90) return false;

      // 技能匹配检查
      const hasRequiredSkills = task.requiredSkills.some(skill => 
        user.skills.includes(skill)
      );
      
      return hasRequiredSkills;
    });
  }

  /**
   * 基于规则的评分
   */
  private scoreByRules(task: TaskInfo, candidates: UserProfile[]): Array<UserProfile & { score: number }> {
    return candidates.map(user => {
      let score = 0;

      // 技能匹配度 (40%)
      const skillMatch = this.calculateSkillMatch(task.requiredSkills, user.skills);
      score += skillMatch * 0.4;

      // 工作量平衡 (25%)
      const workloadScore = (100 - user.currentWorkload) / 100;
      score += workloadScore * 0.25;

      // 历史绩效 (20%)
      score += (user.performance / 100) * 0.2;

      // 地理位置 (10%)
      const locationScore = this.calculateLocationScore(task.location, user.location);
      score += locationScore * 0.1;

      // 优先级调整 (5%)
      const priorityBonus = task.priority > 8 ? 0.05 : 0;
      score += priorityBonus;

      return { ...user, score: Math.round(score * 100) };
    }).sort((a, b) => b.score - a.score);
  }

  /**
   * LLM优化决策
   */
  private async llmOptimize(task: TaskInfo, candidates: Array<UserProfile & { score: number }>): Promise<AssignmentResult> {
    const prompt = this.buildPrompt(task, candidates);
    
    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的任务调度专家，需要根据任务要求和人员情况，智能分配最合适的执行人员。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })
    });

    const result = await response.json();
    return this.parseAssignmentResponse(result.choices[0].message.content, candidates);
  }

  /**
   * 构建LLM提示词
   */
  private buildPrompt(task: TaskInfo, candidates: Array<UserProfile & { score: number }>): string {
    return `
请为以下任务分配最合适的执行人员：

任务信息：
- 名称：${task.name}
- 描述：${task.description}
- 步骤：${task.stepName} (${task.stepCode})
- 所需技能：${task.requiredSkills.join(', ')}
- 执行地点：${task.location || '不限'}
- 优先级：${task.priority}/10
- 预计工时：${task.estimatedHours}小时
- 截止时间：${task.deadline || '无'}
- 特殊要求：${task.specialRequirements?.join(', ') || '无'}

候选人员（已按规则评分排序）：
${candidates.map((user, index) => `
${index + 1}. ${user.name} (ID: ${user.id})
   - 角色：${user.role}
   - 技能：${user.skills.join(', ')}
   - 当前工作量：${user.currentWorkload}%
   - 地点：${user.location}
   - 历史绩效：${user.performance}/100
   - 规则评分：${user.score}/100
   - 可用性：${user.availability ? '可用' : '不可用'}
`).join('')}

请综合考虑以下因素：
1. 技能匹配度和专业能力
2. 当前工作量和时间安排
3. 地理位置和执行便利性
4. 历史绩效和工作质量
5. 任务优先级和紧急程度

请返回JSON格式的分配结果：
{
  "recommendedUserId": 推荐人员ID,
  "confidence": 置信度(0-100),
  "reasons": ["推荐理由1", "推荐理由2", ...],
  "alternatives": [
    {"userId": 备选人员ID, "confidence": 置信度},
    ...
  ]
}
`;
  }

  /**
   * 解析LLM响应
   */
  private parseAssignmentResponse(response: string, candidates: Array<UserProfile & { score: number }>): AssignmentResult {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('无法解析LLM响应');

      const parsed = JSON.parse(jsonMatch[0]);
      const recommendedUser = candidates.find(u => u.id === parsed.recommendedUserId);
      
      if (!recommendedUser) throw new Error('推荐的用户不在候选列表中');

      return {
        userId: parsed.recommendedUserId,
        userName: recommendedUser.name,
        confidence: parsed.confidence,
        reasons: parsed.reasons || [],
        alternatives: parsed.alternatives || []
      };
    } catch (error) {
      console.error('解析LLM响应失败:', error);
      // 降级到最高评分候选人
      const topCandidate = candidates[0];
      return {
        userId: topCandidate.id,
        userName: topCandidate.name,
        confidence: topCandidate.score,
        reasons: ['基于规则引擎的自动分配'],
        alternatives: candidates.slice(1, 3).map(c => ({
          userId: c.id,
          userName: c.name,
          confidence: c.score
        }))
      };
    }
  }

  /**
   * 降级到规则引擎
   */
  private fallbackToRules(task: TaskInfo, users: UserProfile[]): AssignmentResult {
    const candidates = this.preFilterCandidates(task, users);
    const scored = this.scoreByRules(task, candidates);
    
    if (scored.length === 0) {
      throw new Error('没有可用的执行人员');
    }

    const best = scored[0];
    return {
      userId: best.id,
      userName: best.name,
      confidence: best.score,
      reasons: ['基于规则引擎的智能分配'],
      alternatives: scored.slice(1, 3).map(c => ({
        userId: c.id,
        userName: c.name,
        confidence: c.score
      }))
    };
  }

  /**
   * 计算技能匹配度
   */
  private calculateSkillMatch(required: string[], userSkills: string[]): number {
    if (required.length === 0) return 1;
    
    const matches = required.filter(skill => userSkills.includes(skill)).length;
    return matches / required.length;
  }

  /**
   * 计算地理位置评分
   */
  private calculateLocationScore(taskLocation?: string, userLocation?: string): number {
    if (!taskLocation || !userLocation) return 0.5;
    return taskLocation === userLocation ? 1 : 0.3;
  }
}

// 导出单例实例
export const llmScheduler = new LLMTaskScheduler({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY || '',
  baseURL: import.meta.env.VITE_OPENAI_BASE_URL || 'https://api.openai.com/v1'
});
