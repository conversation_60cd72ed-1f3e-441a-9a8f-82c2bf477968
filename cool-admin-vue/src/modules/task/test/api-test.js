/**
 * 任务状态流转API测试脚本
 * 在浏览器控制台中运行此脚本来测试API
 */

// 测试API连接
async function testTaskStatusAPI() {
  console.log('开始测试任务状态流转API...')
  
  try {
    // 测试1: 检查状态转换
    console.log('测试1: 检查状态转换')
    const checkResult = await service.task.status.request({
      url: '/check-transition',
      method: 'GET',
      params: { sourceStatus: 0, targetStatus: 1 }
    })
    console.log('状态转换检查结果:', checkResult)

    // 测试2: 获取可执行操作（需要一个真实的任务ID）
    console.log('测试2: 获取可执行操作')
    try {
      const actionsResult = await service.task.status.request({
        url: '/available-actions/1',
        method: 'GET',
        params: {}
      })
      console.log('可执行操作结果:', actionsResult)
    } catch (error) {
      console.log('获取可执行操作失败（可能是任务不存在）:', error.message)
    }

    // 测试3: 获取状态变更历史（需要一个真实的任务ID）
    console.log('测试3: 获取状态变更历史')
    try {
      const historyResult = await service.task.status.request({
        url: '/history/1',
        method: 'GET'
      })
      console.log('状态变更历史结果:', historyResult)
    } catch (error) {
      console.log('获取状态变更历史失败（可能是任务不存在）:', error.message)
    }
    
    console.log('API测试完成！')
    
  } catch (error) {
    console.error('API测试失败:', error)
  }
}

// 测试状态流转组合式函数
async function testUseTaskStatusTransition() {
  console.log('开始测试状态流转组合式函数...')
  
  // 这里需要在Vue组件中运行
  console.log('请在Vue组件中导入并测试useTaskStatusTransition')
  
  const testCode = `
import { useTaskStatusTransition } from '/@/modules/task/composables/useTaskStatusTransition'

const {
  getStatusInfo,
  isValidTransition,
  getAvailableTransitions,
  checkTransitionPermission,
  getAvailableActions
} = useTaskStatusTransition()

// 测试状态信息
console.log('待分配状态信息:', getStatusInfo(0))
console.log('已完成状态信息:', getStatusInfo(3))

// 测试状态转换
console.log('待分配->待执行 是否有效:', isValidTransition(0, 1))
console.log('待分配->已完成 是否有效:', isValidTransition(0, 3))

// 测试可转换状态
console.log('待分配可转换状态:', getAvailableTransitions(0))
console.log('执行中可转换状态:', getAvailableTransitions(2))

// 测试API调用（需要真实任务ID）
// const actions = await getAvailableActions(1, 1)
// console.log('可执行操作:', actions)
  `
  
  console.log('测试代码:')
  console.log(testCode)
}

// 导出测试函数
window.testTaskStatusAPI = testTaskStatusAPI
window.testUseTaskStatusTransition = testUseTaskStatusTransition

console.log('任务状态流转API测试脚本已加载')
console.log('运行 testTaskStatusAPI() 测试API')
console.log('运行 testUseTaskStatusTransition() 查看组合式函数测试代码')
