export interface TaskExecutionEntity {
	id: number;
	taskId: number;
	assigneeId: number;
	assigneeName: string;
	assigneePhone?: string;
	executionTime?: string;
	completionTime?: string;
	status: number;
	remark?: string;
	attachments?: any[]; // Adjust as per actual attachment structure
}

export interface TaskInfoEntity {
	id: number;
	name: string;
	description?: string;
	priority?: number;
	taskStatus: number;
	taskCategory?: string;
	completionTime?: string;
	closeReason?: string;
	closedBy?: string;
	closeTime?: string;
	scenarioId?: number;
	packageId?: number;
	scenarioCode?: string;
	scenarioName?: string;
	stepId?: number;
	stepCode?: string;
	stepName?: string;
	entityTouchpoint?: string;
	taskActivity?: string;
	employeeBehavior?: string;
	workHighlight?: string;
	employeeRole?: string;
	photoRequired?: boolean;
	attachmentRequired?: boolean;
	remark?: string;
	jobId?: string;
	repeatCount?: number;
	every?: number;
	scheduleStatus: number;
	scheduleType: number;
	type: number;
	data?: string;
	cron?: string;
	nextRunTime?: string;
	startTime?: string;
	endTime?: string;
	assigneeName?: string;
	assigneeId?: number;
	assigneePhone?: string;
	executions?: TaskExecutionEntity[];
	executionEntitys?: TaskExecutionEntity[]; // Note: This seems to be a typo in the backend entity, but we'll include it for compatibility
	departmentId?: number;
	departmentName?: string;
	creatorDepartmentId?: number;
	creatorDepartmentName?: string;
	projectId?: number;
	projectName?: string;
	projectRoleName?: string;
}
