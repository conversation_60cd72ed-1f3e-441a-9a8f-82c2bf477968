<template>
  <div class="dict-test">
    <h3>任务字典测试</h3>
    
    <div class="test-section">
      <h4>任务类型测试</h4>
      <div class="test-items">
        <div v-for="option in taskCategoryOptions" :key="option.value" class="test-item">
          <span>{{ option.value }}:</span>
          <el-tag :type="option.color" size="small">{{ option.label }}</el-tag>
          <span>函数测试:</span>
          <el-tag :type="getTaskCategoryColor(option.value)" size="small">
            {{ getTaskCategoryLabel(option.value) }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>任务状态测试</h4>
      <div class="test-items">
        <div v-for="option in taskBusinessStatusOptions" :key="option.value" class="test-item">
          <span>{{ option.value }}:</span>
          <el-tag :type="option.color" size="small">{{ option.label }}</el-tag>
          <span>函数测试:</span>
          <el-tag :type="getTaskStatusColor(option.value)" size="small">
            {{ getTaskStatusLabel(option.value) }}
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  taskCategoryOptions, 
  taskBusinessStatusOptions,
  getTaskCategoryLabel,
  getTaskCategoryColor,
  getTaskStatusLabel,
  getTaskStatusColor
} from '../dict'

defineOptions({
  name: 'dict-test'
})
</script>

<style scoped>
.dict-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
