<template>
  <cl-crud ref="Crud" @load="onLoad">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <cl-multi-delete-btn />
      <cl-flex1 />
      <cl-search ref="Search" />
    </cl-row>
    
    <cl-row>
      <cl-table ref="Table" />
    </cl-row>
    
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>

<script setup lang="ts">
import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { 
  taskCategoryOptions, 
  taskBusinessStatusOptions,
  getTaskCategoryLabel,
  getTaskCategoryColor,
  getTaskStatusLabel,
  getTaskStatusColor
} from "../dict";

defineOptions({
  name: 'task-info-simple'
})

const { service } = useCool();

// CRUD 配置
const Crud = useCrud({
  service: service.task.info
}, (app) => {
  app.refresh()
});

// 搜索配置
const Search = useSearch({
  items: [
    {
      label: "任务名称",
      prop: "name",
      component: {
        name: "el-input",
        props: {
          clearable: true,
          placeholder: "请输入任务名称"
        }
      }
    },
    {
      label: "任务类别",
      prop: "taskCategory",
      component: {
        name: "cl-select",
        props: {
          clearable: true,
          placeholder: "请选择任务类别",
          options: taskCategoryOptions.map(item => ({
            label: item.label,
            value: item.value
          }))
        }
      }
    },
    {
      label: "任务状态",
      prop: "taskStatus",
      component: {
        name: "cl-select",
        props: {
          clearable: true,
          placeholder: "请选择任务状态",
          options: taskBusinessStatusOptions.map(item => ({
            label: item.label,
            value: item.value
          }))
        }
      }
    }
  ]
});

// 表格配置
const Table = useTable({
  columns: [
    { type: "selection", width: 60 },
    { label: "任务名称", prop: "name", minWidth: 200 },
    { 
      label: "任务类别", 
      prop: "taskCategory", 
      width: 120,
      dict: taskCategoryOptions
    },
    { 
      label: "任务状态", 
      prop: "taskStatus", 
      width: 120,
      dict: taskBusinessStatusOptions
    },
    { label: "场景名称", prop: "scenarioName", minWidth: 150 },
    { label: "创建时间", prop: "createTime", width: 160, sortable: "desc" },
    { type: "op", buttons: ["edit", "delete"], width: 160 }
  ]
});

// 表单配置
const Upsert = useUpsert({
  items: [
    {
      label: "任务名称",
      prop: "name",
      required: true,
      component: {
        name: "el-input",
        props: { clearable: true }
      }
    },
    {
      label: "任务类别",
      prop: "taskCategory",
      component: {
        name: "el-select",
        options: taskCategoryOptions,
        props: { clearable: true }
      }
    },
    {
      label: "任务状态", 
      prop: "taskStatus",
      component: {
        name: "el-select",
        options: taskBusinessStatusOptions,
        props: { clearable: true }
      }
    }
  ]
});

// 刷新函数
function refresh(params?: any) {
  Crud.value?.refresh(params);
}

// 页面加载
const onLoad = ({ ctx, app }) => {
  ctx.service(service.task.info).done()
  app.refresh()
}
</script>
