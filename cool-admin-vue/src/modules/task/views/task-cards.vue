<template>
  <div class="task-cards-page">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="success" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增任务
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedTasks.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除 ({{ selectedTasks.length }})
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <!-- 归属筛选器 -->
        <department-filter 
          v-model="selectedOrganizations"
          v-model:dimension="currentFilterDimension"
          @change="onOrganizationFilterChange"
          style="margin-right: 10px; width: 240px;"
        />
        
        <!-- 视图切换 -->
        <el-button-group>
          <el-button 
            :type="viewMode === 'cards' ? 'primary' : 'default'"
            @click="viewMode = 'cards'"
          >
            <el-icon><Grid /></el-icon>
            卡片
          </el-button>
          <el-button 
            :type="viewMode === 'list' ? 'primary' : 'default'"
            @click="viewMode = 'list'"
          >
            <el-icon><List /></el-icon>
            列表
          </el-button>
        </el-button-group>
        
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索任务名称..."
          style="width: 200px; margin-left: 10px;"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-select 
            v-model="filterStatus" 
            placeholder="按状态筛选"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="待分配" :value="0" />
            <el-option label="执行中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="已暂停" :value="3" />
            <el-option label="已关闭" :value="4" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select 
            v-model="filterPriority" 
            placeholder="按优先级筛选"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="低" :value="1" />
            <el-option label="中" :value="2" />
            <el-option label="高" :value="3" />
            <el-option label="紧急" :value="4" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select 
            v-model="filterCategory" 
            placeholder="按类型筛选"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="清洁卫生" value="CLEANING" />
            <el-option label="客户服务" value="CUSTOMER" />
            <el-option label="设备维护" value="MAINTENANCE" />
            <el-option label="培训活动" value="TRAINING" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <div class="filter-stats">
            共 {{ filteredTasks.length }} 个任务
            <el-divider direction="vertical" />
            已选择 {{ selectedTasks.length }} 个
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 卡片视图 -->
    <div v-if="viewMode === 'cards'" class="cards-container">
      <div class="task-cards-grid">
        <task-card
          v-for="task in paginatedTasks"
          :key="task.id"
          :task="task"
          :selected="selectedTasks.includes(task.id)"
          @click="handleTaskClick"
          @select="handleTaskSelect"
          @edit="handleEdit"
          @delete="handleDelete"
          @department-click="handleDepartmentClick"
        />
      </div>
      
      <!-- 空状态 -->
      <el-empty 
        v-if="filteredTasks.length === 0" 
        description="暂无任务数据"
        image-size="120"
      >
        <el-button type="primary" @click="handleAdd">创建第一个任务</el-button>
      </el-empty>
    </div>
    
    <!-- 列表视图 -->
    <div v-else class="list-container">
      <el-table
        :data="paginatedTasks"
        @selection-change="handleSelectionChange"
        @row-click="handleTaskClick"
        stripe
        height="600"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="任务名称" min-width="200" />
        <el-table-column prop="departmentName" label="所属部门" width="120">
          <template #default="{ row }">
            <department-tag
              :department-id="row.departmentId"
              :department-name="row.departmentName"
              size="small"
              :clickable="true"
              @click="handleDepartmentClick"
            />
          </template>
        </el-table-column>
        <el-table-column prop="taskStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusColor(row.taskStatus)" size="small">
              {{ getTaskStatusLabel(row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <priority-tag :priority="row.priority" />
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="filteredTasks.length"
        :page-sizes="[12, 24, 48, 96]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCool } from '/@/cool'
import { formatDateTime } from '../utils'
import { 
  getTaskStatusLabel, 
  getTaskStatusColor,
  getTaskCategoryLabel,
  getTaskCategoryColor 
} from '../dict'

// 组件导入
import TaskCard from '../components/TaskCard.vue'
import DepartmentFilter from '../components/DepartmentFilter.vue'
import DepartmentTag from '../components/DepartmentTag.vue'
import PriorityTag from '../components/PriorityTag.vue'

defineOptions({
  name: 'task-cards'
})

const { service } = useCool()

// 响应式数据
const tasks = ref([])
const loading = ref(false)
const viewMode = ref('cards')
const searchKeyword = ref('')
const selectedTasks = ref([])
const selectedOrganizations = ref([])
const currentFilterDimension = ref<'department' | 'project'>('project')

// 筛选条件
const filterStatus = ref(null)
const filterPriority = ref(null)
const filterCategory = ref(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(24)

// 计算属性
const filteredTasks = computed(() => {
  let result = tasks.value

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(task => 
      task.name?.toLowerCase().includes(keyword) ||
      task.description?.toLowerCase().includes(keyword) ||
      task.departmentName?.toLowerCase().includes(keyword) ||
      task.projectName?.toLowerCase().includes(keyword)
    )
  }

  // 状态筛选
  if (filterStatus.value !== null) {
    result = result.filter(task => task.taskStatus === filterStatus.value)
  }

  // 优先级筛选
  if (filterPriority.value !== null) {
    result = result.filter(task => task.priority === filterPriority.value)
  }

  // 类型筛选
  if (filterCategory.value) {
    result = result.filter(task => task.taskCategory === filterCategory.value)
  }

  // 归属筛选（部门/项目）
  if (selectedOrganizations.value && selectedOrganizations.value.length > 0) {
    if (currentFilterDimension.value === 'department') {
      result = result.filter(task => 
        selectedOrganizations.value.includes(task.departmentId)
      )
    } else {
      result = result.filter(task => 
        selectedOrganizations.value.includes(task.projectId)
      )
    }
  }

  return result
})

const paginatedTasks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTasks.value.slice(start, end)
})

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    const response = await service.task.info.page({
      page: 1,
      size: 1000  // 获取所有数据，前端分页
    })
    
    tasks.value = response.list || response.records || []
    console.log('加载任务数据:', tasks.value.length, '条')
  } catch (error) {
    console.error('加载任务失败:', error)
    ElMessage.error('加载任务数据失败')
  } finally {
    loading.value = false
  }
}

const handleRefresh = () => {
  loadTasks()
}

const handleAdd = () => {
  // TODO: 打开新增任务对话框
  ElMessage.info('新增任务功能开发中...')
}

const handleEdit = (task) => {
  // TODO: 打开编辑任务对话框
  console.log('编辑任务:', task)
  ElMessage.info('编辑任务功能开发中...')
}

const handleDelete = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务"${task.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await service.task.info.delete({ ids: [task.id] })
    ElMessage.success('删除成功')
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要删除的任务')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedTasks.value.length} 个任务吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await service.task.info.delete({ ids: selectedTasks.value })
    ElMessage.success('批量删除成功')
    selectedTasks.value = []
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleTaskClick = (task) => {
  console.log('点击任务:', task)
  // TODO: 打开任务详情
}

const handleTaskSelect = (task, selected) => {
  if (selected) {
    if (!selectedTasks.value.includes(task.id)) {
      selectedTasks.value.push(task.id)
    }
  } else {
    const index = selectedTasks.value.indexOf(task.id)
    if (index > -1) {
      selectedTasks.value.splice(index, 1)
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedTasks.value = selection.map(item => item.id)
}

const handleDepartmentClick = (departmentId) => {
  console.log('点击部门:', departmentId)
  // 设置部门筛选
  currentFilterDimension.value = 'department'
  if (!selectedOrganizations.value.includes(departmentId)) {
    selectedOrganizations.value = [departmentId]
  }
}

const handleProjectClick = (projectId) => {
  console.log('点击项目:', projectId)
  // 设置项目筛选
  currentFilterDimension.value = 'project'
  if (!selectedOrganizations.value.includes(projectId)) {
    selectedOrganizations.value = [projectId]
  }
};

const onOrganizationFilterChange = (organizationIds) => {
  selectedOrganizations.value = organizationIds
  currentPage.value = 1  // 重置到第一页
}

const handleSearch = () => {
  currentPage.value = 1  // 重置到第一页
}

const handleFilterChange = () => {
  currentPage.value = 1  // 重置到第一页
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 监听筛选条件变化，重置分页
watch([filterStatus, filterPriority, filterCategory], () => {
  currentPage.value = 1
})

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style lang="scss" scoped>
.task-cards-page {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .toolbar-left {
    display: flex;
    gap: 8px;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.filter-bar {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .filter-stats {
    display: flex;
    align-items: center;
    color: var(--el-text-color-regular);
    font-size: 14px;
    height: 32px;
  }
}

.cards-container {
  margin-bottom: 20px;
}

.task-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 16px;
  min-height: 400px;
}

.list-container {
  margin-bottom: 20px;
  background: var(--el-bg-color);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

// 响应式布局
@media (max-width: 768px) {
  .task-cards-grid {
    grid-template-columns: 1fr;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .toolbar-left,
    .toolbar-right {
      justify-content: center;
    }
  }

  .filter-bar {
    .el-row {
      .el-col {
        margin-bottom: 8px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .task-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}
</style>