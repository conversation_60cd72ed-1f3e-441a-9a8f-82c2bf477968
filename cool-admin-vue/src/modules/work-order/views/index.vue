<template>
	<cl-crud ref="Crud">
		<cl-row>
			<cl-refresh-btn />
			<cl-flex1 />
			<!-- 归属筛选器 -->
			<department-filter 
				v-model="selectedOrganizations"
				v-model:dimension="currentFilterDimension"
				@change="onOrganizationFilterChange"
				style="margin-right: 10px; width: 240px;"
			/>
			<cl-search-key />
		</cl-row>

		<cl-row>
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<cl-pagination />
		</cl-row>
	</cl-crud>
</template>

<script setup lang="ts">
import { useCrud, useTable } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { h, ref } from "vue";
import { ElTag } from "element-plus";
import TaskPackageList from "../components/task-package-list.vue";
import DepartmentFilter from "../../task/components/DepartmentFilter.vue";

defineOptions({
	name: "work-order"
});

const { service } = useCool();

// 归属筛选相关
const selectedOrganizations = ref([]);
const currentFilterDimension = ref<'department' | 'project'>('project');

// 归属筛选处理
const onOrganizationFilterChange = (organizationIds: any[]) => {
	selectedOrganizations.value = organizationIds;
	Crud.value?.refresh();
};

const Crud = useCrud(
	{
		service: service.sop.work.order
	},
	(app) => {
		app.refresh();
	}
);

const Table = useTable({
	onRefresh: (params, { next }) => {
		// 应用归属筛选参数
		if (selectedOrganizations.value && selectedOrganizations.value.length > 0) {
			if (currentFilterDimension.value === 'department') {
				params.departmentIds = selectedOrganizations.value;
			} else {
				params.projectIds = selectedOrganizations.value;
			}
		}
		// 合并搜索参数
		const searchParams = Search.value?.getForm() || {};
		console.log('cl-search 获取到的搜索参数:', searchParams);
		Object.assign(params, searchParams);
		
		console.log('cl-crud onRefresh 钩子最终传递的参数:', params);
		next(params);
	},
	columns:[
		{
			type: "expand",
			width: 60,
			component: {
				render: (scope) => {
					// 渲染任务包子组件
					return h(TaskPackageList, { workOrderId: scope.row.id });
				}
			}
		},
		{
			label: "工单名称",
			prop: "title",
			minWidth: 250
		},
		{
			label: "归属",
			prop: "projectName",
			minWidth: 150,
			render: (scope) => {
				if (!scope.row) {
					return h(ElTag, { type: "info", size: "small" }, () => "未指定归属");
				}
				if (scope.row.projectName) {
					return h(ElTag, { type: "success", size: "small" }, () => `项目：${scope.row.projectName}`);
				} else if (scope.row.departmentName) {
					return h(ElTag, { type: "primary", size: "small" }, () => `部门：${scope.row.departmentName}`);
				} else {
					return h(ElTag, { type: "info", size: "small" }, () => "未指定归属");
				}
			}
		},
		{
			label: "状态",
			prop: "status",
			minWidth: 120,
			dict: [
				{ label: "待调度", value: 0, color: "warning" },
				{ label: "已分配", value: 1, color: "primary" },
				{ label: "执行中", value: 2, color: "primary" },
				{ label: "暂停", value: 3, color: "warning" },
				{ label: "已完成", value: 4, color: "success" },
				{ label: "已取消", value: 5, color: "danger" },
				{ label: "执行失败", value: 6, color: "danger" }
			]
		},
		{
			label: "优先级",
			prop: "priority",
			minWidth: 100,
			dict: [
				{ label: "低", value: 1, color: "info" },
				{ label: "中", value: 2, color: "primary" },
				{ label: "高", value: 3, color: "warning" },
				{ label: "紧急", value: 4, color: "danger" },
				{ label: "极紧急", value: 5, color: "danger" }
			]
		},
		{
			label: "申请人",
			prop: "applicantName",
			minWidth: 120
		},
		{
			label: "负责人",
			prop: "assigneeName",
			minWidth: 120
		},
		{
			label: "创建时间",
			prop: "createTime",
			minWidth: 170,
			sortable: "custom",
			component: {
				name: "cl-date-text",
				props: { format: "YYYY-MM-DD HH:mm" }
			}
		},
		{
			label: "更新时间",
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: {
				name: "cl-date-text",
				props: { format: "YYYY-MM-DD HH:mm" }
			}
		}
	]
});
</script>