# 任务状态流转API修复指南

## 问题描述

前端调用任务状态流转API时出现 `service.task.status.getAvailableActions is not a function` 错误。

## 问题原因

Cool Admin框架的EPS（Endpoint Service）系统对于带路径参数的API接口，不会自动生成对应的方法。需要使用 `service.request()` 方法手动调用。

## 解决方案

### 1. API配置（已正确配置）

在 `cool-admin-vue/src/modules/task/config.ts` 中的API配置：

```typescript
{
  path: '/admin/task/status',
  name: 'status',
  options: {
    api: {
      // 新增的状态流转接口
      changeStatus: 'POST /change',
      checkTransition: 'GET /check-transition',
      getAvailableActions: 'GET /available-actions/:taskId',  // 带路径参数
      getStatusChangeHistory: 'GET /history/:taskId',         // 带路径参数
      getTaskHistory: 'GET /history/:taskId/full',            // 带路径参数
      batchChangeStatus: 'POST /batch-change'
    }
  }
}
```

### 2. 前端API调用修复

对于带路径参数的API，需要使用 `service.request()` 方法：

#### 修复前（错误）：
```typescript
const res = await service.task.status.getAvailableActions(taskId, operatorId)
```

#### 修复后（正确）：
```typescript
const res = await service.task.status.request({
  url: `/admin/task/status/available-actions/${taskId}`,
  method: 'GET',
  params: operatorId ? { operatorId } : {}
})
```

### 3. 完整的API调用示例

#### 获取可执行操作
```typescript
const getAvailableActions = async (taskId: number, operatorId?: number) => {
  const params = operatorId ? { operatorId } : {}
  const res = await service.task.status.request({
    url: `/admin/task/status/available-actions/${taskId}`,
    method: 'GET',
    params
  })
  return res || []
}
```

#### 状态变更
```typescript
const changeTaskStatus = async (request: any) => {
  const res = await service.task.status.request({
    url: '/admin/task/status/change',
    method: 'POST',
    data: request
  })
  return res
}
```

#### 检查状态转换
```typescript
const checkTransitionPermission = async (sourceStatus: number, targetStatus: number) => {
  const res = await service.task.status.request({
    url: '/admin/task/status/check-transition',
    method: 'GET',
    params: { sourceStatus, targetStatus }
  })
  return res === true
}
```

#### 批量状态变更
```typescript
const batchChangeStatus = async (taskIds: number[], targetStatus: number, reason: string) => {
  const res = await service.task.status.request({
    url: '/admin/task/status/batch-change',
    method: 'POST',
    data: { taskIds, targetStatus, reason }
  })
  return res
}
```

#### 获取状态变更历史
```typescript
const getStatusChangeHistory = async (taskId: number) => {
  const res = await service.task.status.request({
    url: `/admin/task/status/history/${taskId}`,
    method: 'GET'
  })
  return res || []
}
```

#### 获取任务完整历史
```typescript
const getTaskHistory = async (taskId: number, limit?: number) => {
  const params = limit ? { limit } : {}
  const res = await service.task.status.request({
    url: `/admin/task/status/history/${taskId}/full`,
    method: 'GET',
    params
  })
  return res || []
}
```

## 测试方法

### 1. 浏览器控制台测试

在浏览器控制台中运行：

```javascript
// 加载测试脚本
const script = document.createElement('script')
script.src = '/src/modules/task/test/api-test.js'
document.head.appendChild(script)

// 运行测试
testTaskStatusAPI()
```

### 2. Vue组件中测试

```vue
<script setup>
import { useTaskStatusTransition } from '/@/modules/task/composables/useTaskStatusTransition'

const {
  getStatusInfo,
  isValidTransition,
  getAvailableActions,
  changeTaskStatus
} = useTaskStatusTransition()

// 测试状态信息
console.log('待分配状态:', getStatusInfo(0))

// 测试状态转换
console.log('有效转换:', isValidTransition(0, 1))

// 测试API调用（需要真实任务ID）
const testAPI = async () => {
  try {
    const actions = await getAvailableActions(1)
    console.log('可执行操作:', actions)
  } catch (error) {
    console.error('API调用失败:', error)
  }
}
</script>
```

## 常见问题

### Q1: 为什么有些API可以直接调用，有些需要用request方法？

A: Cool Admin的EPS系统会为简单的API路径自动生成方法，但对于包含路径参数（如 `:taskId`）的API，需要手动使用request方法。

### Q2: 如何确定API路径是否正确？

A: 可以在浏览器开发者工具的Network标签中查看实际发送的请求，确认URL和参数是否正确。

### Q3: 如果API返回404错误怎么办？

A: 检查后端Controller的路径映射是否正确，确认 `@RequestMapping` 和 `@GetMapping`/`@PostMapping` 的路径配置。

## 修复状态

✅ useTaskStatusTransition.ts - API调用方法已修复
✅ TaskStatusActions.vue - 组件API调用已更新
✅ TaskStatusChangeDialog.vue - 用户搜索API正常
✅ project/task.vue - 任务完成和关闭API已修复
✅ 测试脚本已创建

## 最新修复内容

### 任务完成API修复
```typescript
// 修复前
await service.task.status.completeTaskExecution({...})

// 修复后
const result = await service.task.status.request({
  url: '/admin/task/status/change',
  method: 'POST',
  data: {
    taskId: data.taskId,
    targetStatus: 3, // 已完成状态
    reason: data.remark || '任务完成',
    operatorId: data.assigneeId,
    operatorName: '执行人',
    completionNote: data.remark || '任务完成',
    attachments: attachmentUrls,
    photos: []
  }
})
```

### 任务关闭API修复
```typescript
// 修复前
await service.task.status.closeTask({...})

// 修复后
const result = await service.task.status.request({
  url: '/admin/task/status/change',
  method: 'POST',
  data: {
    taskId: data.taskId,
    targetStatus: 4, // 已关闭状态
    reason: data.closeReason,
    operatorId: data.operatorId,
    operatorName: data.operatorName,
    closeReason: data.closeReason
  }
})
```

## 下一步

1. 在浏览器中测试API调用
2. 验证状态流转功能是否正常工作
3. 检查错误处理和用户提示
4. 完善文档和使用指南
