# AssigneeSelector 智能筛选模式说明

## 功能概述

根据任务的归属情况，智能选择分配执行人时的筛选模式：
- **项目归属任务** → 使用项目筛选模式
- **部门归属任务** → 使用部门筛选模式  
- **无明确归属任务** → 使用默认部门筛选模式

## 业务逻辑

### 🎯 **归属判断规则**

#### **1. 项目归属优先**
```typescript
if (task.projectId && task.projectName) {
  // 使用项目筛选模式
  filterMode = 'project'
  contextId = task.projectId
}
```

#### **2. 部门归属次之**
```typescript
else if (task.departmentId && task.departmentName) {
  // 使用部门筛选模式
  filterMode = 'department'
  contextId = task.departmentId
}
```

#### **3. 默认部门筛选**
```typescript
else {
  // 无明确归属，使用默认部门筛选
  filterMode = 'department'
  contextId = undefined  // 显示所有执行人
}
```

### 🔍 **判断依据**

参考 `info.vue` 中的归属显示逻辑：
```typescript
// 归属列显示逻辑
render: (scope) => {
  if (scope.projectId && scope.projectName) {
    return h(ElTag, { type: "success" }, () => `项目：${scope.projectName}`);
  } else if (scope.departmentId && scope.departmentName) {
    return h(ElTag, { type: "primary" }, () => `部门：${scope.departmentName}`);
  } else {
    return h(ElTag, { type: "info" }, () => "未指定归属");
  }
}
```

## 技术实现

### 🔧 **核心方法**

#### **1. 智能筛选模式选择**
```typescript
const getAssigneeFilterMode = () => {
  if (!currentAssignTask.value) {
    return 'department'
  }
  
  const task = currentAssignTask.value
  
  // 优先检查项目归属
  if (task.projectId && task.projectName) {
    console.log(`任务归属于项目: ${task.projectName}，使用项目筛选模式`)
    return 'project'
  } 
  // 其次检查部门归属
  else if (task.departmentId && task.departmentName) {
    console.log(`任务归属于部门: ${task.departmentName}，使用部门筛选模式`)
    return 'department'
  } 
  // 默认部门筛选
  else {
    console.log('任务未指定明确归属，使用默认部门筛选模式')
    return 'department'
  }
}
```

#### **2. 上下文ID获取**
```typescript
const getAssigneeContextId = () => {
  if (!currentAssignTask.value) return undefined
  
  const task = currentAssignTask.value
  const filterMode = getAssigneeFilterMode()
  
  if (filterMode === 'project') {
    // 项目模式：返回项目ID
    return task.projectId
  } else {
    // 部门模式：返回部门ID（可能为undefined）
    return task.departmentId
  }
}
```

#### **3. 任务标签增强**
```typescript
const getTaskTags = (task: any) => {
  const tags = []
  
  // 任务类型标签
  if (task.taskCategory) {
    const categoryMap = {
      'SOP_STEP': '场景步骤',
      'RC': '日常',
      'ZQ': '周期', 
      'LS': '临时'
    }
    tags.push(categoryMap[task.taskCategory])
  }
  
  // 归属标签 - 优先显示项目归属
  if (task.projectId && task.projectName) {
    tags.push(`项目归属: ${task.projectName}`)
  } else if (task.departmentId && task.departmentName) {
    tags.push(`部门归属: ${task.departmentName}`)
  } else {
    tags.push('归属: 未指定')
  }
  
  // 其他信息标签
  if (task.scenarioName) tags.push(`场景: ${task.scenarioName}`)
  if (task.stepName) tags.push(`步骤: ${task.stepName}`)
  if (task.assigneeName) tags.push(`当前执行人: ${task.assigneeName}`)
  
  return tags
}
```

### 🔧 **组件集成**

```vue
<AssigneeSelector
  v-model="showAssignDialog"
  :taskName="currentAssignTask?.name || '未知任务'"
  :taskTags="getTaskTags(currentAssignTask)"
  :filterMode="getAssigneeFilterMode()"
  :contextId="getAssigneeContextId()"
  @confirm="handleAssignConfirm"
/>
```

## 用户体验

### 🎯 **筛选精度提升**

#### **项目归属任务**
- **筛选范围**：仅显示该项目的执行人
- **用户体验**：精确筛选，减少选择干扰
- **业务价值**：确保项目任务分配给项目成员

#### **部门归属任务**
- **筛选范围**：仅显示该部门的执行人
- **用户体验**：部门内分配，符合组织架构
- **业务价值**：保持部门任务的内部流转

#### **无归属任务**
- **筛选范围**：显示所有可用执行人
- **用户体验**：最大选择灵活性
- **业务价值**：临时任务可跨部门分配

### 🎯 **视觉反馈优化**

#### **任务标签显示**
```
[场景步骤] [项目归属: 智慧社区管理系统] [场景: 业主服务场景] [当前执行人: 张三]
```

#### **控制台日志**
```
任务归属于项目: 智慧社区管理系统 (ID: 123)，使用项目筛选模式
使用项目ID作为上下文: 123
```

## 测试验证

### ✅ **测试用例**

#### **1. 项目归属任务**
```typescript
const projectTask = {
  id: 1,
  name: '项目归属任务',
  projectId: 123,
  projectName: '智慧社区管理系统',
  scenarioName: '业主服务场景',
  taskStatus: 0
}
// 预期: filterMode='project', contextId=123
```

#### **2. 部门归属任务**
```typescript
const departmentTask = {
  id: 2,
  name: '部门归属任务',
  departmentId: 456,
  departmentName: '物业管理部',
  taskStatus: 1
}
// 预期: filterMode='department', contextId=456
```

#### **3. 无归属任务**
```typescript
const noOwnershipTask = {
  id: 3,
  name: '无明确归属任务',
  taskStatus: 2
  // 没有 projectId/departmentId
}
// 预期: filterMode='department', contextId=undefined
```

### ✅ **验证要点**

- [ ] 项目归属任务使用项目筛选模式
- [ ] 部门归属任务使用部门筛选模式
- [ ] 无归属任务使用默认部门筛选模式
- [ ] 任务标签正确显示归属信息
- [ ] 控制台输出正确的调试信息
- [ ] AssigneeSelector 接收到正确的参数

### 📋 **使用测试页面**

访问 `test-global-dialog.vue` 测试页面：
1. **项目归属任务测试**：验证项目筛选模式
2. **部门归属任务测试**：验证部门筛选模式
3. **无归属任务测试**：验证默认筛选模式
4. **查看操作日志**：确认筛选逻辑正确执行

## 业务价值

### 🎯 **组织管理优化**

#### **项目维度管理**
- **项目任务内聚**：项目任务只能分配给项目成员
- **权限边界清晰**：避免跨项目的任务分配
- **项目责任明确**：确保项目任务的执行质量

#### **部门维度管理**
- **部门职责分工**：部门任务在部门内部流转
- **组织架构对应**：任务分配符合组织结构
- **管理层级清晰**：便于部门管理和考核

#### **灵活性保障**
- **临时任务支持**：无归属任务可跨部门分配
- **应急处理能力**：特殊情况下的灵活调配
- **业务扩展性**：支持复杂的组织结构

### 🎯 **用户体验提升**

#### **操作效率**
- **精确筛选**：减少无关人员的干扰
- **快速定位**：快速找到合适的执行人
- **决策支持**：清晰的归属信息辅助决策

#### **错误预防**
- **权限控制**：避免错误的跨组织分配
- **业务规范**：强化组织管理规范
- **质量保障**：确保任务分配的合理性

## 总结

智能筛选模式功能实现了：

1. **业务逻辑优化**：根据任务归属智能选择筛选模式
2. **用户体验提升**：精确筛选，减少选择干扰
3. **组织管理强化**：符合项目和部门的管理边界
4. **系统灵活性**：支持各种归属情况的任务分配

现在分配执行人时会根据任务的实际归属情况，智能选择最合适的筛选模式！
