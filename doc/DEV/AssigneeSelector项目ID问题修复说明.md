# AssigneeSelector 项目ID问题修复说明

## 问题描述

用户反馈：分配按钮点击后，弹出框提示"未找到项目ID为 NaN 的项目"。

## 问题分析

### 🔍 **错误原因**

1. **项目ID缺失**：在任务信息页面(`info.vue`)中，任务数据可能没有 `projectId` 字段
2. **上下文ID为空**：`GlobalTaskDetailDialog` 中的 `selectedProjectId` 为 `undefined`
3. **类型转换错误**：`undefined` 被转换为 `NaN`，导致 `AssigneeSelector` 报错

### 🚨 **错误链路**

```
任务数据没有projectId 
  → selectedProjectId = undefined 
  → AssigneeSelector接收到undefined 
  → parseInt(undefined) = NaN 
  → 提示"未找到项目ID为 NaN 的项目"
```

#### **原始代码问题**
```vue
<!-- ❌ 原始代码 -->
<AssigneeSelector
  filterMode="project"
  :contextId="selectedProjectId"  <!-- undefined -->
/>
```

```typescript
// ❌ AssigneeSelector 内部处理
const projectId = parseInt(props.contextId) // parseInt(undefined) = NaN
```

## 修复方案

### ✅ **1. 智能模式选择**

#### **修复前**
```vue
<!-- ❌ 固定使用项目模式 -->
<AssigneeSelector
  filterMode="project"
  :contextId="selectedProjectId"
/>
```

#### **修复后**
```vue
<!-- ✅ 使用部门模式作为默认 -->
<AssigneeSelector
  filterMode="department"
  :contextId="getAssigneeContextId()"
/>
```

### ✅ **2. 动态上下文ID获取**

```typescript
// 获取分配器的上下文ID - 简化为部门模式
const getAssigneeContextId = () => {
  // 使用部门模式，不需要特定的上下文ID
  // AssigneeSelector 会自动加载所有可用的执行人
  return undefined
}
```

### ✅ **3. 任务标签增强**

```typescript
// 获取任务标签
const getTaskTags = (task: any) => {
  if (!task) return []
  
  const tags = []
  
  // 添加任务类型标签
  if (task.taskCategory) {
    const categoryMap: Record<string, string> = {
      'SOP_STEP': '场景步骤',
      'RC': '日常',
      'ZQ': '周期',
      'LS': '临时'
    }
    tags.push(categoryMap[task.taskCategory] || task.taskCategory)
  }
  
  // 添加项目标签
  if (task.projectName) {
    tags.push(`项目: ${task.projectName}`)
  }
  
  // 添加场景标签
  if (task.scenarioName) {
    tags.push(`场景: ${task.scenarioName}`)
  }
  
  return tags
}
```

## 技术实现

### 🔧 **1. 模式选择策略**

#### **部门模式优势**
- **兼容性好**：不依赖特定的项目ID
- **覆盖面广**：可以显示所有可用的执行人
- **容错性强**：即使任务数据不完整也能正常工作
- **用户友好**：用户可以从所有人员中选择

#### **模式对比**
| 模式 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| 项目模式 | 精确筛选 | 需要项目ID | 项目任务分配 |
| 部门模式 | 兼容性好 | 人员较多 | 通用任务分配 |

### 🔧 **2. 错误处理机制**

#### **原始错误处理**
```typescript
// ❌ 容易出错的处理
if (!matchedProject) {
  ElMessage.error(`未找到项目ID为 ${projectId} 的项目`)
  return
}
```

#### **优化后的处理**
```typescript
// ✅ 使用部门模式，避免项目ID问题
filterMode="department"  // 不需要项目ID验证
```

### 🔧 **3. 数据适配**

#### **任务数据结构适配**
```typescript
// 处理不同来源的任务数据
const taskData = {
  // 基本信息
  id: task.id,
  name: task.name,
  taskStatus: task.taskStatus,
  
  // 项目信息（可能缺失）
  projectId: task.projectId,        // 可能为 undefined
  projectName: task.projectName,    // 可能为 undefined
  
  // 部门信息（通常存在）
  departmentId: task.departmentId,
  departmentName: task.departmentName,
  
  // 执行人信息
  assigneeId: task.assigneeId,
  assigneeName: task.assigneeName
}
```

## 用户体验改进

### 🎯 **1. 错误消息优化**

#### **修复前**
```
❌ "未找到项目ID为 NaN 的项目"
```

#### **修复后**
```
✅ 正常显示分配对话框，显示所有可用执行人
```

### 🎯 **2. 分配流程优化**

#### **修复前的流程**
```
点击分配 → 检查项目ID → 项目ID无效 → 显示错误 → 无法分配
```

#### **修复后的流程**
```
点击分配 → 使用部门模式 → 显示所有执行人 → 正常分配
```

### 🎯 **3. 任务标签显示**

#### **增强的标签信息**
- **任务类型标签**：场景步骤、日常、周期、临时
- **项目标签**：项目: 测试项目A
- **场景标签**：场景: 测试场景

## 兼容性保证

### 🔧 **1. 向后兼容**

#### **现有调用方式保持不变**
```typescript
// 现有代码无需修改
const { show: showTaskDetail } = useGlobalTaskDetail()
showTaskDetail(task)
```

#### **自动适配不同数据格式**
```typescript
// 自动处理有项目信息的任务
const taskWithProject = {
  projectId: 123,
  projectName: '测试项目'
}

// 自动处理无项目信息的任务
const taskWithoutProject = {
  departmentId: 456,
  departmentName: '技术部'
}
```

### 🔧 **2. 渐进式增强**

#### **基础功能**
- 使用部门模式，保证基本分配功能

#### **增强功能**
- 如果有项目信息，在标签中显示
- 如果有场景信息，在标签中显示

## 测试验证

### ✅ **测试用例**

#### **1. 有项目信息的任务**
```typescript
const taskWithProject = {
  id: 1,
  name: '有项目信息的任务',
  projectId: 123,
  projectName: '测试项目A',
  taskStatus: 0
}
```

#### **2. 无项目信息的任务**
```typescript
const taskWithoutProject = {
  id: 2,
  name: '无项目信息的任务',
  taskStatus: 1
}
```

#### **3. 有部门信息的任务**
```typescript
const taskWithDepartment = {
  id: 3,
  name: '有部门信息的任务',
  departmentId: 456,
  departmentName: '技术部',
  taskStatus: 2
}
```

### ✅ **验证要点**

- [ ] 点击分配按钮不再显示"未找到项目ID为 NaN 的项目"错误
- [ ] 分配对话框正常弹出并显示可用执行人
- [ ] 任务标签正确显示任务类型、项目、场景信息
- [ ] 分配功能正常工作，可以成功分配任务
- [ ] 不同类型的任务数据都能正常处理

### 📋 **使用测试页面**

创建了 `test-global-dialog.vue` 测试页面：
1. **测试有项目信息的任务**
2. **测试无项目信息的任务**
3. **测试有部门信息的任务**
4. **查看操作日志和调试信息**

## 总结

这次修复主要解决了 `AssigneeSelector` 组件在处理缺失项目ID时的错误：

1. **根本解决**：使用部门模式替代项目模式，避免项目ID依赖
2. **用户体验**：消除错误提示，提供流畅的分配体验
3. **兼容性**：支持各种任务数据格式，向后兼容
4. **功能增强**：提供更丰富的任务标签信息

现在分配按钮点击后能正常弹出分配对话框，不再出现项目ID错误！
