# URL路径修复说明

## 问题描述

在使用Cool Admin框架的API时，出现了URL路径重复的问题，例如：
```
/admin/task/status/admin/task/status/available-actions/554
```

## 问题原因

Cool Admin框架的EPS（Endpoint Service）系统会自动为每个service添加基础路径。当我们在config.ts中配置：

```typescript
{
  path: '/admin/task/status',
  name: 'status',
  options: {
    api: {
      changeStatus: 'POST /change',
      getAvailableActions: 'GET /available-actions/:taskId'
    }
  }
}
```

框架会自动将基础路径 `/admin/task/status` 添加到每个API调用前面。

## 解决方案

### ❌ 错误的API调用方式

```typescript
// 这会导致URL重复：/admin/task/status/admin/task/status/change
const result = await service.task.status.request({
  url: '/admin/task/status/change',
  method: 'POST',
  data: {...}
})
```

### ✅ 正确的API调用方式

```typescript
// 正确的URL：/admin/task/status/change
const result = await service.task.status.request({
  url: '/change',
  method: 'POST',
  data: {...}
})
```

## 修复的API调用

### 1. 状态变更
```typescript
// 修复前
url: '/admin/task/status/change'
// 修复后
url: '/change'
```

### 2. 检查状态转换
```typescript
// 修复前
url: '/admin/task/status/check-transition'
// 修复后
url: '/check-transition'
```

### 3. 获取可执行操作
```typescript
// 修复前
url: `/admin/task/status/available-actions/${taskId}`
// 修复后
url: `/available-actions/${taskId}`
```

### 4. 批量状态变更
```typescript
// 修复前
url: '/admin/task/status/batch-change'
// 修复后
url: '/batch-change'
```

### 5. 获取状态变更历史
```typescript
// 修复前
url: `/admin/task/status/history/${taskId}`
// 修复后
url: `/history/${taskId}`
```

### 6. 获取任务完整历史
```typescript
// 修复前
url: `/admin/task/status/history/${taskId}/full`
// 修复后
url: `/history/${taskId}/full`
```

## 规则总结

1. **基础路径自动添加**：框架会自动添加config.ts中配置的path作为基础路径
2. **相对路径使用**：在request方法中使用相对路径，以 `/` 开头但不包含基础路径
3. **路径参数处理**：对于带路径参数的API，直接在URL中替换参数值

## 验证方法

### 1. 浏览器开发者工具
在Network标签中查看实际发送的请求URL，确认格式正确：
```
✅ 正确：GET /admin/task/status/available-actions/554
❌ 错误：GET /admin/task/status/admin/task/status/available-actions/554
```

### 2. 控制台测试
```javascript
// 在浏览器控制台中测试
service.task.status.request({
  url: '/check-transition',
  method: 'GET',
  params: { sourceStatus: 0, targetStatus: 1 }
}).then(console.log).catch(console.error)
```

## 注意事项

1. **只影响自定义API**：这个问题只影响使用`service.xxx.request()`方法的自定义API调用
2. **标准CRUD不受影响**：框架自动生成的CRUD方法（如`service.task.info.page()`）不受影响
3. **配置文件无需修改**：config.ts中的配置保持不变，只需修改调用时的URL

## 修复状态

✅ useTaskStatusTransition.ts - 所有API调用URL已修复
✅ project/task.vue - 任务完成和关闭API已修复
✅ api-test.js - 测试脚本URL已修复

现在所有的任务状态流转API都应该可以正常工作了！
