# 任务完成与关闭接口修复说明

## 问题描述

用户反馈任务完成与任务关闭接口调用不正确，特别是在有附件要求的情况下，需要参考步骤流程卡片的正确调用方式。

## 问题分析

### 原始问题
1. **参数不完整**：任务完成和关闭接口缺少必要的详细信息
2. **附件处理缺失**：没有正确处理附件和照片上传
3. **组件调用错误**：`TaskCompleter` 和 `TaskCloser` 组件的属性传递不正确

### 后端接口要求

#### **任务完成接口** (`TaskCompletionRequest`)
```java
@Data
@Schema(description = "任务完成请求")
public class TaskCompletionRequest {
    @Schema(description = "任务ID", required = true)
    private Long taskId;
    
    @Schema(description = "执行人ID", required = true)
    private Long assigneeId;
    
    @Schema(description = "完成说明")
    private String completionNote;
    
    @Schema(description = "附件列表")
    private List<String> attachments;
    
    @Schema(description = "照片列表")
    private List<String> photos;
}
```

#### **任务关闭接口** (`TaskCloseRequest`)
```java
@Data
@Schema(description = "任务关闭请求")
public class TaskCloseRequest {
    @Schema(description = "任务ID", required = true)
    private Long taskId;
    
    @Schema(description = "关闭原因", required = true)
    private String closeReason;
    
    @Schema(description = "操作人ID", required = true)
    private Long operatorId;
    
    @Schema(description = "操作人姓名")
    private String operatorName;
}
```

## 修复方案

### 1. 修复任务完成接口调用

#### **修复前**
```typescript
await service.task.status.completeTaskExecution({
    taskId: data.taskId,
    assigneeId: data.assigneeId,
});
```

#### **修复后**
```typescript
const handleCompleteConfirm = async (data: { taskId: number; assigneeId: number; remark?: string; attachments?: any[] }) => {
    try {
        // 处理附件数据，将上传组件的文件对象转换为URL数组
        const attachmentUrls = data.attachments?.map(file => {
            if (typeof file === 'string') {
                return file;
            } else if (file && file.url) {
                return file.url;
            } else if (file && file.response && file.response.data) {
                return file.response.data;
            }
            return '';
        }).filter(url => url) || [];

        await service.task.status.completeTaskExecution({
            taskId: data.taskId,
            assigneeId: data.assigneeId,
            completionNote: data.remark || '任务完成',
            attachments: attachmentUrls,
            photos: [] // TaskCompleter组件暂时没有照片字段
        });
        
        ElMessage.success('任务已完成');
        showCompleteDialog.value = false;
        Crud.value?.refresh();
    } catch (error) {
        ElMessage.error('任务完成失败');
        console.error('任务完成失败:', error);
    }
};
```

### 2. 修复任务关闭接口调用

#### **修复前**
```typescript
await service.task.status.closeTask({
    taskId: data.taskId,
    closeReason: data.closeReason,
});
```

#### **修复后**
```typescript
const handleCloseConfirm = async (data: { taskId: number; closeReason: string; operatorId?: number; operatorName?: string }) => {
    if (!data.taskId || !data.closeReason) {
        ElMessage.warning('请输入关闭原因');
        return;
    }

    try {
        await service.task.status.closeTask({
            taskId: data.taskId,
            closeReason: data.closeReason,
            operatorId: data.operatorId || currentCloseTask.value?.assigneeId || 1, // 获取当前用户ID
            operatorName: data.operatorName || currentCloseTask.value?.assigneeName || '当前用户', // 获取当前用户名
        });
        
        ElMessage.success('任务已关闭');
        showCloseDialog.value = false;
        Crud.value?.refresh();
    } catch (error) {
        ElMessage.error('任务关闭失败');	
        console.error('任务关闭失败:', error);
    }
};
```

### 3. 修复组件属性传递

#### **修复前**
```vue
<!-- 完成任务对话框 -->
<TaskCompleter
    v-model="showCompleteDialog"
    :taskId="currentCompleteTask?.id"
    :assigneeId="currentCompleteTask?.assigneeId"
    @confirm="handleCompleteConfirm"
/>

<!-- 关闭任务对话框 -->
<TaskCloser
    v-model="showCloseDialog"
    :taskId="currentCloseTask?.id"
    @confirm="handleCloseConfirm"
/>
```

#### **修复后**
```vue
<!-- 完成任务对话框 -->
<TaskCompleter
    v-model="showCompleteDialog"
    :task="currentCompleteTask"
    @confirm="handleCompleteConfirm"
/>

<!-- 关闭任务对话框 -->
<TaskCloser
    v-model="showCloseDialog"
    :task="currentCloseTask"
    @confirm="handleCloseConfirm"
/>
```

### 4. 修复拖拽状态更新

#### **修复后**
```typescript
const updateTaskStatus = async (taskId: number, newStatus: number) => {
    try {
        console.log('更新任务状态:', { taskId, newStatus });
        
        // 根据状态调用不同的正规接口
        if (newStatus === 3) {
            // 完成任务 - 需要提供完整的完成信息
            await service.task.status.completeTaskExecution({
                taskId: taskId,
                assigneeId: 1, // 这里需要获取当前用户ID
                completionNote: '拖拽完成任务',
                attachments: [],
                photos: []
            });
        } else if (newStatus === 4) {
            // 关闭任务 - 需要提供完整的关闭信息
            await service.task.status.closeTask({
                taskId: taskId,
                closeReason: '拖拽关闭任务',
                operatorId: 1, // 这里需要获取当前用户ID
                operatorName: '当前用户' // 这里需要获取当前用户名
            });
        } else {
            // 其他状态变更暂时不支持
            ElMessage.warning('该状态变更暂不支持拖拽操作');
            return;
        }

        ElMessage.success('任务状态更新成功');
        
        // 刷新看板数据
        await fetchKanbanData();
    } catch (error) {
        console.error('更新任务状态失败:', error);
        ElMessage.error('任务状态更新失败');
    }
};
```

### 5. 确保任务数据完整性

```typescript
// 完成任务
const handleCompleteTask = async (row: any) => {
    console.log('完成任务:', row);
    // 确保任务数据包含必要的字段
    currentCompleteTask.value = {
        ...row,
        assigneeId: row.assigneeId || 1 // 如果没有assigneeId，使用默认值
    };
    showCompleteDialog.value = true;
};
```

## 组件功能说明

### TaskCompleter 组件
- **功能**：提供完整的任务完成表单
- **字段**：
  - 完成备注 (`remark`)
  - 附件上传 (`attachments`)
- **事件**：`@confirm` 返回 `{ taskId, assigneeId, remark, attachments }`

### TaskCloser 组件
- **功能**：提供任务关闭表单
- **字段**：
  - 关闭原因 (`closeReason`) - 必填
- **事件**：`@confirm` 返回 `{ taskId, closeReason }`

## 附件处理逻辑

```typescript
// 处理附件数据，将上传组件的文件对象转换为URL数组
const attachmentUrls = data.attachments?.map(file => {
    if (typeof file === 'string') {
        return file; // 已经是URL字符串
    } else if (file && file.url) {
        return file.url; // 文件对象的URL属性
    } else if (file && file.response && file.response.data) {
        return file.response.data; // 上传响应中的数据
    }
    return '';
}).filter(url => url) || [];
```

## 验证要点

修复后需要验证以下功能：

### ✅ 任务完成功能
- [ ] 完成对话框正常显示
- [ ] 完成备注可以正常输入
- [ ] 附件上传功能正常
- [ ] 完成后任务状态正确更新
- [ ] 附件信息正确保存

### ✅ 任务关闭功能
- [ ] 关闭对话框正常显示
- [ ] 关闭原因必填验证
- [ ] 关闭后任务状态正确更新
- [ ] 关闭信息正确保存

### ✅ 拖拽功能
- [ ] 拖拽到已完成状态正常
- [ ] 拖拽到已关闭状态正常
- [ ] 拖拽时提供默认的完成/关闭信息

现在任务完成和关闭功能都包含了完整的信息收集，符合业务要求！
