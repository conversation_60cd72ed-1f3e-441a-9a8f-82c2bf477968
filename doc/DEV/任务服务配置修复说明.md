# 任务服务配置修复说明

## 问题描述

用户反馈错误：
- `service.task.status.completeTaskExecution is not a function`
- `service.task.status.closeTask is not a function`

这些错误表明前端无法找到任务服务的相关方法。

## 问题分析

### 根本原因
前端模块系统的服务配置有问题：

1. **服务配置位置错误**：
   - 服务配置在 `cool-admin-vue/src/modules/task/index.ts` 中
   - 但模块系统扫描的是 `cool-admin-vue/src/modules/task/config.ts` 文件
   - 导致任务服务没有被正确注册

2. **接口路径不匹配**：
   - 前端配置：`batchUpdateTaskTime: 'POST /batch-update-time'`
   - 后端实际：`POST /batch-update`

3. **参数格式不匹配**：
   - 前端传递：`{ taskIds: [...] }`
   - 后端期望：`{ ids: [...] }`

## 修复方案

### 1. 合并服务配置到 config.ts

将 `index.ts` 中的服务配置合并到 `config.ts` 中：

```typescript
// cool-admin-vue/src/modules/task/config.ts
import { type ModuleConfig } from '/@/cool';

export default (): ModuleConfig => {
  return {
    // 服务配置
    services: [
      {
        path: '/admin/task/package',
        name: 'taskPackage'
      },
      {
        path: '/admin/task/info',
        name: 'info',
        options: {
          // 自定义API方法
          api: {
            executionDetails: 'GET /execution-details/:taskId',
            batchExecutionDetails: 'POST /batch-execution-details',
            batchUpdateTaskTime: 'POST /batch-update'  // 修复路径
          }
        }
      },
      {
        path: '/admin/task/assignment',
        name: 'assignment',
        options: {
          // 自定义API方法
          api: {
            execute: 'POST /execute',
            single: 'POST /single/:taskId',
            package: 'POST /package/:packageId',
            candidates: {
              task: 'GET /candidates/:taskId',
              all: 'GET /candidates',
              byRoles: 'POST /candidates/by-roles'
            },
            validate: 'POST /validate/:taskId',
            manual: 'POST /manual'
          }
        }
      },
      {
        path: '/admin/task/status',
        name: 'status',
        options: {
          // 自定义API方法
          api: {
            completeTaskExecution: 'POST /task/execution/complete',
            forceCompleteTask: 'POST /task/force-complete',
            closeTask: 'POST /task/close',
            reopenTask: 'POST /task/reopen',
            canCompleteTask: 'GET /task/canComplete',
            canCloseTask: 'GET /task/canClose',
            batchForceCompleteTask: 'POST /task/batch/force-complete',
            batchCloseTask: 'POST /task/batch/close',
            batchReopenTask: 'POST /task/batch/reopen'
          }
        }
      }
    ],

    // 组件和视图配置...
  };
};
```

### 2. 修复接口路径

**批量更新任务时间接口**：
```typescript
// ❌ 修复前
batchUpdateTaskTime: 'POST /batch-update-time'

// ✅ 修复后
batchUpdateTaskTime: 'POST /batch-update'
```

### 3. 修复参数格式

**任务时间调整参数**：
```typescript
// ❌ 修复前
await service.task.info.batchUpdateTaskTime({
    taskIds: [data.taskId],  // 错误的参数名
    startTime: data.startTime,
    endTime: data.endTime,
});

// ✅ 修复后
await service.task.info.batchUpdateTaskTime({
    ids: [data.taskId],      // 正确的参数名
    startTime: data.startTime,
    endTime: data.endTime,
});
```

### 4. 修复类型错误

**分配确认方法参数类型**：
```typescript
// ❌ 修复前
const handleAssignConfirm = async (data: { assigneeId?: number; assigneeIds?: number[]; reason: string }) => {
    const assigneeId = data.assigneeId || (data.assigneeIds && data.assigneeIds[0]);
    // ...
};

// ✅ 修复后
const handleAssignConfirm = async (data: any) => {
    const assigneeId = data.assigneeId || (data.assigneeIds && data.assigneeIds[0]);
    // 确保类型转换
    assigneeIds: [Number(assigneeId)]
    // ...
};
```

## 服务配置说明

### ✅ 正确的服务注册流程

1. **模块扫描**：系统扫描 `src/modules/*/config.ts` 文件
2. **服务注册**：根据 `services` 配置注册服务
3. **API生成**：根据 `api` 配置生成自定义方法
4. **服务调用**：前端可以通过 `service.task.*` 调用

### 🔄 服务映射关系

| 前端调用 | 服务配置 | 后端接口 |
|----------|----------|----------|
| `service.task.assignment.manual()` | `/admin/task/assignment` + `manual: 'POST /manual'` | `POST /admin/task/assignment/manual` |
| `service.task.status.completeTaskExecution()` | `/admin/task/status` + `completeTaskExecution: 'POST /task/execution/complete'` | `POST /admin/task/status/task/execution/complete` |
| `service.task.status.closeTask()` | `/admin/task/status` + `closeTask: 'POST /task/close'` | `POST /admin/task/status/task/close` |
| `service.task.info.batchUpdateTaskTime()` | `/admin/task/info` + `batchUpdateTaskTime: 'POST /batch-update'` | `POST /admin/task/info/batch-update` |

## 验证要点

修复后需要验证以下功能：

### ✅ 服务可用性
- [ ] `service.task.assignment.manual` 方法存在
- [ ] `service.task.status.completeTaskExecution` 方法存在
- [ ] `service.task.status.closeTask` 方法存在
- [ ] `service.task.info.batchUpdateTaskTime` 方法存在

### ✅ 功能正常性
- [ ] 手动分配任务功能正常
- [ ] 完成任务功能正常
- [ ] 关闭任务功能正常
- [ ] 调整任务时间功能正常
- [ ] 拖拽分配功能正常

### ✅ 拖拽状态更新
- [ ] 拖拽到已完成状态正常
- [ ] 拖拽到已关闭状态正常
- [ ] 从待分配拖拽时分配功能正常

## 技术要点

1. **模块配置优先级**：`config.ts` > `index.ts`
2. **服务路径规范**：必须以 `/admin/` 开头
3. **API方法配置**：支持 REST 风格的路径配置
4. **参数类型处理**：注意前后端参数名称和类型的一致性

修复后的任务服务现在应该能正常工作，所有相关的接口调用都不会再出现 "is not a function" 的错误！
