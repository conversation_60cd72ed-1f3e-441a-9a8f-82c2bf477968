# 全局任务详情组件使用指南

## 概述

基于task-steps-flow.vue的任务详情展示，我创建了一个全局的任务详情弹出框组件系统，可以在应用的任何地方使用。

## 组件架构

### 1. 核心组件

#### TaskDetailDialog.vue
- 基础任务详情对话框组件
- 展示任务的完整信息
- 支持编辑回调

#### GlobalTaskDetailDialog.vue
- 全局任务详情对话框组件
- 使用全局状态管理
- 可在任何地方调用

#### useTaskDetail.ts
- 全局任务详情状态管理
- 提供显示/隐藏任务详情的方法
- 支持编辑回调处理

## 功能特性

### 📋 任务信息展示

#### 基本信息
- 任务名称和状态
- 任务ID和类型
- 任务描述和具体活动

#### 执行人员信息
- 执行人角色和姓名
- 执行人头像和ID
- 行为要求说明

#### 时间安排
- **计划时间**：计划开始时间、计划结束时间
- **实际时间**：实际开始时间、实际结束时间、完成时间
- **其他时间**：下次执行时间、创建时间、更新时间

#### 项目信息
- 项目名称
- 场景名称
- 步骤名称

#### 其他信息
- 执行地点
- 工作亮点
- 状态信息和优先级

## 使用方法

### 1. 基本使用

```typescript
import { useTaskDetail } from '/@/modules/task/composables/useTaskDetail'

const { showTaskDetail } = useTaskDetail()

// 显示任务详情
const handleViewTask = (task: any) => {
  showTaskDetail(task, (editTask: any) => {
    // 编辑回调处理
    console.log('编辑任务:', editTask)
  })
}
```

### 2. 在组件中使用

```vue
<template>
  <div>
    <!-- 任务列表 -->
    <div v-for="task in tasks" :key="task.id" @click="viewTask(task)">
      {{ task.name }}
    </div>
    
    <!-- 全局任务详情对话框 -->
    <GlobalTaskDetailDialog />
  </div>
</template>

<script setup>
import { useTaskDetail } from '/@/modules/task/composables/useTaskDetail'
import GlobalTaskDetailDialog from '/@/modules/task/components/GlobalTaskDetailDialog.vue'

const { showTaskDetail } = useTaskDetail()

const viewTask = (task) => {
  showTaskDetail(task, (editTask) => {
    // 处理编辑逻辑
    router.push(`/task/edit/${editTask.id}`)
  })
}
</script>
```

### 3. 右键菜单集成

```typescript
// 在右键菜单中使用
const handleContextAction = (action, task) => {
  if (action === 'view-detail') {
    showTaskDetail(task, (editTask) => {
      Crud.value?.rowEdit(editTask)
    })
  }
}
```

### 4. 快捷方法

```typescript
import { showTaskDetailDialog, TaskDetailActions } from '/@/modules/task/composables/useTaskDetail'

// 直接显示任务详情
showTaskDetailDialog(task, editHandler)

// 在右键菜单中显示
TaskDetailActions.showInContextMenu(task, editHandler)

// 在卡片点击时显示
TaskDetailActions.showOnCardClick(task, editHandler)

// 在列表中显示
TaskDetailActions.showInList(task, editHandler)
```

## 实现细节

### 1. 状态管理

```typescript
class TaskDetailManager {
  private showDialog = ref(false)
  private currentTask = ref<any>(null)
  private editCallback = ref<((task: any) => void) | null>(null)

  showTaskDetail(task: any, onEdit?: (task: any) => void) {
    this.currentTask.value = task
    this.editCallback.value = onEdit || null
    this.showDialog.value = true
  }

  hideTaskDetail() {
    this.showDialog.value = false
    // 延迟清理，避免闪动
    setTimeout(() => {
      this.currentTask.value = null
      this.editCallback.value = null
    }, 300)
  }
}
```

### 2. 组件通信

```vue
<!-- GlobalTaskDetailDialog.vue -->
<template>
  <TaskDetailDialog
    v-model="visible"
    :task="task"
    :show-edit-button="true"
    @edit="onEdit"
  />
</template>

<script setup>
import { useGlobalTaskDetail } from '../composables/useTaskDetail'

const { visible, task, onEdit } = useGlobalTaskDetail()
</script>
```

### 3. 样式设计

```scss
.task-detail-dialog {
  .task-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .task-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .task-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}
```

## 项目集成

### 1. 在project/task.vue中的应用

```vue
<template>
  <!-- 右键菜单 -->
  <div class="context-menu-item" @click="viewTaskDetail(contextMenuTask)">
    <el-icon><View /></el-icon>
    <span>查看详情</span>
  </div>
  
  <!-- 全局任务详情对话框 -->
  <GlobalTaskDetailDialog />
</template>

<script setup>
import { useTaskDetail } from '/@/modules/task/composables/useTaskDetail'
import GlobalTaskDetailDialog from '/@/modules/task/components/GlobalTaskDetailDialog.vue'

const { showTaskDetail } = useTaskDetail()

const viewTaskDetail = (task) => {
  showTaskDetail(task, (editTask) => {
    Crud.value?.rowEdit(editTask)
  })
}
</script>
```

### 2. 替换原有的任务详情显示

```typescript
// 修复前：使用CRUD的rowInfo
const viewTaskDetail = (task) => {
  Crud.value?.rowInfo(task)
}

// 修复后：使用全局任务详情
const viewTaskDetail = (task) => {
  showTaskDetail(task, (editTask) => {
    Crud.value?.rowEdit(editTask)
  })
}
```

## 优势特点

### 1. 🎯 统一的用户体验
- 所有地方的任务详情展示保持一致
- 基于task-steps-flow.vue的成熟设计
- 响应式布局，适配不同屏幕

### 2. 🔧 灵活的集成方式
- 可在任何组件中使用
- 支持自定义编辑回调
- 全局状态管理，无需传递props

### 3. 📱 丰富的信息展示
- 完整的任务信息
- 清晰的时间线展示
- 直观的状态和人员信息

### 4. 🚀 高性能
- 按需加载
- 延迟清理，避免闪动
- 最小化重渲染

## 扩展功能

### 1. 支持任务操作
可以在任务详情中添加快捷操作按钮：

```vue
<template #footer>
  <div class="dialog-footer">
    <el-button @click="handleClose">关闭</el-button>
    <el-button type="primary" @click="handleEdit">编辑任务</el-button>
    <el-button type="success" @click="handleComplete">完成任务</el-button>
  </div>
</template>
```

### 2. 支持任务历史
可以集成任务状态变更历史：

```typescript
// 获取任务历史
const taskHistory = await getTaskHistory(task.id)
```

### 3. 支持附件预览
可以添加附件和图片的预览功能。

## 总结

全局任务详情组件系统提供了：

✅ **统一的任务详情展示** - 基于task-steps-flow.vue的设计
✅ **灵活的调用方式** - 可在任何地方使用
✅ **完整的信息展示** - 涵盖任务的所有重要信息
✅ **良好的用户体验** - 响应式设计，操作流畅
✅ **易于维护** - 全局状态管理，代码复用

现在右键菜单的"查看详情"功能已经完美实现！🎉
