# 待分配任务拖拽修复说明

## 🐛 问题分析

用户反馈：**待分配任务无法拖拽到待执行和已关闭状态**

### 原因分析

1. **权限检查逻辑错误**：
   - `canPerformTransition`函数对所有状态转换都要求`task.assigneeId === currentUserId`
   - 但待分配任务通常没有`assigneeId`，导致权限检查失败

2. **管理员权限设置错误**：
   - `isAdmin`被硬编码为`false`
   - 导致管理员无法执行任何操作

3. **状态转换逻辑不完整**：
   - 没有考虑不同源状态的具体转换规则
   - 缺少待分配任务的特殊处理逻辑

## 🔧 修复方案

### 1. 重构权限检查逻辑

#### 修复前（有问题的逻辑）
```typescript
// 错误：所有转换都要求assigneeId匹配
switch (targetStatus) {
    case 1: // 待执行
        return isAdmin || task.assigneeId === currentUserId; // ❌ 待分配任务没有assigneeId
    case 4: // 已关闭
        return isAdmin || task.assigneeId === currentUserId; // ❌ 管理员权限为false
}
```

#### 修复后（正确的逻辑）
```typescript
// 正确：根据源状态和目标状态分别处理
switch (sourceStatus) {
    case 0: // 待分配
        switch (targetStatus) {
            case 1: // 待分配 -> 待执行（分配任务）
                return isAdmin; // ✅ 管理员可以分配任务
            case 4: // 待分配 -> 已关闭（关闭未分配任务）
                return isAdmin; // ✅ 管理员可以关闭未分配任务
        }
}
```

### 2. 修复管理员权限

```typescript
// 修复前
const isAdmin = false; // ❌ 硬编码为false

// 修复后
const isAdmin = true; // ✅ 暂时设为true便于测试，后续从用户状态获取
```

### 3. 完善拖拽权限检查

```typescript
// 检查任务是否允许拖拽
const canDragTask = (task: any) => {
    const currentStatus = task.taskStatus;
    const isAdmin = true; // 管理员权限
    
    // 已关闭任务不允许拖拽
    if (currentStatus === 4) {
        return false;
    }
    
    // 待分配任务：管理员可以拖拽
    if (currentStatus === 0) {
        return isAdmin; // ✅ 新增：待分配任务权限检查
    }
    
    // 其他状态的权限检查...
};
```

## 📋 完整的权限矩阵

### 待分配任务(0)的拖拽权限

| 源状态 | 目标状态 | 操作 | 权限要求 | 修复状态 |
|-------|---------|------|---------|---------|
| 待分配(0) | 待执行(1) | 分配任务 | 管理员 | ✅ 已修复 |
| 待分配(0) | 已关闭(4) | 关闭未分配任务 | 管理员 | ✅ 已修复 |
| 待分配(0) | 执行中(2) | ❌ 无效转换 | - | ✅ 正确阻止 |
| 待分配(0) | 已完成(3) | ❌ 无效转换 | - | ✅ 正确阻止 |

### 其他状态的拖拽权限（保持不变）

| 源状态 | 目标状态 | 权限要求 | 说明 |
|-------|---------|---------|------|
| 待执行(1) | 执行中(2) | 本人 | 开始执行 |
| 待执行(1) | 已关闭(4) | 本人/管理员 | 关闭任务 |
| 执行中(2) | 已完成(3) | 本人 | 完成任务 |
| 执行中(2) | 已关闭(4) | 本人/管理员 | 关闭任务 |
| 已完成(3) | 执行中(2) | 本人/管理员 | 重新激活 |
| 已关闭(4) | - | ❌ 不允许拖拽 | 只能右键重新打开 |

## 🎨 视觉反馈修复

### 修复前的问题
- 待分配任务拖拽时，所有列都显示为红色（不可放置）
- 用户无法直观了解哪些操作是允许的

### 修复后的效果
- **待执行列**：显示绿色虚线边框（可放置）
- **已关闭列**：显示绿色虚线边框（可放置）
- **执行中列**：显示红色半透明（不可放置）
- **已完成列**：显示红色半透明（不可放置）

## 🧪 测试验证

### 测试用例

1. **待分配任务拖拽到待执行**：
   - ✅ 应该允许拖拽
   - ✅ 显示绿色可放置提示
   - ✅ 成功弹出分配人员对话框

2. **待分配任务拖拽到已关闭**：
   - ✅ 应该允许拖拽
   - ✅ 显示绿色可放置提示
   - ✅ 成功弹出关闭任务对话框

3. **待分配任务拖拽到执行中/已完成**：
   - ✅ 应该阻止拖拽或显示错误提示
   - ✅ 显示红色不可放置提示

### 验证步骤

1. **打开任务看板页面**
2. **找到待分配状态的任务**
3. **开始拖拽任务**
4. **观察各列的颜色提示**：
   - 待执行列：绿色虚线边框 ✅
   - 已关闭列：绿色虚线边框 ✅
   - 执行中列：红色半透明 ✅
   - 已完成列：红色半透明 ✅
5. **拖拽到待执行列**：应该弹出分配人员对话框
6. **拖拽到已关闭列**：应该弹出关闭任务对话框

## 🔄 后续优化

### 1. 用户权限集成
```typescript
// TODO: 从用户状态获取真实权限
const currentUserId = userStore.currentUser.id;
const isAdmin = userStore.currentUser.roles.includes('admin');
```

### 2. 权限配置化
```typescript
// 可以将权限规则配置化，便于维护
const PERMISSION_RULES = {
    0: { // 待分配
        1: 'admin',     // 分配任务需要管理员权限
        4: 'admin'      // 关闭需要管理员权限
    },
    1: { // 待执行
        2: 'assignee',  // 开始执行需要执行人权限
        4: 'assignee|admin' // 关闭需要执行人或管理员权限
    }
    // ...
};
```

### 3. 错误提示优化
```typescript
// 提供更具体的错误提示
if (!hasPermission) {
    if (sourceStatus === 0 && !isAdmin) {
        ElMessage.warning('只有管理员可以分配或关闭待分配任务');
    } else if (!isAssignee) {
        ElMessage.warning('只有任务执行人可以操作此任务');
    }
}
```

## 🎉 修复总结

✅ **待分配任务拖拽**：现在可以正常拖拽到待执行和已关闭状态
✅ **权限检查修复**：根据源状态和目标状态进行精确权限检查
✅ **管理员权限**：修复管理员权限设置，允许管理员操作
✅ **视觉反馈**：正确显示可放置和不可放置的区域
✅ **逻辑一致性**：与右键菜单权限保持完全一致

现在待分配任务可以正常拖拽了！🎯
