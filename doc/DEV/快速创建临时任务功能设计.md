# 快速创建临时任务功能设计

## 功能概述

设计一个全局的快速创建临时任务组件，支持在任何页面快速创建临时任务，提供简化的表单和智能的默认值设置。

## 设计目标

### 🎯 **核心目标**
1. **快速创建**：简化表单，只保留必要字段
2. **全局可用**：封装为全局组件，任何页面都可调用
3. **智能默认**：根据上下文自动设置合理的默认值
4. **用户友好**：提供良好的交互体验和反馈

### 🎯 **业务价值**
- **提高效率**：减少任务创建的步骤和时间
- **降低门槛**：简化操作，让更多用户能快速创建任务
- **增强灵活性**：支持临时性、紧急性任务的快速响应

## 功能设计

### 🔧 **组件架构**

#### **1. 全局任务创建组件**
```
GlobalTaskCreator
├── QuickTaskDialog          # 快速创建对话框
├── TaskFormSimple          # 简化任务表单
├── AssigneeQuickSelector   # 快速执行人选择
└── useGlobalTaskCreator    # 全局状态管理
```

#### **2. 调用方式**
```typescript
// 全局调用接口
const { showQuickCreate } = useGlobalTaskCreator()

// 快速创建临时任务
showQuickCreate({
  title: '快速创建任务',
  defaultValues: {
    taskCategory: 'LS', // 临时任务
    priority: 3,
    departmentId: currentDepartmentId
  },
  onSuccess: (task) => {
    // 创建成功回调
  }
})
```

### 🔧 **表单设计**

#### **简化字段列表**
```typescript
interface QuickTaskForm {
  // 必填字段
  name: string              // 任务名称
  description?: string      // 任务描述
  
  // 分类字段
  taskCategory: string      // 任务类型（默认：LS-临时）
  priority: number          // 优先级（默认：3-中等）
  
  // 归属字段
  departmentId?: number     // 部门ID（智能推断）
  projectId?: number        // 项目ID（可选）
  
  // 执行字段
  assigneeId?: number       // 执行人ID（可选）
  startTime?: string        // 开始时间（默认：当前时间）
  endTime?: string          // 结束时间（默认：当天结束）
  
  // 状态字段
  taskStatus: number        // 任务状态（默认：0-待分配）
}
```

#### **字段优先级**
1. **必填字段**：任务名称
2. **重要字段**：任务描述、执行人
3. **可选字段**：时间范围、项目归属
4. **自动字段**：任务类型、优先级、状态

### 🔧 **智能默认值**

#### **1. 上下文推断**
```typescript
const getSmartDefaults = (context?: TaskCreationContext) => {
  return {
    // 任务分类
    taskCategory: 'LS', // 临时任务
    priority: 3,        // 中等优先级
    taskStatus: 0,      // 待分配
    
    // 时间设置
    startTime: new Date().toISOString(),
    endTime: getEndOfDay().toISOString(),
    
    // 归属推断
    departmentId: context?.departmentId || getCurrentUserDepartment(),
    projectId: context?.projectId,
    
    // 执行人推断
    assigneeId: context?.suggestedAssignee || getCurrentUserId()
  }
}
```

#### **2. 场景化默认值**
```typescript
// 不同场景的默认值
const scenarioDefaults = {
  // 项目页面创建
  project: {
    projectId: currentProjectId,
    departmentId: projectDepartmentId,
    taskCategory: 'LS'
  },
  
  // 部门页面创建
  department: {
    departmentId: currentDepartmentId,
    taskCategory: 'RC' // 日常任务
  },
  
  // 紧急任务创建
  urgent: {
    priority: 5, // 紧急
    taskCategory: 'LS',
    startTime: new Date().toISOString()
  }
}
```

## 技术实现

### 🔧 **1. 全局状态管理**

#### **Composable 设计**
```typescript
// composables/useGlobalTaskCreator.ts
export function useGlobalTaskCreator() {
  const visible = ref(false)
  const options = ref<TaskCreationOptions>({})
  
  const showQuickCreate = (opts?: TaskCreationOptions) => {
    options.value = {
      title: '快速创建任务',
      mode: 'quick',
      ...opts
    }
    visible.value = true
  }
  
  const showFullCreate = (opts?: TaskCreationOptions) => {
    options.value = {
      title: '创建任务',
      mode: 'full',
      ...opts
    }
    visible.value = true
  }
  
  return {
    visible,
    options,
    showQuickCreate,
    showFullCreate
  }
}
```

#### **全局组件注册**
```typescript
// main.ts
import GlobalTaskCreator from '@/components/task/GlobalTaskCreator.vue'

app.component('GlobalTaskCreator', GlobalTaskCreator)
```

### 🔧 **2. 组件实现**

#### **QuickTaskDialog 组件**
```vue
<template>
  <el-dialog
    v-model="visible"
    :title="options.title"
    width="600px"
    :close-on-click-modal="false"
  >
    <TaskFormSimple
      v-model="formData"
      :mode="options.mode"
      :context="options.context"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useGlobalTaskCreator } from '@/composables/useGlobalTaskCreator'
import TaskFormSimple from './TaskFormSimple.vue'

const { visible, options } = useGlobalTaskCreator()
// ... 组件逻辑
</script>
```

#### **TaskFormSimple 组件**
```vue
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
  >
    <!-- 基本信息 -->
    <el-form-item label="任务名称" prop="name">
      <el-input
        v-model="formData.name"
        placeholder="请输入任务名称"
        maxlength="100"
        show-word-limit
      />
    </el-form-item>
    
    <el-form-item label="任务描述" prop="description">
      <el-input
        v-model="formData.description"
        type="textarea"
        :rows="3"
        placeholder="请描述任务内容和要求"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>
    
    <!-- 快速设置 -->
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="formData.priority">
            <el-option label="低" :value="1" />
            <el-option label="普通" :value="2" />
            <el-option label="中等" :value="3" />
            <el-option label="高" :value="4" />
            <el-option label="紧急" :value="5" />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item label="执行人" prop="assigneeId">
          <AssigneeQuickSelector
            v-model="formData.assigneeId"
            :context-id="getAssigneeContextId()"
            :filter-mode="getAssigneeFilterMode()"
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <!-- 时间设置 -->
    <el-form-item label="执行时间" prop="timeRange">
      <el-date-picker
        v-model="timeRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="YYYY-MM-DD HH:mm"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </el-form-item>
    
    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        创建任务
      </el-button>
    </div>
  </el-form>
</template>
```

### 🔧 **3. API 集成**

#### **任务创建服务**
```typescript
// services/taskCreator.ts
export class TaskCreatorService {
  
  async createQuickTask(data: QuickTaskForm): Promise<TaskInfoEntity> {
    // 数据预处理
    const taskData = this.preprocessTaskData(data)
    
    // 调用创建API
    const result = await service.task.info.add(taskData)
    
    // 后处理
    await this.postProcessTask(result.id, data)
    
    return result
  }
  
  private preprocessTaskData(data: QuickTaskForm) {
    return {
      ...data,
      taskCategory: data.taskCategory || 'LS',
      priority: data.priority || 3,
      taskStatus: data.taskStatus || 0,
      scheduleStatus: 1, // 启用调度
      scheduleType: 1,   // 立即执行
      type: 1           // 普通任务
    }
  }
  
  private async postProcessTask(taskId: number, data: QuickTaskForm) {
    // 如果指定了执行人，自动分配任务
    if (data.assigneeId) {
      await this.autoAssignTask(taskId, data.assigneeId)
    }
  }
}
```

## 用户体验设计

### 🎨 **1. 交互流程**

#### **快速创建流程**
```
点击快速创建按钮
  ↓
弹出简化表单对话框
  ↓
填写必要信息（任务名称）
  ↓
选择执行人（可选）
  ↓
点击创建按钮
  ↓
显示创建成功提示
  ↓
可选择：查看任务详情 / 继续创建 / 关闭对话框
```

#### **智能提示流程**
```
输入任务名称
  ↓
AI分析任务类型和优先级
  ↓
自动推荐执行人
  ↓
智能设置时间范围
  ↓
用户确认或调整
```

### 🎨 **2. 视觉设计**

#### **对话框布局**
- **宽度**：600px，适中的宽度
- **高度**：自适应内容，最大不超过屏幕80%
- **分组**：基本信息、快速设置、时间设置
- **按钮**：右下角，取消+创建

#### **表单样式**
- **标签宽度**：100px，统一对齐
- **输入框**：统一高度，圆角设计
- **必填标识**：红色星号，清晰标识
- **字数限制**：实时显示，防止超长

### 🎨 **3. 反馈机制**

#### **成功反馈**
```typescript
// 创建成功后的反馈选项
const successActions = [
  {
    text: '查看任务',
    type: 'primary',
    action: () => showTaskDetail(taskId)
  },
  {
    text: '继续创建',
    type: 'default',
    action: () => showQuickCreate()
  },
  {
    text: '分配任务',
    type: 'success',
    action: () => showAssignDialog(taskId)
  }
]
```

#### **错误处理**
- **网络错误**：显示重试按钮
- **验证错误**：字段级错误提示
- **权限错误**：友好的权限说明

## 扩展功能

### 🚀 **1. 模板功能**

#### **常用任务模板**
```typescript
const taskTemplates = [
  {
    name: '紧急维修',
    category: 'LS',
    priority: 5,
    description: '设备故障需要紧急维修',
    estimatedDuration: 2 // 小时
  },
  {
    name: '日常巡检',
    category: 'RC',
    priority: 2,
    description: '定期设备巡检',
    estimatedDuration: 1
  }
]
```

### 🚀 **2. AI 增强**

#### **智能建议**
- **任务分类识别**：根据任务名称自动识别类型
- **执行人推荐**：基于历史数据推荐合适的执行人
- **时间估算**：根据任务类型估算执行时间
- **优先级建议**：分析任务紧急程度

### 🚀 **3. 批量创建**

#### **批量创建支持**
```typescript
interface BatchTaskCreation {
  template: QuickTaskForm
  variations: Array<{
    name: string
    assigneeId?: number
    startTime?: string
  }>
}
```

## 总结

快速创建临时任务功能的设计重点：

1. **简化操作**：最少的必填字段，智能的默认值
2. **全局可用**：封装为全局组件，统一的调用接口
3. **智能推断**：根据上下文自动设置合理的默认值
4. **良好体验**：清晰的交互流程，及时的反馈机制

这个设计既满足了快速创建的需求，又保持了足够的灵活性，可以适应不同场景下的任务创建需求。

## 实现状态

### ✅ **已完成的组件**

1. **useGlobalTaskCreator.ts** - 全局状态管理
2. **AssigneeQuickSelector.vue** - 快速执行人选择器
3. **TaskFormSimple.vue** - 简化任务表单
4. **GlobalTaskCreator.vue** - 全局任务创建对话框
5. **TaskTemplateSelector.vue** - 任务模板选择器

### ✅ **已集成的功能**

1. **智能默认值设置**
2. **上下文感知的执行人筛选**
3. **快速时间设置**
4. **任务模板支持**
5. **响应式设计**

### 🔧 **使用方式**

#### **在任务信息页面**
```vue
<!-- 快速创建按钮 -->
<el-button type="success" @click="handleQuickCreate">
  <el-icon><Lightning /></el-icon>
  快速创建
</el-button>

<!-- 全局任务创建对话框 -->
<GlobalTaskCreator />
```

#### **JavaScript调用**
```typescript
import { useGlobalTaskCreator } from '../composables/useGlobalTaskCreator'

const { showQuickCreate } = useGlobalTaskCreator()

const handleQuickCreate = () => {
  const context = {
    departmentId: user.info?.departmentId,
    departmentName: user.info?.departmentName
  }

  showQuickCreate({
    title: '快速创建临时任务',
    context,
    onSuccess: (task) => {
      console.log('任务创建成功:', task)
      crud.refresh()
    }
  })
}
```

现在快速创建临时任务功能已经完成设计和基础实现，可以在任何页面快速创建任务！
