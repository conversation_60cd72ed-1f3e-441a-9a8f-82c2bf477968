# 拖拽弹框和服务配置修复说明

## 问题描述

用户反馈两个关键问题：
1. **拖拽时没有弹出框填写**：拖到完成或关闭状态时没有弹出对话框让用户填写详细信息
2. **接口方法不存在**：依旧报 `completeTaskExecution is not a function` 和 `closeTask is not a function` 错误

## 问题分析

### 问题1：拖拽逻辑错误
- **原始逻辑**：拖拽到完成/关闭状态时直接调用接口
- **正确逻辑**：应该弹出对话框让用户填写完成说明、附件等信息

### 问题2：服务配置问题
- **配置冲突**：同时存在 `index.ts` 和 `config.ts` 两个配置文件
- **模块扫描**：系统只扫描 `config.ts` 文件，`index.ts` 被忽略
- **服务未注册**：导致任务服务方法无法正常调用

## 修复方案

### 1. 修复拖拽逻辑

#### **修复前**
```typescript
// 直接更新任务状态
await updateTaskStatus(draggedTask.value.id, targetStatus);
draggedTask.value = null;
```

#### **修复后**
```typescript
// 根据目标状态决定是否需要弹出对话框
if (targetStatus === 3) {
    // 拖拽到完成状态，弹出完成对话框
    currentCompleteTask.value = {
        ...draggedTask.value,
        assigneeId: draggedTask.value.assigneeId || 1
    };
    showCompleteDialog.value = true;
    draggedTask.value = null;
} else if (targetStatus === 4) {
    // 拖拽到关闭状态，弹出关闭对话框
    currentCloseTask.value = draggedTask.value;
    showCloseDialog.value = true;
    draggedTask.value = null;
} else {
    // 其他状态直接更新
    await updateTaskStatus(draggedTask.value.id, targetStatus);
    draggedTask.value = null;
}
```

### 2. 简化状态更新方法

#### **修复后**
```typescript
const updateTaskStatus = async (taskId: number, newStatus: number) => {
    try {
        console.log('更新任务状态:', { taskId, newStatus });
        
        // 这个方法现在只处理简单的状态变更
        // 完成(3)和关闭(4)状态通过对话框处理
        if (newStatus === 3 || newStatus === 4) {
            ElMessage.warning('完成和关闭任务需要通过对话框操作');
            return;
        }

        // 其他状态变更暂时不支持直接拖拽
        ElMessage.warning('该状态变更暂不支持拖拽操作');
        
    } catch (error) {
        console.error('更新任务状态失败:', error);
        ElMessage.error('任务状态更新失败');
    }
};
```

### 3. 清理服务配置

#### **删除冲突文件**
- ❌ 删除 `cool-admin-vue/src/modules/task/index.ts`
- ✅ 保留 `cool-admin-vue/src/modules/task/config.ts`

#### **确保服务配置正确**
```typescript
// cool-admin-vue/src/modules/task/config.ts
export default (): ModuleConfig => {
  return {
    // 服务配置
    services: [
      {
        path: '/admin/task/status',
        name: 'status',
        options: {
          // 自定义API方法
          api: {
            completeTaskExecution: 'POST /task/execution/complete',
            forceCompleteTask: 'POST /task/force-complete',
            closeTask: 'POST /task/close',
            reopenTask: 'POST /task/reopen',
            canCompleteTask: 'GET /task/canComplete',
            canCloseTask: 'GET /task/canClose',
            batchForceCompleteTask: 'POST /task/batch/force-complete',
            batchCloseTask: 'POST /task/batch/close',
            batchReopenTask: 'POST /task/batch/reopen'
          }
        }
      },
      {
        path: '/admin/task/assignment',
        name: 'assignment',
        options: {
          api: {
            manual: 'POST /manual'
            // ... 其他方法
          }
        }
      }
      // ... 其他服务
    ]
  };
};
```

## 拖拽交互流程

### ✅ 修复后的拖拽流程

1. **待分配(0) → 其他状态**：
   - 弹出分配对话框
   - 用户选择执行人和填写分配原因
   - 调用分配接口

2. **任何状态 → 已完成(3)**：
   - 弹出完成对话框 (`TaskCompleter`)
   - 用户填写完成备注和上传附件
   - 调用完成接口

3. **任何状态 → 已关闭(4)**：
   - 弹出关闭对话框 (`TaskCloser`)
   - 用户填写关闭原因
   - 调用关闭接口

4. **其他状态变更**：
   - 暂不支持直接拖拽
   - 提示用户使用按钮操作

## 对话框组件说明

### TaskCompleter 组件
- **触发条件**：拖拽到已完成状态或点击完成按钮
- **必填字段**：无（备注可选）
- **可选字段**：完成备注、附件上传
- **返回数据**：`{ taskId, assigneeId, remark, attachments }`

### TaskCloser 组件
- **触发条件**：拖拽到已关闭状态或点击关闭按钮
- **必填字段**：关闭原因
- **返回数据**：`{ taskId, closeReason }`

### AssigneeSelector 组件
- **触发条件**：从待分配状态拖拽到其他状态
- **必填字段**：执行人选择
- **可选字段**：分配原因
- **返回数据**：`{ assigneeId, assigneeIds, reason }`

## 服务注册机制

### 模块扫描规则
```typescript
// cool-admin-vue/src/cool/bootstrap/module.ts
const files = import.meta.glob('/src/{modules,plugins}/*/{config.ts,service/**,directives/**}', {
    eager: true,
    import: 'default'
});
```

### 服务注册流程
1. **扫描配置**：系统扫描 `src/modules/*/config.ts` 文件
2. **解析服务**：提取 `services` 配置数组
3. **生成API**：根据 `api` 配置生成自定义方法
4. **注册服务**：将服务注册到全局 `service` 对象

### 服务调用路径
```
前端调用: service.task.status.completeTaskExecution()
↓
配置映射: /admin/task/status + POST /task/execution/complete
↓
实际请求: POST /admin/task/status/task/execution/complete
```

## 重要提醒

### ⚠️ 需要重启前端服务
修改 `config.ts` 文件后，需要重启前端开发服务器才能生效：
```bash
npm run dev
# 或
yarn dev
```

### ✅ 验证要点
修复后需要验证：

1. **拖拽交互**：
   - [ ] 拖拽到完成状态弹出完成对话框
   - [ ] 拖拽到关闭状态弹出关闭对话框
   - [ ] 从待分配拖拽弹出分配对话框

2. **服务方法**：
   - [ ] `service.task.status.completeTaskExecution` 方法存在
   - [ ] `service.task.status.closeTask` 方法存在
   - [ ] `service.task.assignment.manual` 方法存在

3. **功能完整性**：
   - [ ] 完成对话框可以填写备注和上传附件
   - [ ] 关闭对话框可以填写关闭原因
   - [ ] 分配对话框可以选择执行人

现在拖拽交互更加用户友好，所有状态变更都需要用户确认和填写详细信息！
