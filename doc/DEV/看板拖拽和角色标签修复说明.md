# 看板拖拽和角色标签修复说明

## 修复内容

### 1. 修复看板任务卡片角色标签未正确渲染问题

#### 问题分析
- **数据格式不匹配**：后端返回的角色数据是英文值（如"PROJECT_OWNER"），但前端显示函数期望中文标签
- **显示逻辑错误**：getRoleType函数只支持中文标签，不支持英文值

#### 修复方案

**1. 扩展getRoleType函数支持双格式**：
```typescript
const getRoleType = (roleName: string): 'danger' | 'warning' | 'primary' | 'info' => {
    const typeMap: Record<string, 'danger' | 'warning' | 'primary' | 'info'> = {
        // 支持英文值
        'PROJECT_OWNER': 'danger',
        'PROJECT_ADMIN': 'warning',
        'PROJECT_MEMBER': 'primary',
        'PROJECT_VIEWER': 'info',
        // 支持中文标签
        '项目负责人': 'danger',
        '项目管理员': 'warning',
        '项目成员': 'primary',
        '项目观察者': 'info'
    };
    return typeMap[roleName] || 'info';
};
```

**2. 新增getRoleText函数进行文本转换**：
```typescript
const getRoleText = (roleName: string): string => {
    const textMap: Record<string, string> = {
        'PROJECT_OWNER': '项目负责人',
        'PROJECT_ADMIN': '项目管理员',
        'PROJECT_MEMBER': '项目成员',
        'PROJECT_VIEWER': '项目观察者'
    };
    return textMap[roleName] || roleName;
};
```

**3. 更新模板使用新的显示逻辑**：
```vue
<div v-if="task.projectRoleName" class="meta-item">
    <el-tag size="small" :type="getRoleType(task.projectRoleName)">
        {{ getRoleText(task.projectRoleName) }}
    </el-tag>
</div>
```

### 2. 实现任务卡片拖动更新任务状态功能

#### 功能特性

**1. 基础拖拽功能**：
- 任务卡片可拖拽到不同状态列
- 拖拽时有视觉反馈效果
- 支持拖拽取消

**2. 智能状态更新**：
- 直接状态变更：其他状态间的拖拽直接更新状态
- 分配+状态变更：从"待分配"拖拽到其他状态时需要选择分配人

**3. 视觉反馈**：
- 拖拽开始：任务卡片半透明+旋转效果
- 拖拽悬停：目标列高亮显示
- 拖拽结束：恢复原始样式

#### 实现方案

**1. 拖拽状态管理**：
```typescript
// 拖拽相关状态
const draggedTask = ref<any>(null);
const showDragAssignDialog = ref(false);
const dragTargetStatus = ref<number>(0);
```

**2. 拖拽事件处理**：
```typescript
// 开始拖拽
const handleDragStart = (event: DragEvent, task: any) => {
    draggedTask.value = task;
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', task.id.toString());
    
    // 添加拖拽样式
    const target = event.target as HTMLElement;
    target.classList.add('dragging');
};

// 拖拽结束
const handleDragEnd = (event: DragEvent) => {
    // 移除拖拽样式
    const target = event.target as HTMLElement;
    target.classList.remove('dragging');
    
    // 移除所有列的拖拽悬停样式
    document.querySelectorAll('.kanban-column').forEach(col => {
        col.classList.remove('drag-over');
    });
};

// 拖拽悬停
const handleDragEnter = (event: DragEvent) => {
    event.preventDefault();
    const target = event.currentTarget as HTMLElement;
    target.classList.add('drag-over');
};

// 拖拽离开
const handleDragLeave = (event: DragEvent) => {
    const target = event.currentTarget as HTMLElement;
    if (!target.contains(event.relatedTarget as Node)) {
        target.classList.remove('drag-over');
    }
};

// 放置处理
const handleDrop = async (event: DragEvent, targetStatus: number) => {
    event.preventDefault();
    
    if (!draggedTask.value) return;

    const sourceStatus = draggedTask.value.taskStatus;
    
    // 状态未变化，不处理
    if (sourceStatus === targetStatus) {
        draggedTask.value = null;
        return;
    }

    // 从待分配拖动到其他状态，需要选择分配人
    if (sourceStatus === 0 && targetStatus !== 0) {
        dragTargetStatus.value = targetStatus;
        showDragAssignDialog.value = true;
        return;
    }

    // 直接更新任务状态
    await updateTaskStatus(draggedTask.value.id, targetStatus);
    draggedTask.value = null;
};
```

**3. 状态更新逻辑**：
```typescript
// 更新任务状态
const updateTaskStatus = async (taskId: number, newStatus: number) => {
    try {
        await service.organization.project.task.updateStatus({
            taskId: taskId,
            taskStatus: newStatus
        });

        ElMessage.success('任务状态更新成功');
        await fetchKanbanData(); // 刷新看板数据
    } catch (error) {
        console.error('更新任务状态失败:', error);
        ElMessage.error('任务状态更新失败');
    }
};

// 拖拽分配确认
const handleDragAssignConfirm = async (data: { assigneeId?: number; assigneeIds?: number[]; reason: string }) => {
    const assigneeId = data.assigneeId || (data.assigneeIds && data.assigneeIds[0]);
    
    try {
        // 先分配任务
        await service.task.assignment.manualAssignTask({
            taskId: draggedTask.value.id,
            assigneeIds: [assigneeId],
            reason: data.reason
        });

        // 再更新任务状态
        await updateTaskStatus(draggedTask.value.id, dragTargetStatus.value);

        ElMessage.success('任务分配并状态更新成功');
        showDragAssignDialog.value = false;
        draggedTask.value = null;
    } catch (error) {
        ElMessage.error('任务分配失败');
        console.error('任务分配失败:', error);
    }
};
```

**4. 模板更新**：
```vue
<!-- 状态列支持拖拽 -->
<div 
    v-for="status in taskStatuses" 
    :key="status.value" 
    class="kanban-column"
    @dragover="handleDragOver"
    @dragenter="handleDragEnter"
    @dragleave="handleDragLeave"
    @drop="handleDrop($event, status.value)"
>
    <!-- 任务卡片支持拖拽 -->
    <div 
        v-for="task in getTasksByStatus(status.value)" 
        :key="task.id"
        class="task-card"
        draggable="true"
        @dragstart="handleDragStart($event, task)"
        @dragend="handleDragEnd"
        @click="viewTaskDetail(task)"
    >
        <!-- 任务内容 -->
    </div>
</div>

<!-- 拖拽分配对话框 -->
<AssigneeSelector
    v-model="showDragAssignDialog"
    :taskName="draggedTask?.name"
    :taskTags="draggedTask?.taskTags"
    filterMode="project"
    :contextId="selectedProjectId"
    @confirm="handleDragAssignConfirm"
/>
```

**5. CSS样式**：
```scss
.kanban-column {
    transition: all 0.2s ease;

    &.drag-over {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
    }
}

.task-card {
    cursor: grab;
    transition: all 0.2s ease;

    &:active {
        cursor: grabbing;
    }

    &.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
        cursor: grabbing;
    }
}
```

## 功能流程

### 角色标签显示流程
1. 后端返回任务数据，包含`projectRoleName`字段（英文值）
2. 前端使用`getRoleType()`获取标签颜色类型
3. 前端使用`getRoleText()`将英文值转换为中文显示
4. 渲染带有正确颜色和文本的角色标签

### 拖拽操作流程

**普通状态变更**：
1. 用户拖拽任务卡片到目标状态列
2. 系统检测状态变化
3. 直接调用API更新任务状态
4. 刷新看板数据显示

**待分配任务拖拽**：
1. 用户从"待分配"列拖拽任务到其他状态列
2. 系统检测到需要分配人
3. 弹出分配人选择对话框
4. 用户选择分配人并确认
5. 系统先执行任务分配
6. 再更新任务状态
7. 刷新看板数据显示

## 用户体验改进

1. **视觉反馈**：拖拽过程中的动画和高亮效果
2. **智能处理**：自动识别是否需要分配人
3. **错误处理**：完善的错误提示和回滚机制
4. **数据同步**：操作完成后自动刷新看板数据

修复后的看板现在支持完整的拖拽操作和正确的角色标签显示！
