# 看板搜索条件完整对齐修复说明

## 问题描述

用户反馈看板的搜索条件应该与列表的**所有**搜索条件保持一致，包括高级搜索的所有字段，而不仅仅是基础的任务名称和执行人搜索。

## 问题分析

### 原始问题
看板搜索区域只包含：
- 任务名称
- 执行人

### 缺失的搜索条件
列表视图中的完整搜索条件包括：

**基础搜索**：
- 任务名称
- 执行时间范围

**高级搜索**：
- 执行人姓名
- 执行人手机号
- 任务状态
- 任务类型

## 修复方案

### 1. 完整的看板搜索表单

```vue
<!-- 看板搜索区域 -->
<div class="kanban-search">
    <!-- 基础搜索行 -->
    <div class="basic-search">
        <el-form :model="searchForm" inline>
            <el-form-item label="任务名称">
                <el-input
                    v-model="searchForm.keyword"
                    placeholder="请输入任务名称"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleSearch"
                />
            </el-form-item>
            <el-form-item label="执行时间">
                <el-date-picker 
                    v-model="searchForm.executionDateRange" 
                    type="daterange" 
                    range-separator="至"
                    start-placeholder="开始日期" 
                    end-placeholder="结束日期" 
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD" 
                    style="width: 240px" 
                    @change="handleSearch" 
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">
                    <el-icon><Search /></el-icon>
                    搜索
                </el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button type="text" @click="showAdvancedSearch = !showAdvancedSearch" style="margin-left: 8px">
                    高级搜索
                    <el-icon>
                        <ArrowDown v-if="!showAdvancedSearch" />
                        <ArrowUp v-else />
                    </el-icon>
                </el-button>
            </el-form-item>
        </el-form>
    </div>

    <!-- 高级搜索区域 -->
    <div v-show="showAdvancedSearch" class="advanced-search">
        <el-form :model="searchForm" inline>
            <el-form-item label="执行人">
                <el-input 
                    v-model="searchForm.assigneeName" 
                    placeholder="请输入执行人姓名" 
                    clearable
                    style="width: 180px" 
                />
            </el-form-item>
            <el-form-item label="执行人手机">
                <el-input 
                    v-model="searchForm.assigneePhone" 
                    placeholder="请输入手机号" 
                    clearable
                    style="width: 180px" 
                />
            </el-form-item>
            <el-form-item label="任务状态">
                <el-select 
                    v-model="searchForm.taskStatus" 
                    placeholder="请选择状态" 
                    clearable
                    style="width: 150px"
                >
                    <el-option label="待分配" :value="0" />
                    <el-option label="待执行" :value="1" />
                    <el-option label="执行中" :value="2" />
                    <el-option label="已完成" :value="3" />
                    <el-option label="已关闭" :value="4" />
                </el-select>
            </el-form-item>
            <el-form-item label="任务类型">
                <el-select 
                    v-model="searchForm.taskCategory" 
                    placeholder="请选择类型" 
                    clearable
                    style="width: 150px"
                >
                    <el-option label="日常任务" value="RC" />
                    <el-option label="周期任务" value="ZQ" />
                    <el-option label="临时任务" value="LS" />
                    <el-option label="场景步骤" value="SOP_STEP" />
                </el-select>
            </el-form-item>
        </el-form>
    </div>
</div>
```

### 2. 统一的样式设计

```scss
.kanban-search {
    flex-shrink: 0;
    margin-bottom: 16px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid var(--el-border-color-light);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

    .basic-search {
        margin-bottom: 0;

        .el-form {
            margin-bottom: 0;
        }

        .el-form-item {
            margin-bottom: 0;
            margin-right: 16px;

            &:last-child {
                margin-right: 0;
            }

            :deep(.el-form-item__label) {
                color: var(--el-text-color-regular);
            }
        }
    }

    .advanced-search {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid var(--el-border-color-lighter);

        .el-form-item {
            margin-bottom: 16px;
            margin-right: 16px;

            &:last-child {
                margin-right: 0;
            }

            :deep(.el-form-item__label) {
                color: var(--el-text-color-regular);
            }
        }
    }

    // 高级搜索展开按钮样式
    :deep(.el-button--text) {
        color: var(--el-color-primary);

        &:hover {
            color: var(--el-color-primary-light-3);
        }
    }
}
```

## 功能特性

### ✅ 修复后的搜索条件对齐

1. **基础搜索条件**：
   - ✅ 任务名称（关键词搜索）
   - ✅ 执行时间范围（日期选择器）

2. **高级搜索条件**：
   - ✅ 执行人姓名（模糊搜索）
   - ✅ 执行人手机号（精确搜索）
   - ✅ 任务状态（下拉选择）
   - ✅ 任务类型（下拉选择）

3. **交互功能**：
   - ✅ 高级搜索展开/收起
   - ✅ 搜索按钮
   - ✅ 重置按钮
   - ✅ 回车键搜索

4. **样式统一**：
   - ✅ 与列表搜索区域相同的视觉设计
   - ✅ 相同的布局和间距
   - ✅ 统一的表单控件样式

### 🔄 数据流程

1. **搜索条件共享**：
   - 看板和列表使用相同的 `searchForm` 响应式对象
   - 所有搜索字段完全一致

2. **搜索逻辑分离**：
   - 看板模式：调用 `fetchKanbanData()` 方法
   - 列表模式：调用 `Crud.value?.refresh()` 方法

3. **参数传递一致**：
   ```typescript
   // 构建请求参数（看板和列表完全相同）
   const requestParams: any = {
       projectId: selectedProjectId.value,
       // 基础搜索
       keyWord: searchForm.value.keyword,
       executionStartDate: searchForm.value.executionDateRange[0],
       executionEndDate: searchForm.value.executionDateRange[1],
       // 高级搜索
       assigneeName: searchForm.value.assigneeName,
       assigneePhone: searchForm.value.assigneePhone,
       taskStatus: searchForm.value.taskStatus,
       taskCategory: searchForm.value.taskCategory
   };
   ```

## 用户体验改进

1. **一致性**：看板和列表的搜索功能完全一致
2. **便利性**：在任一视图中设置的搜索条件在切换视图时保持不变
3. **直观性**：相同的界面布局和交互方式
4. **高效性**：支持组合搜索条件进行精确筛选

## 验证要点

修复后请验证：

1. **搜索条件完整性**：
   - [ ] 看板搜索区域包含所有列表搜索条件
   - [ ] 高级搜索展开/收起功能正常
   - [ ] 所有表单控件工作正常

2. **搜索功能一致性**：
   - [ ] 相同搜索条件在看板和列表中返回相同结果
   - [ ] 搜索条件在视图切换时保持不变
   - [ ] 重置功能清空所有搜索条件

3. **界面一致性**：
   - [ ] 看板和列表搜索区域样式一致
   - [ ] 表单控件尺寸和布局一致
   - [ ] 交互反馈一致

现在看板的搜索条件已经与列表完全对齐，用户可以使用相同的搜索功能在两种视图模式下筛选任务数据。
