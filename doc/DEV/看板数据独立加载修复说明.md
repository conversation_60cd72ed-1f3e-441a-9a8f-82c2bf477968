# 看板数据独立加载修复说明

## 问题描述

用户反馈看板数据不展示的问题。原因是看板使用的是列表的CRUD数据，但看板需要使用独立的接口来加载数据，搜索条件相同但不共用数据。

## 问题分析

### 原始问题
1. **数据源错误**：看板使用 `const tasks = computed(() => Crud.value?.list || []);` 获取数据
2. **接口调用错误**：看板没有调用专用的 `kanbanPage` 接口
3. **搜索逻辑错误**：看板和列表共用搜索逻辑，但应该独立处理
4. **视图切换问题**：切换到看板模式时没有触发数据加载

## 修复方案

### 1. 添加看板独立数据状态

```typescript
// 看板独立的数据状态
const kanbanTasks = ref<any[]>([]);
const kanbanLoading = ref(false);

const getTasksByStatus = (status: number) => {
    return kanbanTasks.value.filter(task => task.taskStatus === status);
};
```

### 2. 实现看板独立数据加载方法

```typescript
// 看板数据加载方法
const fetchKanbanData = async () => {
    if (!selectedProjectId.value) {
        console.log('没有选中项目，清空看板数据');
        kanbanTasks.value = [];
        return;
    }

    kanbanLoading.value = true;
    try {
        console.log('开始加载看板数据，项目ID:', selectedProjectId.value);
        
        // 构建请求参数，包含项目ID和搜索条件
        const requestParams: any = {
            projectId: selectedProjectId.value,
            page: 1,
            size: 1000 // 看板需要获取所有数据，不分页
        };

        // 添加搜索条件（与列表视图相同的搜索条件）
        if (searchForm.value.keyword) {
            requestParams.keyWord = searchForm.value.keyword;
        }

        if (searchForm.value.executionDateRange && searchForm.value.executionDateRange.length === 2) {
            requestParams.executionStartDate = searchForm.value.executionDateRange[0];
            requestParams.executionEndDate = searchForm.value.executionDateRange[1];
        }

        if (searchForm.value.assigneeName) {
            requestParams.assigneeName = searchForm.value.assigneeName;
        }

        if (searchForm.value.assigneePhone) {
            requestParams.assigneePhone = searchForm.value.assigneePhone;
        }

        if (searchForm.value.taskStatus !== undefined && searchForm.value.taskStatus !== null) {
            requestParams.taskStatus = searchForm.value.taskStatus;
        }

        if (searchForm.value.taskCategory) {
            requestParams.taskCategory = searchForm.value.taskCategory;
        }

        console.log('看板请求参数:', requestParams);

        // 使用看板专用接口
        const response = await service.organization.project.task.kanbanPage(requestParams);
        console.log('看板数据响应:', response);

        // 处理响应数据
        if (response && response.list && Array.isArray(response.list)) {
            kanbanTasks.value = response.list;
        } else if (response && Array.isArray(response)) {
            kanbanTasks.value = response;
        } else {
            console.warn('看板数据格式异常:', response);
            kanbanTasks.value = [];
        }

        console.log('看板数据加载完成，任务数量:', kanbanTasks.value.length);
    } catch (error) {
        console.error('看板数据加载失败:', error);
        ElMessage.error('看板数据加载失败');
        kanbanTasks.value = [];
    } finally {
        kanbanLoading.value = false;
    }
};
```

### 3. 修改搜索处理逻辑

```typescript
// 搜索处理函数
const handleSearch = () => {
    console.log('执行搜索，搜索条件:', searchForm.value);

    // 确保有选中的项目
    if (!selectedProjectId.value) {
        ElMessage.warning('请先选择一个项目');
        return;
    }

    // 根据当前视图模式执行不同的搜索逻辑
    if (viewMode.value === 'kanban') {
        // 看板模式：直接调用看板数据加载
        console.log('看板模式搜索');
        fetchKanbanData();
    } else {
        // 列表模式：使用CRUD组件刷新
        console.log('列表模式搜索');
        nextTick(() => {
            // CRUD刷新逻辑...
        });
    }
};
```

### 4. 修改项目切换逻辑

```typescript
function onProjectChange() {
    console.log('项目切换，新项目ID:', selectedProjectId.value);

    if (selectedProjectId.value) {
        // 根据当前视图模式加载数据
        if (viewMode.value === 'kanban') {
            // 看板模式：加载看板数据
            console.log('项目切换 - 看板模式');
            fetchKanbanData();
        } else {
            // 列表模式：刷新CRUD数据
            console.log('项目切换 - 列表模式');
            // CRUD刷新逻辑...
        }
    } else {
        // 清空数据
        if (viewMode.value === 'kanban') {
            kanbanTasks.value = [];
        } else {
            // 清空列表数据...
        }
    }
}
```

### 5. 添加视图模式切换处理

```typescript
// 监听视图模式切换
const handleViewModeChange = (mode: 'kanban' | 'list') => {
    console.log('视图模式切换:', mode);
    viewMode.value = mode;
    
    // 如果切换到看板模式且有选中项目，则加载看板数据
    if (mode === 'kanban' && selectedProjectId.value) {
        fetchKanbanData();
    }
};
```

### 6. 修改模板中的视图切换按钮

```vue
<!-- 视图切换控件 -->
<div class="view-controls">
    <el-button-group>
        <el-button :type="viewMode === 'kanban' ? 'primary' : ''" @click="handleViewModeChange('kanban')">
            <el-icon><Grid /></el-icon>
            看板
        </el-button>
        <el-button :type="viewMode === 'list' ? 'primary' : ''" @click="handleViewModeChange('list')">
            <el-icon><List /></el-icon>
            列表
        </el-button>
    </el-button-group>
</div>
```

### 7. 添加看板加载状态

```vue
<!-- 看板主体 -->
<div class="kanban-board" v-loading="kanbanLoading">
    <!-- 看板内容 -->
</div>
```

### 8. 修复类型错误

```typescript
const getPriorityType = (priority: number): 'success' | 'info' | 'warning' | 'danger' => {
    const types: ('success' | 'info' | 'warning' | 'danger')[] = ['info', 'success', 'info', 'warning', 'danger', 'danger'];
    return types[priority] || 'info';
};

const getCategoryType = (category: string): 'primary' | 'success' | 'warning' | 'info' => {
    const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info'> = {
        'RC': 'primary',
        'ZQ': 'success', 
        'LS': 'warning',
        'SOP_STEP': 'info'
    };
    return typeMap[category] || 'info';
};

const getRoleType = (roleName: string): 'danger' | 'warning' | 'primary' | 'info' => {
    const typeMap: Record<string, 'danger' | 'warning' | 'primary' | 'info'> = {
        '项目负责人': 'danger',
        '项目管理员': 'warning',
        '项目成员': 'primary',
        '项目观察者': 'info'
    };
    return typeMap[roleName] || 'info';
};
```

## 功能特性

### ✅ 修复后的功能

1. **独立数据源**：
   - 看板使用 `kanbanTasks` 独立数据状态
   - 列表使用 `Crud.value?.list` CRUD数据状态
   - 两者互不干扰

2. **独立接口调用**：
   - 看板调用 `service.organization.project.task.kanbanPage()` 接口
   - 列表使用CRUD自动调用 `service.organization.project.task.page()` 接口

3. **相同搜索条件**：
   - 看板和列表使用相同的 `searchForm` 搜索条件
   - 搜索参数构建逻辑相同
   - 但数据加载方式独立

4. **智能数据加载**：
   - 项目切换时根据当前视图模式加载对应数据
   - 视图切换时自动加载看板数据
   - 搜索时根据视图模式调用对应的数据加载方法

5. **加载状态显示**：
   - 看板有独立的加载状态指示
   - 用户体验更好

## 使用说明

1. **页面加载**：自动获取项目列表，默认显示列表视图
2. **切换到看板**：点击看板按钮，自动加载看板数据
3. **项目切换**：选择不同项目，根据当前视图加载对应数据
4. **搜索功能**：在任一视图中搜索，只影响当前视图的数据
5. **数据独立**：看板和列表的数据完全独立，互不影响

修复后的看板应该能正常显示任务数据，并且与列表视图保持独立的数据状态。
