# 项目任务页面修复说明

## 问题描述

项目任务页面 (`cool-admin-vue/src/modules/organization/views/project/task.vue`) 存在以下问题：

1. **看板与列表数据未正确加载**：看板视图无法正确获取和显示任务数据
2. **共用数据搜索条件未显示**：搜索表单的条件无法正确显示和同步
3. **项目选择器响应式绑定问题**：项目选择器的数据绑定不正确

## 修复内容

### 1. 修复项目选择器响应式绑定

**问题**：模板中直接使用 `projectTask.selectedProjectId.value` 导致响应式失效

**修复**：
```vue
<!-- 修复前 -->
<el-select v-model="projectTask.selectedProjectId.value" ...>

<!-- 修复后 -->
<el-select v-model="selectedProjectId" ...>
```

**脚本修改**：
```typescript
// 解构出需要在模板中使用的响应式数据
const { selectedProjectId, projectOptions, onProjectChange } = projectTask;
```

### 2. 修复项目选项获取API

**问题**：使用错误的API接口获取项目列表

**修复**：
```typescript
// 修复前
const response = await service.organization.project.info.options();

// 修复后
const response = await service.organization.project.task.accessibleProjects();
```

**数据处理优化**：
```typescript
let list: { value: number; label: string }[] = [];
if (response && Array.isArray(response)) {
    list = response.map((item: any) => ({ 
        value: item.projectId || item.id, 
        label: item.projectName || item.name 
    }));
}
```

### 3. 修复看板组件搜索条件显示

**问题**：看板组件缺少任务操作方法的解构

**修复**：
```typescript
const {
    // ... 其他属性
    handleAssignTask,
    handleCompleteTask,
    handleCloseTask,
    handleAdjustTime,
    // ... 其他方法
} = props.projectTask;
```

### 4. 增强看板任务卡片功能

**新增功能**：
- 添加任务操作按钮（分配、完成、关闭、调整时间）
- 优化任务卡片布局和样式
- 添加任务操作按钮的样式

**代码实现**：
```vue
<div class="task-actions">
    <el-button type="primary" text size="small" @click.stop="handleAssignTask(task)">分配</el-button>
    <el-button type="success" text size="small" @click.stop="handleCompleteTask(task)">完成</el-button>
    <el-button type="danger" text size="small" @click.stop="handleCloseTask(task)">关闭</el-button>
    <el-button type="warning" text size="small" @click.stop="handleAdjustTime(task)">调整时间</el-button>
</div>
```

### 5. 修复重置功能

**问题**：重置功能在看板视图中不能正确重新加载数据

**修复**：
```typescript
const handleReset = () => {
    resetAction(); // 调用hook中的重置方法
    // 重新初始化看板数据并获取
    initializeKanbanData();
    fetchKanbanData();
};
```

**useProjectTask中的修复**：
```typescript
const handleReset = () => {
    searchForm.value = {
        keyword: '',
        executionDateRange: getTodayRange(),
        assigneeName: '',
        assigneePhone: '',
        taskStatus: undefined,
        taskCategory: '',
    };
    if (selectedProjectId.value) {
        handleSearch();
    }
};
```

## 修改文件列表

1. **主页面文件**：
   - `cool-admin-vue/src/modules/organization/views/project/task.vue`

2. **组合式函数**：
   - `cool-admin-vue/src/modules/organization/composables/useProjectTask.ts`

3. **子组件**：
   - `cool-admin-vue/src/modules/organization/views/project/components/ProjectTaskList.vue`
   - `cool-admin-vue/src/modules/organization/views/project/components/ProjectTaskKanban.vue`

## 功能验证

### 验证步骤

1. **项目选择器测试**：
   - 页面加载时应自动获取项目列表
   - 选择项目后应正确切换数据
   - 清空项目选择应清空任务列表

2. **搜索功能测试**：
   - 基础搜索：任务名称、执行时间
   - 高级搜索：执行人、手机号、任务状态、任务类型
   - 重置功能应清空所有搜索条件并重新加载数据

3. **视图切换测试**：
   - 列表视图和看板视图应显示相同的数据
   - 搜索条件在两个视图间应保持同步
   - 任务操作在两个视图中都应正常工作

4. **看板功能测试**：
   - 任务卡片应正确显示任务信息
   - 任务操作按钮应正常工作
   - 加载更多功能应正常工作
   - 不同状态列应正确显示对应状态的任务

## 预期效果

修复后的项目任务页面应该：

1. ✅ 项目选择器正常工作，能正确获取和显示项目列表
2. ✅ 搜索条件能正确显示和同步
3. ✅ 看板视图能正确加载和显示任务数据
4. ✅ 列表视图和看板视图数据保持同步
5. ✅ 任务操作功能在两个视图中都正常工作
6. ✅ 重置功能能正确清空搜索条件并重新加载数据
