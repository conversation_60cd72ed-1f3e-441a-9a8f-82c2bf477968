# 项目任务页面拆分修复说明

## 问题背景

用户将原始的项目任务页面进行了组件拆分，但拆分后功能完全不能使用。需要参考原始的 `task.vue.bak` 文件来修复现在的拆分设计，保持组件拆分的架构。

## 修复策略

基于原始文件的完整功能，重新实现拆分后的组件架构：

### 1. 主页面 (task.vue)
- 保持简洁的主页面结构
- 负责视图切换和项目选择器
- 通过props传递共享的composable实例给子组件

### 2. 共享逻辑 (useProjectTask.ts)
- 包含完整的CRUD配置
- 包含所有任务操作方法
- 包含项目选择和搜索逻辑
- 在组件挂载时自动初始化

### 3. 列表组件 (ProjectTaskList.vue)
- 使用共享的CRUD、Table、Upsert实例
- 包含完整的搜索表单
- 包含任务操作对话框

### 4. 看板组件 (ProjectTaskKanban.vue)
- 使用看板专用的数据结构
- 调用后端的kanbanPage接口
- 包含任务操作功能

## 主要修复内容

### 1. 修复useProjectTask.ts

**完善CRUD配置**：
```typescript
const Crud = useCrud({
    service: service.organization.project.task,
    onRefresh(params, { next, done, render }) {
        if (!selectedProjectId.value) {
            render([]);
            done();
            return;
        }
        const requestParams: any = { ...params, projectId: selectedProjectId.value };
        
        // 合并搜索条件
        if (searchForm.value.keyword) requestParams.keyWord = searchForm.value.keyword;
        if (searchForm.value.executionDateRange && searchForm.value.executionDateRange.length === 2) {
            requestParams.executionStartDate = searchForm.value.executionDateRange[0];
            requestParams.executionEndDate = searchForm.value.executionDateRange[1];
        }
        // ... 其他搜索条件
        
        next(requestParams);
    }
});
```

**添加任务操作方法**：
```typescript
const handleAssignTask = (row: any) => {
    currentAssignTask.value = row;
    showAssignDialog.value = true;
};

const handleAssignConfirm = async (data: { assigneeId?: number; assigneeIds?: number[]; reason: string }) => {
    const assigneeId = data.assigneeId || (data.assigneeIds && data.assigneeIds[0]);
    if (!currentAssignTask.value?.id || !assigneeId) return ElMessage.warning('请选择任务和执行人');
    try {
        await service.task.assignment.manualAssignTask({
            taskId: currentAssignTask.value.id,
            assigneeIds: [assigneeId],
            reason: data.reason
        });
        ElMessage.success('任务分配成功');
        showAssignDialog.value = false;
        Crud.value?.refresh();
    } catch (error) {
        ElMessage.error('任务分配失败');
        console.error(error);
    }
};
```

**项目选择逻辑**：
```typescript
async function fetchProjectOptions() {
    try {
        let response;
        try {
            response = await service.organization.project.task.accessibleProjects();
        } catch (e) {
            response = await service.organization.project.info.options();
        }

        // 处理不同的响应格式
        let list: { value: number; label: string }[] = [];
        if (Array.isArray(response)) {
            list = response.map(item => ({
                value: item.projectId || item.value || item.id,
                label: item.projectName || item.label || item.name
            }));
        }
        // ... 其他格式处理

        projectOptions.value = list;
        if (list && list.length > 0) {
            selectedProjectId.value = list[0].value;
            nextTick(() => {
                setTimeout(() => {
                    if (Crud.value) {
                        Crud.value.refresh();
                    }
                }, 100);
            });
        }
    } catch (error) {
        ElMessage.error("获取项目列表失败");
        console.error('获取项目列表错误:', error);
    }
}
```

**自动初始化**：
```typescript
onMounted(() => {
    searchForm.value.executionDateRange = getTodayRange();
    fetchProjectOptions();
});
```

### 2. 修复主页面 task.vue

**简化主页面结构**：
```vue
<template>
    <div class="project-task-container">
        <!-- 公共头部：项目选择器 -->
        <div class="project-header">
            <el-select v-model="selectedProjectId" placeholder="请选择项目以查看任务" 
                      @change="onProjectChange" clearable filterable style="width: 300px">
                <el-option v-for="item in projectOptions" :key="item.value" 
                          :label="item.label" :value="item.value" />
            </el-select>
        </div>

        <!-- 视图切换控件 -->
        <div class="view-controls">
            <el-button-group>
                <el-button :type="viewMode === 'list' ? 'primary' : ''" @click="viewMode = 'list'">
                    <el-icon><List /></el-icon>列表
                </el-button>
                <el-button :type="viewMode === 'kanban' ? 'primary' : ''" @click="viewMode = 'kanban'">
                    <el-icon><Grid /></el-icon>看板
                </el-button>
            </el-button-group>
        </div>

        <!-- 动态视图 -->
        <div class="view-wrapper">
            <keep-alive>
                <component :is="currentView" :project-task="projectTask" />
            </keep-alive>
        </div>
    </div>
</template>
```

**脚本部分**：
```typescript
<script setup lang="ts">
import { ref, computed, shallowRef } from 'vue';
import { useProjectTask } from '../../composables/useProjectTask';
import ProjectTaskList from './components/ProjectTaskList.vue';
import ProjectTaskKanban from './components/ProjectTaskKanban.vue';
import { Grid, List } from '@element-plus/icons-vue';

defineOptions({ name: "project-task" });

// 视图模式
const viewMode = ref<'list' | 'kanban'>('list');

// 初始化共享逻辑
const projectTask = useProjectTask();

// 解构出需要在模板中使用的响应式数据
const { selectedProjectId, projectOptions, onProjectChange } = projectTask;

// 动态组件
const currentView = computed(() => {
    return viewMode.value === 'list' ? shallowRef(ProjectTaskList) : shallowRef(ProjectTaskKanban);
});
</script>
```

### 3. 修复子组件

**ProjectTaskList.vue**：
- 正确接收和使用props中的projectTask实例
- 包含完整的搜索表单和任务操作对话框
- 使用共享的CRUD、Table、Upsert实例

**ProjectTaskKanban.vue**：
- 使用看板专用的数据获取逻辑
- 调用service.organization.project.task.kanbanPage接口
- 包含任务操作按钮和对话框

## 功能特性

### ✅ 已修复的功能

1. **项目选择器**：
   - 自动获取可访问的项目列表
   - 默认选中第一个项目
   - 切换项目时自动刷新数据

2. **搜索功能**：
   - 基础搜索：任务名称、执行时间
   - 高级搜索：执行人、手机号、任务状态、任务类型
   - 搜索条件在列表和看板视图间保持同步

3. **列表视图**：
   - 完整的CRUD操作
   - 任务操作按钮（分配、完成、关闭、调整时间）
   - 分页和排序功能

4. **看板视图**：
   - 按任务状态分列显示
   - 任务卡片包含完整信息
   - 支持任务操作
   - 加载更多功能

5. **任务操作**：
   - 调整分配：支持重新分配任务执行人
   - 完成任务：标记任务为已完成状态
   - 关闭任务：关闭任务并记录原因
   - 调整时间：修改任务的执行时间

### 🔧 技术改进

1. **组件拆分**：保持了清晰的组件职责分离
2. **状态共享**：通过composable实现状态和逻辑共享
3. **响应式设计**：确保数据变化时UI正确更新
4. **错误处理**：完善的错误提示和异常处理
5. **性能优化**：使用keep-alive缓存组件状态

## 使用说明

1. **页面加载**：自动获取项目列表并选中第一个项目
2. **切换项目**：选择不同项目查看对应的任务数据
3. **切换视图**：在列表和看板视图间自由切换
4. **搜索过滤**：使用搜索条件过滤任务数据
5. **任务操作**：通过操作按钮执行任务相关操作

修复后的项目任务页面保持了原有的完整功能，同时采用了更好的组件拆分架构，提高了代码的可维护性和复用性。
