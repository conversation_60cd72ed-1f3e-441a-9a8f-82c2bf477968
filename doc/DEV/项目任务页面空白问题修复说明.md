# 项目任务页面空白问题修复说明

## 问题描述

用户反馈项目任务页面的看板和列表都是空白的，没有显示任何数据。

## 问题分析

通过代码分析，发现了以下关键问题：

### 1. **onMounted 位置错误**
- **问题**：`onMounted` 被放在了组合式函数 `useProjectTask.ts` 中
- **影响**：组合式函数中的 `onMounted` 不会被正确执行，导致初始化逻辑没有运行
- **原因**：`onMounted` 只能在组件的 setup 函数中使用，不能在独立的组合式函数中使用

### 2. **CRUD 组件引用问题**
- **问题**：子组件中使用了字符串 ref 而不是响应式引用
- **影响**：CRUD 组件无法正确绑定到共享的实例
- **原因**：Vue 3 的组合式 API 需要使用响应式引用

### 3. **数据初始化时机问题**
- **问题**：项目选择器和任务数据的初始化没有正确执行
- **影响**：页面加载时没有获取项目列表，也没有加载任务数据

## 修复方案

### 1. 修复 useProjectTask.ts

**移除错误的 onMounted**：
```typescript
// ❌ 错误的做法
onMounted(() => {
    searchForm.value.executionDateRange = getTodayRange();
    fetchProjectOptions();
});

// ✅ 正确的做法
const initialize = () => {
    searchForm.value.executionDateRange = getTodayRange();
    fetchProjectOptions();
};
```

**改进项目数据获取**：
```typescript
async function fetchProjectOptions() {
    try {
        console.log('开始获取项目列表...');
        let response;
        try {
            // 首先尝试使用项目信息接口的list方法
            response = await service.organization.project.info.list();
            console.log('项目信息list接口响应:', response);
        } catch (e) {
            console.warn('项目信息list接口失败，尝试使用page接口:', e);
            try {
                // 如果list失败，尝试使用page接口
                response = await service.organization.project.info.page({ page: 1, size: 100 });
                console.log('项目信息page接口响应:', response);
            } catch (e2) {
                console.warn('项目信息page接口也失败，尝试任务接口:', e2);
                // 最后尝试任务接口
                response = await service.organization.project.task.accessibleProjects();
                console.log('项目任务接口响应:', response);
            }
        }

        // 处理不同的响应格式
        let list: { value: number; label: string }[] = [];
        console.log('响应数据类型:', typeof response, '是否为数组:', Array.isArray(response));
        
        if (Array.isArray(response)) {
            // 直接返回数组
            list = response.map(item => ({
                value: item.projectId || item.value || item.id,
                label: item.projectName || item.label || item.name || item.projectName
            }));
        } else if (response && response.data && Array.isArray(response.data)) {
            // data包装格式
            list = response.data.map(item => ({
                value: item.projectId || item.value || item.id,
                label: item.projectName || item.label || item.name || item.projectName
            }));
        } else if (response && Array.isArray(response.list)) {
            // list分页格式
            list = response.list.map(item => ({
                value: item.projectId || item.value || item.id,
                label: item.projectName || item.label || item.name || item.projectName
            }));
        } else if (response && response.records && Array.isArray(response.records)) {
            // MyBatis Plus分页格式
            list = response.records.map(item => ({
                value: item.projectId || item.value || item.id,
                label: item.projectName || item.label || item.name || item.projectName
            }));
        }

        console.log('处理后的项目选项:', list);
        projectOptions.value = list;

        // 默认选中第一个项目
        if (list && list.length > 0) {
            selectedProjectId.value = list[0].value;
            console.log('默认选中项目ID:', selectedProjectId.value);

            // 等待CRUD组件初始化完成后再触发查询
            nextTick(() => {
                setTimeout(() => {
                    if (Crud.value) {
                        console.log('CRUD组件已初始化，触发首次查询');
                        Crud.value.refresh();
                    }
                }, 100);
            });
        } else {
            console.warn('没有可用的项目');
            ElMessage.warning('没有可用的项目，请联系管理员');
        }
    } catch (error) {
        ElMessage.error("获取项目列表失败");
        console.error('获取项目列表错误:', error);
    }
}
```

**暴露初始化方法**：
```typescript
return {
    // ... 其他属性
    initialize,
    fetchProjectOptions,
    getTodayRange
};
```

### 2. 修复 task.vue

**添加正确的初始化逻辑**：
```typescript
<script setup lang="ts">
import { ref, computed, shallowRef, onMounted } from 'vue';
import { useProjectTask } from '../../composables/useProjectTask';
// ... 其他导入

// 初始化共享逻辑
const projectTask = useProjectTask();

// 解构出需要在模板中使用的响应式数据
const { selectedProjectId, projectOptions, onProjectChange, initialize } = projectTask;

// 组件挂载时初始化数据
onMounted(() => {
    console.log('task.vue onMounted - 开始初始化');
    initialize();
});
</script>
```

### 3. 修复 ProjectTaskList.vue

**修复 CRUD 组件引用**：
```vue
<!-- ❌ 错误的做法 -->
<cl-crud ref="Crud">
    <cl-table ref="Table">
    <cl-upsert ref="Upsert" />
</cl-crud>

<!-- ✅ 正确的做法 -->
<cl-crud :ref="Crud">
    <cl-table :ref="Table">
    <cl-upsert :ref="Upsert" />
</cl-crud>
```

**添加操作按钮插槽**：
```vue
<cl-table :ref="Table">
    <template #slot-op-buttons="{ scope }">
        <el-button type="primary" text bg @click="handleAssignTask(scope.row)">调整分配</el-button>
        <el-button type="success" text bg @click="handleCompleteTask(scope.row)">完成</el-button>
        <el-button type="danger" text bg @click="handleCloseTask(scope.row)">关闭</el-button>
        <el-button type="warning" text bg @click="handleAdjustTime(scope.row)">调整时间</el-button>
    </template>
</cl-table>
```

### 4. 修复 Table 配置

**添加操作按钮插槽配置**：
```typescript
const Table = useTable({
    // ... 其他配置
    columns: [
        // ... 其他列
        { 
            type: "op", 
            buttons: ["info", "edit", "delete"],
            width: 300,
            render: "slot-op-buttons" // 使用插槽渲染自定义操作按钮
        }
    ]
});
```

## 修复后的执行流程

1. **页面加载**：
   - `task.vue` 组件挂载
   - `onMounted` 钩子执行
   - 调用 `initialize()` 方法

2. **数据初始化**：
   - 设置默认搜索时间范围
   - 调用 `fetchProjectOptions()` 获取项目列表

3. **项目数据处理**：
   - 尝试多种API接口获取项目数据
   - 处理不同的响应格式
   - 设置项目选择器选项

4. **默认项目选择**：
   - 自动选中第一个项目
   - 触发CRUD数据刷新
   - 加载该项目的任务数据

5. **视图渲染**：
   - 列表视图显示任务表格
   - 看板视图显示任务卡片
   - 操作按钮正常工作

## 调试建议

如果修复后仍有问题，请检查：

1. **浏览器控制台**：查看是否有JavaScript错误
2. **网络请求**：检查API调用是否成功
3. **数据格式**：确认后端返回的数据格式
4. **权限问题**：确认用户是否有访问项目的权限

## 预期效果

修复后应该能看到：
- 项目选择器正常显示项目列表
- 默认选中第一个项目
- 列表视图显示任务数据表格
- 看板视图显示任务卡片
- 搜索功能正常工作
- 任务操作按钮正常显示和工作
