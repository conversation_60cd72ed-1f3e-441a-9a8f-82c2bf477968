# 项目报告筛选功能说明

## 功能概述

为项目报告页面 (`cool-admin-vue/src/modules/organization/views/project/report.vue`) 添加了项目筛选过滤功能，用户可以选择特定的项目来查看相关的报表数据。

## 功能特性

### 前端功能
1. **项目选择器**：在报表页面头部添加了多选项目下拉框
2. **智能标签**：支持折叠标签显示，避免选择过多项目时界面混乱
3. **实时筛选**：选择项目后立即刷新报表数据
4. **全量显示**：未选择项目时显示所有有权限的项目数据

### 后端功能
1. **权限控制**：根据用户权限显示可访问的项目
2. **数据筛选**：支持按项目ID列表筛选报表数据
3. **兼容性**：保持原有API接口不变，新增可选参数

## 修改文件

### 前端文件
- `cool-admin-vue/src/modules/organization/views/project/report.vue`
  - 添加项目选择器组件
  - 新增响应式数据：`selectedProjects`、`projectOptions`
  - 新增方法：`handleProjectChange`、`loadProjectOptions`
  - 修改数据加载方法，传递项目筛选参数

### 后端文件
- `cool-admin-java/src/main/java/com/cool/modules/organization/service/impl/ProjectReportServiceImpl.java`
  - 新增重载方法：`getUserProjects(Long userId, boolean isAdmin, Dict params)`
  - 修改所有报表数据获取方法，支持项目ID筛选
  - 保持向后兼容性

## 使用方法

1. **查看所有项目报表**：不选择任何项目，系统显示用户有权限的所有项目数据
2. **筛选特定项目**：在项目选择器中选择一个或多个项目，报表数据将只显示选中项目的相关信息
3. **导出筛选报表**：导出功能也会应用当前的项目筛选条件

## 技术实现

### 前端实现
```typescript
// 项目选择器数据
const selectedProjects = ref<number[]>([]);
const projectOptions = ref<Array<{id: number, projectName: string}>>([]);

// 项目变更处理
const handleProjectChange = () => {
  loadReportData();
};

// 加载项目选项
const loadProjectOptions = async () => {
  const response = await service.organization.project.info.list();
  projectOptions.value = response.map((project: any) => ({
    id: project.id,
    projectName: project.projectName
  }));
};
```

### 后端实现
```java
// 支持项目筛选的获取方法
private List<ProjectInfoEntity> getUserProjects(Long userId, boolean isAdmin, Dict params) {
    List<ProjectInfoEntity> allProjects = getUserProjects(userId, isAdmin);
    
    // 如果指定了项目ID筛选
    if (params != null && params.containsKey("projectIds")) {
        List<Long> projectIds = (List<Long>) params.get("projectIds");
        if (projectIds != null && !projectIds.isEmpty()) {
            return allProjects.stream()
                .filter(project -> projectIds.contains(project.getId()))
                .collect(Collectors.toList());
        }
    }
    
    return allProjects;
}
```

## 界面效果

- 项目选择器位于报表页面右上角，日期选择器左侧
- 支持多选，选中的项目以标签形式显示
- 当选择项目过多时，自动折叠显示，鼠标悬停可查看完整列表
- 选择项目后，所有报表数据（概览卡片、图表、排行榜）都会实时更新

## 注意事项

1. 项目选择器只显示用户有权限访问的项目
2. 管理员可以看到所有项目，普通用户只能看到参与的项目
3. 筛选功能不影响原有的日期范围筛选
4. 导出功能会应用当前的所有筛选条件（项目+日期）
