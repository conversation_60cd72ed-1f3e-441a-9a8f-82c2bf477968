# AI任务生成支持项目维度需求文档

## 1. 背景与目标

### 1.1 背景

当前AI任务生成功能仅支持按部门分配任务，无法满足项目制管理的需求。随着公司“双维度组织架构”的推行，项目经理需要在项目维度下，快速、智能地为其团队创建和分配任务。

### 1.2 目标

- **功能扩展**：在AI任务生成器中增加“按项目生成”模式。
- **体验优化**：提供清晰的模式切换，确保用户可以直观地选择按部门或按项目生成任务。
- **数据联动**：实现任务分配与所选维度（部门或项目）的动态关联，确保分配人员的数据源准确性。
- **提升效率**：赋能项目经理，使其能够利用AI能力快速规划和启动项目任务。

## 2. 用户故事

- **作为一名项目经理**，我希望能选择一个我负责的项目，然后输入任务描述，让AI为我的项目团队成员生成一系列结构化的任务，这样我就可以快速完成项目启动和任务分解。
- **作为一名任务执行人**，当我被分配任务时，我希望任务能明确归属于某个项目，方便我聚焦当前的项目工作。

## 3. 功能需求

### 3.1 任务生成模式切换

- **UI变更**：在AI任务生成页面的“所属部门”选择器上方，增加一个“生成模式”的单选切换组件（例如，Radio Button）。
- **选项**：提供两个选项：“按部门生成”和“按项目生成”。
- **默认行为**：“按部门生成”应作为默认选项，保持现有用户习惯。
- **交互逻辑**：
    - 当选择“按部门生成”时，显示部门选择器。
    - 当选择“按项目生成”时，隐藏部门选择器，并显示项目选择器。
- **必填校验**：无论是部门还是项目，用户必须至少选择一项，才能启用“生成预览”按钮。

### 3.2 项目选择器

- **组件类型**：应与部门选择器类似，支持搜索和多选。
- **数据来源**：项目选择器的数据应来源于当前用户有权访问的项目列表。

### 3.3 任务分配数据源联动

- **核心逻辑**：任务执行人的数据源（包括AI智能分配和手动分配）必须根据所选的“生成模式”进行动态过滤。
    - **按部门模式**：分配人列表应只显示所选部门下的员工。
    - **按项目模式**：分配人列表应只显示所选项目中的成员。
- **手动分配**：在任务预览阶段，点击“手动分配”弹出的执行人选择器中，其数据源也必须遵循上述过滤规则。

### 3.4 后端接口支持

- **生成接口**：后端的任务生成接口（预览和正式生成）需要能够接收`mode`（`department`或`project`）以及对应的`departmentIds`或`projectIds`。
- **人员查询接口**：获取可用执行人的接口需要支持按部门ID列表或项目ID列表进行过滤。

## 4. 非功能需求

- **性能**：获取项目列表和项目成员的速度应在2秒以内。
- **易用性**：界面切换清晰，用户无需培训即可理解如何按项目生成任务。
- **兼容性**：该功能需要与现有的“双维度数据权限”模型保持一致。

## 5. 任务归属的清晰展示

为了让用户能够直观地理解任务的归属，所有与任务相关的界面都必须清晰地展示其所属的项目或部门。

- **工单与任务包**：在列表和详情页中，应有明确的字段或标签显示其归属于哪个项目或部门。
- **任务列表与卡片**：在任务列表的“归属”列、任务卡片的标签以及任务详情页的显著位置，都需要展示项目或部门名称。
- **手动分配**：在手动分配执行人的对话框中，标题或内容需明确提示当前是为哪个项目或部门进行分配，例如：“为 ‘阳光社区项目’ 分配执行人”。

## 6. 验收标准

1.  用户可以在AI任务生成页面看到“按部门”和“按项目”的切换选项。
2.  选择“按项目”后，可以成功加载并选择一个或多个项目。
3.  输入任务描述并点击“生成预览”后，可以成功生成与项目相关的任务预览。
4.  在预览页面，AI自动分配的执行人必须是所选项目的成员。
5.  在预览页面，手动分配任务时，弹出的可选执行人列表也必须是所选项目的成员。
6.  最终生成的任务能够正确关联到所选项目。
7.  在工单、任务包、任务列表和任务详情等界面，可以清晰地看到任务归属于哪个项目或部门。
