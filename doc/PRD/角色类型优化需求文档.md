# 角色类型优化需求文档 (PRD)

## 1. 背景

当前系统中，所有角色在创建时都与“数据权限”（部门/项目）强耦合，这使得定义“模板化”或“全局性”的角色（如：通用的“项目经理”角色）变得不直观。为了简化角色创建流程，明确角色定位，我们引入“角色类型”的概念。

## 2. 需求详述

### R1: 引入角色类型字段

-   **R1.1**: 在角色管理模块，创建一个新角色或编辑现有角色时，需要增加一个“是否为全局角色”的选项。
-   **R1.2**: 此选项建议使用“开关(Switch)”组件实现，关闭时为“范围角色”，开启时为“全局角色”。
-   **R1.3**: 默认应为“范围角色”。

### R2: 简化全局角色的配置界面

-   **R2.1**: 当用户将角色类型设置为“全局角色”时，表单中的“数据权限”（即部门/项目选择树）配置项应被**隐藏**。
-   **R2.2**: “全局角色”在创建时不关联任何具体的部门/项目，它作为一个纯粹的“权限模板”存在。

### R3: 列表页清晰展示

-   **R3.1**: 在角色管理列表页，增加一列“角色类型”，用于明确标识每个角色是“全局角色”还是“范围角色”。
-   **R3.2**: 建议使用“标签(Tag)”组件进行视觉区分。

### R4: 保持现有权限逻辑不变

-   **R4.1**: 本次优化的范围**仅限于角色创建和管理的UI/UE层面**。
-   **R4.2**: 后端的数据权限校验逻辑、拦截器等**保持现状，不做任何改动**。
-   **R4.3**: 一个用户的数据权限，依然由“用户、角色、部门/项目”三者绑定关系决定。

### R5: 向后兼容

-   **R5.1**: 所有现存角色应自动归类为“范围角色”，确保现有业务不受影响。

## 3. 验收标准

-   [ ] 可以在角色编辑/新增页面看到“是否为全局角色”的开关。
-   [ ] 当开关开启时，“数据权限”配置区被隐藏。
-   [ ] 角色列表中新增“角色类型”列，并能正确显示“全局角色”/“范围角色”。
-   [ ] 创建一个“全局角色”并保存，其关联的部门列表应为空。
-   [ ] 将此“全局角色”在用户管理中分配给某个用户并关联到指定部门，该用户的权限符合预期（与不使用全局角色时表现一致）。
-   [ ] 升级后，所有旧角色的功能和权限表现与升级前完全一致。
