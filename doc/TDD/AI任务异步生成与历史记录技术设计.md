# AI任务异步生成与历史记录技术设计

## 1. 总体架构设计
- 前端：AI生成页面、历史记录页面，SSE优先推送进度，轮询兜底。
- 后端：异步任务处理（入库+队列/线程池），状态/进度实时更新，SSE接口推送。
- 数据库：生成记录表，记录每次生成的参数、状态、结果、失败原因等。

## 2. 主要表结构与字段说明
表名：`ai_task_generate_record`
| 字段名      | 类型     | 说明           |
|-------------|----------|----------------|
| id          | bigint   | 主键           |
| user_id     | bigint   | 发起人ID       |
| user_name   | varchar  | 发起人姓名     |
| task_desc   | text     | 任务描述       |
| params      | json     | 生成参数       |
| status      | tinyint  | 状态（0排队/1生成中/2成功/3失败/4取消）|
| progress    | int      | 进度百分比     |
| result      | json     | 生成结果       |
| fail_reason | varchar  | 失败原因       |
| cost_time   | int      | 耗时(ms)       |
| created_time| datetime | 创建时间       |
| updated_time| datetime | 更新时间       |

## 3. 后端接口设计
- POST `/admin/sop/ai-task-generator/async-generate`：提交异步生成，返回taskId。
- GET `/admin/sop/ai-task-generator/status/{taskId}`：查询任务状态/进度。
- GET `/admin/sop/ai-task-generator/stream/{taskId}`：SSE推送进度/状态。
- GET `/admin/sop/ai-task-generator/history`：分页查询历史记录。
- GET `/admin/sop/ai-task-generator/history/{id}`：查询单条记录详情。
- POST `/admin/sop/ai-task-generator/retry/{id}`：重试生成。
- POST `/admin/sop/ai-task-generator/cancel/{id}`：取消任务（可选）。
- POST `/admin/sop/ai-task-generator/history/delete`：批量删除（可选）。

## 3.1 后端接口与Service实现要点

### 3.1.1 Controller接口说明
- 所有接口统一继承BaseController，路径规范为/admin/sop/ai-task-generator/xxx。
- 参数校验采用Bean Validation，接口鉴权基于Spring Security。
- 仅负责参数接收、权限校验、调用Service、组装响应，不做业务流处理。

### 3.1.2 Service实现要点
- 业务流全部在AiTaskGenerateRecordService实现，AI能力调用通过AILLMService解耦。
- 任务生成、进度推送、历史记录、状态流转、异常处理等均在Service层实现，保证事务一致性。
- 任务状态更新、进度推送、SSE消息、历史落库等均有独立方法，便于维护和扩展。
- 任务生成、分配、部门/角色/执行人等参数全部落库，便于后续统计和权限校验。
- 关键方法均有详细注释，异常使用CoolException统一抛出。

### 3.1.3 典型Service方法示例
```java
/**
 * AI任务异步生成主流程
 * @param request 任务生成请求参数
 * @return 任务ID
 */
@Transactional
public Long asyncGenerate(TaskGenerateRequest request) {
    // 1. 参数校验
    // 2. 记录生成请求，入库
    // 3. 推送排队进度SSE
    // 4. 投递到线程池/队列异步处理
    // 5. 返回任务ID
}

/**
 * SSE推送进度
 * @param taskId 任务ID
 * @param progress 进度百分比
 * @param status 状态
 * @param message 进度消息
 */
public void pushSseProgress(Long taskId, int progress, int status, String message) {
    // 1. 组装SSE消息
    // 2. 推送到前端
    // 3. 更新数据库进度
}
```

## 4. 状态流转与异常处理机制
- 状态枚举：0排队、1生成中、2成功、3失败、4取消。
- 状态流转：排队→生成中→成功/失败/取消。
- 生成中定期更新进度，SSE推送。
- 异常/超时自动置为失败，记录fail_reason。
- SSE断开自动降级为轮询，前端异常友好提示，支持手动重试。

## 5. 前端交互与推送机制
- SSE优先，EventSource监听进度/状态，断开自动切换为轮询。
- 进度条、状态标签、失败原因、重试入口友好展示。
- 历史记录页支持多任务进度并发监听。
- 失败/异常时自动降级、显著提示、重试按钮防抖。

### 5.1 部门选择与分配人筛选体验优化

- 部门选择组件（ai-department-selector）增加本地模糊搜索功能，支持输入关键字实时过滤部门列表。
- 分配人员筛选条件（部门、角色）下拉选项由前端根据可用执行人动态提取，兼容多种后端数据结构，避免下拉无数据。
- 分配人筛选逻辑采用computed响应式过滤，支持部门、角色、姓名、手机号多条件组合筛选。
- 组件初始化时自动预加载分配人员数据，确保弹窗打开时筛选项完整可用。
- 所有下拉选项支持空数据友好提示，提升用户体验。

**实现要点：**
- 部门选择组件使用`el-select`的`filterable`和`remote`属性，结合本地computed过滤实现模糊搜索。
- 分配人筛选下拉选项通过Map去重提取，兼容多种字段命名（如departmentId/departmentName等）。
- 过滤逻辑支持多条件组合，且兼容不同后端返回结构。
- 组件挂载时自动拉取分配人数据，避免首次弹窗下拉为空。

## 5.2 前端页面布局、组件与交互详细说明

### 5.2.1 页面结构
- 主要页面：AI任务生成页、历史记录页、任务详情弹窗。
- 采用模块化布局，页面结构参考demo模块标准写法，使用el-scrollbar包裹。

### 5.2.2 主要组件
- AITaskGenerator.vue：主生成器组件，负责任务描述、部门选择、参数填写、预览、正式生成等。
- ai-department-selector.vue：部门选择组件，支持多选、模糊搜索。
- 分配人选择弹窗：支持部门、角色、姓名、手机号多条件筛选。
- SSE进度面板：实时展示任务生成进度、状态、日志。
- 任务卡片/概览卡片：展示任务包、部门、任务数量、分配状态等。
- 历史记录表格/卡片：展示所有历史生成任务，支持筛选、重试、删除。

### 5.2.3 交互流程
- 用户填写任务描述、选择部门（必填，支持模糊搜索），点击预览。
- 预览时，前端调用/async-generate接口，获取taskId，开启SSE监听。
- SSE实时推送进度，前端进度面板动态展示。
- 预览成功后，展示任务包、部门Tab、任务卡片、分配人选择等。
- 分配人选择弹窗支持部门、角色、姓名、手机号多条件组合筛选，选项自动提取，空数据有友好提示。
- 用户可正式生成任务，或返回编辑。
- 历史记录页支持分页、筛选、详情、重试、删除，进度条、状态标签、失败原因友好展示。

### 5.2.4 组件实现要点
- 部门选择组件使用el-select的filterable和remote属性，结合本地computed过滤实现模糊搜索。
- 分配人筛选下拉选项通过Map去重提取，兼容多种字段命名（如departmentId/departmentName等）。
- 过滤逻辑支持多条件组合，且兼容不同后端返回结构。
- 组件挂载时自动拉取分配人数据，避免首次弹窗下拉为空。
- 所有下拉选项支持空数据友好提示，提升用户体验。
- 进度面板、任务卡片、Tab等均采用响应式设计，适配多部门、多任务场景。

### 5.2.5 典型前端代码片段
```vue
<!-- 部门选择组件 -->
<el-select v-model="selectedDepartments" filterable clearable multiple :remote-method="handleSearch" :loading="loading">
  <el-option v-for="item in filteredDepartments" :key="item.id" :label="item.name" :value="item.id" />
</el-select>

<!-- 分配人筛选 -->
<el-select v-model="assigneeFilter.department" clearable placeholder="按部门筛选">
  <el-option v-for="item in departmentOptions" :key="item.id" :label="item.name" :value="item.id" />
</el-select>
<el-select v-model="assigneeFilter.role" clearable placeholder="按角色筛选">
  <el-option v-for="item in roleOptions" :key="item.id" :label="item.name" :value="item.id" />
</el-select>
<el-input v-model="assigneeFilter.search" placeholder="姓名/手机号模糊搜索" clearable />
```

## 6. 安全与权限设计
- 所有接口需鉴权，防止越权查询/操作。
- 普通用户仅能查看/操作自己发起的记录，管理员可查全部。
- SSE/轮询接口校验token。

## 7. 可扩展性与后续规划
- 支持批量重试、批量删除、进度日志、导出等功能。
- 后续可扩展全局消息中心、WebSocket推送、移动端适配等。 