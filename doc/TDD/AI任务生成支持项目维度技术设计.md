# AI任务生成支持项目维度技术设计

## 1. 概述

本文档旨在为“AI任务生成支持项目维度”功能提供详细的技术实现方案。该功能的核心是在现有按部门生成任务的基础上，增加按项目生成任务的能力，并确保任务分配的数据源与所选维度（部门或项目）动态匹配。

## 2. 整体架构

整体实现将遵循“前端驱动，后端支持”的模式。前端负责UI交互和模式切换，后端负责提供相应的数据接口和业务逻辑。

- **前端 (`cool-admin-vue`)**：修改 `AITaskGenerator.vue` 组件，增加模式切换UI，并根据所选模式，调用不同的数据接口。
- **后端 (`cool-admin-java`)**：修改 `AiTaskGeneratorController`、`AiTaskGenerateRecordService` 和 `AILLMService`，以支持按项目生成任务的逻辑。

## 3. 前端实现

### 3.1 `AITaskGenerator.vue` 组件重构

1.  **增加生成模式切换**
    - 在 `formData` 中增加 `generationMode` 字段，默认为 `'department'`。
    - 在模板中，使用 `el-radio-group` 实现“按部门生成”和“按项目生成”的切换，并与 `formData.generationMode` 绑定。

2.  **动态显示选择器**
    - 使用 `v-if` 或 `v-show` 指令，根据 `formData.generationMode` 的值，动态显示“部门选择器”或“项目选择器”。

3.  **项目选择器**
    - 创建一个新的 `AIProjectSelector.vue` 组件，或直接在 `AITaskGenerator.vue` 中实现项目选择逻辑。
    - 调用后端接口（例如 `/admin/organization/project/info/options`）获取当前用户有权访问的项目列表。

4.  **修改任务生成请求**
    - 在 `handlePreview` 方法中，根据 `formData.generationMode` 的值，构建不同的请求 `payload`。
    - 当 `generationMode` 为 `'project'` 时，`payload` 中应包含 `projectIds` 字段。

5.  **修改手动分配逻辑**
    - 在 `showAssignmentDialog` 方法中，获取可用执行人时，需要根据 `formData.generationMode` 和所选的部门/项目ID，向后端传递不同的过滤参数。

### 3.2 `useSseTaskStatus.ts` 组合式函数

- 无需修改。SSE 的逻辑保持不变，只关心任务记录ID。

## 4. 后端实现

### 4.1 `TaskGenerateRequest.java` DTO 扩展

- 在 `TaskGenerateRequest` DTO 中增加 `projectIds` 字段 (`List<Long>`)。

### 4.2 `AiTaskGeneratorController.java` 修改

- `/preview` 和 `/generate` 接口的实现保持不变，因为它们接收的是 `TaskGenerateRequest` 对象，该对象现在已经包含了 `projectIds`。

### 4.3 `AiTaskGenerateRecordServiceImpl.java` 核心逻辑修改

1.  **`submitAsyncTaskGeneration` 方法**
    - 在方法开始处，增加一个逻辑判断，根据 `request` 中是 `departmentIds` 还是 `projectIds` 不为空，来确定生成模式。

2.  **`processAsyncTaskGeneration` 方法**
    - **获取执行人**：在调用 `autoAssignmentService.executeAssignment` 之前，需要根据生成模式（部门或项目）和对应的ID列表，从数据库中获取正确的执行人候选列表。
    - **构建任务**：`buildTasksFromAIResult` 方法需要修改，使其能够接收项目ID列表，并为每个项目生成任务。

3.  **`persistTasksToDatabase` 方法**
    - 在创建 `TaskPackageEntity` 和 `TaskInfoEntity` 时，需要根据生成模式，正确地设置 `departmentId` 或 `projectId`。

### 4.4 `AILLMService.java` 修改

- `performUnifiedAIRecognition` 方法可能需要微调，以更好地理解与项目相关的任务描述。

### 4.5 `AutoAssignmentService.java` 修改

- `executeAssignment` 方法需要支持按项目成员进行分配。这可能需要一个新的 `getProjectMembers` 方法，该方法根据项目ID列表返回所有成员。

## 5. 数据库设计

- **`ai_task_generate_record` 表**：无需修改。`params` 字段可以存储包含 `projectIds` 的JSON字符串。
- **`task_info` 表**：已有 `project_id` 字段，可以直接使用。
- **`task_package` 表**：已有 `project_id` 字段，可以直接使用。

## 6. 实施计划

1.  **后端**：
    - [ ] 扩展 `TaskGenerateRequest` DTO。
    - [ ] 修改 `AiTaskGenerateRecordServiceImpl` 的核心逻辑。
    - [ ] 修改 `AutoAssignmentService` 以支持按项目成员分配。
2.  **前端**：
    - [ ] 实现 `AITaskGenerator.vue` 的UI重构和逻辑修改。
    - [ ] 实现 `AIProjectSelector.vue` 组件。
3.  **联调测试**：
    - [ ] 测试按项目生成任务的完整流程。
    - [ ] 测试AI智能分配和手动分配的数据源是否正确。

## 7. 风险与应对

- **风险**：AI对项目相关任务描述的理解可能不够准确。
- **应对**：通过优化 `prompt` 和微调 `AILLMService` 来提升识别准确率。
- **风险**：项目成员数量过多，可能导致前端性能问题。
- **应对**：在获取项目成员列表时，进行分页或懒加载处理。

## 8. 任务归属展示实现方案

### 8.1 后端数据准备

**核心原则**：确保所有相关的DTO（Data Transfer Object）都同时包含`projectId`、`projectName`、`departmentId`和`departmentName`字段。

- **`TaskInfoDTO`**：修改`TaskInfoService`中的查询逻辑，使用`LEFT JOIN`同时关联`project_info`和`base_sys_department`表，以填充`projectName`和`departmentName`。
- **`TaskPackageDTO`**：同上，修改`TaskPackageService`的查询逻辑。
- **`WorkOrderDTO`**：同上，修改`WorkOrderService`的查询逻辑。

### 8.2 前端条件渲染

**核心原则**：在Vue组件中，使用`v-if`或计算属性，根据ID字段（`projectId`或`departmentId`）是否有值，来决定显示项目名称还是部门名称。

**示例**：
```vue
<template>
  <el-tag v-if="item.projectId && item.projectName" type="success">
    项目：{{ item.projectName }}
  </el-tag>
  <el-tag v-else-if="item.departmentId && item.departmentName" type="primary">
    部门：{{ item.departmentName }}
  </el-tag>
</template>
```

### 8.3 手动分配上下文提示

- **`AITaskGenerator.vue`**：在调用`showAssignmentDialog`时，需要将当前选定的项目或部门的名称传递给对话框组件。
- **`AssignmentDialog.vue`**（或类似组件）：接收并显示传入的上下文名称，例如：“为 ‘项目X’ 分配执行人”。
