# SOP场景导入功能说明

## 功能概述

SOP场景导入功能允许用户通过Excel文件批量导入SOP场景和步骤数据，支持版本管理、数据校验、冲突检测等高级功能。

## 主要特性

### 1. 版本管理
- **版本号格式**：支持 `x.y` 或 `x.y.z` 格式
- **版本策略**：
  - `MAJOR`：主版本升级 (1.0 → 2.0)
  - `MINOR`：次版本升级 (1.0 → 1.1)
  - `PATCH`：补丁版本升级 (1.0.0 → 1.0.1)
- **版本冲突检测**：自动检测现有版本与导入版本的冲突

### 2. 数据校验
- **必填字段校验**：场景编码、场景名称、模块编码等
- **格式校验**：版本号格式、难度等级范围等
- **业务逻辑校验**：步骤编码格式、步骤顺序等

### 3. 导入模式
- **CREATE_NEW**：创建新版本
- **UPDATE_EXISTING**：更新现有版本
- **MERGE**：合并模式

### 4. 数据备份与恢复
- **自动备份**：导入前自动备份现有数据
- **手动恢复**：支持根据备份ID恢复数据

## API接口

### 1. 下载导入模板
```http
GET /admin/sop/import/template
```
**响应**：Excel模板文件

### 2. 预览导入数据
```http
POST /admin/sop/import/preview
Content-Type: multipart/form-data

file: [Excel文件]
```

**响应示例**：
```json
{
  "success": true,
  "message": "数据校验通过",
  "statistics": {
    "totalScenarios": 5,
    "totalSteps": 25
  },
  "validationErrors": [],
  "versionConflicts": []
}
```

### 3. 执行导入
```http
POST /admin/sop/import/import
Content-Type: multipart/form-data

file: [Excel文件]
forceOverride: false
versionStrategy: MINOR
importMode: CREATE_NEW
backupExisting: true
importDescription: "导入说明"
```

**响应示例**：
```json
{
  "success": true,
  "message": "导入完成，成功: 5, 失败: 0",
  "statistics": {
    "totalScenarios": 5,
    "successScenarios": 5,
    "failedScenarios": 0,
    "totalSteps": 25
  },
  "importedScenarios": [
    {
      "scenarioId": 1,
      "scenarioCode": "S11",
      "scenarioName": "客户满意度调查",
      "version": "1.1",
      "action": "CREATED",
      "stepCount": 5
    }
  ]
}
```

### 4. 检查版本冲突
```http
POST /admin/sop/import/check-conflicts
Content-Type: multipart/form-data

file: [Excel文件]
```

### 5. 恢复备份数据
```http
POST /admin/sop/import/restore/{backupId}
```

### 6. 生成新版本号
```http
GET /admin/sop/import/generate-version?currentVersion=1.0&strategy=MINOR
```

## Excel模板格式

### 场景信息工作表
| 列名 | 说明 | 必填 | 示例 |
|------|------|------|------|
| 行业名称 | 所属行业 | 是 | 物业管理 |
| 阶段 | 业务阶段 | 是 | 日常运营 |
| 模块编码 | 模块编码 | 是 | M01 |
| 模块名称 | 模块名称 | 是 | 客户服务 |
| 场景编码 | 场景唯一编码 | 是 | S11 |
| 场景名称 | 场景名称 | 是 | 客户满意度调查 |
| 执行周期 | 执行频率 | 否 | 月度 |
| 版本号 | 版本号 | 是 | 1.0 |
| 场景描述 | 详细描述 | 否 | 定期进行客户满意度调查 |
| 难度等级 | 1-5级 | 否 | 3 |

### 步骤信息工作表
| 列名 | 说明 | 必填 | 示例 |
|------|------|------|------|
| 步骤编码 | 步骤编码 | 是 | S11.1 |
| 步骤名称 | 步骤名称 | 是 | 制定调查问卷 |
| 步骤描述 | 详细描述 | 是 | 根据调查目标设计问卷内容 |
| 步骤顺序 | 执行顺序 | 是 | 1 |
| 实体触点 | 接触点 | 否 | 调查问卷 |
| 用户活动 | 用户行为 | 否 | 填写问卷 |
| 员工行为 | 员工操作 | 否 | 设计问卷，确保问题清晰明确 |
| 工作亮点 | 关键要点 | 否 | 问卷设计专业，覆盖全面 |
| 员工角色 | 执行角色 | 否 | 客服专员 |
| 预估时间 | 分钟数 | 否 | 60 |

## 使用流程

### 1. 准备数据
1. 下载Excel模板
2. 按照模板格式填写场景和步骤数据
3. 确保数据完整性和格式正确性

### 2. 预览导入
1. 上传Excel文件进行预览
2. 检查数据校验结果
3. 查看版本冲突信息
4. 根据提示修正数据问题

### 3. 配置导入参数
- **版本策略**：选择合适的版本升级策略
- **导入模式**：根据需求选择导入模式
- **强制覆盖**：是否强制覆盖现有版本
- **数据备份**：是否备份现有数据

### 4. 执行导入
1. 确认导入配置
2. 执行导入操作
3. 查看导入结果
4. 如有问题，可使用备份恢复功能

## 错误处理

### 常见错误类型
- **MISSING_FIELD**：必填字段缺失
- **INVALID_FORMAT**：格式不正确
- **DUPLICATE_CODE**：编码重复
- **VERSION_CONFLICT**：版本冲突

### 版本冲突类型
- **VERSION_LOWER**：导入版本低于现有版本
- **VERSION_SAME**：版本号相同
- **VERSION_MISSING**：缺少版本号

### 处理建议
- **SKIP**：跳过导入
- **MERGE**：合并数据
- **UPGRADE**：升级版本

## 注意事项

1. **文件格式**：仅支持 `.xlsx` 格式的Excel文件
2. **文件大小**：限制10MB以内
3. **编码格式**：步骤编码格式为 `场景编码.步骤编码`
4. **版本管理**：建议使用语义化版本号
5. **数据备份**：重要数据导入前建议开启备份功能
6. **权限控制**：确保操作用户具有相应的导入权限

## 前端集成

前端页面位于：`/sop/import`

主要功能：
- 文件上传与预览
- 导入配置设置
- 进度跟踪
- 结果展示
- 错误处理

## 技术实现

### 后端架构
- **控制器**：`AdminSOPImportController`
- **服务层**：`SOPImportService`
- **数据处理**：Apache POI
- **版本管理**：语义化版本比较
- **事务管理**：Spring事务支持

### 前端架构
- **Vue 3 + TypeScript**
- **Element Plus UI组件**
- **步骤式导入流程**
- **实时数据校验**
- **响应式设计**

## 扩展功能

### 未来规划
1. **批量导入**：支持多文件批量导入
2. **模板定制**：支持自定义导入模板
3. **数据映射**：支持字段映射配置
4. **导入历史**：记录导入历史和版本变更
5. **权限细化**：更细粒度的权限控制
