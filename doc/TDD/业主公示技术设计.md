# 业主公示技术设计文档（新版）

## 1. 总体架构与技术选型
- 前端：Vue3 + Element Plus，所有公示页面通过API获取动态数据，公示内容通过html富文本渲染。
- 后端：Spring Boot + MyBatis Flex，RESTful接口，负责数据存储、权限校验、业务逻辑。
- 数据库：MySQL，announcement表结构化存储公示数据。
- H5页面：前端支持独立H5路由，适配移动端浏览器。

## 2. announcement表结构
| 字段名         | 类型         | 说明                                   |
|----------------|--------------|----------------------------------------|
| id             | bigint       | 主键                                   |
| project_id     | bigint       | 项目ID                                 |
| title          | varchar(100) | 公示名称                               |
| month          | varchar(20)  | 月份                                   |
| html           | text         | 公示内容HTML                           |
| status         | tinyint      | 状态（0草稿/1已发布/2已撤销）          |
| created_time   | datetime     | 创建时间                               |
| updated_time   | datetime     | 更新时间                               |

## 2.2 项目表（project）结构

| 字段名                | 类型           | 注释                   |
|-----------------------|----------------|------------------------|
| id                    | bigint         | 主键                   |
| code                  | varchar(50)    | 项目编码               |
| code1                 | varchar(50)    | 项目编码1              |
| name                  | varchar(100)   | 项目名称               |
| alias                 | varchar(100)   | 项目别名               |
| manager               | varchar(50)    | 项目经理               |
| staff_count           | int            | 自有员工数量           |
| outsource_count       | int            | 外包员工数量           |
| belong_dept_name      | varchar(100)   | 所属区域名称           |
| belong_city_name      | varchar(100)   | 所属城区名称           |
| province              | varchar(50)    | 所在省                 |
| city                  | varchar(50)    | 所在市                 |
| district              | varchar(50)    | 所在区                 |
| street                | varchar(100)   | 街道/镇                |
| city_level            | varchar(20)    | 目标城市等级           |
| address               | varchar(255)   | 详细地址               |
| longitude             | decimal(10,6)  | 经度                   |
| latitude              | decimal(10,6)  | 纬度                   |
| job_level             | varchar(50)    | 职场级别               |
| main_type1            | varchar(50)    | 主业态（一级）         |
| main_type2            | varchar(50)    | 主业态（二级）         |
| established           | varchar(20)    | 业委会成立情况         |
| first_sign_date       | date           | 首次签约时间           |
| actual_entry_date     | date           | 实际进场时间           |
| public_revenue_date   | date           | 关键能耗收费时间       |
| is_fund_project       | tinyint        | 是否合资公司项目       |
| project_life_cycle    | varchar(50)    | 项目生命周期           |
| exit_date             | date           | 撤场日期               |
| exit_reason           | varchar(100)   | 撤场原因               |
| mgmt_office_name      | varchar(100)   | 所属管理处名称         |
| mgmt_office_code      | varchar(50)    | 所属管理处编码         |
| operate_status        | varchar(20)    | 运营状态               |
| property_right        | varchar(20)    | 产权属性               |
| charge_mode           | varchar(20)    | 收费方式               |
| charge_standard       | varchar(100)   | 收费标准（m²/月）      |
| property_type         | varchar(50)    | 物业类型               |
| is_joint_project      | tinyint        | 是否共融项目           |
| total_build_area      | decimal(12,2)  | 总房产建筑面积         |
| charge_area           | decimal(12,2)  | 收费面积               |
| household_count       | int            | 户数                   |
| total_area            | decimal(12,2)  | 总建筑面积（m²）       |
| land_area             | decimal(12,2)  | 占地面积（m²）         |
| underground_area      | decimal(12,2)  | 地下室建筑面积（m²）   |
| green_area            | decimal(12,2)  | 绿化面积（m²）         |
| plot_ratio            | decimal(5,2)   | 容积率                 |
| has_parking           | tinyint        | 是否有停车场           |
| total_parking_count   | int            | 总车位个数             |
| self_parking_mgmt     | varchar(10)    | 停车场是否自管         |
| indoor_parking_date   | date           | 室内停车场收费时间     |
| entrance_count        | int            | 出入口数量（个）       |
| access_count          | int            | 门控室数量（个）       |
| fire_control_count    | int            | 消防控制室数量（个）   |
| elevator_count        | int            | 升降系统数量（个）     |
| central_ac_count      | int            | 中央空调主机数量（个） |
| boiler_count          | int            | 供暖锅炉数量（个）     |
| approval_status       | varchar(20)    | 审批状态               |
| start_time            | datetime       | 开始时间               |
| end_time              | datetime       | 结束时间               |
| created_time          | datetime       | 创建时间               |
| updated_time          | datetime       | 更新时间               |

**关联关系说明：**
- 公示表（announcement）的project_id字段关联本表id，实现一对多关联。
- 项目表为业主公示等模块的基础数据来源。

### 2.3 项目表导入与覆盖更新

- 项目表以 code（项目编码）为唯一标识，建立唯一索引。
- 支持批量导入（如Excel），导入时根据 code 判断新增或覆盖更新。
- 若 code 存在，则更新该项目所有字段（除id外）；若不存在，则新增。
- 导入接口需校验数据合法性，返回详细导入结果。
- 推荐SQL：ALTER TABLE project ADD UNIQUE KEY uk_code (code);

## 3. 主要功能与接口设计

### 3.1 公示列表接口
- 支持按project_id和month筛选，返回公示列表（含标题、项目、月份、状态等）。
- 支持发布、撤销、H5预览等管理操作。

### 3.2 公示详情接口
- 根据id查询公示详情，直接返回html字段内容。

### 3.3 H5预览接口
- 提供H5预览接口，返回已发布公示的html内容，适配移动端。
- H5接口为公开接口，仅返回已发布公示。

### 3.4 权限与管理
- 仅有权限的用户可发布/撤销公示。
- 普通用户仅可查看本项目已发布公示。
- 公示数据接口需鉴权，防止未授权访问。

## 4. 前端页面设计
- 公示列表页：筛选、管理、预览、发布/撤销等操作。
- 公示详情页：渲染html内容，支持H5预览、导出、打印。
- H5页面：移动端适配，扫码访问，内容与详情页一致。

## 5. 安全与扩展
- 公示数据接口需鉴权，H5接口仅返回已发布内容。
- 支持多项目切换、历史数据归档、主题色切换等扩展。 