# 任务按照部门数据权限管理 - 技术设计文档 (TDD)

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| 文档标题 | 任务按照部门数据权限管理技术设计文档 |
| 版本号 | v1.0 |
| 创建日期 | 2025-07-02 |
| 最后更新 | 2025-07-02 |
| 负责人 | 技术架构师 |
| 开发团队 | yayaai 开发团队 |

## 🎯 技术概述

### 设计目标

基于现有的Cool Admin部门数据权限体系，为任务管理模块（任务包、任务信息、任务执行）实现完整的部门权限控制，确保数据安全性和操作合规性。

### 核心原则

1. **最小权限原则**: 用户只能访问其有权限的部门任务数据
2. **权限继承**: 支持上下级部门权限的灵活配置
3. **性能优先**: 通过缓存和索引优化保证性能
4. **向后兼容**: 不破坏现有系统功能
5. **审计完整**: 完整记录权限相关操作

## 🏗️ 系统架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────┐
│                    前端层 (Vue.js)                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐  │
│  │   任务包管理     │ │   任务信息管理   │ │  任务执行管理 │  │
│  └─────────────────┘ └─────────────────┘ └─────────────┘  │
└─────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────┐
│                    控制器层 (Controller)                  │
│  ┌─────────────────────────────────────────────────────┐ │
│  │      权限拦截器 (DepartmentPermissionInterceptor)    │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │   TaskPackage/TaskInfo/TaskExecution Controllers    │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────┐
│                    服务层 (Service)                      │
│  ┌─────────────────────────────────────────────────────┐ │
│  │           部门权限服务 (BaseSysPermsService)         │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │      任务服务 + 权限控制                              │ │
│  │  TaskPackageService / TaskInfoService                │ │
│  │  TaskExecutionService                               │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────┐
│                    数据层 (Data)                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │           权限缓存层 (Redis Cache)                   │ │
│  │          admin:department:{userId}                  │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              数据库层 (MySQL)                        │ │
│  │   task_package / task_info / task_execution        │ │
│  │   base_sys_role_department / base_sys_department   │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 📊 数据库设计

### 1. 现有表结构改造

#### 1.1 任务包表 (task_package) 新增字段

```sql
ALTER TABLE task_package ADD COLUMN department_id BIGINT COMMENT '所属部门ID';
ALTER TABLE task_package ADD COLUMN creator_department_id BIGINT COMMENT '创建者部门ID';
ALTER TABLE task_package ADD INDEX idx_department_id (department_id);
ALTER TABLE task_package ADD INDEX idx_creator_department_id (creator_department_id);
```

#### 1.2 任务信息表 (task_info) 新增字段

```sql
ALTER TABLE task_info ADD COLUMN department_id BIGINT COMMENT '所属部门ID';
ALTER TABLE task_info ADD COLUMN creator_department_id BIGINT COMMENT '创建者部门ID';
ALTER TABLE task_info ADD INDEX idx_department_id (department_id);
ALTER TABLE task_info ADD INDEX idx_creator_department_id (creator_department_id);
```

#### 1.3 任务执行表 (task_execution) 新增字段

```sql
ALTER TABLE task_execution ADD COLUMN department_id BIGINT COMMENT '执行部门ID';
ALTER TABLE task_execution ADD COLUMN assignee_department_id BIGINT COMMENT '执行人部门ID';
ALTER TABLE task_execution ADD INDEX idx_department_id (department_id);
ALTER TABLE task_execution ADD INDEX idx_assignee_department_id (assignee_department_id);
```

### 2. 权限审计表设计

#### 2.1 任务权限操作日志表

```sql
CREATE TABLE task_permission_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    username VARCHAR(255) NOT NULL COMMENT '操作用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型(package/info/execution)',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    department_id BIGINT COMMENT '涉及部门ID',
    permission_result TINYINT NOT NULL COMMENT '权限验证结果(0:失败,1:成功)',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理',
    
    INDEX idx_user_id (user_id),
    INDEX idx_operation_time (operation_time),
    INDEX idx_task_type_id (task_type, task_id),
    INDEX idx_department_id (department_id)
) COMMENT '任务权限操作日志表';
```

## 💼 业务逻辑设计

### 1. 权限验证核心逻辑

#### 1.1 权限验证服务接口

```java
public interface TaskDepartmentPermissionService {
    
    /**
     * 验证用户是否有权限访问指定任务包
     */
    boolean hasTaskPackagePermission(Long userId, Long taskPackageId, String operation);
    
    /**
     * 验证用户是否有权限访问指定任务信息
     */
    boolean hasTaskInfoPermission(Long userId, Long taskInfoId, String operation);
    
    /**
     * 验证用户是否有权限访问指定任务执行
     */
    boolean hasTaskExecutionPermission(Long userId, Long taskExecutionId, String operation);
    
    /**
     * 获取用户有权限的部门ID列表
     */
    Long[] getUserDepartmentIds(Long userId);
    
    /**
     * 根据部门权限过滤任务包查询条件
     */
    void applyTaskPackageDepartmentFilter(QueryWrapper queryWrapper, Long userId);
    
    /**
     * 根据部门权限过滤任务信息查询条件
     */
    void applyTaskInfoDepartmentFilter(QueryWrapper queryWrapper, Long userId);
    
    /**
     * 根据部门权限过滤任务执行查询条件
     */
    void applyTaskExecutionDepartmentFilter(QueryWrapper queryWrapper, Long userId);
}
```

#### 1.2 权限验证实现类

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskDepartmentPermissionServiceImpl implements TaskDepartmentPermissionService {
    
    private final BaseSysPermsService baseSysPermsService;
    private final TaskPackageService taskPackageService;
    private final TaskInfoService taskInfoService;
    private final TaskExecutionService taskExecutionService;
    private final TaskPermissionLogService taskPermissionLogService;
    
    @Override
    public boolean hasTaskPackagePermission(Long userId, Long taskPackageId, String operation) {
        try {
            // 1. 获取用户的部门权限
            Long[] userDepartmentIds = getUserDepartmentIds(userId);
            
            // 2. 获取任务包的部门信息
            TaskPackageEntity taskPackage = taskPackageService.getById(taskPackageId);
            if (taskPackage == null) {
                return false;
            }
            
            // 3. 验证权限
            boolean hasPermission = Arrays.asList(userDepartmentIds)
                .contains(taskPackage.getDepartmentId());
            
            // 4. 记录权限验证日志
            logPermissionCheck(userId, operation, "package", taskPackageId, 
                taskPackage.getDepartmentId(), hasPermission);
            
            return hasPermission;
            
        } catch (Exception e) {
            log.error("验证任务包权限失败", e);
            return false;
        }
    }
    
    @Override
    public Long[] getUserDepartmentIds(Long userId) {
        // 复用现有的部门权限服务
        return baseSysPermsService.loginDepartmentIds();
    }
    
    @Override
    public void applyTaskPackageDepartmentFilter(QueryWrapper queryWrapper, Long userId) {
        Long[] departmentIds = getUserDepartmentIds(userId);
        String currentUser = CoolSecurityUtil.getAdminUsername();
        
        // admin用户不需要部门权限过滤
        if (!"admin".equals(currentUser) && departmentIds != null && departmentIds.length > 0) {
            queryWrapper.in("department_id", Arrays.asList(departmentIds));
        }
    }
    
    private void logPermissionCheck(Long userId, String operation, String taskType, 
            Long taskId, Long departmentId, boolean result) {
        // 异步记录权限验证日志
        CompletableFuture.runAsync(() -> {
            try {
                TaskPermissionLogEntity log = new TaskPermissionLogEntity();
                log.setUserId(userId);
                log.setUsername(CoolSecurityUtil.getAdminUsername());
                log.setOperationType(operation);
                log.setTaskType(taskType);
                log.setTaskId(taskId);
                log.setDepartmentId(departmentId);
                log.setPermissionResult(result ? 1 : 0);
                log.setOperationTime(LocalDateTime.now());
                log.setClientIp(RequestUtil.getClientIP());
                log.setUserAgent(RequestUtil.getUserAgent());
                
                taskPermissionLogService.save(log);
            } catch (Exception e) {
                log.error("记录权限日志失败", e);
            }
        });
    }
}
```

### 2. 实体类改造

#### 2.1 任务包实体类改造

```java
@Data
@TableName("task_package")
public class TaskPackageEntity extends BaseEntity<TaskPackageEntity> {
    
    // 现有字段...
    
    /**
     * 所属部门ID
     */
    @ColumnDefine(comment = "所属部门ID", type = "bigint")
    private Long departmentId;
    
    /**
     * 所属部门名称 (查询时填充)
     */
    @TableField(exist = false)
    private String departmentName;
    
    /**
     * 创建者部门ID
     */
    @ColumnDefine(comment = "创建者部门ID", type = "bigint")
    private Long creatorDepartmentId;
    
    /**
     * 创建者部门名称 (查询时填充)
     */
    @TableField(exist = false)
    private String creatorDepartmentName;
    
    /**
     * 是否跨部门任务
     */
    @TableField(exist = false)
    private Boolean isCrossDepartment;
}
```

#### 2.2 任务信息实体类改造

```java
@Data
@TableName("task_info")
public class TaskInfoEntity extends BaseEntity<TaskInfoEntity> {
    
    // 现有字段...
    
    /**
     * 所属部门ID
     */
    @ColumnDefine(comment = "所属部门ID", type = "bigint")
    private Long departmentId;
    
    /**
     * 所属部门名称
     */
    @TableField(exist = false)
    private String departmentName;
    
    /**
     * 创建者部门ID
     */
    @ColumnDefine(comment = "创建者部门ID", type = "bigint")
    private Long creatorDepartmentId;
}
```

#### 2.3 任务执行实体类改造

```java
@Data
@TableName("task_execution")
public class TaskExecutionEntity extends BaseEntity<TaskExecutionEntity> {
    
    // 现有字段...
    
    /**
     * 执行部门ID
     */
    @ColumnDefine(comment = "执行部门ID", type = "bigint")
    private Long departmentId;
    
    /**
     * 执行部门名称
     */
    @TableField(exist = false)
    private String departmentName;
    
    /**
     * 执行人部门ID
     */
    @ColumnDefine(comment = "执行人部门ID", type = "bigint")
    private Long assigneeDepartmentId;
    
    /**
     * 执行人部门名称
     */
    @TableField(exist = false)
    private String assigneeDepartmentName;
}
```

## 🎮 控制器层设计

### 1. 权限拦截器

#### 1.1 部门权限拦截器实现

```java
@Component
@Slf4j
public class TaskDepartmentPermissionInterceptor implements HandlerInterceptor {
    
    @Autowired
    private TaskDepartmentPermissionService taskDepartmentPermissionService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, 
            Object handler) throws Exception {
            
        // 仅拦截任务相关的API
        String requestURI = request.getRequestURI();
        if (!isTaskAPI(requestURI)) {
            return true;
        }
        
        // 获取当前用户信息
        Long userId = CoolSecurityUtil.getAdminUserId();
        if (userId == null) {
            return false;
        }
        
        // 提取任务ID和操作类型
        TaskPermissionContext context = extractPermissionContext(request);
        if (context == null) {
            return true; // 非权限相关操作，放行
        }
        
        // 执行权限验证
        boolean hasPermission = verifyPermission(userId, context);
        
        if (!hasPermission) {
            response.setStatus(HttpStatus.FORBIDDEN.value());
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write(JSON.toJSONString(R.error("权限不足，无法访问该资源")));
            return false;
        }
        
        return true;
    }
    
    private boolean isTaskAPI(String requestURI) {
        return requestURI.contains("/task/package") || 
               requestURI.contains("/task/info") || 
               requestURI.contains("/task/execution");
    }
    
    private TaskPermissionContext extractPermissionContext(HttpServletRequest request) {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        
        // 从URL或请求体中提取任务ID
        Long taskId = extractTaskId(request);
        String taskType = extractTaskType(uri);
        String operation = mapMethodToOperation(method);
        
        if (taskId == null || taskType == null) {
            return null;
        }
        
        return TaskPermissionContext.builder()
            .taskId(taskId)
            .taskType(taskType)
            .operation(operation)
            .build();
    }
    
    private boolean verifyPermission(Long userId, TaskPermissionContext context) {
        switch (context.getTaskType()) {
            case "package":
                return taskDepartmentPermissionService.hasTaskPackagePermission(
                    userId, context.getTaskId(), context.getOperation());
            case "info":
                return taskDepartmentPermissionService.hasTaskInfoPermission(
                    userId, context.getTaskId(), context.getOperation());
            case "execution":
                return taskDepartmentPermissionService.hasTaskExecutionPermission(
                    userId, context.getTaskId(), context.getOperation());
            default:
                return false;
        }
    }
}

@Data
@Builder
class TaskPermissionContext {
    private Long taskId;
    private String taskType;
    private String operation;
}
```

### 2. 控制器改造

#### 2.1 任务包控制器改造

```java
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
@Tag(name = "任务包管理", description = "任务包的增删改查")
public class AdminTaskPackageController extends BaseController<TaskPackageService, TaskPackageEntity> {

    @Autowired
    private TaskDepartmentPermissionService permissionService;

    @Override
    protected void init(QueryWrapper queryWrapper, Map<String, Object> params) {
        // 应用部门权限过滤
        Long userId = CoolSecurityUtil.getAdminUserId();
        permissionService.applyTaskPackageDepartmentFilter(queryWrapper, userId);
        
        // 其他查询条件
        queryWrapper.like("package_name", params.get("packageName"))
                   .eq("package_status", params.get("packageStatus"))
                   .eq("department_id", params.get("departmentId"))
                   .orderBy("id", false);
    }

    @Override
    public R add(HttpServletRequest request, @RequestBody TaskPackageEntity entity) {
        // 设置部门信息
        setupDepartmentInfo(entity);
        return super.add(request, entity);
    }

    @Override
    public R update(HttpServletRequest request, @RequestBody TaskPackageEntity entity) {
        // 验证更新权限
        if (!permissionService.hasTaskPackagePermission(
                CoolSecurityUtil.getAdminUserId(), entity.getId(), "UPDATE")) {
            return R.error("权限不足，无法修改该任务包");
        }
        return super.update(request, entity);
    }

    @PostMapping("/assign")
    @Operation(summary = "批量分配任务包")
    public R batchAssign(@RequestBody TaskPackageBatchAssignRequest request) {
        try {
            Long userId = CoolSecurityUtil.getAdminUserId();
            
            // 验证每个任务包的权限
            List<Long> authorizedPackageIds = new ArrayList<>();
            List<Long> unauthorizedPackageIds = new ArrayList<>();
            
            for (Long packageId : request.getPackageIds()) {
                if (permissionService.hasTaskPackagePermission(userId, packageId, "ASSIGN")) {
                    authorizedPackageIds.add(packageId);
                } else {
                    unauthorizedPackageIds.add(packageId);
                }
            }
            
            // 执行批量分配
            Map<String, Object> result = service.batchAssignPackages(
                authorizedPackageIds, request.getAssigneeId());
            
            // 添加权限验证结果
            result.put("unauthorizedCount", unauthorizedPackageIds.size());
            result.put("unauthorizedPackageIds", unauthorizedPackageIds);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("批量分配任务包失败", e);
            return R.error("分配失败: " + e.getMessage());
        }
    }
    
    private void setupDepartmentInfo(TaskPackageEntity entity) {
        Long currentUserId = CoolSecurityUtil.getAdminUserId();
        
        // 如果未指定部门，使用创建者的部门
        if (entity.getDepartmentId() == null) {
            BaseSysUserEntity currentUser = userService.getById(currentUserId);
            entity.setDepartmentId(currentUser.getDepartmentId());
        }
        
        // 设置创建者部门
        entity.setCreatorDepartmentId(
            userService.getById(currentUserId).getDepartmentId());
    }
}
```

## 🔧 服务层改造

### 1. 任务包服务改造

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskPackageServiceImpl extends ServiceImpl<TaskPackageMapper, TaskPackageEntity> 
        implements TaskPackageService {
    
    private final TaskDepartmentPermissionService permissionService;
    private final BaseSysDepartmentService departmentService;
    private final TaskInfoService taskInfoService;

    @Override
    public Object page(JSONObject requestParams, Page<TaskPackageEntity> page, QueryWrapper qw) {
        // 应用部门权限过滤
        Long userId = CoolSecurityUtil.getAdminUserId();
        permissionService.applyTaskPackageDepartmentFilter(qw, userId);
        
        // 执行分页查询
        Page<TaskPackageEntity> result = mapper.paginate(page, qw);
        
        // 填充部门名称
        fillDepartmentNames(result.getRecords());
        
        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> batchAssignPackages(List<Long> packageIds, Long assigneeId) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        for (Long packageId : packageIds) {
            try {
                TaskPackageEntity taskPackage = getById(packageId);
                if (taskPackage == null) {
                    errorMessages.add("任务包 " + packageId + " 不存在");
                    failCount++;
                    continue;
                }
                
                // 验证执行人的部门权限
                if (!validateAssigneePermission(taskPackage, assigneeId)) {
                    errorMessages.add("执行人不在允许的部门范围内");
                    failCount++;
                    continue;
                }
                
                // 执行分配
                assignPackage(packageId, assigneeId);
                successCount++;
                
            } catch (Exception e) {
                log.error("分配任务包{}失败", packageId, e);
                errorMessages.add("任务包 " + packageId + " 分配失败: " + e.getMessage());
                failCount++;
            }
        }
        
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages);
        
        return result;
    }

    private void fillDepartmentNames(List<TaskPackageEntity> packages) {
        if (packages == null || packages.isEmpty()) {
            return;
        }
        
        // 收集所有部门ID
        Set<Long> departmentIds = packages.stream()
            .flatMap(p -> Stream.of(p.getDepartmentId(), p.getCreatorDepartmentId()))
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        
        // 批量查询部门信息
        Map<Long, String> departmentNameMap = departmentService.getDepartmentNameMap(departmentIds);
        
        // 填充部门名称
        packages.forEach(pkg -> {
            pkg.setDepartmentName(departmentNameMap.get(pkg.getDepartmentId()));
            pkg.setCreatorDepartmentName(departmentNameMap.get(pkg.getCreatorDepartmentId()));
        });
    }
    
    private boolean validateAssigneePermission(TaskPackageEntity taskPackage, Long assigneeId) {
        // 获取执行人信息
        BaseSysUserEntity assignee = userService.getById(assigneeId);
        if (assignee == null) {
            return false;
        }
        
        // 检查执行人的部门是否在当前用户的权限范围内
        Long[] userDepartmentIds = permissionService.getUserDepartmentIds(
            CoolSecurityUtil.getAdminUserId());
        
        return Arrays.asList(userDepartmentIds).contains(assignee.getDepartmentId());
    }
    
    @Transactional
    private void assignPackage(Long packageId, Long assigneeId) {
        // 更新任务包分配信息
        TaskPackageEntity taskPackage = new TaskPackageEntity();
        taskPackage.setId(packageId);
        taskPackage.setOwnerId(assigneeId);
        taskPackage.setPackageStatus(1); // 已分配
        updateById(taskPackage);
        
        // 同时分配任务包下的所有任务
        taskInfoService.batchAssignByPackageId(packageId, assigneeId);
    }
}
```

### 2. AI任务生成器服务集成

```java
@Service
@RequiredArgsConstructor
public class AILLMServiceImpl implements AILLMService {
    
    private final TaskDepartmentPermissionService permissionService;
    
    @Override
    @Transactional
    public TaskGenerateResponse generateTasksByAI(TaskGenerateRequest request) {
        // 现有的AI生成逻辑...
        
        // 在创建任务包时设置部门信息
        TaskPackageEntity taskPackage = createTaskPackageWithDepartment(scenario, request);
        taskPackageService.save(taskPackage);
        
        // 生成的任务继承任务包的部门信息
        List<TaskInfoEntity> tasks = createTaskEntitiesWithDepartment(
            scenario, request, taskPackage);
        
        // 其余逻辑...
        return response;
    }
    
    private TaskPackageEntity createTaskPackageWithDepartment(
            SOPScenarioEntity scenario, TaskGenerateRequest request) {
        TaskPackageEntity taskPackage = new TaskPackageEntity();
        
        // 设置基本信息
        taskPackage.setPackageName("AI生成任务包 - " + scenario.getScenarioName());
        taskPackage.setDescription("基于AI生成的任务包");
        
        // 设置部门信息
        Long currentUserId = CoolSecurityUtil.getAdminUserId();
        BaseSysUserEntity currentUser = userService.getById(currentUserId);
        
        // 支持高级选项自定义选择具有权限的部门
        // 优先使用请求中指定的部门，否则使用当前用户的部门
        Long departmentId = request.getDepartmentId() != null 
            ? request.getDepartmentId() 
            : currentUser.getDepartmentId();
            
        // 验证用户是否有权限在指定部门创建任务
        Long[] userDepartmentIds = permissionService.getUserDepartmentIds(currentUserId);
        if (!Arrays.asList(userDepartmentIds).contains(departmentId)) {
            throw new CoolException("权限不足，无法在指定部门创建任务");
        }
        
        taskPackage.setDepartmentId(departmentId);
        taskPackage.setCreatorDepartmentId(currentUser.getDepartmentId());
        
        return taskPackage;
    }
    
    private List<TaskInfoEntity> createTaskEntitiesWithDepartment(
            SOPScenarioEntity scenario, TaskGenerateRequest request, 
            TaskPackageEntity taskPackage) {
        
        List<TaskInfoEntity> tasks = new ArrayList<>();
        
        // 获取场景步骤
        List<SOPStepEntity> steps = sopStepService.getStepsByScenarioId(scenario.getId());
        
        for (SOPStepEntity step : steps) {
            TaskInfoEntity task = new TaskInfoEntity();
            
            // 设置任务基本信息
            task.setTaskName(step.getStepName());
            task.setTaskDescription(step.getTaskDescription());
            task.setPackageId(taskPackage.getId());
            
            // 继承任务包的部门信息
            task.setDepartmentId(taskPackage.getDepartmentId());
            task.setCreatorDepartmentId(taskPackage.getCreatorDepartmentId());
            
            // 其他字段设置...
            
            tasks.add(task);
        }
        
        return tasks;
    }
}
```

## 🎨 前端集成设计

### 1. AI任务生成器前端设计

#### 1.1 AI生成器部门选择组件

```vue
<template>
  <div class="ai-task-generator">
    <!-- 基础生成选项 -->
    <div class="basic-options">
      <!-- 现有的基础选项... -->
    </div>
    
    <!-- 高级选项面板 -->
    <el-collapse v-model="activeCollapse" class="advanced-options">
      <el-collapse-item title="高级选项" name="advanced">
        <div class="advanced-content">
          <!-- 部门选择器 -->
          <div class="department-selector-section">
            <label class="option-label">目标部门</label>
            <el-tooltip content="选择任务包和任务的所属部门，将影响任务的可见性和分配范围">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
            
            <ai-department-selector 
              v-model="generateOptions.departmentId"
              :user-departments="userDepartments"
              @change="onDepartmentChange"
            />
            
            <!-- 权限范围提示 -->
            <div v-if="selectedDepartment" class="permission-hint">
              <el-alert
                :title="`将在 ${selectedDepartment.name} 部门创建任务`"
                type="info"
                :closable="false"
              >
                <template #default>
                  <div class="permission-details">
                    <p>📋 可见范围：{{ getVisibilityRange() }}</p>
                    <p>👥 可分配范围：{{ getAssigneeRange() }}</p>
                    <p v-if="crossDepartmentCollaboration">
                      🔗 支持跨部门协作：{{ getCrossDepartmentInfo() }}
                    </p>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>
          
          <!-- 其他高级选项 -->
          <div class="other-advanced-options">
            <!-- 现有的其他高级选项... -->
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 生成按钮 -->
    <div class="generate-actions">
      <el-button 
        type="primary" 
        @click="generateTasks"
        :loading="generating"
        :disabled="!canGenerate"
      >
        <el-icon><MagicStick /></el-icon>
        AI智能生成任务
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { usePermission } from '@/hooks/usePermission'
import AIDepartmentSelector from './components/AIDepartmentSelector.vue'

const userStore = useUserStore()
const { userDepartments, getDepartmentPermissionLevel } = usePermission()

const activeCollapse = ref(['advanced'])
const generating = ref(false)
const generateOptions = ref({
  departmentId: null,
  // 其他选项...
})

const selectedDepartment = computed(() => {
  return userDepartments.value.find(dept => dept.id === generateOptions.value.departmentId)
})

const crossDepartmentCollaboration = computed(() => {
  // 检查是否支持跨部门协作
  return selectedDepartment.value?.allowCrossDepartment || false
})

const canGenerate = computed(() => {
  return generateOptions.value.departmentId && 
         selectedDepartment.value && 
         !generating.value
})

const onDepartmentChange = (departmentId) => {
  generateOptions.value.departmentId = departmentId
  // 触发权限范围更新
}

const getVisibilityRange = () => {
  if (!selectedDepartment.value) return ''
  
  const level = getDepartmentPermissionLevel(selectedDepartment.value)
  switch (level) {
    case 'full': return `${selectedDepartment.value.name} 及其子部门`
    case 'department': return `仅 ${selectedDepartment.value.name}`
    case 'limited': return '受限范围'
    default: return '未知'
  }
}

const getAssigneeRange = () => {
  if (!selectedDepartment.value) return ''
  return `${selectedDepartment.value.name} 部门成员`
}

const getCrossDepartmentInfo = () => {
  // 获取跨部门协作信息
  return '已启用，可邀请其他部门成员协作'
}

const generateTasks = async () => {
  try {
    generating.value = true
    
    // 调用AI生成API
    const response = await service.task.ai.generate({
      ...generateOptions.value,
      scenarioId: selectedScenario.value?.id
    })
    
    if (response.success) {
      ElMessage.success('任务生成成功！')
      // 跳转到任务包详情页面
      router.push(`/task/package/${response.data.packageId}`)
    }
  } catch (error) {
    ElMessage.error('任务生成失败：' + error.message)
  } finally {
    generating.value = false
  }
}

onMounted(() => {
  // 默认选择用户当前部门
  if (userStore.userInfo?.departmentId) {
    generateOptions.value.departmentId = userStore.userInfo.departmentId
  }
})
</script>

<style scoped>
.advanced-options {
  margin: 20px 0;
}

.department-selector-section {
  margin-bottom: 20px;
}

.option-label {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  margin-bottom: 8px;
}

.help-icon {
  margin-left: 4px;
  color: #909399;
  cursor: help;
}

.permission-hint {
  margin-top: 12px;
}

.permission-details p {
  margin: 4px 0;
  font-size: 13px;
}

.generate-actions {
  text-align: center;
  margin-top: 24px;
}
</style>
```

#### 1.2 AI部门选择器组件

```vue
<template>
  <div class="ai-department-selector">
    <el-select
      v-model="selectedDepartmentId"
      placeholder="选择目标部门"
      filterable
      clearable
      @change="handleChange"
    >
      <template #prefix>
        <el-icon><OfficeBuilding /></el-icon>
      </template>
      
      <!-- 推荐部门组 -->
      <el-option-group label="📌 推荐部门">
        <el-option
          v-for="dept in recommendedDepartments"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
        >
          <div class="department-option">
            <span class="dept-name">{{ dept.name }}</span>
            <el-tag v-if="dept.isCurrent" type="success" size="small">当前</el-tag>
            <el-tag v-if="dept.isFrequent" type="primary" size="small">常用</el-tag>
          </div>
        </el-option>
      </el-option-group>
      
      <!-- 全部有权限部门 -->
      <el-option-group label="🏢 全部有权限部门">
        <el-option
          v-for="dept in otherDepartments"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
        >
          <div class="department-option">
            <span class="dept-name">{{ dept.name }}</span>
            <el-tag 
              :type="getDepartmentTagType(dept)" 
              size="small"
            >
              {{ getDepartmentPermissionText(dept) }}
            </el-tag>
          </div>
        </el-option>
      </el-option-group>
    </el-select>
    
    <!-- 部门详情卡片 -->
    <transition name="slide-fade">
      <div v-if="selectedDepartment" class="department-info-card">
        <div class="card-header">
          <el-icon><OfficeBuilding /></el-icon>
          <span class="dept-name">{{ selectedDepartment.name }}</span>
          <el-tag :type="getDepartmentTagType(selectedDepartment)" size="small">
            {{ getDepartmentPermissionText(selectedDepartment) }}
          </el-tag>
        </div>
        
        <div class="card-content">
          <div class="info-item">
            <span class="label">部门层级：</span>
            <span class="value">{{ getDepartmentPath(selectedDepartment) }}</span>
          </div>
          <div class="info-item">
            <span class="label">权限级别：</span>
            <span class="value">{{ getPermissionLevelText(selectedDepartment) }}</span>
          </div>
          <div class="info-item" v-if="selectedDepartment.memberCount">
            <span class="label">部门人数：</span>
            <span class="value">{{ selectedDepartment.memberCount }} 人</span>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/store/user'
import { usePermission } from '@/hooks/usePermission'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  userDepartments: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const userStore = useUserStore()
const { getDepartmentPermissionLevel } = usePermission()

const selectedDepartmentId = ref(props.modelValue)

const selectedDepartment = computed(() => {
  return props.userDepartments.find(dept => dept.id === selectedDepartmentId.value)
})

// 推荐部门（当前部门 + 常用部门）
const recommendedDepartments = computed(() => {
  const current = userStore.userInfo?.departmentId
  const frequent = getFrequentDepartments() // 从本地存储获取常用部门
  
  return props.userDepartments.filter(dept => {
    const isCurrent = dept.id === current
    const isFrequent = frequent.includes(dept.id)
    return isCurrent || isFrequent
  }).map(dept => ({
    ...dept,
    isCurrent: dept.id === current,
    isFrequent: frequent.includes(dept.id)
  }))
})

// 其他部门
const otherDepartments = computed(() => {
  const recommendedIds = recommendedDepartments.value.map(d => d.id)
  return props.userDepartments.filter(dept => !recommendedIds.includes(dept.id))
})

const handleChange = (value) => {
  selectedDepartmentId.value = value
  emit('update:modelValue', value)
  emit('change', value)
  
  // 记录到常用部门
  if (value) {
    recordFrequentDepartment(value)
  }
}

const getDepartmentTagType = (dept) => {
  const level = getDepartmentPermissionLevel(dept)
  switch (level) {
    case 'full': return 'success'
    case 'department': return 'primary'
    case 'limited': return 'warning'
    default: return 'info'
  }
}

const getDepartmentPermissionText = (dept) => {
  const level = getDepartmentPermissionLevel(dept)
  switch (level) {
    case 'full': return '完全权限'
    case 'department': return '部门权限'
    case 'limited': return '受限权限'
    default: return '基础权限'
  }
}

const getDepartmentPath = (dept) => {
  // 构建部门路径，如：公司 > 技术部 > 前端组
  return dept.path || dept.name
}

const getPermissionLevelText = (dept) => {
  const level = getDepartmentPermissionLevel(dept)
  switch (level) {
    case 'full': return '可管理该部门及其子部门的所有任务'
    case 'department': return '可管理该部门的任务'
    case 'limited': return '受限管理权限'
    default: return '基础查看权限'
  }
}

const getFrequentDepartments = () => {
  const stored = localStorage.getItem('frequent_departments')
  return stored ? JSON.parse(stored) : []
}

const recordFrequentDepartment = (departmentId) => {
  const frequent = getFrequentDepartments()
  if (!frequent.includes(departmentId)) {
    frequent.push(departmentId)
    // 最多保存5个常用部门
    if (frequent.length > 5) {
      frequent.shift()
    }
    localStorage.setItem('frequent_departments', JSON.stringify(frequent))
  }
}

watch(() => props.modelValue, (newValue) => {
  selectedDepartmentId.value = newValue
})
</script>

<style scoped>
.department-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dept-name {
  flex: 1;
}

.department-info-card {
  margin-top: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.card-header .dept-name {
  margin: 0 8px;
  flex: 1;
}

.card-content {
  font-size: 13px;
}

.info-item {
  display: flex;
  margin: 4px 0;
}

.info-item .label {
  color: #666;
  width: 80px;
}

.info-item .value {
  color: #333;
  flex: 1;
}

.slide-fade-enter-active, .slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from, .slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
```

### 2. 任务列表前端设计

#### 2.1 任务包列表部门展示和筛选

```vue
<template>
  <div class="task-package-list">
    <!-- 搜索工具栏 -->
    <div class="search-toolbar">
      <el-row :gutter="16" align="middle">
        <el-col :span="6">
          <el-input
            v-model="searchParams.keyword"
            placeholder="搜索任务包名称..."
            clearable
            @change="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        
        <el-col :span="4">
          <el-date-picker
            v-model="searchParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="default"
            @change="handleSearch"
          />
        </el-col>
        
        <el-col :span="3">
          <el-select
            v-model="searchParams.status"
            placeholder="状态筛选"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部状态" value="" />
            <el-option label="待开始" value="0" />
            <el-option label="进行中" value="1" />
            <el-option label="已完成" value="2" />
            <el-option label="已暂停" value="3" />
          </el-select>
        </el-col>
        
        <!-- 部门筛选器 -->
        <el-col :span="4">
          <task-department-filter
            v-model="searchParams.departmentIds"
            @change="handleSearch"
          />
        </el-col>
        
        <el-col :span="7">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新建任务包
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="showBatchActions = !showBatchActions">
            <el-icon><Operation /></el-icon>
            批量操作
          </el-button>
        </el-col>
      </el-row>
      
      <!-- 部门筛选快捷选项 -->
      <div v-if="searchParams.departmentIds?.length > 0" class="department-quick-filters">
        <span class="filter-label">已选部门：</span>
        <el-tag
          v-for="deptId in searchParams.departmentIds"
          :key="deptId"
          :type="getDepartmentTagType(deptId)"
          size="small"
          closable
          @close="removeDepartmentFilter(deptId)"
        >
          {{ getDepartmentName(deptId) }} ({{ getDepartmentTaskCount(deptId) }})
        </el-tag>
        <el-button
          type="text"
          size="small"
          @click="clearDepartmentFilters"
        >
          清空筛选
        </el-button>
      </div>
    </div>

    <!-- 任务包列表表格 -->
    <cl-crud ref="Crud">
      <cl-row>
        <cl-table ref="Table">
          <!-- 选择列 -->
          <el-table-column type="selection" width="55" />
          
          <!-- 任务包名称 -->
          <el-table-column label="任务包名称" prop="packageName" min-width="200">
            <template #default="{ row }">
              <div class="package-name-cell">
                <el-button
                  type="text"
                  @click="viewPackageDetail(row)"
                  class="package-name-link"
                >
                  {{ row.packageName }}
                </el-button>
                <div class="package-meta">
                  <el-tag v-if="row.isAiGenerated" type="primary" size="small">
                    AI生成
                  </el-tag>
                  <el-tag v-if="row.isCrossDepartment" type="warning" size="small">
                    跨部门协作
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 所属部门 -->
          <el-table-column label="所属部门" prop="departmentName" width="140">
            <template #default="{ row }">
              <department-tag 
                :department-id="row.departmentId"
                :department-name="row.departmentName"
                @click="filterByDepartment(row.departmentId)"
              />
            </template>
          </el-table-column>
          
          <!-- 创建者信息 -->
          <el-table-column label="创建者" width="120">
            <template #default="{ row }">
              <div class="creator-info">
                <div class="creator-name">{{ row.creatorName }}</div>
                <div class="creator-dept">{{ row.creatorDepartmentName }}</div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 任务统计 -->
          <el-table-column label="任务统计" width="100">
            <template #default="{ row }">
              <div class="task-stats">
                <div class="stat-item">
                  <span class="stat-label">总数:</span>
                  <span class="stat-value">{{ row.totalTasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">完成:</span>
                  <span class="stat-value completed">{{ row.completedTasks || 0 }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 状态 -->
          <el-table-column label="状态" prop="packageStatus" width="100">
            <template #default="{ row }">
              <package-status-tag :status="row.packageStatus" />
            </template>
          </el-table-column>
          
          <!-- 权限状态 -->
          <el-table-column label="权限状态" width="100">
            <template #default="{ row }">
              <permission-status-indicator :task="row" task-type="package" />
            </template>
          </el-table-column>
          
          <!-- 更新时间 -->
          <el-table-column label="更新时间" prop="updateTime" width="140">
            <template #default="{ row }">
              {{ formatDateTime(row.updateTime) }}
            </template>
          </el-table-column>
          
          <!-- 操作列 -->
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <task-operation-buttons
                :task="row"
                task-type="package"
                @view="viewPackageDetail"
                @edit="editPackage"
                @assign="assignPackage"
                @delete="deletePackage"
              />
            </template>
          </el-table-column>
        </cl-table>
      </cl-row>
      
      <!-- 分页 -->
      <cl-row>
        <cl-flex1 />
        <cl-pagination />
      </cl-row>
    </cl-crud>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useCrud, useTable } from '@cool-vue/crud'
import { usePermission } from '@/hooks/usePermission'
import { useCool } from '/@/cool'
import TaskDepartmentFilter from './components/TaskDepartmentFilter.vue'
import DepartmentTag from './components/DepartmentTag.vue'
import PermissionStatusIndicator from './components/PermissionStatusIndicator.vue'
import TaskOperationButtons from './components/TaskOperationButtons.vue'
import PackageStatusTag from './components/PackageStatusTag.vue'

defineOptions({
  name: "task-package-list"
});

const { service } = useCool()
const { userDepartments, getDepartmentPermissionLevel } = usePermission()

const searchParams = reactive({
  keyword: '',
  dateRange: null,
  status: '',
  departmentIds: []
})

const showBatchActions = ref(false)

// CRUD配置
const Crud = useCrud({
  service: service.task.package
}, (app) => {
  app.refresh()
})

// 表格配置
const Table = useTable({
  contextMenu: false,
  onRefresh: (params, { next }) => {
    // 应用搜索参数
    Object.assign(params, searchParams)
    next(params)
  }
})

const departmentTaskCounts = ref({})

const getDepartmentName = (departmentId) => {
  const dept = userDepartments.value.find(d => d.id === departmentId)
  return dept?.name || '未知部门'
}

const getDepartmentTagType = (departmentId) => {
  const level = getDepartmentPermissionLevel({ id: departmentId })
  switch (level) {
    case 'full': return 'success'
    case 'department': return 'primary'
    case 'limited': return 'warning'
    default: return 'info'
  }
}

const getDepartmentTaskCount = (departmentId) => {
  return departmentTaskCounts.value[departmentId] || 0
}

const handleSearch = () => {
  Table.value.refresh()
}

const handleRefresh = () => {
  Table.value.refresh()
  loadDepartmentTaskCounts()
}

const removeDepartmentFilter = (departmentId) => {
  const index = searchParams.departmentIds.indexOf(departmentId)
  if (index > -1) {
    searchParams.departmentIds.splice(index, 1)
    handleSearch()
  }
}

const clearDepartmentFilters = () => {
  searchParams.departmentIds = []
  handleSearch()
}

const filterByDepartment = (departmentId) => {
  if (!searchParams.departmentIds.includes(departmentId)) {
    searchParams.departmentIds.push(departmentId)
    handleSearch()
  }
}

const loadDepartmentTaskCounts = async () => {
  try {
    const response = await service.task.package.getDepartmentTaskCounts()
    departmentTaskCounts.value = response.data || {}
  } catch (error) {
    console.error('加载部门任务统计失败:', error)
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const viewPackageDetail = (row) => {
  // 跳转到任务包详情页面
  router.push(`/task/package/${row.id}`)
}

const editPackage = (row) => {
  // 编辑任务包逻辑
}

const assignPackage = (row) => {
  // 分配任务包逻辑
}

const deletePackage = (row) => {
  // 删除任务包逻辑
}

const handleAdd = () => {
  // 新建任务包逻辑
}

onMounted(() => {
  loadDepartmentTaskCounts()
})
</script>

<style scoped>
.search-toolbar {
  margin-bottom: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
}

.department-quick-filters {
  margin-top: 12px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-label {
  color: #666;
  font-size: 13px;
  margin-right: 8px;
}

.package-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.package-name-link {
  font-weight: 500;
  text-align: left;
  padding: 0;
}

.package-meta {
  display: flex;
  gap: 4px;
}

.creator-info {
  font-size: 13px;
}

.creator-name {
  font-weight: 500;
  color: #333;
}

.creator-dept {
  color: #666;
  margin-top: 2px;
}

.task-stats {
  font-size: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin: 2px 0;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 500;
  color: #333;
}

.stat-value.completed {
  color: #67c23a;
}
</style>
```

#### 2.2 部门筛选器组件

```vue
<template>
  <div class="task-department-filter">
    <el-select
      v-model="selectedDepartments"
      multiple
      placeholder="选择部门筛选"
      collapse-tags
      collapse-tags-tooltip
      @change="handleChange"
    >
      <template #prefix>
        <el-icon><OfficeBuilding /></el-icon>
      </template>
      
      <!-- 快捷选项 -->
      <el-option-group label="🔥 快捷选项">
        <el-option
          label="我的部门"
          :value="myDepartmentFilter"
          @click="selectMyDepartment"
        >
          <div class="quick-option">
            <span>我的部门</span>
            <el-tag type="success" size="small">{{ myDepartmentTaskCount }}</el-tag>
          </div>
        </el-option>
        
        <el-option
          label="全部权限部门"
          :value="allPermissionDepartments"
          @click="selectAllPermissionDepartments"
        >
          <div class="quick-option">
            <span>全部权限部门</span>
            <el-tag type="primary" size="small">{{ allPermissionTaskCount }}</el-tag>
          </div>
        </el-option>
      </el-option-group>
      
      <!-- 具体部门列表 -->
      <el-option-group label="🏢 具体部门">
        <el-option
          v-for="dept in availableDepartments"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
        >
          <div class="department-option">
            <div class="dept-info">
              <span class="dept-name">{{ dept.name }}</span>
              <span class="dept-path">{{ dept.path }}</span>
            </div>
            <div class="dept-stats">
              <el-tag
                :type="getDepartmentTagType(dept)"
                size="small"
              >
                {{ dept.taskCount || 0 }}
              </el-tag>
              <permission-level-icon :level="getDepartmentPermissionLevel(dept)" />
            </div>
          </div>
        </el-option>
      </el-option-group>
    </el-select>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { usePermission } from '@/hooks/usePermission'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const userStore = useUserStore()
const { userDepartments, getDepartmentPermissionLevel } = usePermission()

const selectedDepartments = ref([...props.modelValue])
const departmentTaskCounts = ref({})

// 快捷选项的特殊值
const myDepartmentFilter = 'MY_DEPARTMENT'
const allPermissionDepartments = 'ALL_PERMISSION_DEPARTMENTS'

const myDepartment = computed(() => {
  return userStore.userInfo?.departmentId
})

const availableDepartments = computed(() => {
  return userDepartments.value.map(dept => ({
    ...dept,
    taskCount: departmentTaskCounts.value[dept.id] || 0,
    path: buildDepartmentPath(dept)
  })).sort((a, b) => {
    // 按任务数量排序，任务多的排前面
    return (b.taskCount || 0) - (a.taskCount || 0)
  })
})

const myDepartmentTaskCount = computed(() => {
  return departmentTaskCounts.value[myDepartment.value] || 0
})

const allPermissionTaskCount = computed(() => {
  return Object.values(departmentTaskCounts.value).reduce((sum, count) => sum + count, 0)
})

const handleChange = (values) => {
  selectedDepartments.value = values
  
  // 处理快捷选项
  let actualDepartmentIds = []
  
  if (values.includes(myDepartmentFilter)) {
    actualDepartmentIds.push(myDepartment.value)
  }
  
  if (values.includes(allPermissionDepartments)) {
    actualDepartmentIds = userDepartments.value.map(d => d.id)
  } else {
    // 添加具体选择的部门
    actualDepartmentIds.push(...values.filter(v => 
      typeof v === 'number' && !actualDepartmentIds.includes(v)
    ))
  }
  
  // 去重
  actualDepartmentIds = [...new Set(actualDepartmentIds)]
  
  emit('update:modelValue', actualDepartmentIds)
  emit('change', actualDepartmentIds)
}

const selectMyDepartment = () => {
  if (!selectedDepartments.value.includes(myDepartmentFilter)) {
    selectedDepartments.value.push(myDepartmentFilter)
    handleChange(selectedDepartments.value)
  }
}

const selectAllPermissionDepartments = () => {
  selectedDepartments.value = [allPermissionDepartments]
  handleChange(selectedDepartments.value)
}

const getDepartmentTagType = (dept) => {
  const level = getDepartmentPermissionLevel(dept)
  switch (level) {
    case 'full': return 'success'
    case 'department': return 'primary'
    case 'limited': return 'warning'
    default: return 'info'
  }
}

const buildDepartmentPath = (dept) => {
  // 构建部门路径，如：公司 > 技术部 > 前端组
  // 这里简化处理，实际可能需要递归构建完整路径
  return dept.parentName ? `${dept.parentName} > ${dept.name}` : dept.name
}

const loadDepartmentTaskCounts = async () => {
  try {
    const response = await service.task.package.getDepartmentTaskCounts()
    departmentTaskCounts.value = response.data || {}
  } catch (error) {
    console.error('加载部门任务统计失败:', error)
  }
}

watch(() => props.modelValue, (newValue) => {
  selectedDepartments.value = [...newValue]
}, { deep: true })

onMounted(() => {
  loadDepartmentTaskCounts()
})
</script>

<style scoped>
.quick-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.department-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dept-info {
  flex: 1;
  min-width: 0;
}

.dept-name {
  font-weight: 500;
  color: #333;
}

.dept-path {
  font-size: 12px;
  color: #666;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dept-stats {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
```

### 3. 任务卡片前端设计

#### 3.1 任务卡片组件

```vue
<template>
  <div class="task-card" :class="cardClasses">
    <!-- 卡片头部 -->
    <div class="task-card-header">
      <div class="header-left">
        <!-- 部门标签 -->
        <department-tag
          :department-id="task.departmentId"
          :department-name="task.departmentName"
          size="small"
        />
        
        <!-- 任务标题 -->
        <h3 class="task-title" @click="viewTaskDetail">
          {{ task.taskName || task.packageName }}
        </h3>
      </div>
      
      <div class="header-right">
        <!-- 权限状态指示器 -->
        <permission-status-indicator
          :task="task"
          :task-type="taskType"
          size="small"
        />
        
        <!-- 更多操作 -->
        <el-dropdown trigger="click" @command="handleCommand">
          <el-button type="text" size="small">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="view" icon="View">
                查看详情
              </el-dropdown-item>
              <el-dropdown-item 
                v-if="canEdit" 
                command="edit" 
                icon="Edit"
              >
                编辑
              </el-dropdown-item>
              <el-dropdown-item 
                v-if="canAssign" 
                command="assign" 
                icon="User"
              >
                分配
              </el-dropdown-item>
              <el-dropdown-item 
                v-if="canDelete" 
                command="delete" 
                icon="Delete"
                divided
              >
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="task-card-content">
      <!-- 任务描述 -->
      <div v-if="task.description || task.taskDescription" class="task-description">
        <el-icon><Document /></el-icon>
        <span>{{ task.description || task.taskDescription }}</span>
      </div>
      
      <!-- 关键信息 -->
      <div class="task-key-info">
        <div v-if="task.deadline" class="info-item deadline">
          <el-icon><Clock /></el-icon>
          <span class="label">截止时间：</span>
          <span class="value" :class="{ 'urgent': isUrgent(task.deadline) }">
            {{ formatDate(task.deadline) }}
          </span>
        </div>
        
        <div v-if="task.assigneeName" class="info-item assignee">
          <el-icon><User /></el-icon>
          <span class="label">执行人：</span>
          <span class="value">{{ task.assigneeName }}</span>
          <el-tag v-if="task.assigneeDepartmentName" type="info" size="small">
            {{ task.assigneeDepartmentName }}
          </el-tag>
        </div>
        
        <div v-if="task.progress !== undefined" class="info-item progress">
          <el-icon><TrendCharts /></el-icon>
          <span class="label">进度：</span>
          <el-progress
            :percentage="task.progress"
            :color="getProgressColor(task.progress)"
            :stroke-width="6"
            :show-text="false"
          />
          <span class="progress-text">{{ task.progress }}%</span>
        </div>
        
        <div v-if="task.priority" class="info-item priority">
          <el-icon><Flag /></el-icon>
          <span class="label">优先级：</span>
          <priority-tag :priority="task.priority" />
        </div>
      </div>
    </div>
    
    <!-- 卡片底部 -->
    <div class="task-card-footer">
      <div class="footer-left">
        <!-- 创建者信息 -->
        <div class="creator-info">
          <span class="creator-text">
            创建者：{{ task.creatorName }}
            <span v-if="task.creatorDepartmentName" class="creator-dept">
              ({{ task.creatorDepartmentName }})
            </span>
          </span>
          <span class="create-time">{{ formatDate(task.createTime) }}</span>
        </div>
      </div>
      
      <div class="footer-right">
        <!-- 协作标识 -->
        <el-tag
          v-if="task.isCrossDepartment"
          type="warning"
          size="small"
          class="collaboration-tag"
        >
          <el-icon><Link /></el-icon>
          跨部门协作
        </el-tag>
        
        <!-- 快捷操作按钮 -->
        <div class="quick-actions">
          <el-button
            v-if="canView"
            type="text"
            size="small"
            @click="viewTaskDetail"
          >
            <el-icon><View /></el-icon>
            详情
          </el-button>
          
          <el-button
            v-if="canEdit"
            type="text"
            size="small"
            @click="editTask"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          
          <el-button
            v-if="canAssign"
            type="text"
            size="small"
            @click="assignTask"
          >
            <el-icon><UserFilled /></el-icon>
            分配
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePermission } from '@/hooks/usePermission'
import DepartmentTag from './DepartmentTag.vue'
import PermissionStatusIndicator from './PermissionStatusIndicator.vue'
import PriorityTag from './PriorityTag.vue'

const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  taskType: {
    type: String,
    default: 'package' // package | info | execution
  }
})

const emit = defineEmits(['view', 'edit', 'assign', 'delete'])

const { hasTaskPermission } = usePermission()

const cardClasses = computed(() => {
  return {
    'cross-department': props.task.isCrossDepartment,
    'urgent': isUrgent(props.task.deadline),
    'completed': props.task.status === 2 || props.task.progress === 100
  }
})

const canView = computed(() => {
  return hasTaskPermission(props.taskType, props.task.id, 'VIEW')
})

const canEdit = computed(() => {
  return hasTaskPermission(props.taskType, props.task.id, 'EDIT')
})

const canAssign = computed(() => {
  return hasTaskPermission(props.taskType, props.task.id, 'ASSIGN')
})

const canDelete = computed(() => {
  return hasTaskPermission(props.taskType, props.task.id, 'DELETE')
})

const handleCommand = (command) => {
  switch (command) {
    case 'view':
      viewTaskDetail()
      break
    case 'edit':
      editTask()
      break
    case 'assign':
      assignTask()
      break
    case 'delete':
      deleteTask()
      break
  }
}

const viewTaskDetail = () => {
  emit('view', props.task)
}

const editTask = () => {
  emit('edit', props.task)
}

const assignTask = () => {
  emit('assign', props.task)
}

const deleteTask = () => {
  emit('delete', props.task)
}

const isUrgent = (deadline) => {
  if (!deadline) return false
  const now = new Date()
  const deadlineDate = new Date(deadline)
  const diffDays = (deadlineDate - now) / (1000 * 60 * 60 * 24)
  return diffDays <= 3 && diffDays >= 0
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}
</script>

<style scoped>
.task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.task-card.cross-department {
  border-left: 4px solid #e6a23c;
}

.task-card.urgent {
  border-left: 4px solid #f56c6c;
}

.task-card.completed {
  background: #f0f9ff;
  border-color: #67c23a;
}

.task-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.header-left {
  flex: 1;
  min-width: 0;
}

.task-title {
  margin: 8px 0 0 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  cursor: pointer;
  transition: color 0.3s;
}

.task-title:hover {
  color: #409eff;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-card-content {
  margin-bottom: 12px;
}

.task-description {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.task-key-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.info-item .label {
  color: #666;
  min-width: 60px;
}

.info-item .value {
  color: #333;
  font-weight: 500;
}

.info-item.deadline .value.urgent {
  color: #f56c6c;
}

.info-item.progress {
  align-items: center;
}

.info-item.progress .el-progress {
  flex: 1;
  margin: 0 8px;
}

.progress-text {
  min-width: 35px;
  text-align: right;
  font-weight: 500;
}

.task-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.creator-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.creator-text {
  color: #666;
}

.creator-dept {
  color: #409eff;
}

.create-time {
  color: #999;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.collaboration-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.quick-actions {
  display: flex;
  gap: 4px;
}

.quick-actions .el-button {
  padding: 4px 6px;
}
</style>
```

#### 1.2 权限状态组件

```vue
<template>
  <div class="permission-status">
    <el-tooltip :content="permissionText" placement="top">
      <el-icon :class="permissionClass" :color="permissionColor">
        <component :is="permissionIcon" />
      </el-icon>
    </el-tooltip>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePermission } from '@/hooks/usePermission'

const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

const { getTaskPermissionLevel } = usePermission()

const permissionLevel = computed(() => {
  return getTaskPermissionLevel(props.task)
})

const permissionText = computed(() => {
  switch (permissionLevel.value) {
    case 'full': return '完全权限'
    case 'read': return '只读权限'
    case 'limited': return '受限权限'
    case 'none': return '无权限'
    default: return '未知权限'
  }
})

const permissionClass = computed(() => {
  return `permission-${permissionLevel.value}`
})

const permissionColor = computed(() => {
  switch (permissionLevel.value) {
    case 'full': return '#67c23a'
    case 'read': return '#e6a23c'
    case 'limited': return '#f56c6c'
    case 'none': return '#909399'
    default: return '#909399'
  }
})

const permissionIcon = computed(() => {
  switch (permissionLevel.value) {
    case 'full': return 'SuccessFilled'
    case 'read': return 'View'
    case 'limited': return 'WarningFilled'
    case 'none': return 'CircleCloseFilled'
    default: return 'QuestionFilled'
  }
})
</script>
```

### 4. 前端状态管理设计

#### 4.1 权限状态管理

```javascript
// store/permission.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { service } from '@/service'

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const userDepartments = ref([])
  const permissionCache = ref(new Map())
  const departmentTaskCounts = ref({})
  const lastUpdateTime = ref(null)
  
  // 计算属性
  const currentUserDepartment = computed(() => {
    const userStore = useUserStore()
    return userDepartments.value.find(d => d.id === userStore.userInfo?.departmentId)
  })
  
  const departmentMap = computed(() => {
    const map = new Map()
    userDepartments.value.forEach(dept => {
      map.set(dept.id, dept)
    })
    return map
  })
  
  // 方法
  const loadUserDepartments = async () => {
    try {
      const response = await service.base.sys.department.userDepartments()
      userDepartments.value = response.data || []
      lastUpdateTime.value = Date.now()
    } catch (error) {
      console.error('加载用户部门失败:', error)
    }
  }
  
  const loadDepartmentTaskCounts = async () => {
    try {
      const response = await service.task.package.getDepartmentTaskCounts()
      departmentTaskCounts.value = response.data || {}
    } catch (error) {
      console.error('加载部门任务统计失败:', error)
    }
  }
  
  const cachePermission = (key, hasPermission) => {
    permissionCache.value.set(key, {
      hasPermission,
      timestamp: Date.now()
    })
  }
  
  const getCachedPermission = (key) => {
    const cached = permissionCache.value.get(key)
    if (!cached) return null
    
    // 缓存有效期5分钟
    const isExpired = Date.now() - cached.timestamp > 5 * 60 * 1000
    if (isExpired) {
      permissionCache.value.delete(key)
      return null
    }
    
    return cached.hasPermission
  }
  
  const clearPermissionCache = () => {
    permissionCache.value.clear()
  }
  
  const refreshData = async () => {
    await Promise.all([
      loadUserDepartments(),
      loadDepartmentTaskCounts()
    ])
    clearPermissionCache()
  }
  
  return {
    // 状态
    userDepartments,
    departmentTaskCounts,
    lastUpdateTime,
    
    // 计算属性
    currentUserDepartment,
    departmentMap,
    
    // 方法
    loadUserDepartments,
    loadDepartmentTaskCounts,
    cachePermission,
    getCachedPermission,
    clearPermissionCache,
    refreshData
  }
})
```

#### 4.2 权限Hook实现

```javascript
// hooks/usePermission.js
import { ref, computed } from 'vue'
import { service } from '@/service'
import { useUserStore } from '@/store/user'
import { usePermissionStore } from '@/store/permission'

export function usePermission() {
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()
  
  const userDepartments = computed(() => {
    return permissionStore.userDepartments
  })
  
  const hasTaskPermission = async (taskType, taskId, operation) => {
    try {
      // 检查缓存
      const cacheKey = `${taskType}:${taskId}:${operation}`
      const cached = permissionStore.getCachedPermission(cacheKey)
      if (cached !== null) {
        return cached
      }
      
      // 发送权限检查请求
      const response = await service.request({
        url: `/admin/task/permission/check`,
        method: 'POST',
        data: {
          taskType,
          taskId,
          operation
        }
      })
      
      const hasPermission = response?.data?.hasPermission || false
      
      // 缓存结果
      permissionStore.cachePermission(cacheKey, hasPermission)
      
      return hasPermission
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }
  
  const getDepartmentPermissionLevel = (department) => {
    if (!department || !department.id) {
      return 'none'
    }
    
    const currentUserId = userStore.userInfo?.id
    const currentUserDeptId = userStore.userInfo?.departmentId
    
    // admin用户拥有所有权限
    if (userStore.userInfo?.username === 'admin') {
      return 'full'
    }
    
    // 检查是否为用户的直属部门
    if (department.id === currentUserDeptId) {
      return 'full'
    }
    
    // 检查是否在用户的权限部门列表中
    const userDeptIds = userDepartments.value.map(d => d.id)
    if (userDeptIds.includes(department.id)) {
      // 进一步判断权限级别
      const userDept = userDepartments.value.find(d => d.id === department.id)
      return userDept?.permissionLevel || 'department'
    }
    
    return 'none'
  }
  
  const getTaskPermissionLevel = (task) => {
    const departmentLevel = getDepartmentPermissionLevel({
      id: task.departmentId
    })
    
    if (departmentLevel === 'none') {
      // 检查是否为跨部门协作任务
      if (task.isCrossDepartment) {
        return 'limited'
      }
      return 'none'
    }
    
    return departmentLevel
  }
  
  const canOperateTask = (task, operation) => {
    const permissionLevel = getTaskPermissionLevel(task)
    
    switch (operation) {
      case 'VIEW':
        return permissionLevel !== 'none'
      case 'EDIT':
        return ['full', 'department'].includes(permissionLevel)
      case 'DELETE':
        return permissionLevel === 'full'
      case 'ASSIGN':
        return ['full', 'department'].includes(permissionLevel)
      default:
        return false
    }
  }
  
  const batchCheckPermissions = async (tasks, operation) => {
    try {
      const response = await service.request({
        url: `/admin/task/permission/batch-check`,
        method: 'POST',
        data: {
          tasks: tasks.map(t => ({
            taskType: t.type || 'package',
            taskId: t.id
          })),
          operation
        }
      })
      
      const permissions = response?.data?.permissions || {}
      
      // 缓存批量权限结果
      tasks.forEach(task => {
        const cacheKey = `${task.type || 'package'}:${task.id}:${operation}`
        const hasPermission = permissions[task.id] || false
        permissionStore.cachePermission(cacheKey, hasPermission)
      })
      
      return permissions
    } catch (error) {
      console.error('批量权限检查失败:', error)
      return {}
    }
  }
  
  const getDepartmentName = (departmentId) => {
    if (!departmentId) return '未分配'
    const dept = permissionStore.departmentMap.get(departmentId)
    return dept?.name || '未知部门'
  }
  
  const getDepartmentTaskCount = (departmentId) => {
    return permissionStore.departmentTaskCounts[departmentId] || 0
  }
  
  const refreshPermissionData = async () => {
    await permissionStore.refreshData()
  }
  
  return {
    // 数据
    userDepartments,
    
    // 权限检查方法
    hasTaskPermission,
    getDepartmentPermissionLevel,
    getTaskPermissionLevel,
    canOperateTask,
    batchCheckPermissions,
    
    // 工具方法
    getDepartmentName,
    getDepartmentTaskCount,
    refreshPermissionData
  }
}
```

#### 4.3 任务状态管理

```javascript
// store/task.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { service } from '@/service'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const taskPackages = ref([])
  const taskInfos = ref([])
  const taskExecutions = ref([])
  const loading = ref(false)
  const searchParams = ref({
    keyword: '',
    departmentIds: [],
    status: '',
    dateRange: null
  })
  
  // 计算属性
  const filteredTaskPackages = computed(() => {
    let result = taskPackages.value
    
    // 关键词搜索
    if (searchParams.value.keyword) {
      const keyword = searchParams.value.keyword.toLowerCase()
      result = result.filter(pkg => 
        pkg.packageName?.toLowerCase().includes(keyword) ||
        pkg.description?.toLowerCase().includes(keyword)
      )
    }
    
    // 部门筛选
    if (searchParams.value.departmentIds?.length > 0) {
      result = result.filter(pkg => 
        searchParams.value.departmentIds.includes(pkg.departmentId)
      )
    }
    
    // 状态筛选
    if (searchParams.value.status !== '') {
      result = result.filter(pkg => pkg.packageStatus == searchParams.value.status)
    }
    
    return result
  })
  
  const taskStatistics = computed(() => {
    const stats = {
      total: taskPackages.value.length,
      pending: 0,
      inProgress: 0,
      completed: 0,
      paused: 0
    }
    
    taskPackages.value.forEach(pkg => {
      switch (pkg.packageStatus) {
        case 0: stats.pending++; break
        case 1: stats.inProgress++; break
        case 2: stats.completed++; break
        case 3: stats.paused++; break
      }
    })
    
    return stats
  })
  
  // 方法
  const loadTaskPackages = async (params = {}) => {
    try {
      loading.value = true
      const response = await service.task.package.page({
        ...searchParams.value,
        ...params
      })
      
      if (response.success) {
        taskPackages.value = response.data?.list || []
        return response.data
      }
    } catch (error) {
      console.error('加载任务包列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const createTaskPackage = async (packageData) => {
    try {
      loading.value = true
      const response = await service.task.package.add(packageData)
      
      if (response.success) {
        // 重新加载列表
        await loadTaskPackages()
        return response.data
      }
    } catch (error) {
      console.error('创建任务包失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const updateTaskPackage = async (packageData) => {
    try {
      loading.value = true
      const response = await service.task.package.update(packageData)
      
      if (response.success) {
        // 更新本地数据
        const index = taskPackages.value.findIndex(pkg => pkg.id === packageData.id)
        if (index > -1) {
          taskPackages.value[index] = { ...taskPackages.value[index], ...packageData }
        }
        return response.data
      }
    } catch (error) {
      console.error('更新任务包失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const deleteTaskPackage = async (packageId) => {
    try {
      loading.value = true
      const response = await service.task.package.delete({ ids: [packageId] })
      
      if (response.success) {
        // 从本地数据中移除
        const index = taskPackages.value.findIndex(pkg => pkg.id === packageId)
        if (index > -1) {
          taskPackages.value.splice(index, 1)
        }
        return true
      }
    } catch (error) {
      console.error('删除任务包失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const updateSearchParams = (params) => {
    Object.assign(searchParams.value, params)
  }
  
  const resetSearchParams = () => {
    searchParams.value = {
      keyword: '',
      departmentIds: [],
      status: '',
      dateRange: null
    }
  }
  
  return {
    // 状态
    taskPackages,
    taskInfos,
    taskExecutions,
    loading,
    searchParams,
    
    // 计算属性
    filteredTaskPackages,
    taskStatistics,
    
    // 方法
    loadTaskPackages,
    createTaskPackage,
    updateTaskPackage,
    deleteTaskPackage,
    updateSearchParams,
    resetSearchParams
  }
})
```

#### 4.4 通用工具函数

```javascript
// utils/taskUtils.js

/**
 * 格式化任务状态
 */
export const formatTaskStatus = (status) => {
  const statusMap = {
    0: { text: '待开始', type: 'info' },
    1: { text: '进行中', type: 'warning' },
    2: { text: '已完成', type: 'success' },
    3: { text: '已暂停', type: 'danger' }
  }
  return statusMap[status] || { text: '未知', type: 'info' }
}

/**
 * 格式化优先级
 */
export const formatTaskPriority = (priority) => {
  const priorityMap = {
    1: { text: '低', type: 'success' },
    2: { text: '中', type: 'warning' },
    3: { text: '高', type: 'danger' },
    4: { text: '紧急', type: 'danger' }
  }
  return priorityMap[priority] || { text: '普通', type: 'info' }
}

/**
 * 计算任务紧急程度
 */
export const getTaskUrgency = (deadline) => {
  if (!deadline) return 'normal'
  
  const now = new Date()
  const deadlineDate = new Date(deadline)
  const diffDays = (deadlineDate - now) / (1000 * 60 * 60 * 24)
  
  if (diffDays < 0) return 'overdue'  // 已逾期
  if (diffDays <= 1) return 'urgent'  // 紧急（1天内）
  if (diffDays <= 3) return 'warning' // 警告（3天内）
  return 'normal'                     // 正常
}

/**
 * 格式化部门路径
 */
export const formatDepartmentPath = (department) => {
  if (!department) return ''
  
  const paths = []
  let current = department
  
  while (current) {
    paths.unshift(current.name)
    current = current.parent
  }
  
  return paths.join(' > ')
}

/**
 * 获取任务进度颜色
 */
export const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'   // 红色
  if (progress < 70) return '#e6a23c'   // 橙色
  return '#67c23a'                      // 绿色
}

/**
 * 检查任务是否为跨部门协作
 */
export const isCrossDepartmentTask = (task) => {
  return task.isCrossDepartment || 
         (task.departmentId !== task.creatorDepartmentId) ||
         (task.departmentId !== task.assigneeDepartmentId)
}

/**
 * 生成任务唯一标识
 */
export const generateTaskKey = (taskType, taskId) => {
  return `${taskType}:${taskId}`
}

/**
 * 解析任务唯一标识
 */
export const parseTaskKey = (taskKey) => {
  const [taskType, taskId] = taskKey.split(':')
  return { taskType, taskId: parseInt(taskId) }
}

/**
 * 格式化时间显示
 */
export const formatTaskTime = (time, format = 'relative') => {
  if (!time) return '-'
  
  const date = new Date(time)
  const now = new Date()
  
  if (format === 'relative') {
    const diffMs = now - date
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    
    if (diffDays > 0) return `${diffDays}天前`
    if (diffHours > 0) return `${diffHours}小时前`
    if (diffMinutes > 0) return `${diffMinutes}分钟前`
    return '刚刚'
  }
  
  return date.toLocaleString('zh-CN')
}

/**
 * 计算任务统计信息
 */
export const calculateTaskStats = (tasks) => {
  const stats = {
    total: tasks.length,
    completed: 0,
    inProgress: 0,
    pending: 0,
    overdue: 0,
    avgProgress: 0
  }
  
  let totalProgress = 0
  
  tasks.forEach(task => {
    // 统计进度
    if (task.progress !== undefined) {
      totalProgress += task.progress
      if (task.progress === 100) stats.completed++
      else if (task.progress > 0) stats.inProgress++
      else stats.pending++
    } else {
      // 根据状态统计
      switch (task.status) {
        case 2: stats.completed++; break
        case 1: stats.inProgress++; break
        default: stats.pending++; break
      }
    }
    
    // 统计逾期任务
    if (getTaskUrgency(task.deadline) === 'overdue') {
      stats.overdue++
    }
  })
  
  stats.avgProgress = tasks.length > 0 ? Math.round(totalProgress / tasks.length) : 0
  
  return stats
}
```

### 3. 部门选择器组件

```vue
<template>
  <div class="department-selector">
    <el-select 
      v-model="selectedDepartment" 
      placeholder="选择部门"
      filterable
      @change="handleDepartmentChange"
    >
      <el-option-group
        v-for="group in departmentGroups"
        :key="group.label"
        :label="group.label"
      >
        <el-option
          v-for="dept in group.options"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
          :disabled="!dept.hasPermission"
        >
          <span>{{ dept.name }}</span>
          <span class="permission-badge" v-if="!dept.hasPermission">
            无权限
          </span>
        </el-option>
      </el-option-group>
    </el-select>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { usePermission } from '@/hooks/usePermission'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  taskType: {
    type: String,
    default: 'package'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const { userDepartments } = usePermission()
const allDepartments = ref([])
const selectedDepartment = ref(props.modelValue)

const departmentGroups = computed(() => {
  const groups = []
  
  // 有权限的部门
  const authorizedDepts = allDepartments.value.filter(dept => 
    userDepartments.value.some(userDept => userDept.id === dept.id)
  ).map(dept => ({ ...dept, hasPermission: true }))
  
  if (authorizedDepts.length > 0) {
    groups.push({
      label: '有权限的部门',
      options: authorizedDepts
    })
  }
  
  // 无权限的部门（仅显示，不可选择）
  const unauthorizedDepts = allDepartments.value.filter(dept => 
    !userDepartments.value.some(userDept => userDept.id === dept.id)
  ).map(dept => ({ ...dept, hasPermission: false }))
  
  if (unauthorizedDepts.length > 0) {
    groups.push({
      label: '无权限的部门',
      options: unauthorizedDepts
    })
  }
  
  return groups
})

const handleDepartmentChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}

onMounted(async () => {
  // 获取所有部门列表
  try {
    const response = await service.base.sys.department.list()
    allDepartments.value = response.data || []
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
})
</script>

<style scoped>
.permission-badge {
  color: #f56c6c;
  font-size: 12px;
  margin-left: 8px;
}
</style>
```

## 📈 性能优化策略

### 1. 缓存策略

#### 1.1 权限缓存优化

```java
@Service
@Slf4j
public class TaskPermissionCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String PERMISSION_CACHE_PREFIX = "task:permission:";
    private static final long CACHE_EXPIRE_SECONDS = 1800; // 30分钟
    
    /**
     * 缓存用户任务权限
     */
    public void cacheUserTaskPermission(Long userId, String taskType, 
            Long taskId, String operation, boolean hasPermission) {
        String key = buildPermissionCacheKey(userId, taskType, taskId, operation);
        redisTemplate.opsForValue().set(key, hasPermission, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }
    
    /**
     * 获取缓存的任务权限
     */
    public Boolean getCachedTaskPermission(Long userId, String taskType, 
            Long taskId, String operation) {
        String key = buildPermissionCacheKey(userId, taskType, taskId, operation);
        return (Boolean) redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 清除用户的所有任务权限缓存
     */
    public void clearUserTaskPermissionCache(Long userId) {
        String pattern = PERMISSION_CACHE_PREFIX + userId + ":*";
        Set<String> keys = redisTemplate.keys(pattern);
        if (!keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }
    
    private String buildPermissionCacheKey(Long userId, String taskType, 
            Long taskId, String operation) {
        return PERMISSION_CACHE_PREFIX + userId + ":" + taskType + ":" + taskId + ":" + operation;
    }
}
```

#### 1.2 部门数据缓存

```java
@Service
@Slf4j
public class DepartmentCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String DEPT_CACHE_PREFIX = "department:";
    private static final long CACHE_EXPIRE_SECONDS = 3600; // 1小时
    
    /**
     * 缓存部门信息
     */
    public void cacheDepartmentInfo(Long departmentId, BaseSysDepartmentEntity department) {
        String key = DEPT_CACHE_PREFIX + "info:" + departmentId;
        redisTemplate.opsForValue().set(key, department, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }
    
    /**
     * 批量缓存部门名称映射
     */
    public void cacheDepartmentNameMap(Map<Long, String> departmentNameMap) {
        String key = DEPT_CACHE_PREFIX + "name_map";
        redisTemplate.opsForValue().set(key, departmentNameMap, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }
    
    /**
     * 获取缓存的部门名称映射
     */
    @SuppressWarnings("unchecked")
    public Map<Long, String> getCachedDepartmentNameMap() {
        String key = DEPT_CACHE_PREFIX + "name_map";
        return (Map<Long, String>) redisTemplate.opsForValue().get(key);
    }
}
```

### 2. 数据库优化

#### 2.1 索引优化建议

```sql
-- 任务包表索引优化
CREATE INDEX idx_task_package_dept_status ON task_package(department_id, package_status);
CREATE INDEX idx_task_package_creator_time ON task_package(creator_department_id, create_time);

-- 任务信息表索引优化  
CREATE INDEX idx_task_info_dept_status ON task_info(department_id, status);
CREATE INDEX idx_task_info_package_dept ON task_info(package_id, department_id);

-- 任务执行表索引优化
CREATE INDEX idx_task_execution_dept_assignee ON task_execution(department_id, assignee_id);
CREATE INDEX idx_task_execution_status_dept ON task_execution(status, assignee_department_id);

-- 权限日志表索引优化
CREATE INDEX idx_permission_log_user_time ON task_permission_log(user_id, operation_time);
CREATE INDEX idx_permission_log_task_type ON task_permission_log(task_type, task_id, operation_time);
```

#### 2.2 查询优化

```java
@Repository
public class TaskPackageMapper extends BaseMapper<TaskPackageEntity> {
    
    /**
     * 优化的部门权限查询
     */
    @Select("""
        SELECT tp.*, d.name as department_name, cd.name as creator_department_name
        FROM task_package tp
        LEFT JOIN base_sys_department d ON tp.department_id = d.id
        LEFT JOIN base_sys_department cd ON tp.creator_department_id = cd.id
        WHERE tp.department_id IN 
        <foreach collection="departmentIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
        AND tp.delete_flag = 0
        ORDER BY tp.create_time DESC
        LIMIT #{offset}, #{limit}
    """)
    List<TaskPackageEntity> selectByDepartmentIds(
        @Param("departmentIds") List<Long> departmentIds,
        @Param("offset") int offset,
        @Param("limit") int limit);
    
    /**
     * 批量查询任务包的部门权限验证
     */
    @Select("""
        SELECT id, department_id 
        FROM task_package 
        WHERE id IN 
        <foreach collection="packageIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND delete_flag = 0
    """)
    List<Map<String, Object>> selectDepartmentIdsByIds(@Param("packageIds") List<Long> packageIds);
}
```

## 🔍 测试策略

### 1. 单元测试

#### 1.1 权限验证服务测试

```java
@SpringBootTest
@TestMethodOrder(OrderAnnotation.class)
class TaskDepartmentPermissionServiceTest {
    
    @Autowired
    private TaskDepartmentPermissionService permissionService;
    
    @MockBean
    private BaseSysPermsService baseSysPermsService;
    
    @Test
    @Order(1)
    void testHasTaskPackagePermission_Success() {
        // Given
        Long userId = 1L;
        Long taskPackageId = 100L;
        Long[] userDepartments = {1L, 2L, 3L};
        
        when(baseSysPermsService.loginDepartmentIds()).thenReturn(userDepartments);
        
        TaskPackageEntity taskPackage = new TaskPackageEntity();
        taskPackage.setId(taskPackageId);
        taskPackage.setDepartmentId(2L);
        
        when(taskPackageService.getById(taskPackageId)).thenReturn(taskPackage);
        
        // When
        boolean hasPermission = permissionService.hasTaskPackagePermission(
            userId, taskPackageId, "VIEW");
        
        // Then
        assertTrue(hasPermission);
    }
    
    @Test
    @Order(2)
    void testHasTaskPackagePermission_NoPermission() {
        // Given
        Long userId = 1L;
        Long taskPackageId = 100L;
        Long[] userDepartments = {1L, 2L, 3L};
        
        when(baseSysPermsService.loginDepartmentIds()).thenReturn(userDepartments);
        
        TaskPackageEntity taskPackage = new TaskPackageEntity();
        taskPackage.setId(taskPackageId);
        taskPackage.setDepartmentId(5L); // 不在用户权限范围内
        
        when(taskPackageService.getById(taskPackageId)).thenReturn(taskPackage);
        
        // When
        boolean hasPermission = permissionService.hasTaskPackagePermission(
            userId, taskPackageId, "VIEW");
        
        // Then
        assertFalse(hasPermission);
    }
    
    @Test
    @Order(3)
    void testApplyTaskPackageDepartmentFilter() {
        // Given
        Long userId = 1L;
        Long[] userDepartments = {1L, 2L, 3L};
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        when(baseSysPermsService.loginDepartmentIds()).thenReturn(userDepartments);
        
        // When
        permissionService.applyTaskPackageDepartmentFilter(queryWrapper, userId);
        
        // Then
        String sql = queryWrapper.toSQL();
        assertTrue(sql.contains("department_id IN"));
        assertTrue(sql.contains("1,2,3"));
    }
}
```

### 2. 集成测试

#### 2.1 API权限集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestMethodOrder(OrderAnnotation.class)
class TaskPermissionIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private TaskPackageService taskPackageService;
    
    private String adminToken;
    private String userToken;
    
    @BeforeEach
    void setUp() {
        // 获取管理员token
        adminToken = getAuthToken("admin", "123456");
        // 获取普通用户token
        userToken = getAuthToken("user1", "123456");
    }
    
    @Test
    @Order(1)
    void testTaskPackageList_AdminUser() {
        // Given
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        // When
        ResponseEntity<String> response = restTemplate.exchange(
            "/admin/task/package/page",
            HttpMethod.POST,
            entity,
            String.class
        );
        
        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        // 验证管理员可以看到所有任务包
    }
    
    @Test
    @Order(2)
    void testTaskPackageList_NormalUser() {
        // Given
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(userToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        // When
        ResponseEntity<String> response = restTemplate.exchange(
            "/admin/task/package/page",
            HttpMethod.POST,
            entity,
            String.class
        );
        
        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        // 验证普通用户只能看到有权限的任务包
    }
    
    @Test
    @Order(3)
    void testTaskPackageAccess_NoPermission() {
        // Given
        Long restrictedPackageId = createRestrictedTaskPackage();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(userToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        // When
        ResponseEntity<String> response = restTemplate.exchange(
            "/admin/task/package/info?id=" + restrictedPackageId,
            HttpMethod.GET,
            entity,
            String.class
        );
        
        // Then
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
    }
    
    private String getAuthToken(String username, String password) {
        // 模拟登录获取token
        Map<String, String> loginRequest = new HashMap<>();
        loginRequest.put("username", username);
        loginRequest.put("password", password);
        
        ResponseEntity<Map> response = restTemplate.postForEntity(
            "/admin/base/open/login",
            loginRequest,
            Map.class
        );
        
        Map<String, Object> data = (Map<String, Object>) response.getBody().get("data");
        return (String) data.get("token");
    }
    
    private Long createRestrictedTaskPackage() {
        // 创建一个受限部门的任务包用于测试
        TaskPackageEntity restrictedPackage = new TaskPackageEntity();
        restrictedPackage.setPackageName("受限任务包");
        restrictedPackage.setDepartmentId(999L); // 用户无权限的部门
        taskPackageService.save(restrictedPackage);
        return restrictedPackage.getId();
    }
}
```

## 📚 部署与运维

### 1. 数据迁移脚本

```sql
-- 数据迁移脚本：为现有任务数据添加部门信息

-- 1. 为现有任务包设置部门信息
UPDATE task_package tp
SET department_id = (
    SELECT u.department_id 
    FROM base_sys_user u 
    WHERE u.id = tp.creator_id
),
creator_department_id = (
    SELECT u.department_id 
    FROM base_sys_user u 
    WHERE u.id = tp.creator_id
)
WHERE tp.department_id IS NULL;

-- 2. 为现有任务信息设置部门信息
UPDATE task_info ti
SET department_id = (
    SELECT tp.department_id 
    FROM task_package tp 
    WHERE tp.id = ti.package_id
),
creator_department_id = (
    SELECT tp.creator_department_id 
    FROM task_package tp 
    WHERE tp.id = ti.package_id
)
WHERE ti.department_id IS NULL;

-- 3. 为现有任务执行设置部门信息
UPDATE task_execution te
SET department_id = (
    SELECT ti.department_id 
    FROM task_info ti 
    WHERE ti.id = te.task_id
),
assignee_department_id = (
    SELECT u.department_id 
    FROM base_sys_user u 
    WHERE u.id = te.assignee_id
)
WHERE te.department_id IS NULL;

-- 4. 处理孤儿数据（没有关联用户或部门的数据）
UPDATE task_package 
SET department_id = 1, creator_department_id = 1  -- 默认部门
WHERE department_id IS NULL;

UPDATE task_info 
SET department_id = 1, creator_department_id = 1
WHERE department_id IS NULL;

UPDATE task_execution 
SET department_id = 1, assignee_department_id = 1
WHERE department_id IS NULL;
```

### 2. 监控指标

```java
@Component
@Slf4j
public class TaskPermissionMetrics {
    
    private final Counter permissionCheckCounter;
    private final Counter permissionDeniedCounter;
    private final Timer permissionCheckTimer;
    private final Gauge activeDepartmentCount;
    
    public TaskPermissionMetrics(MeterRegistry meterRegistry) {
        this.permissionCheckCounter = Counter.builder("task.permission.check.total")
            .description("任务权限检查总数")
            .register(meterRegistry);
            
        this.permissionDeniedCounter = Counter.builder("task.permission.denied.total")
            .description("任务权限拒绝总数")
            .register(meterRegistry);
            
        this.permissionCheckTimer = Timer.builder("task.permission.check.duration")
            .description("任务权限检查耗时")
            .register(meterRegistry);
            
        this.activeDepartmentCount = Gauge.builder("task.department.active.count")
            .description("活跃部门数量")
            .register(meterRegistry, this, TaskPermissionMetrics::getActiveDepartmentCount);
    }
    
    public void recordPermissionCheck(String operation, boolean granted, long duration) {
        permissionCheckCounter.increment(
            Tags.of("operation", operation, "result", granted ? "granted" : "denied"));
            
        if (!granted) {
            permissionDeniedCounter.increment(Tags.of("operation", operation));
        }
        
        permissionCheckTimer.record(duration, TimeUnit.MILLISECONDS,
            Tags.of("operation", operation));
    }
    
    private double getActiveDepartmentCount() {
        // 计算活跃部门数量的逻辑
        return departmentService.getActiveDepartmentCount();
    }
}
```

### 3. 性能监控

```yaml
# application.yml 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      application: cool-admin
      module: task-permission
    export:
      prometheus:
        enabled: true

# 自定义监控指标
cool:
  metrics:
    task-permission:
      enabled: true
      slow-query-threshold: 1000  # 慢查询阈值(ms)
      cache-hit-rate-threshold: 90  # 缓存命中率阈值(%)
```

### 5. 前端路由和页面结构

#### 5.1 路由配置更新

```javascript
// router/modules/task.js
export default {
  name: "task",
  path: "/task",
  component: () => import("/@/layout/index.vue"),
  children: [
    {
      path: "package",
      component: () => import("/@/modules/task/views/package/index.vue"),
      meta: {
        title: "任务包管理",
        requireDepartmentPermission: true
      },
      children: [
        {
          path: "",
          component: () => import("/@/modules/task/views/package/list.vue"),
          meta: { title: "任务包列表" }
        },
        {
          path: "create",
          component: () => import("/@/modules/task/views/package/create.vue"),
          meta: { title: "创建任务包" }
        },
        {
          path: ":id",
          component: () => import("/@/modules/task/views/package/detail.vue"),
          meta: { title: "任务包详情" }
        }
      ]
    },
    {
      path: "ai-generator",
      component: () => import("/@/modules/task/views/ai/generator.vue"),
      meta: {
        title: "AI任务生成器",
        requireDepartmentPermission: true
      }
    }
  ]
}
```

#### 5.2 页面组件结构

```
src/modules/task/
├── views/
│   ├── package/
│   │   ├── index.vue          # 任务包主页
│   │   ├── list.vue           # 任务包列表页
│   │   ├── create.vue         # 创建任务包页
│   │   └── detail.vue         # 任务包详情页
│   ├── info/
│   │   ├── index.vue          # 任务信息主页
│   │   └── list.vue           # 任务信息列表页
│   ├── execution/
│   │   ├── index.vue          # 任务执行主页
│   │   └── list.vue           # 任务执行列表页
│   └── ai/
│       └── generator.vue      # AI任务生成器页
├── components/
│   ├── DepartmentTag.vue      # 部门标签组件
│   ├── DepartmentFilter.vue   # 部门筛选器组件
│   ├── PermissionIndicator.vue # 权限状态指示器
│   ├── TaskCard.vue           # 任务卡片组件
│   ├── TaskOperations.vue     # 任务操作按钮组件
│   ├── AIDepartmentSelector.vue # AI部门选择器
│   └── TaskStatistics.vue     # 任务统计组件
├── hooks/
│   ├── usePermission.js       # 权限Hook
│   └── useTaskManagement.js   # 任务管理Hook
├── store/
│   ├── permission.js          # 权限状态管理
│   └── task.js               # 任务状态管理
└── utils/
    ├── taskUtils.js          # 任务工具函数
    └── permissionUtils.js    # 权限工具函数
```

## 📋 总结

### 前端技术实现要点

1. **AI任务生成器优化**: 
   - 在高级选项中新增部门选择器，支持权限范围内的部门选择
   - 提供部门权限范围可视化提示，让用户了解生成任务的影响范围
   - 支持常用部门记忆和推荐，提升用户体验

2. **任务列表交互优化**:
   - 新增部门信息列，清晰展示任务所属部门
   - 实现部门筛选器，支持多部门选择和快捷筛选
   - 添加部门任务统计，显示每个部门的任务数量
   - 支持点击部门标签快速筛选

3. **任务卡片可视化改进**:
   - 卡片头部显示部门标签和权限状态
   - 跨部门协作任务特殊标识
   - 创建者部门信息展示
   - 权限感知的操作按钮

4. **权限状态可视化**:
   - 统一的权限状态图标设计
   - 部门标签颜色编码系统
   - 友好的权限提示信息
   - 响应式设计适配

### 技术架构亮点

1. **状态管理优化**: 
   - 权限数据集中管理和缓存
   - 部门信息的高效存储和查询
   - 任务状态的响应式更新

2. **性能优化策略**:
   - 权限检查结果缓存（5分钟有效期）
   - 批量权限验证减少API调用
   - 部门任务统计数据预加载

3. **用户体验提升**:
   - 智能部门推荐（当前部门+常用部门）
   - 权限范围实时提示
   - 操作权限的预检查和友好提示

4. **组件化设计**:
   - 可复用的部门相关组件
   - 模块化的权限检查逻辑
   - 统一的工具函数库

### 后端技术实现要点

1. **权限继承机制**: 基于现有RBAC体系，复用`relevance`配置实现上下级部门权限继承
2. **性能优化**: 通过Redis缓存、数据库索引优化、批量查询等手段保证性能
3. **渐进式集成**: 不破坏现有功能，支持平滑升级和数据迁移
4. **完整审计**: 记录所有权限相关操作，支持安全审计和问题追踪
5. **API设计**: RESTful API设计，支持批量操作和权限预检查

### 风险控制措施

1. **数据迁移风险**: 
   - 提供完整的迁移脚本和回滚方案
   - 支持灰度发布和数据验证

2. **性能风险**: 
   - 通过缓存和索引优化，监控响应时间
   - 前端权限缓存减少服务器压力

3. **兼容性风险**: 
   - 保持API向后兼容，渐进式启用权限控制
   - 前端组件向下兼容，支持渐进式升级

4. **安全风险**: 
   - 所有操作都经过权限验证，记录完整审计日志
   - 前后端双重权限校验，防止绕过攻击

5. **用户体验风险**:
   - 提供充分的权限提示和帮助信息
   - 支持权限申请和协作流程
   - 响应式设计保证各端体验一致

### 实施建议

1. **分阶段实施**:
   - 第一阶段：后端权限集成 + 基础前端展示
   - 第二阶段：AI生成器部门选择 + 任务列表优化
   - 第三阶段：任务卡片优化 + 高级权限功能

2. **测试策略**:
   - 权限边界测试确保安全性
   - 性能测试验证缓存效果
   - 用户体验测试优化交互流程

3. **监控指标**:
   - 权限检查响应时间 < 100ms
   - 缓存命中率 > 95%
   - 用户满意度评分 > 4.5/5

该技术设计确保了任务系统与部门权限的深度集成，通过完善的前端交互设计和优化的技术架构，在提供强大权限控制能力的同时，保持了优秀的用户体验和系统性能。 