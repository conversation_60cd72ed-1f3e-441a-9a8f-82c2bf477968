# 任务状态流转技术设计

## 1. 概述

本文档旨在为 Cool Admin 项目中的任务模块设计一个健壮、清晰的状态流转机制。该机制将严格定义任务从创建到完成或关闭的整个生命周期，确保状态转换的合规性、可追溯性，并集成相应的权限校验。

## 2. 任务状态定义

系统定义了以下五种核心任务状态：

| 状态值 | 状态名称 | 枚举常量 (建议) | 描述 |
| :--- | :--- | :--- | :--- |
| 0 | 待分配 | `TO_BE_ASSIGNED` | 任务已创建，但尚未指定执行人。 |
| 1 | 待执行 | `TO_BE_EXECUTED` | 任务已分配给执行人，等待开始。 |
| 2 | 执行中 | `IN_PROGRESS` | 执行人已开始处理任务。 |
| 3 | 已完成 | `COMPLETED` | 任务的所有执行要求均已满足。 |
| 4 | 已关闭 | `CLOSED` | 任务因故被手动终止，非正常完成。 |

已在com.cool.modules.task.enums.TaskBusinessStatusEnum类中定义
## 3. 状态流转图

```mermaid
stateDiagram-v2
    [*] --> TO_BE_ASSIGNED: 创建任务

    TO_BE_ASSIGNED --> TO_BE_EXECUTED: 分配执行人
    TO_BE_ASSIGNED --> CLOSED: 关闭任务

    TO_BE_EXECUTED --> IN_PROGRESS: 开始执行
    TO_BE_EXECUTED --> CLOSED: 关闭任务

    IN_PROGRESS --> COMPLETED: 完成任务
    IN_PROGRESS --> CLOSED: 关闭任务

    COMPLETED --> IN_PROGRESS: 重新激活

    CLOSED --> TO_BE_EXECUTED: 重新打开
```

## 4. 状态转换规则与权限设计

每一条状态转换路径都由一个特定的 **事件 (Event)** 触发，并伴随着严格的 **条件 (Guard)** 校验。

| 当前状态 | 触发事件 | 目标状态 | 条件 (Guard) / 权限校验 |
| :--- | :--- | :--- | :--- |
| **待分配 (0)** | `ASSIGN` | **待执行 (1)** | - 必须提供至少一个有效的执行人。<br>- 操作者需具备项目管理权限。 |
| **待分配 (0)** | `CLOSE` | **已关闭 (4)** | - 必须提供关闭原因。<br>- 操作者需具备项目管理权限。 |
| **待执行 (1)** | `START` | **执行中 (2)** | - **操作者必须是任务的指定执行人之一。**<br>- 任务当前状态必须是“待执行”。 |
| **待执行 (1)** | `CLOSE` | **已关闭 (4)** | - 必须提供关闭原因。<br>- 操作者需具备项目管理权限或为任务执行人。 |
| **执行中 (2)** | `COMPLETE` | **已完成 (3)** | - **操作者必须是任务的指定执行人之一。**<br>- 任务当前状态必须是“执行中”。<br>- 如有要求，必须上传附件或照片。 |
| **执行中 (2)** | `CLOSE` | **已关闭 (4)** | - 必须提供关闭原因。<br>- 操作者需具备项目管理权限。 |
| **已完成 (3)** | `REACTIVATE` | **执行中 (2)** | - 用于补充信息或修正结果。<br>- 操作者需具备项目管理权限。 |
| **已关闭 (4)** | `REOPEN` | **待执行 (1)** | - 必须提供重新打开的原因。<br>- 任务必须已有执行人。<br>- 操作者需具备项目管理权限。 |

## 5. 后端实现方案 (`TaskStatusServiceImpl`)

### 5.1. 核心设计思想 (修订版)

- **保留公共接口**：为了不影响现有 Controller 和前端调用，`TaskStatusService` 接口的公共方法签名（如 `completeTaskExecution`, `closeTask` 等）保持不变。
- **内部状态机**：在 `TaskStatusServiceImpl` 内部实现一个私有的、核心的状态流转处理方法，例如 `private TaskInfoEntity changeStatus(Long taskId, Integer targetStatus, Long operatorId, StatusChangeContext context)`。
- **规则驱动**：使用 `Map` 或 `EnumMap` 定义状态转换规则，`key` 为源状态，`value` 为允许转换的目标状态列表。
- **逻辑委托**：现有的公共方法将委托核心的 `changeStatus` 方法来执行状态转换和校验，然后继续处理它们各自的特定逻辑（如更新执行记录、停止调度器等）。
- **保留副作用**：在状态变更成功后，必须保留原有的副作用逻辑，例如调用 `taskPackageService.updatePackageStats()` 来更新关联任务包的统计信息。
- **原子操作**：所有操作（状态校验、状态更新、更新执行记录、写历史日志、更新统计）都必须在同一个数据库事务中完成。

### 5.2. 伪代码实现 (修订版)

```java
@Service
public class TaskStatusServiceImpl implements TaskStatusService {

    // 状态转换规则定义
    private static final Map<Integer, Set<Integer>> ALLOWED_TRANSITIONS = new HashMap<>();

    static {
        ALLOWED_TRANSITIONS.put(0, Set.of(1, 4)); // 待分配 -> 待执行, 已关闭
        ALLOWED_TRANSITIONS.put(1, Set.of(2, 4)); // 待执行 -> 执行中, 已关闭
        ALLOWED_TRANSITIONS.put(2, Set.of(3, 4)); // 执行中 -> 已完成, 已关闭
        ALLOWED_TRANSITIONS.put(3, Set.of(2));    // 已完成 -> 执行中
        ALLOWED_TRANSITIONS.put(4, Set.of(1));    // 已关闭 -> 待执行
    }

    // 公共接口实现 (保持不变)
    @Override
    @Transactional
    public Boolean completeTaskExecution(TaskCompletionRequest request) {
        // ... 原有的获取 TaskExecutionEntity 逻辑 ...

        // 调用核心状态机进行校验和状态变更
        StatusChangeContext context = new StatusChangeContext(request.getCompletionNote());
        TaskInfoEntity task = changeStatus(request.getTaskId(), TaskBusinessStatusEnum.COMPLETED.getCode(), request.getAssigneeId(), context);

        // ... 原有的更新 TaskExecutionEntity (附件、备注等) 和写操作日志的逻辑 ...

        // 检查是否所有执行人都已完成，如果是，则更新任务主状态
        if (areAllExecutionsCompleted(request.getTaskId())) {
            // ... 更新任务主状态为已完成 ...
            // 保留副作用：更新任务包统计
            if (task.getPackageId() != null) {
                taskPackageService.updatePackageStats(task.getPackageId());
            }
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean closeTask(TaskCloseRequest request) {
        // 调用核心状态机
        StatusChangeContext context = new StatusChangeContext(request.getCloseReason());
        TaskInfoEntity task = changeStatus(request.getTaskId(), TaskBusinessStatusEnum.CLOSED.getCode(), request.getOperatorId(), context);

        // 更新任务实体
        task.setCloseReason(request.getCloseReason());
        task.setClosedBy(request.getOperatorName());
        task.setCloseTime(new Date());
        task.setScheduleStatus(0); // 停止调度
        taskInfoService.updateById(task);

        // ... 停止 Quartz 调度器 ...

        // 保留副作用：更新任务包统计
        if (task.getPackageId() != null) {
            taskPackageService.updatePackageStats(task.getPackageId());
        }
        return true;
    }
    
    // ... 其他公共方法的实现类似 ...

    /**
     * 核心状态转换方法 (私有)
     */
    @Transactional
    private TaskInfoEntity changeStatus(Long taskId, Integer targetStatus, Long operatorId, StatusChangeContext context) {
        TaskInfoEntity task = taskInfoService.getById(taskId);
        CoolPreconditions.checkNotNull(task, "任务不存在");

        Integer sourceStatus = task.getTaskStatus();

        // 1. 校验是否允许状态转换
        if (!ALLOWED_TRANSITIONS.getOrDefault(sourceStatus, Set.of()).contains(targetStatus)) {
            throw new CoolException("无效的状态转换：从 " + sourceStatus + " 到 " + targetStatus);
        }

        // 2. 根据具体的转换路径执行权限和业务校验
        validateTransition(task, targetStatus, operatorId, context);

        // 3. 更新任务状态 (如果适用)
        // 注意：某些操作（如completeTaskExecution）可能只更新执行记录，而不是主任务状态
        // 主任务状态的更新逻辑应放在公共方法中
        
        // 4. 记录状态变更历史
        taskHistoryService.recordStatusChange(taskId, sourceStatus, targetStatus, operatorId, context.getReason());
        
        return task;
    }

    /**
     * 统一的转换校验方法 (私有)
     */
    private void validateTransition(TaskInfoEntity task, Integer targetStatus, Long operatorId, StatusChangeContext context) {
        Integer sourceStatus = task.getTaskStatus();
        
        // 示例：校验 "待执行" -> "执行中"
        if (sourceStatus == 1 && targetStatus == 2) {
            boolean isAssignee = taskExecutionService.isUserAssignee(task.getId(), operatorId);
            CoolPreconditions.check(isAssignee, "只有任务执行人才能开始任务");
        }
        
        // ... 实现所有状态转换的校验逻辑 ...
    }
}
```

### 5.3. Service 接口调整

- **接口保持不变**：`TaskStatusService` 接口定义无需修改。
- **新增内部方法**：在 `TaskStatusServiceImpl` 中新增 `startTask` 等内部需要的业务方法。
- **保留副作用**：确保在状态变更后，所有关联操作（如更新任务包统计 `updatePackageStats`）都得到正确执行。

## 6. 前端实现方案 (`project/task.vue`)

### 6.1. 核心设计思想

- **动态操作**：根据当前任务的状态，动态地在右键菜单和操作按钮中展示允许的操作。
- **权限分离**：UI 层只负责展示，所有权限判断逻辑由后端 `canXXX` 接口（如 `canStartTask`, `canCompleteTask`）提供。
- **统一入口**：所有状态变更操作都调用 `handleStatusChange(taskId, targetStatus)` 方法，该方法内部再根据目标状态弹出相应的对话框（如关闭、完成、重新打开需要填写原因）。

### 6.2. 伪代码实现

```typescript
// project/task.vue

// 根据任务状态决定可用的操作列表
const getAvailableActions = (task) => {
    const actions = [];
    switch (task.taskStatus) {
        case 0: // 待分配
            actions.push({ label: '分配', command: 'assign' });
            actions.push({ label: '关闭', command: 'close' });
            break;
        case 1: // 待执行
            actions.push({ label: '开始执行', command: 'start' });
            actions.push({ label: '关闭', command: 'close' });
            break;
        // ... 其他 case
    }
    return actions;
}

// 统一的状态变更处理函数
const handleStatusChange = async (task, targetStatus) => {
    if (targetStatus === 4) { // 关闭
        currentCloseTask.value = task;
        showCloseDialog.value = true;
    } else if (targetStatus === 3) { // 完成
        currentCompleteTask.value = task;
        showCompleteDialog.value = true;
    } else {
        // 对于不需要额外信息的状态变更，直接调用后端接口
        try {
            await service.request({
                url: '/admin/task/status/change', // 假设后端提供一个统一的状态变更接口
                method: 'POST',
                data: { taskId: task.id, targetStatus }
            });
            ElMessage.success('状态更新成功');
            refreshData(); // 刷新列表或看板
        } catch (error) {
            // ... 错误处理
        }
    }
}
```

## 7. 总结

通过以上设计，我们可以构建一个清晰、可控、可扩展的任务状态管理系统。后端通过状态机模型保证了业务规则的严格执行，前端则根据后端状态提供动态、友好的用户交互。
