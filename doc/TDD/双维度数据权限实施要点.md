# 双维度数据权限实施要点

## 📋 核心问题解决

您提出的关键问题：**所有业务数据都需要按照部门或项目的权限来过滤数据**

这确实是双维度组织架构的核心挑战，我们的解决方案如下：

## 🎯 解决方案概述

### 1. 数据归属双维度标识
```java
// 每个业务实体都增加双维度字段
@ColumnDefine(comment = "所属部门ID", type = "bigint")
private Long departmentId;

@ColumnDefine(comment = "关联项目ID", type = "bigint") 
private Long projectId;
```

### 2. 统一权限过滤服务
```java
public interface DualDimensionDataPermissionService {
    /**
     * 根据用户当前组织形态应用数据权限过滤
     */
    void applyDataPermissionFilter(QueryWrapper queryWrapper, Long userId, String entityType);
}
```

### 3. 权限过滤逻辑
```java
// 部门形态：过滤部门权限
if (OrganizationModeEnum.DEPARTMENT.equals(currentMode)) {
    queryWrapper.in("department_id", userDepartmentIds);
}

// 项目形态：过滤项目权限  
if (OrganizationModeEnum.PROJECT.equals(currentMode)) {
    queryWrapper.in("project_id", userProjectIds);
}
```

## 🔧 具体实施方案

### 1. 业务实体扩展

#### 1.1 任务包 (TaskPackageEntity)
```java
// 现有字段
private Long departmentId;           // 所属部门ID
private Long creatorDepartmentId;    // 创建者部门ID

// 新增字段
private Long projectId;              // 关联项目ID
private String projectName;          // 项目名称(查询填充)
```

#### 1.2 任务信息 (TaskInfoEntity)
```java
// 现有字段
private Long departmentId;           // 所属部门ID
private Long creatorDepartmentId;    // 创建者部门ID

// 新增字段  
private Long projectId;              // 关联项目ID
private String projectName;          // 项目名称(查询填充)
```

#### 1.3 任务执行 (TaskExecutionEntity)
```java
// 现有字段
private Long departmentId;           // 执行部门ID
private Long assigneeDepartmentId;   // 执行人部门ID

// 新增字段
private Long projectId;              // 关联项目ID
private String projectName;          // 项目名称(查询填充)
```

#### 1.4 工单 (WorkOrderEntity)
```java
// 现有字段
private String applicantDept;        // 申请部门(字符串)

// 新增字段
private Long applicantDeptId;        // 申请部门ID(规范化)
private Long projectId;              // 关联项目ID
private String projectName;          // 项目名称(查询填充)
```

### 2. 权限过滤集成

#### 2.1 Service层集成
```java
@Service
public class TaskPackageServiceImpl implements TaskPackageService {
    
    @Autowired
    private DualDimensionDataPermissionService permissionService;
    
    @Override
    public Object page(JSONObject requestParams, Page<TaskPackageEntity> page, QueryWrapper qw) {
        // 应用双维度权限过滤
        Long userId = CoolSecurityUtil.getAdminUserId();
        permissionService.applyDataPermissionFilter(qw, userId, "TaskPackage");
        
        // 执行查询
        return mapper.paginate(page, qw);
    }
}
```

#### 2.2 Controller层集成
```java
@CoolRestController
public class AdminTaskPackageController extends BaseController<TaskPackageService, TaskPackageEntity> {

    @Autowired
    private DualDimensionDataPermissionService permissionService;

    @Override
    protected void init(QueryWrapper queryWrapper, Map<String, Object> params) {
        // 自动应用权限过滤
        Long userId = CoolSecurityUtil.getAdminUserId();
        permissionService.applyDataPermissionFilter(queryWrapper, userId, "TaskPackage");
        
        // 其他查询条件...
    }
}
```

### 3. 数据创建时的维度分配

#### 3.1 自动维度分配
```java
@Override
public boolean save(TaskPackageEntity entity) {
    // 根据当前组织形态自动设置归属维度
    setEntityOrganizationInfo(entity);
    return super.save(entity);
}

private void setEntityOrganizationInfo(TaskPackageEntity entity) {
    Long userId = CoolSecurityUtil.getAdminUserId();
    String currentMode = organizationModeService.getCurrentMode(userId);
    
    if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
        // 部门形态：设置部门信息
        if (entity.getDepartmentId() == null) {
            BaseSysUserEntity user = baseSysUserService.getById(userId);
            entity.setDepartmentId(user.getDepartmentId());
        }
        entity.setProjectId(null); // 部门形态下项目ID为空
    } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
        // 项目形态：必须指定项目ID
        if (entity.getProjectId() == null) {
            throw new CoolException("项目形态下创建任务包必须指定项目ID");
        }
        entity.setDepartmentId(null); // 项目形态下部门ID为空
    }
}
```

## 🚀 关键实施步骤

### 第一步：数据库结构扩展
```sql
-- 为所有业务表添加项目维度字段
ALTER TABLE task_package ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE task_info ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE task_execution ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE sop_work_order ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE sop_work_order ADD COLUMN applicant_dept_id BIGINT COMMENT '申请部门ID';

-- 创建索引优化查询性能
CREATE INDEX idx_task_package_project_id ON task_package(project_id);
CREATE INDEX idx_task_info_project_id ON task_info(project_id);
CREATE INDEX idx_task_execution_project_id ON task_execution(project_id);
CREATE INDEX idx_work_order_project_id ON sop_work_order(project_id);
```

### 第二步：实体类扩展
```java
// 为所有业务实体添加项目维度字段
// TaskPackageEntity, TaskInfoEntity, TaskExecutionEntity, WorkOrderEntity
```

### 第三步：权限服务实现
```java
// 实现DualDimensionDataPermissionService
// 支持根据组织形态动态切换权限过滤逻辑
```

### 第四步：业务服务集成
```java
// 更新所有业务Service的查询方法
// 在每个查询方法中应用权限过滤
```

### 第五步：数据迁移
```java
// 为现有数据设置合适的维度归属
// 部门形态数据：departmentId有值，projectId为null
// 项目形态数据：projectId有值，departmentId为null
```

## ⚡ 性能优化策略

### 1. 缓存用户权限范围
```java
// 缓存用户可访问的部门ID和项目ID列表
@Cacheable("user:permission:scope")
public DataPermissionScope getUserPermissionScope(Long userId) {
    // 返回用户权限范围
}
```

### 2. 批量权限检查
```java
// 避免N+1查询问题
public Map<Long, Boolean> batchCheckDataAccess(Long userId, String entityType, List<Long> entityIds) {
    // 批量检查权限
}
```

### 3. 数据库索引优化
```sql
-- 创建复合索引支持双维度查询
CREATE INDEX idx_task_package_dept_project ON task_package(department_id, project_id);
CREATE INDEX idx_task_info_dept_project ON task_info(department_id, project_id);
```

## 🔒 安全保障

### 1. 权限验证
```java
// 在所有数据操作前验证权限
if (!permissionService.hasDataAccess(userId, entityType, entityId)) {
    throw new CoolException("无权限访问此数据");
}
```

### 2. 操作审计
```java
// 记录所有权限相关操作
permissionMonitorService.logPermissionCheck(userId, operationType, entityType, entityId, accessGranted);
```

### 3. 异常处理
```java
// 权限不足时的友好提示
catch (DataPermissionException e) {
    return R.error("您没有权限访问此数据，请联系管理员");
}
```

## ✅ 预期效果

1. **数据隔离**：用户在部门形态下只能看到本部门的数据，在项目形态下只能看到参与项目的数据
2. **自动过滤**：所有查询自动应用权限过滤，无需手动处理
3. **性能优化**：通过缓存和索引优化，确保权限过滤不影响查询性能
4. **安全可控**：完整的权限验证和操作审计，确保数据安全

## 🎯 关键优势

1. **完全兼容**：基于现有的部门权限体系扩展，不破坏现有功能
2. **自动化**：通过AOP和统一服务，自动应用权限过滤
3. **高性能**：多层缓存和批量查询优化，确保系统性能
4. **可扩展**：支持未来更多业务实体和组织维度的扩展

这个方案完美解决了您提出的核心问题：**所有业务数据都按照部门或项目权限进行过滤**，确保用户在不同组织形态下只能看到对应维度的数据。
