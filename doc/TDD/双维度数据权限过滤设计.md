# 双维度数据权限过滤设计文档

## 📋 概述

基于Cool Admin现有的部门数据权限体系，扩展支持项目维度的数据权限过滤。所有业务数据（工单、任务包、任务、执行人）都需要根据用户当前的组织形态进行相应的权限过滤。

## 🏗️ 现有权限体系分析

### 1. 现有部门权限实现
```java
// 获取用户部门权限
Long[] permsDepartmentArr = coolCache.get("admin:department:" + userId, Long[].class);

// 应用部门权限过滤
qw.and(ENTITY.DEPARTMENT_ID.in(
    permsDepartmentArr == null || permsDepartmentArr.length == 0 
        ? new Long[]{null} : permsDepartmentArr,
    !CoolSecurityUtil.getAdminUsername().equals("admin")));
```

### 2. 业务数据现状
- **TaskPackageEntity**: 已有 `departmentId`, `creatorDepartmentId`
- **TaskInfoEntity**: 已有 `departmentId`, `creatorDepartmentId`  
- **TaskExecutionEntity**: 已有 `departmentId`, `assigneeDepartmentId`
- **WorkOrderEntity**: 已有 `applicantDept` (字符串类型)

## 🎯 双维度权限扩展方案

### 1. 数据模型扩展

#### 1.1 业务实体增加项目维度字段
```java
// TaskPackageEntity 扩展
@ColumnDefine(comment = "关联项目ID", type = "bigint")
private Long projectId;

@Column(ignore = true)
private String projectName;

// TaskInfoEntity 扩展  
@ColumnDefine(comment = "关联项目ID", type = "bigint")
private Long projectId;

@Column(ignore = true)
private String projectName;

// TaskExecutionEntity 扩展
@ColumnDefine(comment = "关联项目ID", type = "bigint") 
private Long projectId;

@Column(ignore = true)
private String projectName;

// WorkOrderEntity 扩展
@ColumnDefine(comment = "关联项目ID", type = "bigint")
private Long projectId;

@Column(ignore = true)
private String projectName;

@ColumnDefine(comment = "申请部门ID", type = "bigint")
private Long applicantDeptId; // 替换字符串类型的applicantDept
```

### 2. 统一数据权限服务

#### 2.1 双维度权限服务接口
```java
public interface DualDimensionDataPermissionService {
    
    /**
     * 获取用户可访问的数据ID列表
     */
    List<Long> getAccessibleDataIds(Long userId, String dataType);
    
    /**
     * 应用数据权限过滤条件
     */
    void applyDataPermissionFilter(QueryWrapper queryWrapper, Long userId, String entityType);
    
    /**
     * 检查用户是否有数据访问权限
     */
    boolean hasDataAccess(Long userId, String entityType, Long entityId);
    
    /**
     * 获取用户在当前组织形态下的权限范围
     */
    DataPermissionScope getUserPermissionScope(Long userId);
}
```

#### 2.2 权限范围数据结构
```java
@Data
public class DataPermissionScope {
    private String organizationMode; // DEPARTMENT, PROJECT
    private List<Long> departmentIds; // 可访问的部门ID列表
    private List<Long> projectIds;    // 可访问的项目ID列表
    private boolean isSystemAdmin;    // 是否系统管理员
    private boolean isUnlimited;      // 是否无限制权限
}
```

### 3. 双维度权限服务实现

#### 3.1 核心权限服务
```java
@Service
@RequiredArgsConstructor
@Slf4j
public class DualDimensionDataPermissionServiceImpl implements DualDimensionDataPermissionService {
    
    private final OrganizationModeService organizationModeService;
    private final UserOrganizationService userOrganizationService;
    private final BaseSysPermsService baseSysPermsService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public DataPermissionScope getUserPermissionScope(Long userId) {
        String cacheKey = "user:permission:scope:" + userId;
        DataPermissionScope cachedScope = (DataPermissionScope) redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedScope != null) {
            return cachedScope;
        }
        
        DataPermissionScope scope = new DataPermissionScope();
        
        // 检查是否系统管理员
        String currentUser = CoolSecurityUtil.getAdminUsername();
        scope.setSystemAdmin("admin".equals(currentUser));
        
        if (scope.isSystemAdmin()) {
            scope.setUnlimited(true);
            redisTemplate.opsForValue().set(cacheKey, scope, 10, TimeUnit.MINUTES);
            return scope;
        }
        
        // 获取当前组织形态
        String currentMode = organizationModeService.getCurrentMode(userId);
        scope.setOrganizationMode(currentMode);
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
            // 部门形态：获取部门权限
            Long[] departmentIds = baseSysPermsService.loginDepartmentIds();
            scope.setDepartmentIds(departmentIds != null ? Arrays.asList(departmentIds) : new ArrayList<>());
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
            // 项目形态：获取项目权限
            List<Long> projectIds = getAccessibleProjectIds(userId);
            scope.setProjectIds(projectIds);
        }
        
        // 缓存10分钟
        redisTemplate.opsForValue().set(cacheKey, scope, 10, TimeUnit.MINUTES);
        return scope;
    }
    
    @Override
    public void applyDataPermissionFilter(QueryWrapper queryWrapper, Long userId, String entityType) {
        DataPermissionScope scope = getUserPermissionScope(userId);
        
        // 系统管理员无需过滤
        if (scope.isUnlimited()) {
            return;
        }
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(scope.getOrganizationMode())) {
            applyDepartmentFilter(queryWrapper, scope.getDepartmentIds(), entityType);
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(scope.getOrganizationMode())) {
            applyProjectFilter(queryWrapper, scope.getProjectIds(), entityType);
        }
    }
    
    private void applyDepartmentFilter(QueryWrapper queryWrapper, List<Long> departmentIds, String entityType) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            // 无部门权限，返回空结果
            queryWrapper.and(qw -> qw.eq("1", "0"));
            return;
        }
        
        switch (entityType) {
            case "TaskPackage":
                queryWrapper.in("department_id", departmentIds);
                break;
            case "TaskInfo":
                queryWrapper.in("department_id", departmentIds);
                break;
            case "TaskExecution":
                queryWrapper.in("department_id", departmentIds);
                break;
            case "WorkOrder":
                queryWrapper.in("applicant_dept_id", departmentIds);
                break;
            default:
                log.warn("未知的实体类型: {}", entityType);
        }
    }
    
    private void applyProjectFilter(QueryWrapper queryWrapper, List<Long> projectIds, String entityType) {
        if (projectIds == null || projectIds.isEmpty()) {
            // 无项目权限，返回空结果
            queryWrapper.and(qw -> qw.eq("1", "0"));
            return;
        }
        
        switch (entityType) {
            case "TaskPackage":
                queryWrapper.in("project_id", projectIds);
                break;
            case "TaskInfo":
                queryWrapper.in("project_id", projectIds);
                break;
            case "TaskExecution":
                queryWrapper.in("project_id", projectIds);
                break;
            case "WorkOrder":
                queryWrapper.in("project_id", projectIds);
                break;
            default:
                log.warn("未知的实体类型: {}", entityType);
        }
    }
    
    private List<Long> getAccessibleProjectIds(Long userId) {
        List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
            userId, OrganizationModeEnum.PROJECT.getCode());
        
        return projectRoles.stream()
            .filter(role -> role.getStatus() == 1)
            .filter(role -> role.getExpireTime() == null || role.getExpireTime().after(new Date()))
            .map(UserOrganizationEntity::getOrganizationId)
            .distinct()
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean hasDataAccess(Long userId, String entityType, Long entityId) {
        DataPermissionScope scope = getUserPermissionScope(userId);
        
        if (scope.isUnlimited()) {
            return true;
        }
        
        // 根据实体类型和ID查询数据归属
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(scope.getOrganizationMode())) {
            Long departmentId = getEntityDepartmentId(entityType, entityId);
            return departmentId != null && scope.getDepartmentIds().contains(departmentId);
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(scope.getOrganizationMode())) {
            Long projectId = getEntityProjectId(entityType, entityId);
            return projectId != null && scope.getProjectIds().contains(projectId);
        }
        
        return false;
    }
    
    private Long getEntityDepartmentId(String entityType, Long entityId) {
        // 根据实体类型查询对应的部门ID
        switch (entityType) {
            case "TaskPackage":
                TaskPackageEntity taskPackage = taskPackageService.getById(entityId);
                return taskPackage != null ? taskPackage.getDepartmentId() : null;
            case "TaskInfo":
                TaskInfoEntity taskInfo = taskInfoService.getById(entityId);
                return taskInfo != null ? taskInfo.getDepartmentId() : null;
            case "TaskExecution":
                TaskExecutionEntity taskExecution = taskExecutionService.getById(entityId);
                return taskExecution != null ? taskExecution.getDepartmentId() : null;
            case "WorkOrder":
                WorkOrderEntity workOrder = workOrderService.getById(entityId);
                return workOrder != null ? workOrder.getApplicantDeptId() : null;
            default:
                return null;
        }
    }
    
    private Long getEntityProjectId(String entityType, Long entityId) {
        // 根据实体类型查询对应的项目ID
        switch (entityType) {
            case "TaskPackage":
                TaskPackageEntity taskPackage = taskPackageService.getById(entityId);
                return taskPackage != null ? taskPackage.getProjectId() : null;
            case "TaskInfo":
                TaskInfoEntity taskInfo = taskInfoService.getById(entityId);
                return taskInfo != null ? taskInfo.getProjectId() : null;
            case "TaskExecution":
                TaskExecutionEntity taskExecution = taskExecutionService.getById(entityId);
                return taskExecution != null ? taskExecution.getProjectId() : null;
            case "WorkOrder":
                WorkOrderEntity workOrder = workOrderService.getById(entityId);
                return workOrder != null ? workOrder.getProjectId() : null;
            default:
                return null;
        }
    }
}
```

### 4. AOP权限拦截器

#### 4.1 数据权限注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermissionFilter {
    String entityType(); // 实体类型
    boolean enable() default true; // 是否启用权限过滤
}
```

#### 4.2 权限拦截切面
```java
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class DataPermissionAspect {
    
    private final DualDimensionDataPermissionService permissionService;
    
    @Around("@annotation(dataPermissionFilter)")
    public Object applyDataPermissionFilter(ProceedingJoinPoint joinPoint, 
                                           DataPermissionFilter dataPermissionFilter) throws Throwable {
        
        if (!dataPermissionFilter.enable()) {
            return joinPoint.proceed();
        }
        
        Object[] args = joinPoint.getArgs();
        Long userId = CoolSecurityUtil.getAdminUserId();
        
        // 查找QueryWrapper参数
        for (Object arg : args) {
            if (arg instanceof QueryWrapper) {
                QueryWrapper queryWrapper = (QueryWrapper) arg;
                permissionService.applyDataPermissionFilter(queryWrapper, userId, dataPermissionFilter.entityType());
                break;
            }
        }
        
        return joinPoint.proceed();
    }
}
```

### 5. 业务服务集成

#### 5.1 任务包服务集成
```java
@Service
@RequiredArgsConstructor
public class TaskPackageServiceImpl extends ServiceImpl<TaskPackageMapper, TaskPackageEntity> 
        implements TaskPackageService {
    
    private final DualDimensionDataPermissionService permissionService;
    
    @Override
    @DataPermissionFilter(entityType = "TaskPackage")
    public Object page(JSONObject requestParams, Page<TaskPackageEntity> page, QueryWrapper qw) {
        // 权限过滤通过AOP自动应用
        
        // 其他查询条件
        qw.like("package_name", requestParams.get("packageName"))
          .eq("package_status", requestParams.get("packageStatus"))
          .orderBy("id", false);
        
        Page<TaskPackageEntity> result = mapper.paginate(page, qw);
        
        // 填充关联信息
        fillRelatedInfo(result.getRecords());
        
        return result;
    }
    
    @Override
    public boolean save(TaskPackageEntity entity) {
        // 根据当前组织形态设置归属信息
        setEntityOrganizationInfo(entity);
        return super.save(entity);
    }
    
    private void setEntityOrganizationInfo(TaskPackageEntity entity) {
        Long userId = CoolSecurityUtil.getAdminUserId();
        String currentMode = organizationModeService.getCurrentMode(userId);
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
            // 部门形态：设置部门信息
            if (entity.getDepartmentId() == null) {
                BaseSysUserEntity user = baseSysUserService.getById(userId);
                entity.setDepartmentId(user.getDepartmentId());
            }
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
            // 项目形态：需要指定项目ID
            if (entity.getProjectId() == null) {
                throw new CoolException("项目形态下创建任务包必须指定项目ID");
            }
        }
    }
}
```

#### 5.2 控制器层集成
```java
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
@Tag(name = "任务包管理")
public class AdminTaskPackageController extends BaseController<TaskPackageService, TaskPackageEntity> {

    @Autowired
    private DualDimensionDataPermissionService permissionService;

    @Override
    protected void init(QueryWrapper queryWrapper, Map<String, Object> params) {
        // 应用双维度权限过滤
        Long userId = CoolSecurityUtil.getAdminUserId();
        permissionService.applyDataPermissionFilter(queryWrapper, userId, "TaskPackage");
        
        // 其他查询条件
        queryWrapper.like("package_name", params.get("packageName"))
                   .eq("package_status", params.get("packageStatus"))
                   .orderBy("id", false);
    }
    
    @PostMapping("/create")
    @Operation(summary = "创建任务包")
    public R<TaskPackageEntity> createTaskPackage(@RequestBody TaskPackageEntity entity) {
        // 权限验证
        Long userId = CoolSecurityUtil.getAdminUserId();
        if (!permissionService.hasDataAccess(userId, "TaskPackage", entity.getId())) {
            throw new CoolException("无权限操作此任务包");
        }
        
        service.save(entity);
        return R.ok(entity);
    }
}
```

### 6. 数据迁移策略

#### 6.1 数据库表结构迁移
```sql
-- 为业务表添加项目维度字段
ALTER TABLE task_package ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE task_info ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE task_execution ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE sop_work_order ADD COLUMN project_id BIGINT COMMENT '关联项目ID';
ALTER TABLE sop_work_order ADD COLUMN applicant_dept_id BIGINT COMMENT '申请部门ID';

-- 创建索引优化查询性能
CREATE INDEX idx_task_package_project_id ON task_package(project_id);
CREATE INDEX idx_task_info_project_id ON task_info(project_id);
CREATE INDEX idx_task_execution_project_id ON task_execution(project_id);
CREATE INDEX idx_work_order_project_id ON sop_work_order(project_id);
CREATE INDEX idx_work_order_applicant_dept_id ON sop_work_order(applicant_dept_id);

-- 创建复合索引支持双维度查询
CREATE INDEX idx_task_package_dept_project ON task_package(department_id, project_id);
CREATE INDEX idx_task_info_dept_project ON task_info(department_id, project_id);
```

#### 6.2 现有数据迁移脚本
```java
@Component
@RequiredArgsConstructor
@Slf4j
public class DualDimensionDataMigration {

    private final TaskPackageService taskPackageService;
    private final WorkOrderService workOrderService;
    private final BaseSysDepartmentService departmentService;

    @PostConstruct
    public void migrateExistingData() {
        log.info("开始双维度数据迁移...");

        migrateWorkOrderDepartmentIds();
        initializeProjectDimension();

        log.info("双维度数据迁移完成");
    }

    private void migrateWorkOrderDepartmentIds() {
        // 将工单的申请部门字符串转换为部门ID
        List<WorkOrderEntity> workOrders = workOrderService.list();

        for (WorkOrderEntity workOrder : workOrders) {
            if (workOrder.getApplicantDept() != null && workOrder.getApplicantDeptId() == null) {
                BaseSysDepartmentEntity dept = departmentService.getByName(workOrder.getApplicantDept());
                if (dept != null) {
                    workOrder.setApplicantDeptId(dept.getId());
                    workOrderService.updateById(workOrder);
                }
            }
        }
    }

    private void initializeProjectDimension() {
        // 为现有数据初始化项目维度字段为NULL
        // 这些数据将在部门形态下可见，在项目形态下不可见
        log.info("现有业务数据项目维度字段已初始化为NULL，将在部门形态下可见");
    }
}
```

### 7. 缓存优化策略

#### 7.1 多层缓存设计
```java
@Service
@RequiredArgsConstructor
public class DataPermissionCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final LoadingCache<String, DataPermissionScope> localCache;

    public DataPermissionScope getUserPermissionScope(Long userId) {
        String cacheKey = "user:permission:scope:" + userId;

        try {
            // 1. 本地缓存（1分钟）
            return localCache.get(cacheKey, () -> {
                // 2. Redis缓存（10分钟）
                DataPermissionScope redisScope = (DataPermissionScope) redisTemplate.opsForValue().get(cacheKey);
                if (redisScope != null) {
                    return redisScope;
                }

                // 3. 数据库查询
                DataPermissionScope dbScope = calculateUserPermissionScope(userId);
                redisTemplate.opsForValue().set(cacheKey, dbScope, 10, TimeUnit.MINUTES);
                return dbScope;
            });
        } catch (Exception e) {
            log.error("获取用户权限范围失败，userId: {}", userId, e);
            return calculateUserPermissionScope(userId);
        }
    }

    public void clearUserPermissionCache(Long userId) {
        String cacheKey = "user:permission:scope:" + userId;
        localCache.invalidate(cacheKey);
        redisTemplate.delete(cacheKey);
    }
}
```

### 8. 性能优化

#### 8.1 批量权限检查
```java
@Service
public class OptimizedDataPermissionService {

    /**
     * 批量权限检查，避免N+1查询问题
     */
    public Map<Long, Boolean> batchCheckDataAccess(Long userId, String entityType, List<Long> entityIds) {
        if (entityIds.isEmpty()) {
            return new HashMap<>();
        }

        DataPermissionScope scope = getUserPermissionScope(userId);
        Map<Long, Boolean> result = new HashMap<>();

        if (scope.isUnlimited()) {
            entityIds.forEach(id -> result.put(id, true));
            return result;
        }

        // 批量查询实体的归属信息
        Map<Long, Long> entityOrganizationMap = batchGetEntityOrganizationIds(entityType, entityIds, scope.getOrganizationMode());

        // 批量检查权限
        List<Long> allowedOrganizationIds = scope.getOrganizationMode().equals("DEPARTMENT")
            ? scope.getDepartmentIds() : scope.getProjectIds();

        entityIds.forEach(entityId -> {
            Long organizationId = entityOrganizationMap.get(entityId);
            boolean hasAccess = organizationId != null && allowedOrganizationIds.contains(organizationId);
            result.put(entityId, hasAccess);
        });

        return result;
    }
}
```

## 🚀 实施步骤

### 第一阶段：数据模型扩展
1. 为业务实体添加项目维度字段
2. 创建数据库索引优化查询性能
3. 实现数据迁移脚本

### 第二阶段：权限服务开发
1. 实现双维度权限服务核心逻辑
2. 开发AOP权限拦截器
3. 集成缓存优化策略

### 第三阶段：业务服务集成
1. 更新所有业务服务的查询方法
2. 实现数据创建时的维度分配
3. 添加权限验证和异常处理

### 第四阶段：监控和优化
1. 实现权限操作审计日志
2. 添加性能监控和统计
3. 优化查询性能和缓存策略

## ✅ 预期效果

1. **权限精确控制**：用户在不同组织形态下只能看到对应维度的数据
2. **性能优化**：通过多层缓存和批量查询优化性能
3. **操作透明**：完整的权限操作审计日志
4. **扩展性强**：支持未来更多业务实体和组织维度
