# 角色类型优化技术设计 (TDD)

## 1. 概述

本文档旨在为“角色类型优化”需求提供技术实现方案。核心目标是在不改变现有数据权限校验逻辑的前提下，通过引入“全局角色”类型来简化角色创建流程。

## 2. 数据模型变更

-   **目标表**：`base_sys_role`
-   **操作**：新增一个字段以区分角色类型。
-   **字段名**：`type`
-   **字段类型**：`TINYINT(1)`
-   **注释**：`角色类型：1-全局角色，2-范围角色`
-   **默认值**：`2` (确保所有现有角色默认为范围角色，实现向后兼容)

-   **SQL脚本**:
    ```sql
    ALTER TABLE `base_sys_role` ADD COLUMN `type` TINYINT(1) DEFAULT 2 COMMENT '角色类型：1-全局角色，2-范围角色';
    ```

## 3. 后端实现

### 3.1 DTO/Entity层

-   在 `SysRoleEntity` (或对应的PO/VO/DTO) 中增加 `type` 属性。
    ```java
    // In SysRoleEntity.java
    private Integer type;
    ```

### 3.2 Service/Controller层

-   **`add` / `update` 接口**：修改角色的新增和更新接口逻辑。
    -   接口接收前端传递的 `type` 参数。
    -   当 `type` 为 `1` (全局角色) 时，在执行数据库保存操作前，将该角色的 `departmentIdList` 字段强制设置为空数组或 `null`。
    -   这确保了“全局角色”本身不与任何部门数据直接关联。
-   **数据权限拦截器**：**无需任何改动**。保持现有逻辑不变。

## 4. 前端实现

-   **目标文件**：`cool-admin-vue/src/modules/base/views/role.vue`

### 4.1 列表页 (`cl-table`)

-   在 `useTable` 的 `columns` 配置中新增一列“角色类型”。
-   使用 `formatter` 或 `slot` 结合 `el-tag` 组件来展示角色类型。
    ```javascript
    // In useTable columns array
    {
        prop: 'type',
        label: '角色类型',
        minWidth: 120,
        formatter: (row) => {
            return row.type === 1 ? '全局角色' : '范围角色';
        }
        // 或使用 slot 和 el-tag 实现更丰富的视觉效果
    }
    ```

### 4.2 新增/编辑页 (`cl-upsert`)

-   在 `useUpsert` 的 `items` 配置中增加“角色类型”表单项。
    -   建议放在“名称”和“标识”下方。
    -   使用 `el-switch` 组件，并绑定到 `type` 字段。需要注意 `el-switch` 的 `active-value` 和 `inactive-value` 应分别设置为 `1` 和 `2`。
    ```javascript
    // In useUpsert items array
    {
        prop: 'type',
        label: '全局角色',
        value: 2, // 默认值
        component: {
            name: 'el-switch',
            props: {
                activeValue: 1,
                inactiveValue: 2
            }
        }
    }
    ```
-   **联动隐藏数据权限配置**：
    -   找到 `label` 为“数据权限”的那个表单项配置。
    -   为其增加一个 `hidden` 或 `v-if` 的动态属性，该属性与 `type` 字段的值关联。
    -   在 `cl-upsert` 组件中，可以使用 `hidden` 属性，它通常是一个函数，接收 `scope` 作为参数。
    ```javascript
    // In useUpsert items array, for '数据权限'
    {
        label: '数据权限',
        prop: 'relevance',
        component: {
            name: 'slot-relevance'
        },
        hidden: ({ scope }) => scope.type === 1
    }
    ```
    *如果组件库不支持 `hidden` 函数，则需要修改模板，使用 `<template v-if="Upsert.form.type !== 1">` 将数据权限的 `slot` 包裹起来。*

## 5. 总结

该方案通过最小化的改动（一个字段和纯前端UI调整），实现了需求的既定目标，且对现有核心权限逻辑无任何侵入，风险低，易于实现和验证。
