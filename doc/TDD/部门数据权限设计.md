# Cool Admin 部门数据权限设计

## 📋 概述

Cool Admin 系统采用基于 RBAC（基于角色的访问控制）+ 部门层级的数据权限控制模型，实现了灵活的多部门管理和可配置的层级权限继承机制。

## 🏗️ 核心架构设计

### 数据模型关系图

```
用户 (User) 
    ↓ 1:N
角色关联 (UserRole)
    ↓ N:1  
角色 (Role) ←→ 部门权限 (RoleDepartment) 
    ↓ N:1                    ↓ N:1
权限配置                    部门 (Department)
```

### 核心数据表结构

#### 1. 用户基础表 `base_sys_user`
```sql
CREATE TABLE base_sys_user (
    id BIGINT PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    department_id BIGINT,  -- 用户所属部门
    -- 其他字段...
);
```

#### 2. 用户角色关联表 `base_sys_user_role`
```sql
CREATE TABLE base_sys_user_role (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL,    -- 用户ID
    role_id BIGINT NOT NULL     -- 角色ID
);
```

#### 3. 角色配置表 `base_sys_role`
```sql
CREATE TABLE base_sys_role (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label VARCHAR(255) NOT NULL,
    relevance INT DEFAULT 1,    -- 数据权限是否关联上下级 (0:否, 1:是)
    department_id_list JSON,    -- 部门权限列表
    -- 其他字段...
);
```

#### 4. 角色部门权限表 `base_sys_role_department`
```sql
CREATE TABLE base_sys_role_department (
    id BIGINT PRIMARY KEY,
    role_id BIGINT NOT NULL,       -- 角色ID
    department_id BIGINT NOT NULL  -- 部门ID
);
```

#### 5. 部门层级表 `base_sys_department`
```sql
CREATE TABLE base_sys_department (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    parent_id BIGINT,      -- 上级部门ID
    order_num INT DEFAULT 0
);
```

## 🔧 核心功能实现

### 1. 权限获取机制

#### 用户登录时权限计算
```java
@Override
public Long[] loginDepartmentIds() {
    String username = CoolSecurityUtil.getAdminUsername();
    
    // 超级管理员拥有所有部门权限
    if (username.equals("admin")) {
        return baseSysDepartmentMapper.selectAll()
            .stream().map(BaseSysDepartmentEntity::getId)
            .toArray(Long[]::new);
    } 
    
    // 普通用户：通过角色获取部门权限
    Long[] roleIds = getRoles(username);
    return baseSysRoleDepartmentMapper
        .selectListByQuery(
            QueryWrapper.create().in(BaseSysRoleDepartmentEntity::getRoleId, roleIds))
        .stream().map(BaseSysRoleDepartmentEntity::getDepartmentId)
        .toArray(Long[]::new);
}
```

#### 权限缓存策略
- **缓存键**: `admin:department:{userId}`
- **缓存内容**: 用户可访问的部门ID数组
- **更新时机**: 用户角色变更、角色部门权限变更时自动刷新

### 2. 数据查询权限过滤

#### 用户列表查询示例
```java
@Override
public Object page(JSONObject requestParams, Page<BaseSysUserEntity> page, QueryWrapper qw) {
    // 获取当前用户的部门权限
    JSONObject tokenInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
    Long[] permsDepartmentArr = coolCache.get("admin:department:" + tokenInfo.get("userId"), Long[].class);
    
    // 构建查询条件
    qw.from(BASE_SYS_USER_ENTITY)
      // 过滤部门权限
      .and(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.in(
          permsDepartmentArr == null || permsDepartmentArr.length == 0 
              ? new Long[]{null} : permsDepartmentArr,
          !CoolSecurityUtil.getAdminUsername().equals("admin")));
    
    return mapper.paginate(page, qw);
}
```

### 3. 角色权限配置

#### 权限更新接口
```java
@Override
public void updatePerms(Long roleId, Long[] menuIdList, Long[] departmentIds) {
    // 更新菜单权限
    baseSysRoleMenuMapper.deleteByQuery(
        QueryWrapper.create().eq(BaseSysRoleMenuEntity::getRoleId, roleId));
    // 批量插入菜单权限...
    
    // 更新部门权限
    baseSysRoleDepartmentMapper.deleteByQuery(
        QueryWrapper.create().eq(BaseSysRoleDepartmentEntity::getRoleId, roleId));
    
    List<BaseSysRoleDepartmentEntity> batchRoleDepartmentList = new ArrayList<>();
    for (Long departmentId : departmentIds) {
        BaseSysRoleDepartmentEntity roleDepartmentEntity = new BaseSysRoleDepartmentEntity();
        roleDepartmentEntity.setRoleId(roleId);
        roleDepartmentEntity.setDepartmentId(departmentId);
        batchRoleDepartmentList.add(roleDepartmentEntity);
    }
    
    if (ObjectUtil.isNotEmpty(batchRoleDepartmentList)) {
        baseSysRoleDepartmentMapper.insertBatch(batchRoleDepartmentList);
    }
    
    // 异步刷新相关用户权限缓存
    cachedThreadPool.submit(() -> {
        List<BaseSysUserRoleEntity> userRoles = baseSysUserRoleMapper
            .selectListByQuery(QueryWrapper.create().eq(BaseSysUserRoleEntity::getRoleId, roleId));
        for (BaseSysUserRoleEntity userRole : userRoles) {
            refreshPerms(userRole.getUserId());
        }
    });
}
```

## 🎯 核心特性

### 1. 多部门管理支持

#### 实现方式
- **用户 → 多角色 → 多部门**: 用户可以分配多个角色，每个角色可绑定不同部门
- **权限合并**: 系统自动合并用户所有角色的部门权限
- **动态计算**: 权限变更时实时重新计算用户的部门权限范围

#### 应用场景
```java
// 场景示例：区域经理张三
// 角色1: 华东区销售经理 → 绑定部门: [上海销售部, 杭州销售部]
// 角色2: 产品推广专员 → 绑定部门: [产品部]
// 最终权限: 张三可以管理 [上海销售部, 杭州销售部, 产品部] 的数据
```

### 2. 层级权限继承

#### 配置字段 `relevance`
- **relevance = 1**: 关联上下级（默认）
  - 选择父部门时自动包含所有子部门权限
  - 适用于管理层角色，需要查看下属部门数据
  
- **relevance = 0**: 不关联上下级
  - 严格按选中的部门执行权限
  - 适用于专职角色，只能查看特定部门数据

#### 前端配置界面
```vue
<template #slot-relevance="{ scope }">
    <div>
        <el-row>
            <cl-switch v-model="scope.relevance" />
            <span>{{ t('是否关联上下级') }}</span>
        </el-row>
        
        <!-- 部门选择组件，根据relevance控制选择模式 -->
        <cl-dept-check
            v-model="scope.departmentIdList"
            :check-strictly="scope.relevance == 0"
        />
    </div>
</template>
```

## 🔄 权限计算流程

### 完整流程图
```
1. 用户登录
    ↓
2. 获取用户角色列表 (getRoles)
    ↓
3. 查询角色绑定的部门权限 (baseSysRoleDepartmentMapper)
    ↓
4. 合并所有角色的部门权限
    ↓
5. 根据relevance处理上下级关联
    ↓
6. 缓存用户部门权限 (admin:department:{userId})
    ↓
7. 数据查询时应用权限过滤
```

### 缓存更新机制
```java
// 权限变更触发缓存刷新的场景：
1. 用户角色变更 → refreshPerms(userId)
2. 角色部门权限变更 → refreshPermsByRoleId(roleId)  
3. 菜单权限变更 → refreshPermsByMenuId(menuId)
4. 用户状态变更 → 自动清理相关缓存
```

## 📝 使用指南

### 1. 配置多部门管理权限

#### 步骤说明
1. **创建角色**: 系统管理 → 角色管理 → 新增角色
2. **配置数据权限**: 
   - 设置"是否关联上下级"选项
   - 选择该角色可管理的部门
3. **分配用户**: 用户管理 → 编辑用户 → 分配角色

#### 最佳实践
```java
// 推荐的角色设计模式：
1. 公司级管理角色：relevance=1, 绑定顶级部门（自动包含所有子部门）
2. 部门级管理角色：relevance=1, 绑定具体部门（包含子部门）
3. 专职操作角色：relevance=0, 绑定特定部门（不包含子部门）
```

### 2. 权限验证示例

#### 在业务代码中应用部门权限
```java
// 获取当前用户的部门权限
Long[] departmentIds = baseSysPermsService.loginDepartmentIds();

// 在查询中应用权限过滤
QueryWrapper qw = QueryWrapper.create()
    .in(YourEntity::getDepartmentId, departmentIds, 
        !CoolSecurityUtil.getAdminUsername().equals("admin"));
```

### 3. 前端部门树展示
```vue
<!-- 部门列表组件会自动根据用户权限过滤显示内容 -->
<dept-list @refresh="refresh" @user-add="onUserAdd" />
```

## ⚠️ 注意事项

### 1. 权限设计原则
- **最小权限原则**: 只分配必要的部门权限
- **职责分离**: 管理权限和操作权限分开配置
- **定期审计**: 定期检查和清理不必要的权限分配

### 2. 性能优化建议
- **缓存策略**: 利用Redis缓存减少数据库查询
- **批量操作**: 权限变更时使用批量更新减少数据库压力
- **异步刷新**: 权限缓存刷新采用异步方式，避免阻塞主流程

### 3. 安全注意事项
- **超级管理员**: admin用户拥有所有权限，需严格控制账号安全
- **权限验证**: 所有数据操作都必须进行部门权限验证
- **日志记录**: 重要权限操作需要记录审计日志

## 🔧 扩展开发

### 自定义业务模块集成部门权限

#### 1. 实体类添加部门字段
```java
@ColumnDefine(comment = "所属部门ID", type = "bigint")
private Long departmentId;
```

#### 2. 查询方法添加权限过滤
```java
@Override
public Object page(JSONObject requestParams, Page<YourEntity> page, QueryWrapper qw) {
    // 获取当前用户部门权限
    JSONObject tokenInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
    Long[] permsDepartmentArr = coolCache.get("admin:department:" + tokenInfo.get("userId"), Long[].class);
    
    // 应用部门权限过滤
    qw.and(YOUR_ENTITY.DEPARTMENT_ID.in(
        permsDepartmentArr == null || permsDepartmentArr.length == 0 
            ? new Long[]{null} : permsDepartmentArr,
        !CoolSecurityUtil.getAdminUsername().equals("admin")));
    
    return mapper.paginate(page, qw);
}
```

#### 3. 新增数据时自动设置部门
```java
@Override
public Object add(JSONObject requestParams, YourEntity entity) {
    // 如果未指定部门，使用用户所属部门
    if (entity.getDepartmentId() == null) {
        JSONObject userInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
        entity.setDepartmentId(userInfo.getLong("departmentId"));
    }
    return super.add(requestParams, entity);
}
```

## 📊 总结

Cool Admin 的部门数据权限设计具有以下特点：

1. **灵活性**: 支持多角色、多部门的复杂权限配置
2. **可扩展性**: 基于标准RBAC模型，易于扩展到其他业务模块
3. **性能优化**: 采用缓存机制和异步更新策略
4. **用户友好**: 提供直观的前端配置界面
5. **安全可靠**: 完整的权限验证和审计机制

这套设计能够满足大多数企业级应用的部门权限控制需求，同时保持了良好的扩展性和维护性。
