# 项目工作台任务管理功能使用说明

## 功能概述

项目工作台任务管理功能允许用户在项目维度下查看和管理任务，实现了数据隔离和项目上下文的任务操作。

## 主要特性

1. **项目数据隔离**：任务列表根据选定项目进行严格过滤
2. **项目切换**：支持在不同项目间切换查看任务
3. **权限控制**：只显示用户有权限访问的项目
4. **操作闭环**：新增、编辑、删除任务都与当前项目关联

## 技术实现

### 后端实现

#### 1. 项目任务Controller
- **路径**: `AdminProjectTaskController`
- **API路径**: `/admin/organization/project/task`
- **EPS服务**: `service.organization.project.task`

#### 2. 核心功能
- 通过`projectId`参数过滤任务列表
- 验证用户项目访问权限
- 通过任务包关联查询项目任务
- 提供用户可访问项目列表接口

#### 3. 权限验证
```java
// 验证用户是否有项目访问权限
Long userId = CoolSecurityUtil.getAdminUserId();
if (!projectAccessService.hasProjectAccess(userId, projectId)) {
    // 返回空结果
}
```

#### 4. 数据过滤
```java
// 通过任务包关联筛选项目任务
List<Long> projectPackageIds = getPackageIdsByProjectId(projectId);
// 根据任务包ID列表筛选任务
setPageOption(createOp()
    .fieldEq(TASK_INFO_ENTITY.PACKAGE_ID)
    .and(t -> t.in(TASK_INFO_ENTITY.PACKAGE_ID, projectPackageIds))
);
```

### 前端实现

#### 1. 项目选择器
```vue
<el-select
    v-model="selectedProjectId"
    placeholder="请选择项目以查看任务"
    @change="onProjectChange"
>
    <el-option
        v-for="item in projectOptions"
        :key="item.projectId"
        :label="item.projectName"
        :value="item.projectId"
    />
</el-select>
```

#### 2. CRUD配置
```javascript
const Crud = useCrud({
    service: service.organization.project.task, // 指向项目任务API
    onRefresh(params, { next }) {
        if (!selectedProjectId.value) {
            Table.value?.clear();
            return;
        }
        next({ ...params, projectId: selectedProjectId.value });
    }
});
```

#### 3. 任务创建关联
```javascript
const Upsert = useUpsert({
    onSubmit(data, { next }) {
        if (!selectedProjectId.value) {
            ElMessage.warning("请先选择一个项目");
            return;
        }
        next({ ...data, projectId: selectedProjectId.value });
    }
});
```

## 使用流程

### 1. 进入页面
1. 页面加载时自动获取用户可访问的项目列表
2. 默认选中第一个项目
3. 自动加载该项目的任务列表

### 2. 切换项目
1. 在项目选择器中选择不同的项目
2. 任务列表自动刷新显示新项目的任务
3. 所有操作都在新项目上下文中进行

### 3. 任务操作
1. **查看任务**：列表显示当前项目的所有任务
2. **新增任务**：新任务自动关联到当前选中的项目
3. **编辑任务**：只能编辑当前项目的任务
4. **删除任务**：只能删除当前项目的任务

## API接口说明

### 1. 获取项目列表
```http
GET /admin/organization/project/task/accessible-projects
```
返回用户可访问的项目列表，包含项目ID、名称、用户角色等信息。

### 2. 分页查询任务
```http
POST /admin/organization/project/task/page
Content-Type: application/json

{
    "projectId": 1,
    "page": 1,
    "size": 20
}
```

### 3. 新增任务
```http
POST /admin/organization/project/task/add
Content-Type: application/json

{
    "name": "任务名称",
    "description": "任务描述",
    "projectId": 1
}
```

## 数据流转

### 1. 任务与项目关联
```
任务 (TaskInfoEntity)
  ↓ package_id
任务包 (TaskPackageEntity)
  ↓ project_id  
项目 (ProjectInfoEntity)
```

### 2. 权限验证流程
```
用户请求 → 提取projectId → 验证项目访问权限 → 查询任务包ID → 过滤任务列表
```

## 注意事项

1. **权限控制**：用户只能看到有权限访问的项目和任务
2. **数据隔离**：不同项目的任务完全隔离，不会相互影响
3. **操作限制**：所有任务操作都必须在项目上下文中进行
4. **缓存机制**：项目列表和权限信息有缓存，提升性能

## 故障排除

### 1. 项目列表为空
- 检查用户是否有项目权限
- 确认用户组织关系配置正确
- 验证ProjectAccessService是否正常工作

### 2. 任务列表为空
- 确认项目是否有关联的任务包
- 检查任务包状态是否正常
- 验证任务与任务包的关联关系

### 3. 权限错误
- 检查用户在项目中的角色
- 确认项目访问权限配置
- 验证权限验证逻辑

## 扩展功能

### 1. 任务状态管理
可以扩展任务状态的看板视图，支持拖拽操作。

### 2. 任务分配
可以添加任务分配功能，支持将任务分配给项目成员。

### 3. 任务统计
可以添加项目任务统计功能，显示任务完成情况。

### 4. 任务导出
可以添加任务导出功能，支持导出项目任务列表。