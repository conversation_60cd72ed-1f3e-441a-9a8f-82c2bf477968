# 项目工作台-任务管理功能设计文档

## 1. 背景与目标

### 1.1 背景

当前 `cool-admin-vue/src/modules/organization/views/project/task.vue` 页面虽然存在，但其数据服务直接指向全局任务接口 (`service.task.info`)，导致页面展示的是所有任务，未能根据项目上下文进行过滤。这使得项目维度的任务管理功能实际上处于不可用状态。

### 1.2 目标

改造现有的 `task.vue` 页面，实现一个功能完备、与后端数据联动的项目任务管理界面。具体要求如下：

1.  **数据隔离**：任务列表必须根据选定的项目进行严格过滤。
2.  **默认展示**：进入页面时，自动加载用户可访问的第一个项目的任务列表。
3.  **项目切换**：提供项目选择器，允许用户在不同项目间自由切换，并刷新任务列表。
4.  **操作闭环**：在页面内进行任务的新增、编辑、删除等操作，都必须与当前选定的项目关联。
5.  **规范遵循**：所有改造必须严格遵循 Cool Admin 的现有开发规范，特别是EPS服务调用和CRUD组件的使用。

## 2. 技术方案

本方案通过“后端提供专用接口，前端改造交互逻辑”的方式，以最小化改动实现功能目标。

### 2.1 后端 API 设计

为了提供按项目过滤的任务数据，需要创建一个新的 Controller。

**文件路径**: `cool-admin-java/src/main/java/com/cool/modules/project/controller/admin/AdminProjectTaskController.java`

**核心实现**:

```java
package com.cool.modules.project.controller.admin;

import com.cool.core.base.BaseController;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.core.annotation.CoolRestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.servlet.http.HttpServletRequest;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.RequestMapping;

@Tag(name = "项目任务管理", description = "根据项目ID过滤任务列表")
@CoolRestController(api = {"page", "list", "add", "update", "delete", "info"})
@RequestMapping("/admin/project/task") // 定义新的、唯一的请求路径
public class AdminProjectTaskController extends BaseController<TaskInfoService, TaskInfoEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 从请求参数中安全地获取 projectId
        Long projectId = requestParams.getLong("projectId");
        
        // 如果 projectId 未提供，则构造一个永不成立的查询条件，返回空列表
        if (projectId == null) {
            setListOption(createOp().and(t -> t.eq("1", "0")));
            return;
        }

        // 关键：将 projectId 作为强制过滤条件应用到查询中
        // 此处假设 TaskInfoEntity 已有 projectId 字段
        setListOption(
            createOp()
                .and(t -> t.eq("projectId", projectId))
                .keyWordLikeFields("name", "description") // 保留关键字搜索功能
        );
    }
}
```

**设计说明**:

*   **专用接口**: 新建的 `AdminProjectTaskController` 会通过EPS系统生成 `service.project.task` 服务，与全局任务服务 `service.task.info` 分离。
*   **强制过滤**: 通过重写 `init` 方法，所有通过此 Controller 的查询都必须携带 `projectId` 参数，否则不返回任何数据，保证了数据的隔离性与安全性。
*   **复用逻辑**: 继承 `BaseController<TaskInfoService, TaskInfoEntity>` 复用了任务模块的 Service 和 Entity，避免了代码冗余。

### 2.2 前端页面改造

对现有的 `task.vue` 文件进行重构，引入项目选择和数据联动逻辑。

**文件路径**: `cool-admin-vue/src/modules/organization/views/project/task.vue`

**核心实现**:

```vue
<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 顶部操作栏 -->
			<cl-refresh-btn />
			<cl-add-btn />
			<cl-multi-delete-btn />
			<cl-flex1 />

			<!-- 核心改造：项目选择器 -->
			<el-select
				v-model="selectedProjectId"
				placeholder="请选择项目"
				@change="onProjectChange"
				clearable
				filterable
				style="width: 240px; margin-right: 10px"
			>
				<el-option
					v-for="item in projectOptions"
					:key="item.id"
					:label="item.projectName"
					:value="item.id"
				/>
			</el-select>

			<cl-search-key />
		</cl-row>

		<cl-row>
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<cl-pagination />
		</cl-row>

		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ElMessage } from "element-plus";

defineOptions({
	name: "project-task"
});

const { service } = useCool();

// 响应式变量，用于存储项目列表和当前选中的项目ID
const selectedProjectId = ref<number | null>(null);
const projectOptions = ref<{ id: number; projectName: string }[]>([]);

const Crud = useCrud(
	{
		// 1. 关键：服务指向新创建的后端API
		service: service.project.task,
		
        // 2. 改造 onRefresh 钩子，动态注入 projectId
		onRefresh(params, { next }) {
			if (!selectedProjectId.value) {
                // 如果未选择项目，则清空表格，不发送请求
				Table.value?.clear();
				return;
			}
			// 将当前选中的项目ID附加到请求参数中
			next({ ...params, projectId: selectedProjectId.value });
		}
	},
    // 进入页面后不立即刷新，等待项目列表加载后手动刷新
	(app) => {} 
);

const Table = useTable({
	columns: [
		{ type: "selection", width: 60 },
		{ label: "任务名称", prop: "name", minWidth: 160 },
		{ label: "状态", prop: "status", minWidth: 100 },
		{ label: "负责人", prop: "principalName", minWidth: 120 },
		{ label: "截止日期", prop: "deadline", minWidth: 160 },
		{ label: "创建时间", prop: "createTime", minWidth: 160 },
		{ type: "op", buttons: ["info", "edit", "delete"] }
	]
});

const Upsert = useUpsert({
	items: [
		{ label: "任务名称", prop: "name", required: true, component: { name: "el-input" } },
		{ label: "任务描述", prop: "description", component: { name: "el-input", props: { type: "textarea" } } },
	],
    // 3. 改造 onSubmit 钩子，确保新增/编辑的任务能关联到当前项目
	onSubmit(data, { next }) {
		if (!selectedProjectId.value) {
			ElMessage.warning("请先选择一个项目");
			return;
		}
		next({
			...data,
			projectId: selectedProjectId.value
		});
	}
});

// 获取用户可访问的项目列表
async function fetchProjectOptions() {
	try {
        // 假设 service.project.info.list 接口能返回用户可访问的项目列表
		const list = await service.project.info.list();
		projectOptions.value = list;

        // 4. 默认选中第一个项目并刷新任务列表
		if (list && list.length > 0) {
			selectedProjectId.value = list[0].id;
			Crud.value?.refresh();
		}
	} catch (error) {
		ElMessage.error("获取项目列表失败");
		console.error(error);
	}
}

// 当用户在选择器中切换项目时，刷新CRUD
function onProjectChange() {
	Crud.value?.refresh();
}

// 组件挂载后，立即获取项目列表
onMounted(() => {
	fetchProjectOptions();
});
</script>
