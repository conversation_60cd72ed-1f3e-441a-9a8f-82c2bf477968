import json

def parse_kv_txt(txt):
    """解析key-value型txt，返回dict"""
    lines = [l.strip() for l in txt.split('\n') if l.strip() and not l.strip().startswith('| ----')]
    d = {}
    for line in lines:
        if line.startswith('|') and '|' in line[1:]:
            parts = [p.strip() for p in line.split('|')[1:]]
            if len(parts) == 2:
                k, v = parts
                d[k] = v
    return d

def parse_table_txt(txt):
    """解析表格型txt，返回list[dict]"""
    lines = [l.strip() for l in txt.split('\n') if l.strip() and not l.strip().startswith('| --')]
    if not lines or len(lines) < 2:
        return []
    headers = [h.strip() for h in lines[0].split('|')[1:-1]]
    data = []
    for line in lines[1:]:
        if line.startswith('|'):
            values = [v.strip() for v in line.split('|')[1:-1]]
            if len(values) == len(headers):
                data.append(dict(zip(headers, values)))
    return data

def main(master_data:str, business_data:str, financial_data:str, smart_work_order_data:str, template_key:str,announcement_date:str)->dict:
    master_obj = parse_kv_txt(master_data)
    result = {}
    if template_key == "area-overview":
        result = {"data": master_data}
    elif template_key == "owner-income-expense":
        result = {"data": financial_data}
    elif template_key == "fund-income-expense":
        result = {"data": financial_data}
    elif template_key == "issue-report":
        meta = {
            "日期": announcement_date
        }
        channel_list = ["客户报修", "客户投诉", "客户问询", "智慧工单", "其他"]
        def classify_channel(row, is_smart):
            if is_smart:
                return "智慧工单"
            etype = row.get("事件类型") or row.get("事项分类") or row.get("渠道来源") or row.get("任务来源") or row.get("工单类型") or ""
            if "报修" in etype:
                return "客户报修"
            elif "投诉" in etype:
                return "客户投诉"
            elif "问询" in etype or "咨询" in etype:
                return "客户问询"
            elif "智慧工单" in etype:
                return "智慧工单"
            else:
                return "其他"
        def is_solved(row):
            val = row.get("状态") or row.get("任务状态") or row.get("工单状态") or row.get("处理状态") or ""
            return any(x in val for x in ["已解决", "已完成", "已关闭"])
        def is_unsolved(row):
            val = row.get("状态") or row.get("任务状态") or row.get("工单状态") or row.get("处理状态") or ""
            return any(x in val for x in ["未解决", "待处理", "处理中", "待执行", "待解决"])
        def get_satisfaction(row):
            if row.get("是否好评"):
                return 1 if row["是否好评"] in ["是", "满意", "好评", "1"] else 0
            if row.get("满意度"):
                try:
                    val = float(row["满意度"])
                    return 1 if val >= 4 else 0
                except:
                    return 0
            if row.get("评价星级") and row["评价星级"] != "系统默认三星好评":
                try:
                    val = float(row["评价星级"])
                    return 1 if val >= 4 else 0
                except:
                    return 0
            return None
        def get_create_time(row):
            return row.get("创建时间") or row.get("工单创建时间") or row.get("事件创建时间") or row.get("受理时间")
        def get_finish_time(row):
            return row.get("完成时间") or row.get("任务完成时间") or row.get("工单完成时间") or row.get("事件完成时间")
        def parse_time(t):
            import re, datetime
            if not t or not isinstance(t, str):
                return None
            t = t.strip()
            for fmt in ["%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S", "%Y-%m-%d", "%Y/%m/%d"]:
                try:
                    return datetime.datetime.strptime(t, fmt)
                except:
                    continue
            m = re.match(r"(\d{4}-\d{1,2}-\d{1,2})", t)
            if m:
                try:
                    return datetime.datetime.strptime(m.group(1), "%Y-%m-%d")
                except:
                    return None
            return None
        business_obj = parse_table_txt(business_data)
        smart_work_order_obj = parse_table_txt(smart_work_order_data)
        all_orders = []
        for row in (business_obj or []):
            all_orders.append((row, False))
        for row in (smart_work_order_obj or []):
            all_orders.append((row, True))
        stats = {ch: {
            "total": 0,
            "response_time_sum": 0,
            "response_time_count": 0,
            "solved": 0,
            "unsolved": 0,
            "satisfaction_sum": 0,
            "satisfaction_count": 0
        } for ch in channel_list}
        for row, is_smart in all_orders:
            channel = classify_channel(row, is_smart)
            if channel not in stats:
                channel = "其他"
            stats[channel]["total"] += 1
            ct, ft = get_create_time(row), get_finish_time(row)
            if ct and ft:
                try:
                    delta = (parse_time(ft) - parse_time(ct)).total_seconds() / 86400
                    if delta >= 0:
                        stats[channel]["response_time_sum"] += delta
                        stats[channel]["response_time_count"] += 1
                except:
                    pass
            if is_solved(row):
                stats[channel]["solved"] += 1
            if is_unsolved(row):
                stats[channel]["unsolved"] += 1
            sat = get_satisfaction(row)
            if sat is not None:
                stats[channel]["satisfaction_sum"] += sat
                stats[channel]["satisfaction_count"] += 1
        table_section = {
            "type": "table",
            "title": "物业小区问题解决情况",
            "headers": [
                "序号", "诉求渠道", "问题总量", "响应时间", "已解决", "待解决", "满意度", "备注"
            ],
            "rows": []
        }
        for idx, channel in enumerate(channel_list, 1):
            s = stats[channel]
            if s["response_time_count"] > 0:
                avg_time = round(s["response_time_sum"] / s["response_time_count"], 2)
                avg_time_str = f"{avg_time}天"
            else:
                avg_time_str = "-"
            if s["satisfaction_count"] > 0:
                satisfaction = round(s["satisfaction_sum"] / s["satisfaction_count"] * 100)
                satisfaction_str = f"{satisfaction}%"
            else:
                satisfaction_str = "-"
            row = {
                "序号": idx,
                "诉求渠道": channel,
                "问题总量": s["total"] if s["total"] > 0 else "-",
                "响应时间": avg_time_str,
                "已解决": s["solved"] if s["solved"] > 0 else "-",
                "待解决": s["unsolved"] if s["unsolved"] > 0 else "-",
                "满意度": satisfaction_str,
                "备注": ""
            }
            table_section["rows"].append(row)
        result = {
            "template_key": "issue-report",
            "meta": meta,
            "sections": [table_section]
        }
    return {"data_source": json.dumps(result, ensure_ascii=False)}  