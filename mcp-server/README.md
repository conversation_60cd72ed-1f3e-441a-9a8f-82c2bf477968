# Cool Admin MCP Server

基于Spring AI的简洁SSE Stream MCP服务器，为Cool Admin系统提供MCP工具服务。

## 🎯 特性

- **简洁设计** - 使用`@Tool`注解，代码简洁易维护
- **SSE异步** - 支持Server-Sent Events实时通信
- **模块化** - 按业务模块组织工具服务
- **易扩展** - 新增工具只需添加`@Tool`方法

## 🏗️ 架构

```
mcp-server/
├── src/main/java/com/cool/mcp/
│   ├── McpServerApplication.java     # 启动类
│   ├── config/
│   │   └── McpServerConfig.java      # MCP配置
│   └── service/
│       ├── SOPToolService.java       # SOP工具服务
│       ├── TaskToolService.java      # 任务工具服务
│       └── AnalyticsService.java     # 统计分析服务
├── src/main/resources/
│   └── application.yml               # 应用配置
├── pom.xml                          # Maven配置
├── start.sh                         # 启动脚本
└── README.md                        # 说明文档
```

## 🔧 支持的工具

### SOP管理工具 (6个)
- `sop_scenario_list` - 获取SOP场景列表
- `sop_scenario_get` - 获取SOP场景详情
- `sop_scenario_stats` - 获取SOP场景统计
- `sop_step_list` - 获取SOP步骤列表
- `sop_step_get` - 获取SOP步骤详情
- `sop_industry_list` - 获取SOP行业列表

### 任务管理工具 (13个)
- `task_list` - 获取任务列表
- `task_get` - 获取任务详情
- `task_create` - 创建任务
- `task_update` - 更新任务
- `task_assign` - 分配任务
- `task_start` - 开始任务
- `task_complete` - 完成任务
- `task_package_list` - 获取任务包列表
- `task_package_get` - 获取任务包详情
- `task_package_create` - 创建任务包
- `task_package_update` - 更新任务包
- `task_package_complete` - 完成任务包

### 统计分析工具 (4个)
- `analytics_task_stats` - 任务统计分析
- `analytics_sop_stats` - SOP统计分析
- `analytics_performance_stats` - 性能统计分析
- `analytics_overview` - 系统概览统计

## 🚀 快速开始

### 前置条件
- Java 17+
- Maven 3.6+
- Cool Admin服务器运行在端口18001

### 启动服务器

```bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 使用Maven
mvn spring-boot:run
```

### 验证服务

```bash
# 健康检查
curl http://localhost:18002/actuator/health

# SSE连接测试
curl -N http://localhost:18002/mcp/messages
```

## 📡 MCP连接

### SSE端点
```
http://localhost:18002/mcp/messages
```

### 工具调用示例

通过MCP客户端调用工具：

```json
{
  "tool": "sop_scenario_list",
  "params": {
    "page": 1,
    "size": 10,
    "keyword": "客户服务"
  }
}
```

## 🔧 配置说明

### application.yml

```yaml
server:
  port: 18002

spring:
  ai:
    mcp:
      server:
        enabled: true
        type: ASYNC              # 异步模式
        sse-message-endpoint: mcp/messages
        sse:
          enabled: true          # 启用SSE

cool-admin:
  base-url: "http://localhost:18001"
  timeout: 30000
  retry-count: 3
```

## 🛠️ 开发指南

### 添加新工具

1. 在对应的服务类中添加方法
2. 使用`@Tool`注解标记
3. 重启服务器即可

```java
@Tool(name = "my_new_tool", description = "我的新工具")
public Object myNewTool(String param1, Integer param2) {
    // 工具逻辑
    return result;
}
```

### 新增工具服务

1. 创建新的服务类
2. 在`McpServerConfig`中注册`ToolCallbackProvider`

```java
@Bean
public ToolCallbackProvider myTools(MyToolService myToolService) {
    return MethodToolCallbackProvider.builder()
        .toolObjects(myToolService)
        .build();
}
```

## 📝 注意事项

1. **依赖关系** - 确保Cool Admin服务器先启动
2. **端口冲突** - 默认使用18002端口，如有冲突请修改配置
3. **工具命名** - 工具名称使用下划线分隔，保持一致性
4. **错误处理** - 工具方法应包含适当的错误处理逻辑

## 🎉 优势

相比复杂的自定义实现，这个方案具有以下优势：

- **简洁** - 代码量减少80%，只需关注业务逻辑
- **标准** - 使用Spring AI标准MCP实现，兼容性好
- **高效** - 开发效率高，新增工具只需几行代码
- **稳定** - 基于成熟的Spring框架，稳定可靠
- **易维护** - 结构清晰，便于后续维护和扩展

## 📞 支持

如有问题，请检查：
1. Java和Maven版本是否正确
2. Cool Admin服务器是否正常运行
3. 端口是否被占用
4. 网络连接是否正常
