package com.cool.mcp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Cool Admin MCP Server 启动类
 *
 * 基于Spring AI MCP的简洁SSE Stream MCP服务器
 * 使用@Tool注解方式提供MCP工具服务
 */
@SpringBootApplication
public class McpServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(McpServerApplication.class, args);
        System.out.println("""

            ╔══════════════════════════════════════════════════════════════╗
            ║                                                              ║
            ║               Cool Admin MCP Server                          ║
            ║                                                              ║
            ║   🚀 SSE Stream MCP服务器已启动                               ║
            ║   📡 端口: 18002                                             ║
            ║   🔗 SSE连接: http://localhost:18002/mcp/messages            ║
            ║   📋 工具测试: http://localhost:18002/admin/mcp/test          ║
            ║   ❤️  健康检查: http://localhost:18002/admin/mcp/health       ║
            ║                                                              ║
            ║   支持的工具模块:                                              ║
            ║   • SOP场景管理 (6个工具)                                     ║
            ║   • 任务管理 (8个工具)                                        ║
            ║   • 任务包管理 (5个工具)                                      ║
            ║   • 统计分析 (4个工具)                                        ║
            ║                                                              ║
            ║   💡 使用@Tool注解方式，简洁高效！                             ║
            ║                                                              ║
            ╚══════════════════════════════════════════════════════════════╝
            """);
    }
}
