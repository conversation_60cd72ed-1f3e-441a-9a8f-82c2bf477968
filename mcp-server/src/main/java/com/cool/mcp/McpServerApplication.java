package com.cool.mcp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.reactive.config.EnableWebFlux;

/**
 * Cool Admin MCP Server 启动类
 * 
 * 基于Spring Boot WebFlux的SSE Stream MCP服务器
 * 为Cool Admin系统提供MCP工具服务
 */
@SpringBootApplication
@EnableWebFlux
public class McpServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(McpServerApplication.class, args);
        System.out.println("""
            
            ╔══════════════════════════════════════════════════════════════╗
            ║                                                              ║
            ║               Cool Admin MCP Server                          ║
            ║                                                              ║
            ║   🚀 SSE Stream MCP服务器已启动                               ║
            ║   📡 端口: 18002                                             ║
            ║   🔗 SSE连接: http://localhost:18002/mcp/sse                 ║
            ║   📋 工具列表: http://localhost:18002/mcp/tools               ║
            ║   ❤️  健康检查: http://localhost:18002/mcp/health             ║
            ║                                                              ║
            ║   支持的工具模块:                                              ║
            ║   • SOP场景管理 (8个工具)                                     ║
            ║   • 任务管理 (10个工具)                                       ║
            ║   • 任务包管理 (5个工具)                                      ║
            ║   • 统计分析 (4个工具)                                        ║
            ║                                                              ║
            ╚══════════════════════════════════════════════════════════════╝
            """);
    }
}
