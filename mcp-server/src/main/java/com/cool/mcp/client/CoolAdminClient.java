package com.cool.mcp.client;

import com.cool.mcp.config.McpServerConfig.McpProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Map;

/**
 * Cool Admin API客户端
 * 负责调用现有Cool Admin系统的API接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoolAdminClient {

    private final WebClient webClient;
    private final McpProperties mcpProperties;

    /**
     * 通用GET请求
     */
    public Mono<Object> get(String path, Map<String, Object> params) {
        return webClient.get()
            .uri(uriBuilder -> {
                var builder = uriBuilder.path(path);
                if (params != null) {
                    params.forEach((key, value) -> {
                        if (value != null) {
                            builder.queryParam(key, value);
                        }
                    });
                }
                return builder.build();
            })
            .retrieve()
            .bodyToMono(Object.class)
            .timeout(Duration.ofMillis(mcpProperties.getCoolAdmin().getTimeout()))
            .retryWhen(Retry.backoff(mcpProperties.getCoolAdmin().getRetryCount(), Duration.ofSeconds(1)))
            .doOnSubscribe(subscription -> log.debug("发送GET请求: {}, 参数: {}", path, params))
            .doOnSuccess(result -> log.debug("GET请求成功: {}", path))
            .doOnError(error -> log.error("GET请求失败: {}, 错误: {}", path, error.getMessage()));
    }

    /**
     * 通用POST请求
     */
    public Mono<Object> post(String path, Object body) {
        return webClient.post()
            .uri(path)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body != null ? body : Map.of())
            .retrieve()
            .bodyToMono(Object.class)
            .timeout(Duration.ofMillis(mcpProperties.getCoolAdmin().getTimeout()))
            .retryWhen(Retry.backoff(mcpProperties.getCoolAdmin().getRetryCount(), Duration.ofSeconds(1)))
            .doOnSubscribe(subscription -> log.debug("发送POST请求: {}, 请求体: {}", path, body))
            .doOnSuccess(result -> log.debug("POST请求成功: {}", path))
            .doOnError(error -> log.error("POST请求失败: {}, 错误: {}", path, error.getMessage()));
    }

    /**
     * 通用PUT请求
     */
    public Mono<Object> put(String path, Object body) {
        return webClient.put()
            .uri(path)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body != null ? body : Map.of())
            .retrieve()
            .bodyToMono(Object.class)
            .timeout(Duration.ofMillis(mcpProperties.getCoolAdmin().getTimeout()))
            .retryWhen(Retry.backoff(mcpProperties.getCoolAdmin().getRetryCount(), Duration.ofSeconds(1)))
            .doOnSubscribe(subscription -> log.debug("发送PUT请求: {}, 请求体: {}", path, body))
            .doOnSuccess(result -> log.debug("PUT请求成功: {}", path))
            .doOnError(error -> log.error("PUT请求失败: {}, 错误: {}", path, error.getMessage()));
    }

    /**
     * 通用DELETE请求
     */
    public Mono<Object> delete(String path, Map<String, Object> params) {
        return webClient.delete()
            .uri(uriBuilder -> {
                var builder = uriBuilder.path(path);
                if (params != null) {
                    params.forEach((key, value) -> {
                        if (value != null) {
                            builder.queryParam(key, value);
                        }
                    });
                }
                return builder.build();
            })
            .retrieve()
            .bodyToMono(Object.class)
            .timeout(Duration.ofMillis(mcpProperties.getCoolAdmin().getTimeout()))
            .retryWhen(Retry.backoff(mcpProperties.getCoolAdmin().getRetryCount(), Duration.ofSeconds(1)))
            .doOnSubscribe(subscription -> log.debug("发送DELETE请求: {}, 参数: {}", path, params))
            .doOnSuccess(result -> log.debug("DELETE请求成功: {}", path))
            .doOnError(error -> log.error("DELETE请求失败: {}, 错误: {}", path, error.getMessage()));
    }

    // ==================== SOP相关API ====================

    /**
     * 获取SOP场景列表
     */
    public Mono<Object> getSopScenarios(Map<String, Object> params) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getSop().getScenario() + "/page", params);
    }

    /**
     * 获取SOP场景详情
     */
    public Mono<Object> getSopScenario(Long id) {
        return get(mcpProperties.getCoolAdmin().getEndpoints().getSop().getScenario() + "/info", 
                  Map.of("id", id));
    }

    /**
     * 创建SOP场景
     */
    public Mono<Object> createSopScenario(Map<String, Object> scenario) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getSop().getScenario() + "/add", scenario);
    }

    /**
     * 更新SOP场景
     */
    public Mono<Object> updateSopScenario(Map<String, Object> scenario) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getSop().getScenario() + "/update", scenario);
    }

    /**
     * 获取SOP步骤列表
     */
    public Mono<Object> getSopSteps(Map<String, Object> params) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getSop().getStep() + "/page", params);
    }

    /**
     * 获取SOP步骤详情
     */
    public Mono<Object> getSopStep(Long id) {
        return get(mcpProperties.getCoolAdmin().getEndpoints().getSop().getStep() + "/info", 
                  Map.of("id", id));
    }

    /**
     * 获取SOP行业列表
     */
    public Mono<Object> getSopIndustries() {
        return get(mcpProperties.getCoolAdmin().getEndpoints().getSop().getIndustry() + "/list", null);
    }

    // ==================== 任务相关API ====================

    /**
     * 获取任务列表
     */
    public Mono<Object> getTasks(Map<String, Object> params) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getTask().getInfo() + "/page", params);
    }

    /**
     * 获取任务详情
     */
    public Mono<Object> getTask(Long id) {
        return get(mcpProperties.getCoolAdmin().getEndpoints().getTask().getInfo() + "/info", 
                  Map.of("id", id));
    }

    /**
     * 创建任务
     */
    public Mono<Object> createTask(Map<String, Object> task) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getTask().getInfo() + "/add", task);
    }

    /**
     * 更新任务
     */
    public Mono<Object> updateTask(Map<String, Object> task) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getTask().getInfo() + "/update", task);
    }

    // ==================== 任务包相关API ====================

    /**
     * 获取任务包列表
     */
    public Mono<Object> getTaskPackages(Map<String, Object> params) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getTask().getPackagePath() + "/page", params);
    }

    /**
     * 获取任务包详情
     */
    public Mono<Object> getTaskPackage(Long id) {
        return get(mcpProperties.getCoolAdmin().getEndpoints().getTask().getPackagePath() + "/info", 
                  Map.of("id", id));
    }

    /**
     * 创建任务包
     */
    public Mono<Object> createTaskPackage(Map<String, Object> taskPackage) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getTask().getPackagePath() + "/add", taskPackage);
    }

    /**
     * 更新任务包
     */
    public Mono<Object> updateTaskPackage(Map<String, Object> taskPackage) {
        return post(mcpProperties.getCoolAdmin().getEndpoints().getTask().getPackagePath() + "/update", taskPackage);
    }
}
