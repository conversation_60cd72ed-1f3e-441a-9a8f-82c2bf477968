package com.cool.mcp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Cool Admin配置属性
 */
@Component
@ConfigurationProperties(prefix = "cool-admin")
public class CoolAdminProperties {
    
    /**
     * Cool Admin基础地址
     */
    private String baseUrl = "http://localhost:18001";
    
    /**
     * 请求超时时间(毫秒)
     */
    private Long timeout = 30000L;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 3;

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public Long getTimeout() {
        return timeout;
    }

    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }
}
