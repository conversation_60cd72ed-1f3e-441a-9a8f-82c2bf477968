package com.cool.mcp.config;

import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

import com.cool.mcp.service.SOPToolService;
import com.cool.mcp.service.TaskToolService;
import com.cool.mcp.service.AnalyticsService;

/**
 * MCP服务器配置
 * 简洁的Spring AI MCP配置
 */
@Configuration
public class McpServerConfig {

    /**
     * WebClient配置 - 用于调用Cool Admin API
     */
    @Bean
    public WebClient webClient() {
        return WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
    }

    /**
     * SOP工具回调提供者
     */
    @Bean
    public ToolCallbackProvider sopTools(SOPToolService sopToolService) {
        return MethodToolCallbackProvider.builder()
            .toolObjects(sopToolService)
            .build();
    }

    /**
     * 任务工具回调提供者
     */
    @Bean
    public ToolCallbackProvider taskTools(TaskToolService taskToolService) {
        return MethodToolCallbackProvider.builder()
            .toolObjects(taskToolService)
            .build();
    }

    /**
     * 统计分析工具回调提供者
     */
    @Bean
    public ToolCallbackProvider analyticsTools(AnalyticsService analyticsService) {
        return MethodToolCallbackProvider.builder()
            .toolObjects(analyticsService)
            .build();
    }
}
