package com.cool.mcp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;

/**
 * MCP服务器配置
 */
@Configuration
public class McpServerConfig {

    /**
     * WebClient配置 - 用于调用Cool Admin API
     */
    @Bean
    public WebClient webClient(McpProperties mcpProperties) {
        return WebClient.builder()
            .baseUrl(mcpProperties.getCoolAdmin().getBaseUrl())
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
    }

    /**
     * MCP配置属性
     */
    @Data
    @Configuration
    @ConfigurationProperties(prefix = "mcp")
    public static class McpProperties {
        
        private Server server = new Server();
        private CoolAdmin coolAdmin = new CoolAdmin();
        
        @Data
        public static class Server {
            private String name = "cool-admin-mcp-server";
            private String version = "1.0.0";
            private String description = "Cool Admin系统的SSE Stream MCP服务器";
            private Sse sse = new Sse();
            private Tools tools = new Tools();
            
            @Data
            public static class Sse {
                private String endpoint = "/mcp/sse";
                private Long heartbeatInterval = 30000L;
                private Long connectionTimeout = 300000L;
                private Integer maxConnections = 100;
            }
            
            @Data
            public static class Tools {
                private Boolean enabled = true;
                private Boolean autoRegister = true;
            }
        }
        
        @Data
        public static class CoolAdmin {
            private String baseUrl = "http://localhost:18001";
            private Long timeout = 30000L;
            private Integer retryCount = 3;
            private Endpoints endpoints = new Endpoints();
            
            @Data
            public static class Endpoints {
                private Sop sop = new Sop();
                private Task task = new Task();
                private Analytics analytics = new Analytics();
                
                @Data
                public static class Sop {
                    private String scenario = "/admin/sop/scenario";
                    private String step = "/admin/sop/step";
                    private String industry = "/admin/sop/industry";
                }
                
                @Data
                public static class Task {
                    private String info = "/admin/task/info";
                    private String packagePath = "/admin/task/package";
                    private String assignment = "/admin/task/assignment";
                    private String status = "/admin/task/status";
                }
                
                @Data
                public static class Analytics {
                    private String stats = "/admin/analytics/stats";
                }
            }
        }
    }
}
