package com.cool.mcp.constant;

/**
 * API接口地址常量
 * 集中管理所有Cool Admin API接口地址
 */
public class ApiEndpoints {
    
    /**
     * SOP相关接口
     */
    public static class SOP {
        public static final String BASE = "/admin/sop";
        
        // SOP场景接口
        public static final String SCENARIO_PAGE = BASE + "/scenario/page";
        public static final String SCENARIO_INFO = BASE + "/scenario/info";
        public static final String SCENARIO_ADD = BASE + "/scenario/add";
        public static final String SCENARIO_UPDATE = BASE + "/scenario/update";
        public static final String SCENARIO_DELETE = BASE + "/scenario/delete";
        public static final String SCENARIO_STATS = BASE + "/scenario/stats";
        
        // SOP步骤接口
        public static final String STEP_PAGE = BASE + "/step/page";
        public static final String STEP_INFO = BASE + "/step/info";
        public static final String STEP_ADD = BASE + "/step/add";
        public static final String STEP_UPDATE = BASE + "/step/update";
        public static final String STEP_DELETE = BASE + "/step/delete";
        
        // SOP行业接口
        public static final String INDUSTRY_LIST = BASE + "/industry/list";
        public static final String INDUSTRY_ACTIVE = BASE + "/industry/active";
    }
    
    /**
     * 任务相关接口
     */
    public static class TASK {
        public static final String BASE = "/admin/task";
        
        // 任务信息接口
        public static final String INFO_PAGE = BASE + "/info/page";
        public static final String INFO_INFO = BASE + "/info/info";
        public static final String INFO_ADD = BASE + "/info/add";
        public static final String INFO_UPDATE = BASE + "/info/update";
        public static final String INFO_DELETE = BASE + "/info/delete";
        public static final String INFO_START = BASE + "/info/start-task";
        
        // 任务包接口
        public static final String PACKAGE_PAGE = BASE + "/package/page";
        public static final String PACKAGE_INFO = BASE + "/package/info";
        public static final String PACKAGE_ADD = BASE + "/package/add";
        public static final String PACKAGE_UPDATE = BASE + "/package/update";
        public static final String PACKAGE_DELETE = BASE + "/package/delete";
        public static final String PACKAGE_COMPLETE = BASE + "/package/complete";
        
        // 任务分配接口
        public static final String ASSIGNMENT_EXECUTE = BASE + "/assignment/execute";
        public static final String ASSIGNMENT_CANCEL = BASE + "/assignment/cancel";
        
        // 任务状态接口
        public static final String STATUS_COMPLETE = BASE + "/status/task/execution/complete";
        public static final String STATUS_PAUSE = BASE + "/status/task/execution/pause";
        public static final String STATUS_RESUME = BASE + "/status/task/execution/resume";
    }
    
    /**
     * 统计分析接口
     */
    public static class ANALYTICS {
        public static final String BASE = "/admin/analytics";
        
        public static final String STATS = BASE + "/stats";
        public static final String TASK_STATS = BASE + "/task/stats";
        public static final String SOP_STATS = BASE + "/sop/stats";
        public static final String PERFORMANCE_STATS = BASE + "/performance/stats";
        public static final String OVERVIEW_STATS = BASE + "/overview/stats";
    }
    
    /**
     * 用户相关接口
     */
    public static class USER {
        public static final String BASE = "/admin/user";
        
        public static final String LIST = BASE + "/list";
        public static final String INFO = BASE + "/info";
        public static final String ADD = BASE + "/add";
        public static final String UPDATE = BASE + "/update";
        public static final String DELETE = BASE + "/delete";
    }
    
    /**
     * 系统相关接口
     */
    public static class SYSTEM {
        public static final String BASE = "/admin/system";
        
        public static final String HEALTH = BASE + "/health";
        public static final String INFO = BASE + "/info";
        public static final String CONFIG = BASE + "/config";
    }
    

}
