package com.cool.mcp.controller;

import com.cool.mcp.config.McpServerConfig.McpProperties;
import com.cool.mcp.model.McpMessage;
import com.cool.mcp.service.ConnectionManager;
import com.cool.mcp.service.ToolRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * SSE控制器
 * 处理MCP协议的SSE连接和消息传输
 */
@Slf4j
@RestController
@RequestMapping("/mcp")
@RequiredArgsConstructor
public class SseController {

    private final ConnectionManager connectionManager;
    private final ToolRegistry toolRegistry;
    private final McpProperties mcpProperties;

    /**
     * 建立SSE连接
     */
    @GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> connect(
            @RequestParam(required = false) String clientId) {
        
        log.info("新的SSE连接请求, clientId: {}", clientId);
        
        return connectionManager.createConnection(clientId)
            .map(message -> ServerSentEvent.<String>builder()
                .event(message.getType())
                .data(connectionManager.serializeMessage(message))
                .build())
            .doOnSubscribe(subscription -> log.info("SSE连接已建立: {}", clientId))
            .doOnCancel(() -> {
                log.info("SSE连接已取消: {}", clientId);
                connectionManager.removeConnection(clientId);
            })
            .doOnError(error -> {
                log.error("SSE连接错误: {}, 错误: {}", clientId, error.getMessage());
                connectionManager.removeConnection(clientId);
            });
    }

    /**
     * 调用MCP工具
     */
    @PostMapping("/call")
    public Mono<Map<String, Object>> callTool(@RequestBody Map<String, Object> request) {
        String tool = (String) request.get("tool");
        @SuppressWarnings("unchecked")
        Map<String, Object> params = (Map<String, Object>) request.get("params");
        String clientId = (String) request.get("clientId");
        
        log.info("工具调用请求: tool={}, clientId={}", tool, clientId);
        
        return toolRegistry.callTool(tool, params, clientId)
            .doOnSuccess(result -> log.info("工具调用成功: tool={}", tool))
            .doOnError(error -> log.error("工具调用失败: tool={}, 错误: {}", tool, error.getMessage()));
    }

    /**
     * 获取可用工具列表
     */
    @GetMapping("/tools")
    public Mono<Map<String, Object>> getTools() {
        return Mono.fromSupplier(() -> {
            Map<String, Object> response = Map.of(
                "tools", toolRegistry.getAvailableTools(),
                "count", toolRegistry.getToolCount(),
                "server", Map.of(
                    "name", mcpProperties.getServer().getName(),
                    "version", mcpProperties.getServer().getVersion(),
                    "description", mcpProperties.getServer().getDescription()
                )
            );
            log.info("返回工具列表, 共{}个工具", toolRegistry.getToolCount());
            return response;
        });
    }

    /**
     * 获取连接状态
     */
    @GetMapping("/status")
    public Mono<Map<String, Object>> getStatus() {
        return Mono.fromSupplier(() -> {
            Map<String, Object> status = Map.of(
                "activeConnections", connectionManager.getActiveConnectionCount(),
                "maxConnections", mcpProperties.getServer().getSse().getMaxConnections(),
                "serverStatus", "healthy",
                "uptime", connectionManager.getUptime()
            );
            log.debug("返回服务器状态: {}", status);
            return status;
        });
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Mono<Map<String, Object>> health() {
        return Mono.fromSupplier(() -> {
            boolean isHealthy = connectionManager.getActiveConnectionCount() 
                < mcpProperties.getServer().getSse().getMaxConnections();
            
            return Map.of(
                "status", isHealthy ? "UP" : "DOWN",
                "timestamp", System.currentTimeMillis(),
                "details", Map.of(
                    "connections", connectionManager.getActiveConnectionCount(),
                    "tools", toolRegistry.getToolCount(),
                    "memory", Runtime.getRuntime().freeMemory()
                )
            );
        });
    }
}
