package com.cool.mcp.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP消息基类
 * 定义MCP协议的基础消息格式
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = McpToolCall.class, name = "tool_call"),
    @JsonSubTypes.Type(value = McpToolResult.class, name = "tool_result"),
    @JsonSubTypes.Type(value = McpError.class, name = "error"),
    @JsonSubTypes.Type(value = McpHeartbeat.class, name = "heartbeat"),
    @JsonSubTypes.Type(value = McpConnection.class, name = "connection")
})
public abstract class McpMessage {
    
    /**
     * 消息类型
     */
    private String type;
    
    /**
     * 消息ID
     */
    private String id;
    
    /**
     * 客户端ID
     */
    private String clientId;
    
    /**
     * 时间戳
     */
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 额外数据
     */
    private Map<String, Object> metadata;
    
    protected McpMessage(String type) {
        this.type = type;
    }
}

/**
 * 工具调用消息
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
class McpToolCall extends McpMessage {
    
    /**
     * 工具名称
     */
    private String tool;
    
    /**
     * 工具参数
     */
    private Map<String, Object> params;
    
    public McpToolCall() {
        super("tool_call");
    }
}

/**
 * 工具结果消息
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
class McpToolResult extends McpMessage {
    
    /**
     * 工具名称
     */
    private String tool;
    
    /**
     * 执行结果
     */
    private Object result;
    
    /**
     * 执行状态
     */
    private String status = "success";
    
    /**
     * 执行时间(毫秒)
     */
    private Long executionTime;
    
    public McpToolResult() {
        super("tool_result");
    }
}

/**
 * 错误消息
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
class McpError extends McpMessage {
    
    /**
     * 错误代码
     */
    private String code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 错误详情
     */
    private Object details;
    
    public McpError() {
        super("error");
    }
}

/**
 * 心跳消息
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
class McpHeartbeat extends McpMessage {
    
    /**
     * 活跃连接数
     */
    private Integer activeConnections;
    
    /**
     * 服务器状态
     */
    private String serverStatus = "healthy";
    
    public McpHeartbeat() {
        super("heartbeat");
    }
}

/**
 * 连接消息
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
class McpConnection extends McpMessage {
    
    /**
     * 连接状态
     */
    private String status;
    
    /**
     * 可用工具列表
     */
    private Object availableTools;
    
    /**
     * 服务器信息
     */
    private Object serverInfo;
    
    public McpConnection() {
        super("connection");
    }
}
