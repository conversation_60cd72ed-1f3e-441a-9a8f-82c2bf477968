package com.cool.mcp.service;

import com.cool.mcp.config.McpServerConfig.McpProperties;
import com.cool.mcp.model.McpMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 连接管理器
 * 管理SSE连接的生命周期和消息传输
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConnectionManager {

    private final McpProperties mcpProperties;
    private final ObjectMapper objectMapper;
    private final ToolRegistry toolRegistry;
    
    // 存储活跃连接
    private final Map<String, Sinks.Many<McpMessage>> connections = new ConcurrentHashMap<>();
    
    // 服务器启动时间
    private final LocalDateTime startTime = LocalDateTime.now();

    /**
     * 创建新的SSE连接
     */
    public Flux<McpMessage> createConnection(String clientId) {
        // 生成客户端ID
        if (clientId == null || clientId.trim().isEmpty()) {
            clientId = "client_" + UUID.randomUUID().toString().substring(0, 8);
        }
        
        // 检查连接数限制
        if (connections.size() >= mcpProperties.getServer().getSse().getMaxConnections()) {
            log.warn("连接数已达上限: {}", mcpProperties.getServer().getSse().getMaxConnections());
            return Flux.error(new RuntimeException("连接数已达上限"));
        }
        
        // 创建消息流
        Sinks.Many<McpMessage> sink = Sinks.many().multicast().onBackpressureBuffer();
        connections.put(clientId, sink);
        
        final String finalClientId = clientId;
        
        // 发送连接成功消息
        McpMessage connectionMessage = createConnectionMessage(finalClientId);
        sink.tryEmitNext(connectionMessage);
        
        // 创建心跳流
        Flux<McpMessage> heartbeatFlux = createHeartbeatFlux(finalClientId);
        
        // 合并消息流和心跳流
        return Flux.merge(
            sink.asFlux(),
            heartbeatFlux
        )
        .doOnCancel(() -> removeConnection(finalClientId))
        .doOnError(error -> {
            log.error("连接错误: {}, 错误: {}", finalClientId, error.getMessage());
            removeConnection(finalClientId);
        });
    }

    /**
     * 移除连接
     */
    public void removeConnection(String clientId) {
        if (clientId != null) {
            Sinks.Many<McpMessage> sink = connections.remove(clientId);
            if (sink != null) {
                sink.tryEmitComplete();
                log.info("连接已移除: {}", clientId);
            }
        }
    }

    /**
     * 向指定客户端发送消息
     */
    public void sendMessage(String clientId, McpMessage message) {
        Sinks.Many<McpMessage> sink = connections.get(clientId);
        if (sink != null) {
            message.setClientId(clientId);
            sink.tryEmitNext(message);
        } else {
            log.warn("客户端连接不存在: {}", clientId);
        }
    }

    /**
     * 广播消息给所有连接
     */
    public void broadcast(McpMessage message) {
        connections.forEach((clientId, sink) -> {
            McpMessage clientMessage = cloneMessage(message);
            clientMessage.setClientId(clientId);
            sink.tryEmitNext(clientMessage);
        });
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return connections.size();
    }

    /**
     * 获取服务器运行时间
     */
    public Duration getUptime() {
        return Duration.between(startTime, LocalDateTime.now());
    }

    /**
     * 序列化消息为JSON字符串
     */
    public String serializeMessage(McpMessage message) {
        try {
            return objectMapper.writeValueAsString(message);
        } catch (JsonProcessingException e) {
            log.error("消息序列化失败: {}", e.getMessage());
            return "{\"type\":\"error\",\"message\":\"序列化失败\"}";
        }
    }

    /**
     * 创建连接消息
     */
    private McpMessage createConnectionMessage(String clientId) {
        return new McpMessage("connection") {}
            .setClientId(clientId)
            .setId(UUID.randomUUID().toString())
            .setMetadata(Map.of(
                "status", "connected",
                "message", "SSE连接已建立",
                "availableTools", toolRegistry.getAvailableTools(),
                "serverInfo", Map.of(
                    "name", mcpProperties.getServer().getName(),
                    "version", mcpProperties.getServer().getVersion(),
                    "description", mcpProperties.getServer().getDescription()
                )
            ));
    }

    /**
     * 创建心跳流
     */
    private Flux<McpMessage> createHeartbeatFlux(String clientId) {
        return Flux.interval(Duration.ofMillis(mcpProperties.getServer().getSse().getHeartbeatInterval()))
            .map(tick -> new McpMessage("heartbeat") {}
                .setClientId(clientId)
                .setId(UUID.randomUUID().toString())
                .setMetadata(Map.of(
                    "activeConnections", getActiveConnectionCount(),
                    "serverStatus", "healthy",
                    "tick", tick
                )));
    }

    /**
     * 克隆消息
     */
    private McpMessage cloneMessage(McpMessage original) {
        try {
            String json = objectMapper.writeValueAsString(original);
            return objectMapper.readValue(json, McpMessage.class);
        } catch (JsonProcessingException e) {
            log.error("消息克隆失败: {}", e.getMessage());
            return original; // 返回原始消息作为fallback
        }
    }
}
