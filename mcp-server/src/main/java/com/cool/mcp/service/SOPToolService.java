package com.cool.mcp.service;

import com.cool.mcp.client.CoolAdminClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * SOP工具服务
 * 提供SOP场景、步骤、行业管理的MCP工具
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SOPToolService {

    private final ToolRegistry toolRegistry;
    private final CoolAdminClient coolAdminClient;

    @PostConstruct
    public void registerTools() {
        // 注册SOP场景相关工具
        toolRegistry.registerTool("sop_scenario_list", "获取SOP场景列表，支持分页和多条件筛选", "SOP管理", this::sopScenarioList);
        toolRegistry.registerTool("sop_scenario_get", "根据ID获取SOP场景详细信息", "SOP管理", this::sopScenarioGet);
        toolRegistry.registerTool("sop_scenario_create", "创建新的SOP场景", "SOP管理", this::sopScenarioCreate);
        toolRegistry.registerTool("sop_scenario_update", "更新SOP场景信息", "SOP管理", this::sopScenarioUpdate);
        
        // 注册SOP步骤相关工具
        toolRegistry.registerTool("sop_step_list", "获取SOP步骤列表", "SOP管理", this::sopStepList);
        toolRegistry.registerTool("sop_step_get", "根据ID获取SOP步骤详细信息", "SOP管理", this::sopStepGet);
        
        // 注册SOP行业相关工具
        toolRegistry.registerTool("sop_industry_list", "获取SOP行业列表", "SOP管理", this::sopIndustryList);
        
        log.info("SOP工具服务已注册 7 个工具");
    }

    /**
     * 获取SOP场景列表
     */
    private Mono<Object> sopScenarioList(Map<String, Object> params) {
        Map<String, Object> requestParams = new HashMap<>();
        
        // 分页参数
        requestParams.put("page", params.getOrDefault("page", 1));
        requestParams.put("size", params.getOrDefault("size", 20));
        
        // 筛选参数
        if (params.containsKey("keyword")) {
            requestParams.put("keyword", params.get("keyword"));
        }
        if (params.containsKey("industryCode")) {
            requestParams.put("industryCode", params.get("industryCode"));
        }
        if (params.containsKey("phaseCode")) {
            requestParams.put("phaseCode", params.get("phaseCode"));
        }
        if (params.containsKey("status")) {
            requestParams.put("status", params.get("status"));
        }
        
        return coolAdminClient.getSopScenarios(requestParams)
            .doOnSuccess(result -> log.debug("获取SOP场景列表成功"))
            .onErrorResume(error -> {
                log.error("获取SOP场景列表失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取SOP场景列表失败: " + error.getMessage()));
            });
    }

    /**
     * 获取SOP场景详情
     */
    private Mono<Object> sopScenarioGet(Map<String, Object> params) {
        Object idObj = params.get("id");
        if (idObj == null) {
            return Mono.just(createErrorResult("场景ID不能为空"));
        }
        
        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return Mono.just(createErrorResult("场景ID格式错误"));
        }
        
        return coolAdminClient.getSopScenario(id)
            .doOnSuccess(result -> log.debug("获取SOP场景详情成功: {}", id))
            .onErrorResume(error -> {
                log.error("获取SOP场景详情失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取SOP场景详情失败: " + error.getMessage()));
            });
    }

    /**
     * 创建SOP场景
     */
    private Mono<Object> sopScenarioCreate(Map<String, Object> params) {
        // 验证必需参数
        if (!params.containsKey("name") || params.get("name") == null || 
            params.get("name").toString().trim().isEmpty()) {
            return Mono.just(createErrorResult("场景名称不能为空"));
        }
        
        Map<String, Object> scenario = new HashMap<>();
        scenario.put("name", params.get("name"));
        
        // 可选参数
        if (params.containsKey("description")) {
            scenario.put("description", params.get("description"));
        }
        if (params.containsKey("industryCode")) {
            scenario.put("industryCode", params.get("industryCode"));
        }
        if (params.containsKey("phaseCode")) {
            scenario.put("phaseCode", params.get("phaseCode"));
        }
        if (params.containsKey("executionCycle")) {
            scenario.put("executionCycle", params.get("executionCycle"));
        }
        if (params.containsKey("totalSteps")) {
            scenario.put("totalSteps", params.get("totalSteps"));
        }
        
        return coolAdminClient.createSopScenario(scenario)
            .doOnSuccess(result -> log.info("创建SOP场景成功: {}", params.get("name")))
            .onErrorResume(error -> {
                log.error("创建SOP场景失败: {}", error.getMessage());
                return Mono.just(createErrorResult("创建SOP场景失败: " + error.getMessage()));
            });
    }

    /**
     * 更新SOP场景
     */
    private Mono<Object> sopScenarioUpdate(Map<String, Object> params) {
        Object idObj = params.get("id");
        if (idObj == null) {
            return Mono.just(createErrorResult("场景ID不能为空"));
        }
        
        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return Mono.just(createErrorResult("场景ID格式错误"));
        }
        
        Map<String, Object> scenario = new HashMap<>(params);
        scenario.put("id", id);
        
        return coolAdminClient.updateSopScenario(scenario)
            .doOnSuccess(result -> log.info("更新SOP场景成功: {}", id))
            .onErrorResume(error -> {
                log.error("更新SOP场景失败: {}", error.getMessage());
                return Mono.just(createErrorResult("更新SOP场景失败: " + error.getMessage()));
            });
    }

    /**
     * 获取SOP步骤列表
     */
    private Mono<Object> sopStepList(Map<String, Object> params) {
        Map<String, Object> requestParams = new HashMap<>();
        
        // 分页参数
        requestParams.put("page", params.getOrDefault("page", 1));
        requestParams.put("size", params.getOrDefault("size", 20));
        
        // 筛选参数
        if (params.containsKey("sopId")) {
            requestParams.put("sopId", params.get("sopId"));
        }
        if (params.containsKey("keyword")) {
            requestParams.put("keyword", params.get("keyword"));
        }
        
        return coolAdminClient.getSopSteps(requestParams)
            .doOnSuccess(result -> log.debug("获取SOP步骤列表成功"))
            .onErrorResume(error -> {
                log.error("获取SOP步骤列表失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取SOP步骤列表失败: " + error.getMessage()));
            });
    }

    /**
     * 获取SOP步骤详情
     */
    private Mono<Object> sopStepGet(Map<String, Object> params) {
        Object idObj = params.get("id");
        if (idObj == null) {
            return Mono.just(createErrorResult("步骤ID不能为空"));
        }
        
        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return Mono.just(createErrorResult("步骤ID格式错误"));
        }
        
        return coolAdminClient.getSopStep(id)
            .doOnSuccess(result -> log.debug("获取SOP步骤详情成功: {}", id))
            .onErrorResume(error -> {
                log.error("获取SOP步骤详情失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取SOP步骤详情失败: " + error.getMessage()));
            });
    }

    /**
     * 获取SOP行业列表
     */
    private Mono<Object> sopIndustryList(Map<String, Object> params) {
        return coolAdminClient.getSopIndustries()
            .doOnSuccess(result -> log.debug("获取SOP行业列表成功"))
            .onErrorResume(error -> {
                log.error("获取SOP行业列表失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取SOP行业列表失败: " + error.getMessage()));
            });
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String message) {
        return Map.of(
            "code", 500,
            "message", message,
            "data", null
        );
    }
}
