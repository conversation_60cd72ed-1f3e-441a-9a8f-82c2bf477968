package com.cool.mcp.service;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.Map;

/**
 * SOP工具服务
 * 使用@Tool注解简洁定义MCP工具
 */
@Service
public class SOPToolService {

    @Autowired
    private WebClient webClient;

    /**
     * 获取SOP场景列表
     */
    @Tool(name = "sop_scenario_list", description = "获取SOP场景列表，支持分页和多条件筛选")
    public Object sopScenarioList(
        Integer page,
        Integer size,
        String keyword,
        String industryCode,
        String phaseCode
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page != null ? page : 1);
        params.put("size", size != null ? size : 20);
        if (keyword != null) params.put("keyword", keyword);
        if (industryCode != null) params.put("industryCode", industryCode);
        if (phaseCode != null) params.put("phaseCode", phaseCode);

        return webClient.post()
            .uri("http://localhost:18001/admin/sop/scenario/page")
            .bodyValue(params)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 获取SOP场景详情
     */
    @Tool(name = "sop_scenario_get", description = "根据ID获取SOP场景详细信息")
    public Object sopScenarioGet(Long id) {
        if (id == null) {
            return Map.of("code", 500, "message", "场景ID不能为空");
        }

        return webClient.get()
            .uri("http://localhost:18001/admin/sop/scenario/info?id={id}", id)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 获取SOP场景统计
     */
    @Tool(name = "sop_scenario_stats", description = "获取SOP场景统计信息")
    public Object sopScenarioStats(String industryCode) {
        String uri = "http://localhost:18001/admin/sop/scenario/stats";
        if (industryCode != null) {
            uri += "?industryCode=" + industryCode;
        }

        return webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 获取SOP步骤列表
     */
    @Tool(name = "sop_step_list", description = "获取SOP步骤列表")
    public Object sopStepList(
        Integer page,
        Integer size,
        Long sopId,
        String keyword
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page != null ? page : 1);
        params.put("size", size != null ? size : 20);
        if (sopId != null) params.put("sopId", sopId);
        if (keyword != null) params.put("keyword", keyword);

        return webClient.post()
            .uri("http://localhost:18001/admin/sop/step/page")
            .bodyValue(params)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 获取SOP步骤详情
     */
    @Tool(name = "sop_step_get", description = "根据ID获取SOP步骤详细信息")
    public Object sopStepGet(Long id) {
        if (id == null) {
            return Map.of("code", 500, "message", "步骤ID不能为空");
        }

        return webClient.get()
            .uri("http://localhost:18001/admin/sop/step/info?id={id}", id)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 获取SOP行业列表
     */
    @Tool(name = "sop_industry_list", description = "获取SOP行业列表")
    public Object sopIndustryList(Boolean activeOnly) {
        String uri = "http://localhost:18001/admin/sop/industry/list";
        if (activeOnly != null && activeOnly) {
            uri = "http://localhost:18001/admin/sop/industry/active";
        }

        return webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }
}
