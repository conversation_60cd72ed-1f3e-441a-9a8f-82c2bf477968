package com.cool.mcp.service;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务工具服务
 * 使用@Tool注解简洁定义任务和任务包管理的MCP工具
 */
@Service
public class TaskToolService {

    @Autowired
    private WebClient webClient;

    // ==================== 任务管理工具 ====================

    /**
     * 获取任务列表
     */
    @Tool(name = "task_list", description = "获取任务列表，支持分页和多条件筛选")
    public Object taskList(
        Integer page,
        Integer size,
        String keyword,
        Integer taskStatus,
        String taskCategory,
        Integer priority,
        Long scenarioId,
        Long packageId
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page != null ? page : 1);
        params.put("size", size != null ? size : 20);
        if (keyword != null) params.put("keyword", keyword);
        if (taskStatus != null) params.put("taskStatus", taskStatus);
        if (taskCategory != null) params.put("taskCategory", taskCategory);
        if (priority != null) params.put("priority", priority);
        if (scenarioId != null) params.put("scenarioId", scenarioId);
        if (packageId != null) params.put("packageId", packageId);

        return webClient.post()
            .uri("http://localhost:18001/admin/task/info/page")
            .bodyValue(params)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 获取任务详情
     */
    @Tool(name = "task_get", description = "根据ID获取任务详细信息")
    public Object taskGet(Long id) {
        if (id == null) {
            return Map.of("code", 500, "message", "任务ID不能为空");
        }

        return webClient.get()
            .uri("http://localhost:18001/admin/task/info/info?id={id}", id)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 创建任务
     */
    @Tool(name = "task_create", description = "创建新任务")
    public Object taskCreate(
        String name,
        String description,
        Integer priority,
        String taskCategory,
        Long scenarioId,
        Long packageId
    ) {
        if (name == null || name.trim().isEmpty()) {
            return Map.of("code", 500, "message", "任务名称不能为空");
        }

        Map<String, Object> task = new HashMap<>();
        task.put("name", name);
        if (description != null) task.put("description", description);
        task.put("priority", priority != null ? priority : 1);
        task.put("taskCategory", taskCategory != null ? taskCategory : "AD_HOC");
        if (scenarioId != null) task.put("scenarioId", scenarioId);
        if (packageId != null) task.put("packageId", packageId);
        task.put("taskStatus", 0); // 待分配

        return webClient.post()
            .uri("http://localhost:18001/admin/task/info/add")
            .bodyValue(task)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 更新任务
     */
    @Tool(name = "task_update", description = "更新任务信息")
    public Object taskUpdate(
        Long id,
        String name,
        String description,
        Integer priority,
        Integer taskStatus,
        String taskCategory
    ) {
        if (id == null) {
            return Map.of("code", 500, "message", "任务ID不能为空");
        }

        Map<String, Object> task = new HashMap<>();
        task.put("id", id);
        if (name != null) task.put("name", name);
        if (description != null) task.put("description", description);
        if (priority != null) task.put("priority", priority);
        if (taskStatus != null) task.put("taskStatus", taskStatus);
        if (taskCategory != null) task.put("taskCategory", taskCategory);

        return webClient.post()
            .uri("http://localhost:18001/admin/task/info/update")
            .bodyValue(task)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 分配任务
     */
    @Tool(name = "task_assign", description = "分配任务给指定用户")
    public Object taskAssign(Long taskId, Long assigneeId, String assignReason) {
        if (taskId == null) {
            return Map.of("code", 500, "message", "任务ID不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("taskId", taskId);
        params.put("assigneeId", assigneeId);
        if (assignReason != null) params.put("assignReason", assignReason);

        return webClient.post()
            .uri("http://localhost:18001/admin/task/assignment/execute")
            .bodyValue(params)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 开始任务
     */
    @Tool(name = "task_start", description = "开始执行任务")
    public Object taskStart(Long taskId, Long assigneeId) {
        if (taskId == null) {
            return Map.of("code", 500, "message", "任务ID不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("taskId", taskId);
        params.put("assigneeId", assigneeId);

        return webClient.post()
            .uri("http://localhost:18001/admin/task/info/start-task")
            .bodyValue(params)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 完成任务
     */
    @Tool(name = "task_complete", description = "完成任务")
    public Object taskComplete(Long taskId, String completeNote) {
        if (taskId == null) {
            return Map.of("code", 500, "message", "任务ID不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("taskId", taskId);
        if (completeNote != null) params.put("completeNote", completeNote);

        return webClient.post()
            .uri("http://localhost:18001/admin/task/status/task/execution/complete")
            .bodyValue(params)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    // ==================== 任务包管理工具 ====================

    /**
     * 获取任务包列表
     */
    @Tool(name = "task_package_list", description = "获取任务包列表")
    public Object taskPackageList(
        Integer page,
        Integer size,
        String keyword,
        Long scenarioId,
        Integer status
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page != null ? page : 1);
        params.put("size", size != null ? size : 20);
        if (keyword != null) params.put("keyword", keyword);
        if (scenarioId != null) params.put("scenarioId", scenarioId);
        if (status != null) params.put("status", status);

        return webClient.post()
            .uri("http://localhost:18001/admin/task/package/page")
            .bodyValue(params)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 获取任务包详情
     */
    @Tool(name = "task_package_get", description = "根据ID获取任务包详细信息")
    public Object taskPackageGet(Long id) {
        if (id == null) {
            return Map.of("code", 500, "message", "任务包ID不能为空");
        }

        return webClient.get()
            .uri("http://localhost:18001/admin/task/package/info?id={id}", id)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 创建任务包
     */
    @Tool(name = "task_package_create", description = "创建新任务包")
    public Object taskPackageCreate(
        String name,
        String description,
        Long scenarioId,
        Integer priority
    ) {
        if (name == null || name.trim().isEmpty()) {
            return Map.of("code", 500, "message", "任务包名称不能为空");
        }

        Map<String, Object> taskPackage = new HashMap<>();
        taskPackage.put("name", name);
        if (description != null) taskPackage.put("description", description);
        if (scenarioId != null) taskPackage.put("scenarioId", scenarioId);
        taskPackage.put("priority", priority != null ? priority : 1);
        taskPackage.put("status", 0); // 待开始

        return webClient.post()
            .uri("http://localhost:18001/admin/task/package/add")
            .bodyValue(taskPackage)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 更新任务包
     */
    @Tool(name = "task_package_update", description = "更新任务包信息")
    public Object taskPackageUpdate(
        Long id,
        String name,
        String description,
        Integer status,
        Integer priority
    ) {
        if (id == null) {
            return Map.of("code", 500, "message", "任务包ID不能为空");
        }

        Map<String, Object> taskPackage = new HashMap<>();
        taskPackage.put("id", id);
        if (name != null) taskPackage.put("name", name);
        if (description != null) taskPackage.put("description", description);
        if (status != null) taskPackage.put("status", status);
        if (priority != null) taskPackage.put("priority", priority);

        return webClient.post()
            .uri("http://localhost:18001/admin/task/package/update")
            .bodyValue(taskPackage)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * 完成任务包
     */
    @Tool(name = "task_package_complete", description = "完成任务包")
    public Object taskPackageComplete(Long packageId) {
        if (packageId == null) {
            return Map.of("code", 500, "message", "任务包ID不能为空");
        }

        return webClient.post()
            .uri("http://localhost:18001/admin/task/package/complete/{packageId}", packageId)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }
}
