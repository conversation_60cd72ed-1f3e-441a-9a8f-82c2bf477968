package com.cool.mcp.service;

import com.cool.mcp.client.CoolAdminClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务工具服务
 * 提供任务和任务包管理的MCP工具
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskToolService {

    private final ToolRegistry toolRegistry;
    private final CoolAdminClient coolAdminClient;

    @PostConstruct
    public void registerTools() {
        // 注册任务相关工具
        toolRegistry.registerTool("task_list", "获取任务列表，支持分页和多条件筛选", "任务管理", this::taskList);
        toolRegistry.registerTool("task_get", "根据ID获取任务详细信息", "任务管理", this::taskGet);
        toolRegistry.registerTool("task_create", "创建新任务", "任务管理", this::taskCreate);
        toolRegistry.registerTool("task_update", "更新任务信息", "任务管理", this::taskUpdate);
        
        // 注册任务包相关工具
        toolRegistry.registerTool("task_package_list", "获取任务包列表", "任务包管理", this::taskPackageList);
        toolRegistry.registerTool("task_package_get", "根据ID获取任务包详细信息", "任务包管理", this::taskPackageGet);
        toolRegistry.registerTool("task_package_create", "创建新任务包", "任务包管理", this::taskPackageCreate);
        toolRegistry.registerTool("task_package_update", "更新任务包信息", "任务包管理", this::taskPackageUpdate);
        
        log.info("任务工具服务已注册 8 个工具");
    }

    /**
     * 获取任务列表
     */
    private Mono<Object> taskList(Map<String, Object> params) {
        Map<String, Object> requestParams = new HashMap<>();
        
        // 分页参数
        requestParams.put("page", params.getOrDefault("page", 1));
        requestParams.put("size", params.getOrDefault("size", 20));
        
        // 筛选参数
        if (params.containsKey("keyword")) {
            requestParams.put("keyword", params.get("keyword"));
        }
        if (params.containsKey("taskStatus")) {
            requestParams.put("taskStatus", params.get("taskStatus"));
        }
        if (params.containsKey("taskCategory")) {
            requestParams.put("taskCategory", params.get("taskCategory"));
        }
        if (params.containsKey("priority")) {
            requestParams.put("priority", params.get("priority"));
        }
        if (params.containsKey("scenarioId")) {
            requestParams.put("scenarioId", params.get("scenarioId"));
        }
        if (params.containsKey("packageId")) {
            requestParams.put("packageId", params.get("packageId"));
        }
        
        return coolAdminClient.getTasks(requestParams)
            .doOnSuccess(result -> log.debug("获取任务列表成功"))
            .onErrorResume(error -> {
                log.error("获取任务列表失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取任务列表失败: " + error.getMessage()));
            });
    }

    /**
     * 获取任务详情
     */
    private Mono<Object> taskGet(Map<String, Object> params) {
        Object idObj = params.get("id");
        if (idObj == null) {
            return Mono.just(createErrorResult("任务ID不能为空"));
        }
        
        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return Mono.just(createErrorResult("任务ID格式错误"));
        }
        
        return coolAdminClient.getTask(id)
            .doOnSuccess(result -> log.debug("获取任务详情成功: {}", id))
            .onErrorResume(error -> {
                log.error("获取任务详情失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取任务详情失败: " + error.getMessage()));
            });
    }

    /**
     * 创建任务
     */
    private Mono<Object> taskCreate(Map<String, Object> params) {
        // 验证必需参数
        if (!params.containsKey("name") || params.get("name") == null || 
            params.get("name").toString().trim().isEmpty()) {
            return Mono.just(createErrorResult("任务名称不能为空"));
        }
        
        Map<String, Object> task = new HashMap<>();
        task.put("name", params.get("name"));
        
        // 可选参数
        if (params.containsKey("description")) {
            task.put("description", params.get("description"));
        }
        if (params.containsKey("priority")) {
            task.put("priority", params.get("priority"));
        } else {
            task.put("priority", 1); // 默认优先级
        }
        if (params.containsKey("taskCategory")) {
            task.put("taskCategory", params.get("taskCategory"));
        } else {
            task.put("taskCategory", "AD_HOC"); // 默认类别
        }
        if (params.containsKey("scenarioId")) {
            task.put("scenarioId", params.get("scenarioId"));
        }
        if (params.containsKey("packageId")) {
            task.put("packageId", params.get("packageId"));
        }
        
        // 设置默认状态
        task.put("taskStatus", 0); // 待分配
        
        return coolAdminClient.createTask(task)
            .doOnSuccess(result -> log.info("创建任务成功: {}", params.get("name")))
            .onErrorResume(error -> {
                log.error("创建任务失败: {}", error.getMessage());
                return Mono.just(createErrorResult("创建任务失败: " + error.getMessage()));
            });
    }

    /**
     * 更新任务
     */
    private Mono<Object> taskUpdate(Map<String, Object> params) {
        Object idObj = params.get("id");
        if (idObj == null) {
            return Mono.just(createErrorResult("任务ID不能为空"));
        }
        
        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return Mono.just(createErrorResult("任务ID格式错误"));
        }
        
        Map<String, Object> task = new HashMap<>(params);
        task.put("id", id);
        
        return coolAdminClient.updateTask(task)
            .doOnSuccess(result -> log.info("更新任务成功: {}", id))
            .onErrorResume(error -> {
                log.error("更新任务失败: {}", error.getMessage());
                return Mono.just(createErrorResult("更新任务失败: " + error.getMessage()));
            });
    }

    /**
     * 获取任务包列表
     */
    private Mono<Object> taskPackageList(Map<String, Object> params) {
        Map<String, Object> requestParams = new HashMap<>();
        
        // 分页参数
        requestParams.put("page", params.getOrDefault("page", 1));
        requestParams.put("size", params.getOrDefault("size", 20));
        
        // 筛选参数
        if (params.containsKey("keyword")) {
            requestParams.put("keyword", params.get("keyword"));
        }
        if (params.containsKey("scenarioId")) {
            requestParams.put("scenarioId", params.get("scenarioId"));
        }
        if (params.containsKey("status")) {
            requestParams.put("status", params.get("status"));
        }
        
        return coolAdminClient.getTaskPackages(requestParams)
            .doOnSuccess(result -> log.debug("获取任务包列表成功"))
            .onErrorResume(error -> {
                log.error("获取任务包列表失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取任务包列表失败: " + error.getMessage()));
            });
    }

    /**
     * 获取任务包详情
     */
    private Mono<Object> taskPackageGet(Map<String, Object> params) {
        Object idObj = params.get("id");
        if (idObj == null) {
            return Mono.just(createErrorResult("任务包ID不能为空"));
        }
        
        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return Mono.just(createErrorResult("任务包ID格式错误"));
        }
        
        return coolAdminClient.getTaskPackage(id)
            .doOnSuccess(result -> log.debug("获取任务包详情成功: {}", id))
            .onErrorResume(error -> {
                log.error("获取任务包详情失败: {}", error.getMessage());
                return Mono.just(createErrorResult("获取任务包详情失败: " + error.getMessage()));
            });
    }

    /**
     * 创建任务包
     */
    private Mono<Object> taskPackageCreate(Map<String, Object> params) {
        // 验证必需参数
        if (!params.containsKey("name") || params.get("name") == null || 
            params.get("name").toString().trim().isEmpty()) {
            return Mono.just(createErrorResult("任务包名称不能为空"));
        }
        
        Map<String, Object> taskPackage = new HashMap<>();
        taskPackage.put("name", params.get("name"));
        
        // 可选参数
        if (params.containsKey("description")) {
            taskPackage.put("description", params.get("description"));
        }
        if (params.containsKey("scenarioId")) {
            taskPackage.put("scenarioId", params.get("scenarioId"));
        }
        if (params.containsKey("priority")) {
            taskPackage.put("priority", params.get("priority"));
        } else {
            taskPackage.put("priority", 1); // 默认优先级
        }
        
        // 设置默认状态
        taskPackage.put("status", 0); // 待开始
        
        return coolAdminClient.createTaskPackage(taskPackage)
            .doOnSuccess(result -> log.info("创建任务包成功: {}", params.get("name")))
            .onErrorResume(error -> {
                log.error("创建任务包失败: {}", error.getMessage());
                return Mono.just(createErrorResult("创建任务包失败: " + error.getMessage()));
            });
    }

    /**
     * 更新任务包
     */
    private Mono<Object> taskPackageUpdate(Map<String, Object> params) {
        Object idObj = params.get("id");
        if (idObj == null) {
            return Mono.just(createErrorResult("任务包ID不能为空"));
        }
        
        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return Mono.just(createErrorResult("任务包ID格式错误"));
        }
        
        Map<String, Object> taskPackage = new HashMap<>(params);
        taskPackage.put("id", id);
        
        return coolAdminClient.updateTaskPackage(taskPackage)
            .doOnSuccess(result -> log.info("更新任务包成功: {}", id))
            .onErrorResume(error -> {
                log.error("更新任务包失败: {}", error.getMessage());
                return Mono.just(createErrorResult("更新任务包失败: " + error.getMessage()));
            });
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String message) {
        return Map.of(
            "code", 500,
            "message", message,
            "data", null
        );
    }
}
