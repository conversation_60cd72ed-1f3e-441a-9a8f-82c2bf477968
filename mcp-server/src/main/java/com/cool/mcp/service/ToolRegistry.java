package com.cool.mcp.service;

import com.cool.mcp.model.McpMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 工具注册中心
 * 管理所有MCP工具的注册、调用和元数据
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToolRegistry {

    private final ConnectionManager connectionManager;
    
    // 工具注册表
    private final Map<String, ToolDefinition> tools = new ConcurrentHashMap<>();
    
    // 工具调用处理器
    private final Map<String, Function<Map<String, Object>, Mono<Object>>> toolHandlers = new ConcurrentHashMap<>();

    /**
     * 注册工具
     */
    public void registerTool(String name, String description, String category, 
                           Function<Map<String, Object>, Mono<Object>> handler) {
        ToolDefinition tool = new ToolDefinition()
            .setName(name)
            .setDescription(description)
            .setCategory(category)
            .setRegisteredAt(LocalDateTime.now());
            
        tools.put(name, tool);
        toolHandlers.put(name, handler);
        
        log.info("工具已注册: {} - {}", name, description);
    }

    /**
     * 调用工具
     */
    public Mono<Map<String, Object>> callTool(String toolName, Map<String, Object> params, String clientId) {
        long startTime = System.currentTimeMillis();
        
        // 发送工具调用开始消息
        sendToolCallStartMessage(toolName, params, clientId);
        
        Function<Map<String, Object>, Mono<Object>> handler = toolHandlers.get(toolName);
        if (handler == null) {
            String errorMsg = "工具不存在: " + toolName;
            sendToolCallErrorMessage(toolName, errorMsg, clientId);
            return Mono.just(createErrorResponse(errorMsg));
        }

        return handler.apply(params != null ? params : new HashMap<>())
            .map(result -> {
                long executionTime = System.currentTimeMillis() - startTime;
                
                // 发送工具调用结果消息
                sendToolCallResultMessage(toolName, result, executionTime, clientId);
                
                return createSuccessResponse(result, executionTime);
            })
            .onErrorResume(error -> {
                long executionTime = System.currentTimeMillis() - startTime;
                String errorMsg = "工具调用失败: " + error.getMessage();
                
                // 发送工具调用错误消息
                sendToolCallErrorMessage(toolName, errorMsg, clientId);
                
                return Mono.just(createErrorResponse(errorMsg, executionTime));
            });
    }

    /**
     * 获取可用工具列表
     */
    public List<Map<String, Object>> getAvailableTools() {
        return tools.values().stream()
            .map(tool -> Map.of(
                "name", tool.getName(),
                "description", tool.getDescription(),
                "category", tool.getCategory(),
                "registeredAt", tool.getRegisteredAt().toString()
            ))
            .sorted((a, b) -> String.valueOf(a.get("category")).compareTo(String.valueOf(b.get("category"))))
            .toList();
    }

    /**
     * 获取工具数量
     */
    public int getToolCount() {
        return tools.size();
    }

    /**
     * 获取工具分类统计
     */
    public Map<String, Long> getToolCategoryStats() {
        return tools.values().stream()
            .collect(java.util.stream.Collectors.groupingBy(
                ToolDefinition::getCategory,
                java.util.stream.Collectors.counting()
            ));
    }

    /**
     * 发送工具调用开始消息
     */
    private void sendToolCallStartMessage(String toolName, Map<String, Object> params, String clientId) {
        McpMessage message = new McpMessage("tool_call_start") {}
            .setId(UUID.randomUUID().toString())
            .setMetadata(Map.of(
                "tool", toolName,
                "params", params != null ? params : Map.of()
            ));
        
        if (clientId != null) {
            connectionManager.sendMessage(clientId, message);
        }
    }

    /**
     * 发送工具调用结果消息
     */
    private void sendToolCallResultMessage(String toolName, Object result, long executionTime, String clientId) {
        McpMessage message = new McpMessage("tool_call_result") {}
            .setId(UUID.randomUUID().toString())
            .setMetadata(Map.of(
                "tool", toolName,
                "result", result,
                "executionTime", executionTime,
                "status", "success"
            ));
        
        if (clientId != null) {
            connectionManager.sendMessage(clientId, message);
        }
    }

    /**
     * 发送工具调用错误消息
     */
    private void sendToolCallErrorMessage(String toolName, String error, String clientId) {
        McpMessage message = new McpMessage("tool_call_error") {}
            .setId(UUID.randomUUID().toString())
            .setMetadata(Map.of(
                "tool", toolName,
                "error", error,
                "status", "error"
            ));
        
        if (clientId != null) {
            connectionManager.sendMessage(clientId, message);
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object result, long executionTime) {
        return Map.of(
            "success", true,
            "data", result,
            "executionTime", executionTime,
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String error) {
        return createErrorResponse(error, 0L);
    }

    private Map<String, Object> createErrorResponse(String error, long executionTime) {
        return Map.of(
            "success", false,
            "error", error,
            "executionTime", executionTime,
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * 工具定义类
     */
    public static class ToolDefinition {
        private String name;
        private String description;
        private String category;
        private LocalDateTime registeredAt;

        // Getters and Setters
        public String getName() { return name; }
        public ToolDefinition setName(String name) { this.name = name; return this; }
        
        public String getDescription() { return description; }
        public ToolDefinition setDescription(String description) { this.description = description; return this; }
        
        public String getCategory() { return category; }
        public ToolDefinition setCategory(String category) { this.category = category; return this; }
        
        public LocalDateTime getRegisteredAt() { return registeredAt; }
        public ToolDefinition setRegisteredAt(LocalDateTime registeredAt) { this.registeredAt = registeredAt; return this; }
    }
}
