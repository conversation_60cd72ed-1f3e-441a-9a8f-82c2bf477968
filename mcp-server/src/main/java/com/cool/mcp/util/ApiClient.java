package com.cool.mcp.util;

import com.cool.mcp.config.CoolAdminProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;

/**
 * API客户端工具类
 * 提供统一的API调用方法
 */
@Component
public class ApiClient {

    @Autowired
    private WebClient webClient;

    @Autowired
    private CoolAdminProperties coolAdminProperties;

    /**
     * 构建完整URL
     */
    private String buildUrl(String endpoint) {
        return coolAdminProperties.getBaseUrl() + endpoint;
    }

    /**
     * GET请求 - 无参数
     */
    public Object get(String endpoint) {
        return webClient.get()
            .uri(buildUrl(endpoint))
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * GET请求 - 带单个参数
     */
    public Object get(String endpoint, String paramName, Object paramValue) {
        return webClient.get()
            .uri(buildUrl(endpoint) + "?" + paramName + "=" + paramValue)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * GET请求 - 带多个参数
     */
    public Object get(String endpoint, Map<String, Object> params) {
        return webClient.get()
            .uri(uriBuilder -> {
                var builder = uriBuilder.path(buildUrl(endpoint));
                if (params != null) {
                    params.forEach((key, value) -> {
                        if (value != null) {
                            builder.queryParam(key, value);
                        }
                    });
                }
                return builder.build();
            })
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * POST请求
     */
    public Object post(String endpoint, Object body) {
        return webClient.post()
            .uri(buildUrl(endpoint))
            .bodyValue(body != null ? body : Map.of())
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * PUT请求
     */
    public Object put(String endpoint, Object body) {
        return webClient.put()
            .uri(buildUrl(endpoint))
            .bodyValue(body != null ? body : Map.of())
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * DELETE请求
     */
    public Object delete(String endpoint) {
        return webClient.delete()
            .uri(buildUrl(endpoint))
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    /**
     * DELETE请求 - 带参数
     */
    public Object delete(String endpoint, String paramName, Object paramValue) {
        return webClient.delete()
            .uri(buildUrl(endpoint) + "?" + paramName + "=" + paramValue)
            .retrieve()
            .bodyToMono(Object.class)
            .block();
    }

    // ==================== 便捷方法 ====================

    /**
     * 分页查询
     */
    public Object page(String endpoint, Map<String, Object> params) {
        return post(endpoint, params);
    }

    /**
     * 根据ID获取详情
     */
    public Object getById(String endpoint, Long id) {
        if (id == null) {
            return Map.of("code", 500, "message", "ID不能为空");
        }
        return get(endpoint, "id", id);
    }

    /**
     * 添加数据
     */
    public Object add(String endpoint, Map<String, Object> data) {
        return post(endpoint, data);
    }

    /**
     * 更新数据
     */
    public Object update(String endpoint, Map<String, Object> data) {
        return post(endpoint, data);
    }

    /**
     * 删除数据
     */
    public Object deleteById(String endpoint, Long id) {
        if (id == null) {
            return Map.of("code", 500, "message", "ID不能为空");
        }
        return delete(endpoint, "id", id);
    }
}
