server:
  port: 18002
  servlet:
    context-path: /

spring:
  application:
    name: cool-admin-mcp-server
  
  # JSON配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # WebFlux配置
  webflux:
    base-path: /

# MCP服务器配置
mcp:
  server:
    name: "cool-admin-mcp-server"
    version: "1.0.0"
    description: "Cool Admin系统的SSE Stream MCP服务器"
    
    # SSE配置
    sse:
      endpoint: "/mcp/sse"
      heartbeat-interval: 30000  # 心跳间隔(毫秒)
      connection-timeout: 300000 # 连接超时(毫秒)
      max-connections: 100       # 最大连接数
      
    # 工具配置
    tools:
      enabled: true
      auto-register: true
      
  # Cool Admin API配置
  cool-admin:
    base-url: "http://localhost:18001"
    timeout: 30000
    retry-count: 3
    
    # API端点配置
    endpoints:
      sop:
        scenario: "/admin/sop/scenario"
        step: "/admin/sop/step"
        industry: "/admin/sop/industry"
      task:
        info: "/admin/task/info"
        package: "/admin/task/package"
        assignment: "/admin/task/assignment"
        status: "/admin/task/status"
      analytics:
        stats: "/admin/analytics/stats"

# 日志配置
logging:
  level:
    com.cool.mcp: DEBUG
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
