server:
  port: 18002

spring:
  application:
    name: cool-admin-mcp-server

  # JSON配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

  # Spring AI MCP配置
  ai:
    mcp:
      server:
        enabled: true
        type: ASYNC              # 异步模式
        sse-message-endpoint: mcp/sse
        stdio:
          enabled: false
        sse:
          enabled: true          # 启用SSE

# Cool Admin API配置
cool-admin:
  base-url: "http://localhost:18001"
  timeout: 30000
  retry-count: 3

# 日志配置
logging:
  level:
    com.cool.mcp: DEBUG
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
