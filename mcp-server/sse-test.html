<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cool Admin MCP Server 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .section-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            color: #333;
        }
        .section-content {
            padding: 15px;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .tool-card {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            background: #f8f9fa;
        }
        .tool-name {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .tool-category {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
        }
        .tool-description {
            font-size: 14px;
            color: #333;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info {
            color: #007bff;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warning {
            color: #ffc107;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group textarea, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .input-group textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Cool Admin MCP Server</h1>
            <p>SSE Stream MCP 测试页面</p>
        </div>
        
        <div class="content">
            <!-- 连接状态 -->
            <div class="section">
                <div class="section-header">
                    📡 连接状态
                </div>
                <div class="section-content">
                    <p>状态: <span id="connectionStatus" class="status disconnected">未连接</span></p>
                    <p>客户端ID: <span id="clientId">-</span></p>
                    <p>活跃连接数: <span id="activeConnections">-</span></p>
                    <button id="connectBtn" class="btn">连接</button>
                    <button id="disconnectBtn" class="btn danger">断开连接</button>
                </div>
            </div>

            <!-- 可用工具 -->
            <div class="section">
                <div class="section-header">
                    🔧 可用工具 (<span id="toolCount">0</span>个)
                </div>
                <div class="section-content">
                    <button id="loadToolsBtn" class="btn">加载工具列表</button>
                    <div id="toolsGrid" class="tool-grid"></div>
                </div>
            </div>

            <!-- 工具测试 -->
            <div class="section">
                <div class="section-header">
                    🧪 工具测试
                </div>
                <div class="section-content">
                    <div class="input-group">
                        <label for="toolSelect">选择工具:</label>
                        <select id="toolSelect">
                            <option value="">请先加载工具列表</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="toolParams">参数 (JSON格式):</label>
                        <textarea id="toolParams" placeholder='{"page": 1, "size": 10}'></textarea>
                    </div>
                    <button id="callToolBtn" class="btn">调用工具</button>
                </div>
            </div>

            <!-- 消息日志 -->
            <div class="section">
                <div class="section-header">
                    📋 消息日志
                    <button id="clearLogBtn" class="btn" style="float: right; font-size: 12px; padding: 4px 8px;">清空日志</button>
                </div>
                <div class="section-content">
                    <div id="messageLog" class="log"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let clientId = null;
        let tools = [];

        // DOM元素
        const connectionStatus = document.getElementById('connectionStatus');
        const clientIdSpan = document.getElementById('clientId');
        const activeConnectionsSpan = document.getElementById('activeConnections');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const loadToolsBtn = document.getElementById('loadToolsBtn');
        const toolsGrid = document.getElementById('toolsGrid');
        const toolCount = document.getElementById('toolCount');
        const toolSelect = document.getElementById('toolSelect');
        const toolParams = document.getElementById('toolParams');
        const callToolBtn = document.getElementById('callToolBtn');
        const messageLog = document.getElementById('messageLog');
        const clearLogBtn = document.getElementById('clearLogBtn');

        // 连接SSE
        function connect() {
            if (eventSource) {
                eventSource.close();
            }

            clientId = 'web_client_' + Date.now();
            const url = `http://localhost:18002/mcp/sse?clientId=${clientId}`;
            
            addLog('info', `正在连接到: ${url}`);
            
            eventSource = new EventSource(url);
            
            eventSource.onopen = function(event) {
                updateConnectionStatus(true);
                addLog('success', 'SSE连接已建立');
            };
            
            eventSource.onerror = function(event) {
                updateConnectionStatus(false);
                addLog('error', 'SSE连接错误');
            };
            
            eventSource.addEventListener('connection', function(event) {
                const data = JSON.parse(event.data);
                addLog('info', `连接消息: ${JSON.stringify(data, null, 2)}`);
                if (data.metadata && data.metadata.availableTools) {
                    tools = data.metadata.availableTools;
                    updateToolsList();
                }
            });
            
            eventSource.addEventListener('heartbeat', function(event) {
                const data = JSON.parse(event.data);
                if (data.metadata && data.metadata.activeConnections !== undefined) {
                    activeConnectionsSpan.textContent = data.metadata.activeConnections;
                }
                addLog('info', `心跳: ${data.metadata.activeConnections} 个活跃连接`);
            });
            
            eventSource.addEventListener('tool_call_result', function(event) {
                const data = JSON.parse(event.data);
                addLog('success', `工具调用结果: ${JSON.stringify(data.metadata, null, 2)}`);
            });
            
            eventSource.addEventListener('tool_call_error', function(event) {
                const data = JSON.parse(event.data);
                addLog('error', `工具调用错误: ${JSON.stringify(data.metadata, null, 2)}`);
            });
        }

        // 断开连接
        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            updateConnectionStatus(false);
            addLog('warning', 'SSE连接已断开');
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            if (connected) {
                connectionStatus.textContent = '已连接';
                connectionStatus.className = 'status connected';
                clientIdSpan.textContent = clientId;
            } else {
                connectionStatus.textContent = '未连接';
                connectionStatus.className = 'status disconnected';
                clientIdSpan.textContent = '-';
                activeConnectionsSpan.textContent = '-';
            }
        }

        // 加载工具列表
        async function loadTools() {
            try {
                const response = await fetch('http://localhost:18002/mcp/tools');
                const data = await response.json();
                tools = data.tools || [];
                toolCount.textContent = tools.length;
                updateToolsList();
                addLog('success', `加载了 ${tools.length} 个工具`);
            } catch (error) {
                addLog('error', `加载工具失败: ${error.message}`);
            }
        }

        // 更新工具列表显示
        function updateToolsList() {
            // 更新工具网格
            toolsGrid.innerHTML = '';
            tools.forEach(tool => {
                const toolCard = document.createElement('div');
                toolCard.className = 'tool-card';
                toolCard.innerHTML = `
                    <div class="tool-name">${tool.name}</div>
                    <div class="tool-category">${tool.category}</div>
                    <div class="tool-description">${tool.description}</div>
                `;
                toolsGrid.appendChild(toolCard);
            });

            // 更新工具选择下拉框
            toolSelect.innerHTML = '<option value="">请选择工具</option>';
            tools.forEach(tool => {
                const option = document.createElement('option');
                option.value = tool.name;
                option.textContent = `${tool.name} - ${tool.description}`;
                toolSelect.appendChild(option);
            });

            toolCount.textContent = tools.length;
        }

        // 调用工具
        async function callTool() {
            const toolName = toolSelect.value;
            if (!toolName) {
                addLog('warning', '请选择要调用的工具');
                return;
            }

            let params = {};
            try {
                if (toolParams.value.trim()) {
                    params = JSON.parse(toolParams.value);
                }
            } catch (error) {
                addLog('error', `参数格式错误: ${error.message}`);
                return;
            }

            try {
                addLog('info', `调用工具: ${toolName}, 参数: ${JSON.stringify(params)}`);
                
                const response = await fetch('http://localhost:18002/mcp/call', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool: toolName,
                        params: params,
                        clientId: clientId
                    })
                });

                const result = await response.json();
                addLog('success', `工具调用响应: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                addLog('error', `工具调用失败: ${error.message}`);
            }
        }

        // 添加日志
        function addLog(type, message) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;
            messageLog.appendChild(logEntry);
            messageLog.scrollTop = messageLog.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            messageLog.innerHTML = '';
        }

        // 事件监听
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        loadToolsBtn.addEventListener('click', loadTools);
        callToolBtn.addEventListener('click', callTool);
        clearLogBtn.addEventListener('click', clearLog);

        // 页面加载完成后自动加载工具列表
        window.addEventListener('load', function() {
            addLog('info', '页面加载完成，可以开始测试MCP服务器');
            loadTools();
        });
    </script>
</body>
</html>
