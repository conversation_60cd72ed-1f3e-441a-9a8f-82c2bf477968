@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║               Cool Admin MCP Server                          ║
echo ║                                                              ║
echo ║   🚀 正在启动SSE Stream MCP服务器...                          ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境，请确保已安装Java 17或更高版本
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo ✅ Java和Maven环境检查通过
echo.

REM 编译项目
echo 📦 正在编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo ❌ 编译失败，请检查代码
    pause
    exit /b 1
)

echo ✅ 编译成功
echo.

REM 启动服务器
echo 🚀 启动MCP服务器...
echo.
echo 📋 服务信息:
echo    • 端口: 18002
echo    • SSE连接: http://localhost:18002/mcp/sse
echo    • 工具列表: http://localhost:18002/mcp/tools
echo    • 健康检查: http://localhost:18002/mcp/health
echo    • 测试页面: sse-test.html
echo.
echo 💡 提示: 
echo    • 确保Cool Admin服务器(端口18001)已启动
echo    • 使用Ctrl+C停止服务器
echo    • 可以在浏览器中打开sse-test.html进行测试
echo.

call mvn spring-boot:run

echo.
echo 👋 MCP服务器已停止
pause
