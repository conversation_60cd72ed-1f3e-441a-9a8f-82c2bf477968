#!/bin/bash

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                                                              ║"
echo "║               Cool Admin MCP Server                          ║"
echo "║                                                              ║"
echo "║   🚀 正在启动简洁的SSE Stream MCP服务器...                    ║"
echo "║                                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境，请确保已安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: 未找到Maven环境，请确保已安装Maven"
    exit 1
fi

echo "✅ Java和Maven环境检查通过"
echo

# 编译项目
echo "📦 正在编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "❌ 编译失败，请检查代码"
    exit 1
fi

echo "✅ 编译成功"
echo

# 启动服务器
echo "🚀 启动MCP服务器..."
echo
echo "📋 服务信息:"
echo "   • 端口: 18002"
echo "   • SSE连接: http://localhost:18002/mcp/messages"
echo "   • 健康检查: http://localhost:18002/actuator/health"
echo
echo "💡 提示: "
echo "   • 确保Cool Admin服务器(端口18001)已启动"
echo "   • 使用Ctrl+C停止服务器"
echo "   • 支持的工具:"
echo "     - SOP管理: 6个工具"
echo "     - 任务管理: 13个工具"
echo "     - 统计分析: 4个工具"
echo

mvn spring-boot:run

echo
echo "👋 MCP服务器已停止"
